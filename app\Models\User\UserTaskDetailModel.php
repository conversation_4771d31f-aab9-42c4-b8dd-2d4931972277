<?php

namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class UserTaskDetailModel extends BaseModel
{
    protected $table = 'user_task_detail';

    protected $fillable = [
        'user_id',
        'task_id',
        'directory_id',
        'user_account_id',
        'user_goods_id',
        'user_goods_sku_id',
        'goods_id',
        'goods_name',
        'goods_name_ai',
        'is_name_ai',
        'goods_property',
        'goods_property_ai',
        'is_property_ai',
        'thumb_url',
        'currentcy',
        'currentcy_goods',
        'price',
        'price_third',
        'price_list_third',
        'quantity',
        'vat_rate',
        'preparing_day',
        'spec_key_values',
        'spec_values',
        'is_skc_gallery',
        'category_id',
        'product_main_id',
        'stock_code',
        'status',
        'third_task_id',
        'third_type',
        'third_status',
        'third_result',
        'memo'
    ];

    //关联AI改写后的商品名称
    public function aiGoodsName(): HasOne
    {
        return $this->hasOne(UserTaskDetailGoodsNameModel::class, 'task_detail_id', 'id');
    }

    // 关联任务
    public function task(): BelongsTo
    {
        return $this->belongsTo(UserTaskModel::class, 'task_id', 'id');
    }

    // 关联商品
    public function goods(): BelongsTo
    {
        return $this->belongsTo(GoodsModel::class, 'user_goods_id', 'id');
    }

    // 关联商品SKU
    public function goodsSku(): BelongsTo
    {
        return $this->belongsTo(GoodsSkuModel::class, 'user_goods_sku_id', 'id');
    }

    // 关联店铺
    public function store(): BelongsTo
    {
        return $this->belongsTo(UserAccountModel::class, 'user_account_id', 'id');
    }

    // 关联上传参数
    public function uploadParams(): BelongsTo
    {
        return $this->belongsTo(UserTaskDetailUpParamsModel::class, 'id', 'task_detail_id');
    }

    // 作用域：按用户ID筛选
    public function scopeByUserId($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    // 作用域：按任务ID筛选
    public function scopeByTaskId($query, int $taskId)
    {
        return $query->where('task_id', $taskId);
    }

    // 作用域：按状态筛选
    public function scopeByStatus($query, int $status)
    {
        return $query->where('status', $status);
    }

    // 作用域：等待上传状态
    public function scopePending($query)
    {
        return $query->where('status', 0);
    }
}