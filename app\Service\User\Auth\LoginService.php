<?php
namespace App\Service\User\Auth;

use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Models\User\User;
use App\Utils\Tools;
use App\Utils\Jwt\Jwt;

class LoginService extends BaseService{

    protected $jwtService;
    protected $userModel;

    public function __construct(Jwt $jwtService,User $userModel)
    {
        $this->jwtService = $jwtService;
        $this->userModel = $userModel;
    }

    public function loginAuthCanEmpty(){
        $httpToken = request()->header()['authorization'][0] ?? '';
        if(!$httpToken){
            return ['user' => null, 'token' => ''];
        }
        return $this->loginAuth();
    }

    public function loginAuth(){
        $httpToken = request()->header()['authorization'][0] ?? '';
        if(!$httpToken){
            throw new MyException("请先登录",401);
        }
        if (strpos($httpToken, 'Bearer ') !== false) {
            $token_new = '';
            $token = str_replace('Bearer ', '', $httpToken);
            $data = $this->jwtService->verifyToken($token);
            if (!$data) {
                throw new MyException("请登录", 401);
            }
            if(!isset($data['exp']) || $data['exp']<=time()){
                if(empty($data['newtoken'])){
                    throw new MyException("登录已失效 请重新登录",401);
                }
            }
            if(!isset($data['data']['id']) || intval($data['data']['id'])<=0){
                throw new MyException("登录已失效 请重新登录",401);
            }

            if(isset($data['newtoken']) && !empty($data['newtoken'])){
                $token_new = $data['newtoken'];
            }
            $uid = $data['data']['id'];

            $fields = ['*'];
            $user_info = $this->userModel::where('id',$uid)->select($fields)->first();
            if(!$user_info){
                throw new MyException("会员不存在",500);
            }

            // 先转换为数组，再格式化时间字段
            $user_info = $user_info->toArray();

            if(!empty($user_info['vip_start_time'])){
                $user_info['vip_start_time'] = date('Y-m-d H:i:s', strtotime($user_info['vip_start_time']));
            }
            if(!empty($user_info['vip_end_time'])){
                $user_info['vip_end_time'] = date('Y-m-d H:i:s', strtotime($user_info['vip_end_time']));
            }

            if($user_info['pid'] > 0){
                //子账号
                $parentUser = $this->userModel::find($user_info['pid']);
                if(!$parentUser){
                    throw new MyException('账号不存在');
                }
                $parentUser = $parentUser->toArray();
                if($parentUser['status'] != 1){
                    throw new MyException('账号不存在');
                }
                $user_info['is_admin'] = 0;
                $user_info['is_card_admin'] = 0;
                $user_info['points'] = 0;
                if($parentUser['is_admin'] == 1){
                    $user_info['is_vip'] = 1;
                    $user_info['vip_end_time'] = "9999-12-31 23:59:59";
                }else{
                    if(empty($parentUser['vip_start_time']) && empty($parentUser['vip_end_time'])){
                        $user_info['is_vip'] = 0;
                    }else{
                        if(!empty($parentUser['vip_start_time'])){
                            $user_info['vip_start_time'] = date('Y-m-d H:i:s', strtotime($parentUser['vip_start_time']));
                        }
                        if(!empty($user_info['vip_end_time'])){
                            $user_info['vip_end_time'] = date('Y-m-d H:i:s', strtotime($parentUser['vip_end_time']));
                        }
                    }
                }
            }


            if(!empty($user_info['login_time'])){
                $user_info['login_time'] = date('Y-m-d H:i:s', strtotime($user_info['login_time']));
            }

            if((int)$user_info['status'] == 0){
                throw new MyException("会员已禁用",500);
            }
            $field_token = 'token_pc';
            $update = [];
            if($token_new){
                $update[$field_token] = $token_new;
            }else{
                if(empty($user_info[$field_token])){
                    throw new MyException("您的账户已退出登录",401);
                }
                if(trim($user_info[$field_token]) != trim($token)){
                    throw new MyException("您的账户在其它地方登录",401);
                }
            }

            if($user_info['is_admin']==1){
                $user_info['is_vip'] = 1;
                $user_info['vip_end_time'] = "9999-12-31 23:59:59";
            }else{
                if($user_info['is_vip'] == 1){
                    if(time() > strtotime($user_info['vip_end_time'])){
                        $user_info['is_vip'] = 0;
                    }
                }
            }
            $user_info['points'] = $user_info['points'] ?? 0;

            $data = [];
            $data['token_new'] = $token_new;
            $data['token']     = $token;
            $data['user']      = $user_info;
            return $data;
        }else{
            throw new MyException("请登录", 401);
        }
    }

    public function test_token($httpToken){
        if (strpos($httpToken, 'Bearer ') !== false) {
            $httpToken = str_replace('Bearer ', '', $httpToken);
        }
        $token = explode(':', $httpToken);
        if(count($token) != 2){
            throw new MyException("请先登录",401);
        }
        $user = User::where('phone', $token[0])->first();
        if(!$user){
            throw new MyException("用户不存在",401);
        }   
        if($user->isset ==2){
            throw new MyException("用户已禁用",403);
        }
        $token[1] = str_ireplace([' '],['+'],$token[1]);
        $dataStr = Tools::jsDecrypt($token[1],$user->pwd);
        if(!$dataStr || !stristr($dataStr,':')){
            //追加+ 再尝试一次
            $token[1] .= '+';
            $dataStr = Tools::jsDecrypt($token[1],$user->pwd);
            if(!$dataStr || !stristr($dataStr,':')){
                throw new MyException("用户不存在1",401);
            }
        }
        $data = explode(':',$dataStr);
        if(count($data) != 2){
            throw new MyException("用户不存在2",401);
        }
        if($data[0] != $user->phone){
            throw new MyException("用户不存在3",401);
        }
        if($data[1] != $user->pwd){
            throw new MyException("用户不存在4",401);
        }
        $user = $user->toArray();
        unset($user['pwd']);
        return ['user'  => $user,
                'token' => request()->header()['authorization'][0] ?? ''];
    }


}