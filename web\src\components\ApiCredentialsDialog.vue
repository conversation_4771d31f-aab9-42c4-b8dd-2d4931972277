<template>
  <el-dialog
    v-model="visible"
    title="API凭证信息"
    width="600px"
    :close-on-click-modal="false"
    :z-index="10002"
    append-to-body
    @close="handleClose"
    class="api-credentials-dialog"
  >
    <div class="credentials-content">
      <!-- 安全提示 -->
      <div class="security-notice">
        <div class="notice-card">
          <div class="notice-header">
            <el-icon class="notice-icon"><WarningFilled /></el-icon>
            <span class="notice-title">安全提示</span>
          </div>
          <div class="notice-content">
            <div class="notice-item">
              <el-icon class="item-icon"><CircleCheckFilled /></el-icon>
              <span>请妥善保管您的API凭证信息，不要泄露给他人</span>
            </div>
            <div class="notice-item">
              <el-icon class="item-icon"><CircleCheckFilled /></el-icon>
              <span>API Secret仅在首次生成时显示，请及时保存</span>
            </div>
            <div class="notice-item">
              <el-icon class="item-icon"><CircleCheckFilled /></el-icon>
              <span>如凭证丢失，可通过重置功能生成新凭证</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 凭证信息显示区域 -->
      <div class="credentials-info">
        <!-- App ID -->
        <div class="credential-item">
          <div class="credential-label">
            <el-icon><Key /></el-icon>
            <span>App ID</span>
          </div>
          <div class="credential-value">
            <div class="value-display">
              <el-input
                :model-value="credentials.appid"
                readonly
                class="credential-input"
              >
                <template #suffix>
                  <el-button
                    type="text"
                    @click="copyToClipboard(credentials.appid, 'App ID')"
                    class="copy-button"
                  >
                    <el-icon><DocumentCopy /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </div>
          </div>
        </div>

        <!-- App Secret -->
        <div class="credential-item" v-if="credentials.appsecret">
          <div class="credential-label">
            <el-icon><Lock /></el-icon>
            <span>App Secret</span>
          </div>
          <div class="credential-value">
            <div class="value-display">
              <el-input
                :model-value="showSecret ? credentials.appsecret : '••••••••••••••••••••••••••••••••'"
                readonly
                class="credential-input"
              >
                <template #suffix>
                  <div class="input-actions">
                    <el-button
                      type="text"
                      @click="toggleSecretVisibility"
                      class="toggle-button"
                    >
                      <el-icon>
                        <View v-if="!showSecret" />
                        <Hide v-else />
                      </el-icon>
                    </el-button>
                    <el-button
                      type="text"
                      @click="copyToClipboard(credentials.appsecret, 'App Secret')"
                      class="copy-button"
                    >
                      <el-icon><DocumentCopy /></el-icon>
                    </el-button>
                  </div>
                </template>
              </el-input>
            </div>
          </div>
        </div>

        <!-- 生成时间信息 -->
        <div class="credential-meta" v-if="credentials.created_at">
          <div class="meta-item">
            <span class="meta-label">生成时间：</span>
            <span class="meta-value">{{ formatDateTime(credentials.created_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="usage-instructions">
        <h4>使用说明</h4>
        <ul>
          <li>App ID 用于标识您的应用身份</li>
          <li>App Secret 用于API请求的身份验证，请妥善保管</li>
          <li>在API请求中，请将这些凭证作为认证参数使用</li>
          <li>如需重新生成凭证，请关闭此窗口后点击"重置凭证"按钮</li>
        </ul>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          <el-icon><Close /></el-icon>
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Key, Lock, DocumentCopy, View, Hide, Close, WarningFilled, CircleCheckFilled } from '@element-plus/icons-vue'
import type { ApiCredentialsData } from '../utils/apiCredentialsApi'
import { formatDateTime } from '../utils/date'

interface Props {
  modelValue: boolean
  credentials: ApiCredentialsData
}

interface Emits {
  'update:modelValue': [value: boolean]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const showSecret = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听弹窗关闭，重置敏感数据显示状态
watch(visible, (newVal) => {
  if (!newVal) {
    showSecret.value = false
  }
})

/**
 * 切换密钥显示状态
 */
const toggleSecretVisibility = () => {
  showSecret.value = !showSecret.value
}

/**
 * 复制到剪贴板
 */
const copyToClipboard = async (text: string, label: string) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // 使用现代 Clipboard API
      await navigator.clipboard.writeText(text)
    } else {
      // 降级方案：使用传统方法
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      textArea.remove()
    }
    
    ElMessage.success(`${label} 已复制到剪贴板`)
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动选择复制')
  }
}



/**
 * 处理弹窗关闭
 */
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.api-credentials-dialog {
  --primary-color: #409eff;
  --warning-color: #e6a23c;
  --success-color: #67c23a;
  --border-color: #dcdfe6;
  --text-color: #303133;
  --text-color-secondary: #606266;
  --bg-color: #f5f7fa;
}

:deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__header) {
  padding: 20px 24px 0;
  border-bottom: 1px solid var(--border-color);
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 24px;
  border-top: 1px solid var(--border-color);
}

.credentials-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.security-notice {
  margin-bottom: 8px;
}

.notice-card {
  background: linear-gradient(135deg, #fff7ed, #fef3c7);
  border: 1px solid #fbbf24;
  border-radius: 12px;
  padding: 16px;
}

.notice-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.notice-icon {
  color: #f59e0b;
  font-size: 18px;
}

.notice-title {
  font-size: 15px;
  font-weight: 600;
  color: #92400e;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
  color: #a16207;
  line-height: 1.4;
}

.item-icon {
  color: #059669;
  font-size: 14px;
  margin-top: 1px;
  flex-shrink: 0;
}

.credentials-info {
  background: var(--bg-color);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--border-color);
}

.credential-item {
  margin-bottom: 20px;
}

.credential-item:last-child {
  margin-bottom: 0;
}

.credential-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
  font-size: 14px;
}

.credential-label .el-icon {
  color: var(--primary-color);
}

.credential-value {
  position: relative;
}

.value-display {
  position: relative;
}

.credential-input {
  font-family: 'Courier New', monospace;
}

.credential-input :deep(.el-input__wrapper) {
  background-color: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding-right: 8px;
}

.credential-input :deep(.el-input__inner) {
  font-size: 13px;
  letter-spacing: 0.5px;
}

.input-actions {
  display: flex;
  gap: 4px;
}

.copy-button,
.toggle-button {
  padding: 4px;
  color: var(--text-color-secondary);
  transition: color 0.2s;
}

.copy-button:hover,
.toggle-button:hover {
  color: var(--primary-color);
}

.credential-meta {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  color: var(--text-color-secondary);
  min-width: 80px;
}

.meta-value {
  color: var(--text-color);
  font-family: 'Courier New', monospace;
}

.usage-instructions {
  background: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
}

.usage-instructions h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.usage-instructions ul {
  margin: 0;
  padding-left: 20px;
  line-height: 1.6;
}

.usage-instructions li {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.usage-instructions li:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

.dialog-footer .el-button {
  min-width: 120px;
  border-radius: 6px;
  font-weight: 500;
}

.dialog-footer .el-icon {
  margin-right: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  :deep(.el-dialog__body) {
    padding: 16px;
  }
  
  .credentials-content {
    gap: 16px;
  }
  
  .credentials-info,
  .usage-instructions {
    padding: 16px;
  }
  
  .credential-input :deep(.el-input__inner) {
    font-size: 12px;
  }
  
  .input-actions {
    flex-direction: column;
    gap: 2px;
  }
}
</style>