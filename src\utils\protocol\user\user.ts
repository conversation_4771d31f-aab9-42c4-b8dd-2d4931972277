import * as utils_new from '@/utils/new/utils';
import config from "@/config";
import { handleApiResponse, getWebCookie } from '../common/common';

/**
 * 用户登录响应接口
 */
interface LoginResponse {
  id: number;           // 用户ID
  phone: string;        // 手机号
  is_vip: number;       // VIP状态 (1: 是VIP, 0: 不是VIP)
  vip_end_time: string; // VIP到期时间
  is_admin: number;     // 管理员状态 (1: 是管理员, 0: 不是管理员)
  is_card_admin: number; // 卡密管理员状态 (1: 是卡密管理员, 0: 不是卡密管理员)
  is_sub: number;       // 子账号状态 (1: 是子账号, 0: 不是子账号)
  appid: string;        // 应用ID
  appstatus: number;    // 应用状态
  token: string;        // 认证令牌
  points: number;       // 用户积分
}

/**
 * 获取图片验证码
 * @returns Promise<{image_key: string, image_base64: string}> 验证码结果
 */
export const getCaptcha = async (): Promise<{image_key: string, image_base64: string}> => {
  const url = config.apiCaptchaUrl;
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  }

  return new Promise<{image_key: string, image_base64: string}>((resolve) => {
    // 创建请求对象
    const request = {
      funType: 'axios',
      funName: 'getCaptchaRequest',
      pramas: {},
      headers,
      method: 'get',
      url,
      auth: false    // 不需要认证
      // 不需要指定encrypto参数，会自动根据url判断
    };

    chrome.runtime.sendMessage(request, (response: any) => {
      console.log("-----------获取验证码-----------");
      console.log('response', response);
      const result = handleApiResponse(response);
      console.log('result', result);
      resolve(result);
    });
  });
}

/**
 * 用户注册方法
 * @param phone 手机号
 * @param password 密码
 * @param captcha 验证码
 * @param imageKey 验证码key
 * @returns Promise<{any}> 注册结果
 */
export const registerUser = async (phone: string, password: string, captcha: string, imageKey: string): Promise<any> => {
  const url = config.apiRegisterUrl;
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  }

  const data = {
    phone,
    password,
    captcha,
    imageKey,
    version: chrome.runtime.getManifest().version
  }

  return new Promise((resolve) => {
    // 创建请求对象
    const request = {
      funType: 'axios',
      funName: 'userRegisterRequest',
      pramas: data,
      headers,
      method: 'post',
      url,
      auth: false     // 不需要认证
    };

    chrome.runtime.sendMessage(request, (response: any) => {
      console.log("-----------用户注册-----------");
      console.log('response', response);
      const result = handleApiResponse(response);
      console.log('result', result);
      resolve(result);
    });
  });
}

/**
 * 用户登录方法
 * @param phone 手机号
 * @param password 密码
 * @returns Promise<LoginResponse> 登录结果，包含用户信息和权限状态
 */
export const loginUser = async (phone: string, password: string): Promise<LoginResponse> => {
  const url = config.apiLoginUrl;
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  }

  const data = {
    phone,
    password,
    version: chrome.runtime.getManifest().version
  }

  return new Promise<LoginResponse>((resolve) => {
    // 创建请求对象
    const request = {
      funType: 'axios',
      funName: 'userLoginRequest',
      pramas: data,
      headers,
      method: 'post',
      url,
      auth: false     // 不需要认证
    };

    chrome.runtime.sendMessage(request, (response: any) => {
      console.log("-----------用户登录-----------");
      console.log('response', response);
      const result = handleApiResponse(response);
      console.log('result', result);
      resolve(result);
    });
  });
}
