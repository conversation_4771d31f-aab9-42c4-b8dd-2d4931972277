/**
 * 子账号管理API接口
 */
import { sendRequestViaBackground } from './api'
import { API_URLS, getApiUrl } from './apiConfig'

// 子账号接口类型定义
export interface SubAccount {
  id: number
  name: string
  phone: string
  status: number
  status_text: string
  created_at: string
  updated_at: string
  last_login_at?: string
  total_goods_count: number  // 商品总数
  today_goods_count: number  // 今日商品数量
}

// 子账号列表参数
export interface SubAccountListParams {
  page?: number
  pageSize?: number
  name?: string
  phone?: string
  status?: number
}

// 分页信息接口
export interface PaginationInfo {
  current: number
  pageSize: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
}

// 子账号列表响应（sendRequestViaBackground已自动提取data字段）
export interface SubAccountListResponse {
  list: SubAccount[]
  pagination: PaginationInfo
  sub_num_limit: number
  can_add_more: boolean
}

// 创建子账号参数
export interface CreateSubAccountParams {
  name: string
  phone: string
  password: string
}

// 更新子账号参数
export interface UpdateSubAccountParams {
  id: number
  name: string
  phone: string
  password?: string
}

// 切换状态参数
export interface ToggleStatusParams {
  id: number
  status: number
}

// 删除子账号参数
export interface DeleteSubAccountParams {
  id: number
}

// API响应基础接口（sendRequestViaBackground已自动提取data字段）
// 对于创建、更新、删除等操作，通常返回操作结果数据
export interface OperationResult {
  id?: number
  name?: string
  phone?: string
  status?: number
  created_at?: string
  updated_at?: string
}



/**
 * 获取子账号列表
 * @param params 查询参数
 * @returns 子账号列表响应
 */
export const getSubAccountList = async (params?: SubAccountListParams): Promise<SubAccountListResponse> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_LIST);
  console.log('获取子账号列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getSubAccountList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 创建子账号
 * @param params 创建参数
 * @returns 操作结果
 */
export const createSubAccount = async (params: CreateSubAccountParams): Promise<OperationResult> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_CREATE);
  console.log('创建子账号URL:', url)
  return sendRequestViaBackground({
    funName: 'createSubAccount',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};

/**
 * 更新子账号
 * @param params 更新参数
 * @returns 操作结果
 */
export const updateSubAccount = async (params: UpdateSubAccountParams): Promise<OperationResult> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_UPDATE);
  console.log('更新子账号URL:', url)
  return sendRequestViaBackground({
    funName: 'updateSubAccount',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};

/**
 * 切换子账号状态
 * @param params 切换状态参数
 * @returns 操作结果
 */
export const toggleSubAccountStatus = async (params: ToggleStatusParams): Promise<OperationResult> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_TOGGLE_STATUS);
  console.log('切换子账号状态URL:', url)
  return sendRequestViaBackground({
    funName: 'toggleSubAccountStatus',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};

/**
 * 删除子账号
 * @param params 删除参数
 * @returns 操作结果
 */
export const deleteSubAccount = async (params: DeleteSubAccountParams): Promise<OperationResult> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_DELETE);
  console.log('删除子账号URL:', url)
  return sendRequestViaBackground({
    funName: 'deleteSubAccount',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};

/**
 * 获取子账号详情
 * @param id 子账号ID
 * @returns 子账号详情
 */
export const getSubAccountDetail = async (id: number): Promise<SubAccount> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_DETAIL);
  console.log('获取子账号详情URL:', url)
  return sendRequestViaBackground({
    funName: 'getSubAccountDetail',
    url,
    method: 'get',
    params: { id },
    auth: true
  });
};
