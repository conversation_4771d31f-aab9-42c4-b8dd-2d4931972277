<?php
declare(strict_types=1);

namespace App\Models\User;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GoodsSkuModel extends BaseModel
{
    protected $table = 'user_goods_sku';
    
    protected $fillable = [
        'user_goods_id',
        'sku_id',
        'goods_id',
        'thumb_url',
        'currentcy',
        'price',
        'spec_key_values',
        'spec_values',
        'skc_gallery',
        'is_skc_gallery',
        'url',
        'is_price_modified',
    ];

/*     protected $casts = [
        'user_goods_id' => 'integer',
        'sku_id' => 'integer',
        'goods_id' => 'integer',
        'price' => 'decimal:2',
        'is_skc_gallery' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'skc_gallery' => 'array',
    ]; */

    // 关联商品
    public function goods(): BelongsTo
    {
        return $this->belongsTo(GoodsModel::class, 'user_goods_id', 'id');
    }

    // 作用域：按商品ID筛选
    public function scopeByGoodsId($query, int $goodsId)
    {
        return $query->where('goods_id', $goodsId);
    }

    // 作用域：按用户商品ID筛选
    public function scopeByUserGoodsId($query, int $userGoodsId)
    {
        return $query->where('user_goods_id', $userGoodsId);
    }

    // 作用域：按SKU ID筛选
    public function scopeBySkuId($query, int $skuId)
    {
        return $query->where('sku_id', $skuId);
    }

    // 关联价格调整日志
    public function priceAdjustmentLogs(): HasMany
    {
        return $this->hasMany(GoodsPriceAdjustmentLogModel::class, 'sku_id', 'id');
    }
}