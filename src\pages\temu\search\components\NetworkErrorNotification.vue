<template>
  <div v-if="visible" class="network-error-notification">
    <div class="notification-content">
      <div class="notification-icon">⚠️</div>
      <div class="notification-text">
        <div class="notification-title">智能获取更多搜索结果启动失败</div>
        <div class="notification-message">如需获取更多搜索结果并导出EXCEL，请关闭该页面重新打开新的页面检索</div>
      </div>
      <button @click="handleClose" class="notification-close">×</button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'close'): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const handleClose = () => {
  emit('close');
};
</script>

<style scoped>
.network-error-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10001;
  max-width: 350px;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-content {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: flex-start;
  gap: 12px;
  position: relative;
}

.notification-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-text {
  flex: 1;
}

.notification-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  line-height: 1.3;
}

.notification-message {
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.notification-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>