<?php

declare(strict_types=1);

namespace App\Exceptions;

/**
 * API认证异常
 * 用于EXE API接口的认证失败场景
 */
class ApiAuthenticationException extends \Exception
{
    protected $code = 401;
    protected $message = 'API认证失败';

    public function __construct(?string $message = null, ?int $code = null, ?\Throwable $previous = null)
    {
        parent::__construct(
            $message ?? $this->message,
            $code ?? $this->code,
            $previous
        );
    }

    /**
     * 获取异常的响应数据
     *
     * @return array
     */
    public function getResponseData(): array
    {
        return [
            'code' => $this->getCode(),
            'message' => $this->getMessage(),
            'data' => null,
            'errors' => [
                'type' => 'authentication_error',
                'detail' => '请检查appid和appsecret是否正确'
            ]
        ];
    }
}