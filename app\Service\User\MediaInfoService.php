<?php

declare(strict_types=1);

namespace App\Service\User;

use App\Service\BaseService;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsInstructionImagesModel;
use App\Models\User\GoodsSkuModel;

/**
 * 媒体文件信息获取服务
 * 复用ImageProcessService的媒体文件获取逻辑
 */
class MediaInfoService extends BaseService
{
    /**
     * 获取商品的所有媒体文件信息
     * 基于ImageProcessService::getMediaInfo的逻辑
     *
     * @param GoodsModel $goods
     * @param bool $onlyRemote 是否只返回远程URL的文件
     * @return array
     */
    public function getGoodsMediaInfo(GoodsModel $goods, bool $onlyRemote = true): array
    {
        $mediaInfo = [
            'images' => [],
            'videos' => [],
            'pdfs' => [],
            'total_images' => 0,
            'total_videos' => 0,
            'total_pdfs' => 0,
            'processed_images' => 0,
            'processed_videos' => 0,
            'processed_pdfs' => 0
        ];

        // 用于统计已处理数量的临时数组（避免重复查询）
        $allImages = [];
        $allVideos = [];
        $allPdfs = [];

        // 处理商品主图片
        if (!empty($goods->goods_pic)) {
            $goodsPics = json_decode($goods->goods_pic, true);
            if (is_array($goodsPics)) {
                foreach ($goodsPics as $pic) {
                    if (is_string($pic) && !empty($pic)) {
                        $imageData = [
                            'url' => $pic,
                            'type' => 'goods_pic',
                            'field' => 'goods_pic'
                        ];

                        $allImages[] = $imageData;
                        if (!$onlyRemote || $this->isRemoteUrl($pic)) {
                            $mediaInfo['images'][] = $imageData;
                        }
                    }
                }
            }
        }

        // 处理商品详情图片
        if (!empty($goods->goods_detail)) {
            $goodsDetailPics = json_decode($goods->goods_detail, true);
            if (is_array($goodsDetailPics)) {
                foreach ($goodsDetailPics as $pic) {
                    if (is_string($pic) && !empty($pic)) {
                        $imageData = [
                            'url' => $pic,
                            'type' => 'goods_detail',
                            'field' => 'goods_detail'
                        ];

                        $allImages[] = $imageData;
                        if (!$onlyRemote || $this->isRemoteUrl($pic)) {
                            $mediaInfo['images'][] = $imageData;
                        }
                    }
                }
            }
        }

        // 处理商品视频
        if (!empty($goods->goods_video) && is_string($goods->goods_video)) {
            $videoData = [
                'url' => $goods->goods_video,
                'type' => 'goods_video',
                'field' => 'goods_video'
            ];

            $allVideos[] = $videoData;
            if (!$onlyRemote || $this->isRemoteUrl($goods->goods_video)) {
                $mediaInfo['videos'][] = $videoData;
            }
        }

        // 处理商品PDF文件
        if (!empty($goods->goods_pdf) && is_string($goods->goods_pdf)) {
            // 同时支持远程URL和本地路径
            if (preg_match('/\.pdf(?:\?.*)?$/i', $goods->goods_pdf)) {
                $pdfData = [
                    'url' => $goods->goods_pdf,
                    'type' => 'goods_pdf',
                    'field' => 'goods_pdf'
                ];

                $allPdfs[] = $pdfData;
                if (!$onlyRemote || $this->isRemoteUrl($goods->goods_pdf)) {
                    $mediaInfo['pdfs'][] = $pdfData;
                }
            }
        }

        // 处理商品介绍图片（只查询一次）
        $instructionImages = GoodsInstructionImagesModel::where('user_goods_id', $goods->id)->first();
        if ($instructionImages && !empty($instructionImages->urls)) {
            $urls = json_decode($instructionImages->urls, true);
            if (is_array($urls)) {
                foreach ($urls as $url) {
                    if (is_string($url) && !empty($url)) {
                        $imageData = [
                            'url' => $url,
                            'type' => 'instruction_images',
                            'field' => 'urls',
                            'model_id' => $instructionImages->id
                        ];

                        $allImages[] = $imageData;
                        if (!$onlyRemote || $this->isRemoteUrl($url)) {
                            $mediaInfo['images'][] = $imageData;
                        }
                    }
                }
            }
        }

        // 处理SKU图片（只查询一次）
        $skus = GoodsSkuModel::where('user_goods_id', $goods->id)->get();
        foreach ($skus as $sku) {
            // 处理缩略图
            if (!empty($sku->thumb_url) && is_string($sku->thumb_url)) {
                $imageData = [
                    'url' => $sku->thumb_url,
                    'type' => 'sku_thumb',
                    'field' => 'thumb_url',
                    'sku_id' => $sku->id
                ];

                $allImages[] = $imageData;
                if (!$onlyRemote || $this->isRemoteUrl($sku->thumb_url)) {
                    $mediaInfo['images'][] = $imageData;
                }
            }

            // 处理SKC图片集
            if (!empty($sku->skc_gallery)) {
                $skcGallery = json_decode($sku->skc_gallery, true);
                if (is_array($skcGallery)) {
                    foreach ($skcGallery as $item) {
                        if (isset($item['url']) && is_string($item['url']) && !empty($item['url'])) {
                            $imageData = [
                                'url' => $item['url'],
                                'type' => 'sku_skc_gallery',
                                'field' => 'skc_gallery',
                                'sku_id' => $sku->id,
                                'gallery_item' => $item
                            ];

                            $allImages[] = $imageData;
                            if (!$onlyRemote || $this->isRemoteUrl($item['url'])) {
                                $mediaInfo['images'][] = $imageData;
                            }
                        }
                    }
                }
            }
        }

        // 统计数量
        $mediaInfo['total_images'] = count($mediaInfo['images']);
        $mediaInfo['total_videos'] = count($mediaInfo['videos']);
        $mediaInfo['total_pdfs'] = count($mediaInfo['pdfs']);

        // 如果需要统计已处理数量，使用已收集的所有文件数据（避免重复查询）
        if ($onlyRemote) {
            // 统计已处理数量（本地文件数量）
            foreach ($allImages as $image) {
                if (!$this->isRemoteUrl($image['url'])) {
                    $mediaInfo['processed_images']++;
                }
            }

            foreach ($allVideos as $video) {
                if (!$this->isRemoteUrl($video['url'])) {
                    $mediaInfo['processed_videos']++;
                }
            }

            foreach ($allPdfs as $pdf) {
                if (!$this->isRemoteUrl($pdf['url'])) {
                    $mediaInfo['processed_pdfs']++;
                }
            }
        }

        return $mediaInfo;
    }

    /**
     * 获取商品的待处理媒体文件（返回所有图片数据）
     *
     * @param GoodsModel $goods
     * @return array
     */
    public function getPendingMediaFiles(GoodsModel $goods): array
    {
        $mediaInfo = $this->getGoodsMediaInfo($goods, false);
        
        // 统计已本地化和待本地化的数量
        $processed_images = 0;
        $pending_images = 0;
        $processed_videos = 0;
        $pending_videos = 0;
        $processed_pdfs = 0;
        $pending_pdfs = 0;

        // 统计图片
        foreach ($mediaInfo['images'] as $image) {
            if ($this->isRemoteUrl($image['url'])) {
                $pending_images++;
            } else {
                $processed_images++;
            }
        }

        // 统计视频
        foreach ($mediaInfo['videos'] as $video) {
            if ($this->isRemoteUrl($video['url'])) {
                $pending_videos++;
            } else {
                $processed_videos++;
            }
        }

        // 统计PDF
        foreach ($mediaInfo['pdfs'] as $pdf) {
            if ($this->isRemoteUrl($pdf['url'])) {
                $pending_pdfs++;
            } else {
                $processed_pdfs++;
            }
        }
        
        return [
            'images' => $mediaInfo['images'],
            'videos' => $mediaInfo['videos'],
            'pdfs' => $mediaInfo['pdfs'],
            'statistics' => [
                'processed_images' => $processed_images,
                'pending_images' => $pending_images,
                'processed_videos' => $processed_videos,
                'pending_videos' => $pending_videos,
                'processed_pdfs' => $processed_pdfs,
                'pending_pdfs' => $pending_pdfs,
                'total_images' => $processed_images + $pending_images,
                'total_videos' => $processed_videos + $pending_videos,
                'total_pdfs' => $processed_pdfs + $pending_pdfs
            ]
        ];
    }

    /**
     * 获取商品的媒体文件统计信息
     *
     * @param GoodsModel $goods
     * @return array
     */
    public function getMediaStatistics(GoodsModel $goods): array
    {
        $mediaInfo = $this->getGoodsMediaInfo($goods, true);
        
        return [
            'total_images' => $mediaInfo['total_images'],
            'total_videos' => $mediaInfo['total_videos'],
            'total_pdfs' => $mediaInfo['total_pdfs'],
            'processed_images' => $mediaInfo['processed_images'],
            'processed_videos' => $mediaInfo['processed_videos'],
            'processed_pdfs' => $mediaInfo['processed_pdfs'],
            'remaining_images' => $mediaInfo['total_images'],
            'remaining_videos' => $mediaInfo['total_videos'],
            'remaining_pdfs' => $mediaInfo['total_pdfs']
        ];
    }

    /**
     * 判断URL是否为远程URL
     * 远程URL以http://或https://开头，本地路径以attachment/开头
     *
     * @param string $url
     * @return bool
     */
    private function isRemoteUrl(string $url): bool
    {
        return (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) && 
               strpos($url, 'attachment/') !== 0;
    }
}