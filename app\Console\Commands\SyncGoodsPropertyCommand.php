<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class SyncGoodsPropertyCommand extends Command
{
    /**
     * 命令名称和参数
     *
     * @var string
     */
    protected $signature = 'task:sync-goods-property';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '同步商品属性从user_goods表到user_task_detail表中goods_property为空的记录';

    /**
     * 统计信息
     */
    private $stats = [
        'total_empty_records' => 0,
        'records_with_source' => 0,
        'records_without_source' => 0,
        'successfully_updated' => 0,
        'failed_updates' => 0,
        'updated_with_ai' => 0,
        'updated_without_ai' => 0,
        'failed_records' => [],
        'sample_records' => []
    ];

    /**
     * 执行命令
     */
    public function handle()
    {
        try {
            $this->info('开始同步商品属性...');
            
            // 1. 查找需要更新的记录
            if (!$this->findEmptyPropertyRecords()) {
                return Command::FAILURE;
            }

            // 2. 显示统计信息并确认
            if (!$this->confirmExecution()) {
                $this->info('操作已取消');
                return Command::SUCCESS;
            }

            // 3. 执行同步操作
            $this->executeSyncOperation();

            // 4. 显示结果统计
            $this->showResults();

            return Command::SUCCESS;
            
        } catch (Exception $e) {
            $this->error("执行过程中发生错误: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 查找需要更新的记录
     */
    private function findEmptyPropertyRecords(): bool
    {
        $this->info('正在查找goods_property为空的记录...');
        
        try {
            // 查找所有goods_property为空或null的记录
            $emptyRecords = DB::table('user_task_detail')
                ->where(function($query) {
                    $query->whereNull('goods_property')
                          ->orWhere('goods_property', '');
                })
                ->get(['id', 'user_goods_id', 'is_property_ai', 'goods_name']);

            $this->stats['total_empty_records'] = $emptyRecords->count();
            
            if ($this->stats['total_empty_records'] == 0) {
                $this->info('没有找到需要更新的记录');
                return false;
            }

            $this->info("找到 {$this->stats['total_empty_records']} 条需要更新的记录");

            // 分析这些记录，检查对应的user_goods记录是否存在
            $this->analyzeEmptyRecords($emptyRecords);

            return true;
            
        } catch (Exception $e) {
            $this->error("查找空记录时发生错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 分析空记录
     */
    private function analyzeEmptyRecords($emptyRecords): void
    {
        $this->info('正在分析空记录...');
        
        $progressBar = $this->output->createProgressBar($emptyRecords->count());
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');
        $progressBar->setMessage('准备开始分析...');
        $progressBar->start();

        foreach ($emptyRecords as $record) {
            $progressBar->setMessage("分析记录: ID={$record->id}, user_goods_id={$record->user_goods_id}");
            
            // 查找对应的user_goods记录
            $userGoods = DB::table('user_goods')
                ->where('id', $record->user_goods_id)
                ->first(['id', 'goods_property']);

            if ($userGoods && !empty($userGoods->goods_property)) {
                $this->stats['records_with_source']++;
                
                // 收集前10个记录的详细信息用于人工核验
                if (count($this->stats['sample_records']) < 10) {
                    $this->stats['sample_records'][] = [
                        'task_detail_id' => $record->id,
                        'user_goods_id' => $record->user_goods_id,
                        'is_property_ai' => $record->is_property_ai,
                        'goods_name' => $record->goods_name,
                        'source_property' => mb_substr($userGoods->goods_property, 0, 100) . '...'
                    ];
                }
            } else {
                $this->stats['records_without_source']++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);
        $this->info('记录分析完成');
        
        $this->info("有源数据的记录数: {$this->stats['records_with_source']}");
        $this->info("无源数据的记录数: {$this->stats['records_without_source']}");
    }

    /**
     * 确认执行
     */
    private function confirmExecution(): bool
    {
        $this->newLine();
        $this->info('=== 同步统计 ===');
        $this->info("总需要更新的记录数: {$this->stats['total_empty_records']}");
        $this->info("有源数据可同步的记录数: {$this->stats['records_with_source']}");
        $this->info("无源数据无法同步的记录数: {$this->stats['records_without_source']}");
        $this->newLine();

        // 显示样本记录供人工核验
        $this->showSampleRecords();
        
        $this->warn('此操作将会：');
        $this->warn('1. 从user_goods表获取goods_property值');
        $this->warn('2. 更新user_task_detail表中的goods_property字段');
        $this->warn('3. 根据is_property_ai字段决定是否同时更新goods_property_ai字段');
        $this->newLine();
        
        return $this->confirm('确认要执行同步操作吗？');
    }

    /**
     * 显示样本记录供人工核验
     */
    private function showSampleRecords(): void
    {
        if (empty($this->stats['sample_records'])) {
            return;
        }

        $this->info('=== 样本记录（前10条）===');
        $this->newLine();

        foreach ($this->stats['sample_records'] as $index => $sample) {
            $this->info("样本 " . ($index + 1) . ":");
            $this->info("  task_detail_id: {$sample['task_detail_id']}");
            $this->info("  user_goods_id: {$sample['user_goods_id']}");
            $this->info("  is_property_ai: {$sample['is_property_ai']}");
            $this->info("  商品名称: " . mb_substr($sample['goods_name'], 0, 50) . "...");
            $this->info("  源属性数据: {$sample['source_property']}");
            $this->newLine();
        }

        $this->warn('请仔细核验以上样本记录，确认同步逻辑是否正确！');
        $this->newLine();
    }

    /**
     * 执行同步操作
     */
    private function executeSyncOperation(): void
    {
        $this->info('开始执行同步操作...');
        
        // 获取所有需要更新的记录
        $emptyRecords = DB::table('user_task_detail')
            ->where(function($query) {
                $query->whereNull('goods_property')
                      ->orWhere('goods_property', '')
                      ->orWhere('goods_property', 'null');
            })
            ->get(['id', 'user_goods_id', 'is_property_ai']);

        $progressBar = $this->output->createProgressBar($emptyRecords->count());
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');
        $progressBar->setMessage('准备开始同步...');
        $progressBar->start();

        foreach ($emptyRecords as $record) {
            $progressBar->setMessage("同步记录: ID={$record->id}");
            
            DB::beginTransaction();
            
            try {
                // 查找对应的user_goods记录
                $userGoods = DB::table('user_goods')
                    ->where('id', $record->user_goods_id)
                    ->first(['goods_property']);

                if ($userGoods && !empty($userGoods->goods_property)) {
                    $updateData = [
                        'goods_property' => $userGoods->goods_property,
                        'updated_at' => now()
                    ];

                    // 根据is_property_ai字段决定goods_property_ai的值
                    if ($record->is_property_ai == 1) {
                        $updateData['goods_property_ai'] = $userGoods->goods_property;
                        $this->stats['updated_with_ai']++;
                    } else {
                        $updateData['goods_property_ai'] = null;
                        $this->stats['updated_without_ai']++;
                    }

                    // 更新记录
                    DB::table('user_task_detail')
                        ->where('id', $record->id)
                        ->update($updateData);

                    $this->stats['successfully_updated']++;
                } else {
                    // 记录无源数据的情况
                    $this->stats['records_without_source']++;
                }

                DB::commit();
                
            } catch (Exception $e) {
                DB::rollback();
                $this->stats['failed_updates']++;
                $this->stats['failed_records'][] = [
                    'id' => $record->id,
                    'user_goods_id' => $record->user_goods_id,
                    'error' => $e->getMessage()
                ];
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);
        $this->info('同步操作完成');
    }

    /**
     * 显示结果统计
     */
    private function showResults(): void
    {
        $this->info('=== 同步结果统计 ===');
        $this->info("总处理记录数: {$this->stats['total_empty_records']}");
        $this->info("成功更新记录数: {$this->stats['successfully_updated']}");
        $this->info("失败更新记录数: {$this->stats['failed_updates']}");
        $this->info("同时更新AI属性的记录数: {$this->stats['updated_with_ai']}");
        $this->info("未更新AI属性的记录数: {$this->stats['updated_without_ai']}");
        $this->info("无源数据的记录数: {$this->stats['records_without_source']}");
        
        if (!empty($this->stats['failed_records'])) {
            $this->warn("处理失败的记录数: " . count($this->stats['failed_records']));
            foreach ($this->stats['failed_records'] as $failed) {
                $this->warn("失败记录: ID={$failed['id']}, user_goods_id={$failed['user_goods_id']} - {$failed['error']}");
            }
        }
        
        // 计算成功率
        if ($this->stats['total_empty_records'] > 0) {
            $successRate = round($this->stats['successfully_updated'] / $this->stats['total_empty_records'] * 100, 2);
            $this->info("处理成功率: {$successRate}%");
        }
        
        $this->newLine();
        $this->info('商品属性同步操作已完成！');
    }
} 