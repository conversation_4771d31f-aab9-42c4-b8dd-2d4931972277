/**
 * 用户信息状态管理
 * 与web目录保持一致的用户信息管理逻辑
 */

// 用户信息接口
interface UserInfo {
  isLogin: boolean;
  phone: string;
  expiryDate: string;
  isVip: boolean;
  isAdmin: boolean;
  isCardAdmin: boolean;
  isSub: boolean;        // 新增：是否是子账号
  appId?: string;        // 新增：应用ID
  appStatus?: number;    // 新增：应用状态
  token: string;
  userId?: number;
  points?: number;
}

// 用户信息状态（使用普通对象，因为content script不支持Vue的reactive）
export const userInfo: UserInfo = {
  isLogin: false,
  phone: '',
  expiryDate: '',
  isVip: false,
  isAdmin: false,
  isCardAdmin: false,
  isSub: false,          // 新增：默认不是子账号
  appId: undefined,      // 新增：应用ID
  appStatus: undefined,  // 新增：应用状态
  token: '',
  userId: undefined,
  points: 0
};

// 事件监听器
const eventListeners: Array<() => void> = [];

/**
 * 添加用户信息变化监听器
 */
export const onUserInfoChange = (callback: () => void) => {
  eventListeners.push(callback);

  // 返回取消监听的函数
  return () => {
    const index = eventListeners.indexOf(callback);
    if (index > -1) {
      eventListeners.splice(index, 1);
    }
  };
};

/**
 * 触发用户信息变化事件
 */
const triggerUserInfoChange = () => {
  eventListeners.forEach(callback => {
    try {
      callback();
    } catch (error) {
      console.error('用户信息变化监听器执行失败:', error);
    }
  });
};

/**
 * 更新用户信息
 */
export const updateUserInfo = (newUserInfo: Partial<UserInfo>) => {
  Object.assign(userInfo, newUserInfo);
  triggerUserInfoChange();
};

/**
 * 清空用户信息
 */
export const clearUserInfo = () => {
  updateUserInfo({
    isLogin: false,
    phone: '',
    expiryDate: '',
    isVip: false,
    isAdmin: false,
    isCardAdmin: false,
    isSub: false,          // 新增：重置子账号状态
    appId: undefined,      // 新增：重置应用ID
    appStatus: undefined,  // 新增：重置应用状态
    token: '',
    userId: undefined,
    points: 0
  });
};

/**
 * 从Chrome存储获取数据
 */
const getSyncStorage = (keys: string | string[]): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (!chrome.storage) {
      reject(new Error('chrome.storage API不可用'));
      return;
    }

    chrome.storage.sync.get(keys, (result: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(result);
      }
    });
  });
};

/**
 * 从Chrome本地存储获取数据
 */
const getLocalStorage = (keys: string | string[]): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (!chrome.storage) {
      reject(new Error('chrome.storage API不可用'));
      return;
    }

    chrome.storage.local.get(keys, (result: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(result);
      }
    });
  });
};

/**
 * 获取用户信息（从sync和local存储中）
 */
const getUserInfoFromStorage = async (): Promise<UserInfo> => {
  try {
    // 从 sync storage 获取用户基本信息
    const syncData = await getSyncStorage(['isLogin', 'is_login', 'phone', 'expiryDate', 'isVip', 'isAdmin', 'is_admin', 'is_card_admin', 'is_sub', 'app_id', 'app_status', 'userId', 'points']);

    // 从 local storage 获取 token
    const localData = await getLocalStorage(['token']);

    // 处理登录状态的兼容性（支持 isLogin 和 is_login 两种格式）
    const isLogin = syncData.isLogin || syncData.is_login || false;
    const isAdmin = syncData.isAdmin || syncData.is_admin || false;
    const isCardAdmin = syncData.isCardAdmin || syncData.is_card_admin || false;
    const isSub = syncData.isSub || syncData.is_sub || false;
    return {
      isLogin,
      phone: syncData.phone || '',
      expiryDate: syncData.expiryDate || '',
      isVip: syncData.isVip || false,
      isAdmin,
      isCardAdmin,
      isSub,                                    // 新增：子账号状态
      appId: syncData.appId || syncData.app_id, // 新增：应用ID
      appStatus: syncData.appStatus || syncData.app_status, // 新增：应用状态
      token: localData.token || '',
      userId: syncData.userId || undefined,
      points: syncData.points || 0
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      isLogin: false,
      phone: '',
      expiryDate: '',
      isVip: false,
      isAdmin: false,
      isCardAdmin: false,
      isSub: false,          // 新增：默认不是子账号
      appId: undefined,      // 新增：应用ID
      appStatus: undefined,  // 新增：应用状态
      token: '',
      userId: undefined,
      points: 0
    };
  }
};

/**
 * 从缓存加载用户信息
 */
export const loadUserInfoFromCache = async () => {
  try {
    const cachedInfo = await getUserInfoFromStorage();

    if (cachedInfo.isLogin && cachedInfo.phone) {
      Object.assign(userInfo, {
        isLogin: true,
        phone: cachedInfo.phone,
        expiryDate: cachedInfo.expiryDate || '',
        isVip: cachedInfo.isVip || false,
        isAdmin: cachedInfo.isAdmin || false,
        isCardAdmin: cachedInfo.isCardAdmin || false,
        isSub: cachedInfo.isSub || false,          // 新增：子账号状态
        appId: cachedInfo.appId,                   // 新增：应用ID
        appStatus: cachedInfo.appStatus,           // 新增：应用状态
        token: cachedInfo.token || '',
        userId: cachedInfo.userId || undefined,
        points: cachedInfo.points || 0
      });

      console.log('从缓存加载用户信息成功:', {
        phone: userInfo.phone,
        isVip: userInfo.isVip,
        isAdmin: userInfo.isAdmin,
        isCardAdmin: userInfo.isCardAdmin,
        expiryDate: userInfo.expiryDate,
        userId: userInfo.userId
      });

      triggerUserInfoChange();
    } else {
      console.log('缓存中没有有效的用户信息');
      clearUserInfo();
    }
  } catch (error) {
    console.error('从缓存加载用户信息失败:', error);
    clearUserInfo();
  }
};

/**
 * 检查用户登录状态
 */
export const checkUserLoginStatus = async (): Promise<{ isLoggedIn: boolean; userInfo?: any }> => {
  try {
    // 首先从缓存加载用户信息
    await loadUserInfoFromCache();

    // 检查是否有有效的登录信息
    if (userInfo.isLogin && userInfo.phone && userInfo.token) {
      return {
        isLoggedIn: true,
        userInfo: {
          phone: userInfo.phone,
          name: userInfo.phone, // 使用手机号作为显示名称
          isVip: userInfo.isVip,
          isAdmin: userInfo.isAdmin,
          isCardAdmin: userInfo.isCardAdmin,
          isSub: userInfo.isSub,                   // 新增：子账号状态
          appId: userInfo.appId,                   // 新增：应用ID
          appStatus: userInfo.appStatus,           // 新增：应用状态
          expiryDate: userInfo.expiryDate
        }
      };
    } else {
      return {
        isLoggedIn: false
      };
    }
  } catch (error) {
    console.error('检查用户登录状态失败:', error);
    return {
      isLoggedIn: false
    };
  }
};

/**
 * 处理用户退出登录
 */
export const handleUserLogout = async () => {
  console.log('收到用户退出登录通知');
  clearUserInfo();
};

/**
 * 监听登录状态变化
 */
export const setupLoginStateListener = () => {
  // 监听来自popup的消息
  const messageListener = (message: any) => {
    if (message.type === 'USER_LOGIN_SUCCESS') {
      console.log('收到登录成功消息，重新加载用户信息');
      loadUserInfoFromCache();
    } else if (message.type === 'USER_LOGOUT') {
      console.log('收到退出登录消息');
      handleUserLogout();
    }
  };

  // 监听chrome runtime消息
  if (chrome.runtime && chrome.runtime.onMessage) {
    chrome.runtime.onMessage.addListener(messageListener);
  }

  // 监听storage变化
  const storageListener = (changes: any, namespace: any) => {
    if (namespace === 'sync') {
      // 检查登录状态变化
      if (changes.isLogin || changes.is_login) {
        const newValue = changes.isLogin?.newValue || changes.is_login?.newValue;
        if (newValue === false && userInfo.isLogin === true) {
          console.log('检测到storage中登录状态变化，处理退出登录');
          handleUserLogout();
        } else if (newValue === true && userInfo.isLogin === false) {
          console.log('检测到storage中登录状态变化，重新加载用户信息');
          loadUserInfoFromCache();
        }
      }

      // 检查手机号码变化
      if (changes.phone && userInfo.isLogin === true) {
        const newPhone = changes.phone.newValue;
        if (!newPhone || newPhone.trim() === '') {
          console.log('检测到storage中手机号码被清空，执行退出登录');
          handleUserLogout();
        }
      }
    }
  };

  if (chrome.storage && chrome.storage.onChanged) {
    chrome.storage.onChanged.addListener(storageListener);
  }

  return () => {
    // 清理监听器
    if (chrome.runtime && chrome.runtime.onMessage) {
      chrome.runtime.onMessage.removeListener(messageListener);
    }
    if (chrome.storage && chrome.storage.onChanged) {
      chrome.storage.onChanged.removeListener(storageListener);
    }
  };
};

// 自动设置监听器
let cleanupListener: (() => void) | null = null;

/**
 * 初始化用户信息管理
 */
export const initUserStore = async () => {
  // 设置监听器
  cleanupListener = setupLoginStateListener();

  // 加载初始用户信息
  await loadUserInfoFromCache();
};

/**
 * 清理用户信息管理
 */
export const cleanupUserStore = () => {
  if (cleanupListener) {
    cleanupListener();
    cleanupListener = null;
  }
  clearUserInfo();
};

/**
 * 处理积分变化后的用户信息更新（用于AI改写任务完成后）
 */
export const handlePointsChanged = async () => {
  console.log('积分可能已变化，触发用户信息更新');

  // 发送消息通知其他页面刷新用户信息
  try {
    chrome.runtime.sendMessage({
      type: 'USER_INFO_UPDATED',
      source: 'content',
      timestamp: Date.now()
    }, (response:any) => {
      if (chrome.runtime.lastError) {
        console.log('发送用户信息更新通知失败:', chrome.runtime.lastError.message);
      } else {
        console.log('用户信息更新通知已发送');
      }
    });
  } catch (error) {
    console.error('发送用户信息更新通知失败:', error);
  }

  // 重新加载用户信息
  await loadUserInfoFromCache();
};
