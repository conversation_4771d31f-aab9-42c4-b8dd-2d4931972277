<?php

namespace App\Service\User;

use App\Service\BaseService;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use App\Models\User\GoodsPriceAdjustmentLogModel;
use App\Models\User\UserTaskCurrentcyModel;
use App\Models\User\UserGoodsDirectoryModel;
use App\Models\N11\ProductCategoryN11Model;
use App\Exceptions\MyException;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class GoodsService extends BaseService
{
    /**
     * 批量处理的默认大小
     */
    private const BATCH_SIZE = 1000;
    /**
     * 获取商品列表（分页）
     */
    public function getGoodsList(int $userId, array $params): array
    {
        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));
        
        // 获取筛选参数
        $type = $params['type'] ?? null;
        $status = $params['status'] ?? null;
        $goodsName = $params['goods_name'] ?? null;
        $goodsId = $params['goods_id'] ?? null;
        $directoryId = $params['directory_id'] ?? null;
        $priceAdjusted = $params['price_adjusted'] ?? null;
        $catAdjusted = $params['cat_adjusted'] ?? null;
        
        // 新增筛选参数
        $timeType = $params['time_type'] ?? 'created_at'; // created_at 或 updated_at
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;
        $subAccountName = $params['sub_account_name'] ?? null;
        $subAccountPhone = $params['sub_account_phone'] ?? null;
        $onlySubAccount = $params['only_sub_account'] ?? null; // 1=仅显示子账号商品
        
        $sortField = $params['sort_field'] ?? 'id';
        $sortOrder = $params['sort_order'] ?? 'desc';

        if(empty($directoryId)){
            throw new MyException('必须选择一个目录');
        }

        // 构建查询
        $query = GoodsModel::byUserId($userId)->byStatus(1);

        // 按商品类型筛选
        if ($type && in_array($type, [1])) {
            $query->byType($type);
        }

        if ($goodsName) {
            $query->where('goods_name', 'like', "%{$goodsName}%");
        }

        if ($goodsId) {
            $query->where('goods_id', $goodsId);
        }

        /* if (is_numeric($status) && in_array($status, [0, 1])) {
            $query->byStatus($status);
        } else {
            // 默认只显示正常状态的商品（status=1），已删除商品（status=0）不显示
            $query->byStatus(1);
        } */

        // 按目录ID筛选
        if (is_numeric($directoryId)) {
            $query->byDirectoryId($directoryId);
        }

        // 按价格调整状态筛选
        if ($priceAdjusted !== null) {
            $query->where('is_price_modified', (int)$priceAdjusted);
        }

        // 按分类调整状态筛选
        if ($catAdjusted !== null) {
            $query->where('is_cat_modified', (int)$catAdjusted);
        }

        // 按时间范围筛选
        if ($startDate && $endDate) {
            $startDateTime = $startDate . ' 00:00:00';
            $endDateTime = $endDate . ' 23:59:59';
            
            if ($timeType === 'updated_at') {
                $query->whereBetween('updated_at', [$startDateTime, $endDateTime]);
            } else {
                $query->whereBetween('created_at', [$startDateTime, $endDateTime]);
            }
        }

        // 子账号筛选
        if ($onlySubAccount === '1') {
            $query->where('user_sub_id', '>', 0);
        }

        // 按子账号姓名筛选
        if ($subAccountName) {
            $query->whereHas('subAccount', function ($q) use ($subAccountName) {
                $q->where('name', 'like', "%{$subAccountName}%");
            });
        }

        // 按子账号手机号筛选
        if ($subAccountPhone) {
            $query->whereHas('subAccount', function ($q) use ($subAccountPhone) {
                $q->where('phone', 'like', "%{$subAccountPhone}%");
            });
        }

        // 排序
        $allowedSortFields = ['id', 'updated_at', 'created_at', 'goods_name'];
        $allowedSortOrders = ['asc', 'desc'];

        if (in_array($sortField, $allowedSortFields) && in_array($sortOrder, $allowedSortOrders)) {
            $query->orderBy($sortField, $sortOrder);
        } else {
            $query->orderBy('id', 'desc');
        }

        // 分页查询
        $total = $query->count();
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;
        // 关联前端分类2  frontCategory2
        // ******** 强制改成 cat_id
        $goods = $query->offset($offset)
                      ->limit($pageSize)
                      ->with(['skus', 'frontCategory2', 'catRelations', 'userCatRelations', 'subAccount'])
                      ->get()
                      ->map(function($item) {
                          return $this->formatGoodsData($item);
                      });

        return [
            'list' => $goods,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasNext' => $page < $totalPages,
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 创建商品
     */
    public function createGoods(int $userId, array $data): array
    {
        // 验证数据
        $this->validateGoodsData($data, true);

        // 检查同一用户下是否已存在相同的商品ID和类型
        $exists = GoodsModel::byUserId($userId)
            ->where('type', $data['type'])
            ->where('goods_id', $data['goods_id'])
            ->exists();

        if ($exists) {
            throw new MyException('该商品已存在');
        }

        DB::beginTransaction();
        try {
            // 创建商品
            $goodsData = array_merge($data, [
                'user_id' => $userId,
                'status' => $data['status'] ?? 1,
            ]);

            // 处理JSON字段
            if (isset($goodsData['goods_pic']) && is_string($goodsData['goods_pic'])) {
                $goodsData['goods_pic'] = json_decode($goodsData['goods_pic'], true);
            }
            if (isset($goodsData['goods_sku']) && is_string($goodsData['goods_sku'])) {
                $goodsData['goods_sku'] = json_decode($goodsData['goods_sku'], true);
            }
            if (isset($goodsData['goods_property']) && is_string($goodsData['goods_property'])) {
                $goodsData['goods_property'] = json_decode($goodsData['goods_property'], true);
            }

            $goods = GoodsModel::create($goodsData);

            // 创建SKU数据
            if (!empty($data['skus']) && is_array($data['skus'])) {
                foreach ($data['skus'] as $skuData) {
                    $skuData['user_goods_id'] = $goods->id;
                    $skuData['goods_id'] = $goods->goods_id;
                    
                    // 处理JSON字段
                    if (isset($skuData['skc_gallery']) && is_string($skuData['skc_gallery'])) {
                        $skuData['skc_gallery'] = json_decode($skuData['skc_gallery'], true);
                    }
                    
                    GoodsSkuModel::create($skuData);
                }
            }

            DB::commit();

            return [
                'id' => $goods->id,
                'message' => '商品创建成功'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('商品创建失败：' . $e->getMessage());
        }
    }

    /**
     * 更新商品
     */
    public function updateGoods(int $userId, array $data): array
    {
        // 验证数据
        $this->validateGoodsData($data, false);

        if (!isset($data['id'])) {
            throw new MyException('商品ID不能为空');
        }

        // 查找商品并验证所有权
        $goods = GoodsModel::find($data['id']);
        
        if (!$goods) {
            throw new MyException('商品不存在');
        }

        if (!$goods->belongsToUser($userId)) {
            throw new MyException('无权限操作此商品');
        }

        // 如果更新了type或goods_id，检查是否重复
        if (isset($data['type']) || isset($data['goods_id'])) {
            $type = $data['type'] ?? $goods->type;
            $goodsId = $data['goods_id'] ?? $goods->goods_id;
            
            $exists = GoodsModel::byUserId($userId)
                ->where('type', $type)
                ->where('goods_id', $goodsId)
                ->where('id', '!=', $goods->id)
                ->exists();

            if ($exists) {
                throw new MyException('该商品已存在');
            }
        }

        DB::beginTransaction();
        try {
            // 更新商品
            $updateData = $data;
            unset($updateData['id'], $updateData['skus']);

            // 处理JSON字段
            if (isset($updateData['goods_pic']) && is_string($updateData['goods_pic'])) {
                $updateData['goods_pic'] = json_decode($updateData['goods_pic'], true);
            }
            if (isset($updateData['goods_sku']) && is_string($updateData['goods_sku'])) {
                $updateData['goods_sku'] = json_decode($updateData['goods_sku'], true);
            }
            if (isset($updateData['goods_property']) && is_string($updateData['goods_property'])) {
                $updateData['goods_property'] = json_decode($updateData['goods_property'], true);
            }

            $goods->update($updateData);

            // 更新SKU数据
            if (isset($data['skus']) && is_array($data['skus'])) {
                // 删除原有SKU
                GoodsSkuModel::where('user_goods_id', $goods->id)->delete();
                
                // 创建新SKU
                foreach ($data['skus'] as $skuData) {
                    $skuData['user_goods_id'] = $goods->id;
                    $skuData['goods_id'] = $goods->goods_id;
                    
                    // 处理JSON字段
                    if (isset($skuData['skc_gallery']) && is_string($skuData['skc_gallery'])) {
                        $skuData['skc_gallery'] = json_decode($skuData['skc_gallery'], true);
                    }
                    
                    GoodsSkuModel::create($skuData);
                }
            }

            DB::commit();

            return [
                'message' => '商品更新成功'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('商品更新失败：' . $e->getMessage());
        }
    }

    /**
     * 批量更新商品
     */
    public function batchUpdateGoods(int $userId, array $data): array
    {
        $count = GoodsModel::where('user_id', $userId)->whereIn('id', $data['ids'])->count();
        if ($count != count($data['ids'])) {
            throw new MyException('参数错误');
        }

        $ids = $data['ids'];
        unset($data['ids']);

        if (count($data) == 0) {
            throw new MyException('请至少选择一项');
        }

        // 在更新前获取被更新商品的目录信息，用于后续重新计算商品数量
        $affectedDirectories = GoodsModel::where('user_id', $userId)
            ->whereIn('id', $ids)
            ->select('user_id', 'directory_id')
            ->distinct()
            ->get()
            ->toArray();

        GoodsModel::where('user_id', $userId)->whereIn('id', $ids)->update($data);

        // 重新计算受影响目录的商品数量
        $this->recalculateAffectedDirectoriesGoodsCount($affectedDirectories);

        return [
            'message' => '商品批量更新成功'
        ];
    }

    /**
     * 删除商品
     */
    public function deleteGoods(int $userId, int $goodsId): array
    {
        throw new MyException('无权操作');
        // 验证商品ID
        if (!$goodsId) {
            throw new MyException('商品ID不能为空');
        }

        // 查找商品并验证所有权
        $goods = GoodsModel::find($goodsId);
        
        if (!$goods) {
            throw new MyException('商品不存在');
        }

        if (!$goods->belongsToUser($userId)) {
            throw new MyException('无权限操作此商品');
        }

        DB::beginTransaction();
        try {
            // 删除关联的SKU
            GoodsSkuModel::where('user_goods_id', $goods->id)->delete();
            
            // 删除商品
            $goods->delete();

            DB::commit();

            return [
                'message' => '商品删除成功'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('商品删除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取商品详情
     */
    public function getGoodsDetail(int $userId, int $goodsId): array
    {
        // 验证商品ID
        if (!$goodsId) {
            throw new MyException('商品ID不能为空');
        }

        // 查找商品并验证所有权
        $goods = GoodsModel::with(['skus', 'frontCategory1', 'frontCategory2'])->find($goodsId);
        
        if (!$goods) {
            throw new MyException('商品不存在');
        }

        if (!$goods->belongsToUser($userId)) {
            throw new MyException('无权限查看此商品');
        }

        return $this->formatGoodsDetailData($goods);
    }

    /**
     * 验证商品数据
     */
    private function validateGoodsData(array $data, bool $isCreate = false): void
    {
        $rules = [];
        
        if ($isCreate) {
            $rules = [
                'type' => ['required', 'integer', Rule::in([1])],
                'goods_id' => 'required|integer',
                'goods_name' => 'required|string|max:500',
            ];
        } else {
            $rules = [
                'id' => 'required|integer',
                'type' => ['sometimes', 'integer', Rule::in([1])],
                'goods_id' => 'sometimes|integer',
                'goods_name' => 'sometimes|string|max:500',
            ];
        }

        // 通用验证规则
        $commonRules = [
            'source_url' => 'nullable|string',
            'cat_id' => 'nullable|integer',
            'front_cat_id_1' => 'nullable|integer',
            'front_cat_id_2' => 'nullable|integer',
            'front_cat_desc' => 'nullable|string|max:500',
            'mall_id' => 'nullable|integer',
            'goods_detail' => 'nullable|string',
            'goods_video' => 'nullable|string|max:500',
            'goods_pic' => 'nullable',
            'goods_sku' => 'nullable',
            'goods_sku_num' => 'nullable|integer|min:1',
            'goods_property' => 'nullable',
            'status' => 'sometimes|integer|in:0,1',
            'directory_id' => 'nullable|integer|min:0',
            'skus' => 'nullable|array',
            'skus.*.sku_id' => 'required_with:skus|integer',
            'skus.*.thumb_url' => 'nullable|string|max:500',
            'skus.*.currentcy' => 'nullable|string|max:20',
            'skus.*.price' => 'nullable|numeric|min:0',
            'skus.*.spec_key_values' => 'nullable|string|max:50',
            'skus.*.spec_values' => 'nullable|string|max:255',
            'skus.*.skc_gallery' => 'nullable',
            'skus.*.is_skc_gallery' => 'nullable|boolean',
            'skus.*.url' => 'nullable|string|max:500',
        ];

        $rules = array_merge($rules, $commonRules);

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new MyException($validator->errors()->first());
        }
    }

    /**
     * 格式化商品数据（列表用）
     */
    private function formatGoodsData($goods): array
    {
        //systemLog(json_encode($goods,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),'goods');
        $front_cat_2_path_name = $goods->frontCategory2?->path_name ?? '';
        $front_cat_2_path_name = str_ireplace(',', '->', $front_cat_2_path_name);
        $goods_pic = $goods->goods_pic;
        if(!empty($goods_pic)){
            $goods_pic = json_decode($goods_pic,true);
        }
        $goods_thumb = '';
        if(is_array($goods_pic)){
            $goods_pic = array_map(function($pic) {
                return uploadFilePath($pic);
            }, $goods_pic);

            $goods_thumb = $goods_pic[0] ?? '';
            if(!empty($goods_thumb)){
                $goods_thumb = uploadFilePath($goods_thumb);
            }
        }
        $goods_skus = $goods->skus;
        $formattedSkus = [];
        $first_sku = '';
        $first_sku_price = 0;
        $first_sku_currentcy = '';
        $first_sku_thumb_url = '';//小图 
        $first_sku_thumb_url_h500 = '';//大图
        if($goods->goods_sku_num > 0){
            foreach ($goods_skus as $key=>$sku) {
                if($key == 0){
                    $first_sku = $sku->spec_key_values;
                    $first_sku_price = $sku->price;
                    $first_sku_currentcy = $sku->currentcy;
                    $first_sku_thumb_url = $sku->thumb_url ? uploadFilePath($sku->thumb_url) : '';
                    $first_sku_thumb_url_h500 = $sku->thumb_url ? uploadFilePath($sku->thumb_url) : '';
                }
                $formattedSkus[] = [
                    'id' => $sku->id,
                    'sku' => $sku->spec_key_values,
                    'price' => $sku->price,
                    'currentcy' => $sku->currentcy,
                    'thumb_url' => $sku->thumb_url ? uploadFilePath($sku->thumb_url) : '',
                    'thumb_url_h500' => $sku->thumb_url ? uploadFilePath($sku->thumb_url) : ''
                ];
            }
        }

        // 处理关联分类信息
        $platformRelations = $this->formatPlatformRelations($goods->catRelations);

        // 处理用户自定义分类关联信息
        $userCatRelations = $this->formatUserCatRelations($goods->userCatRelations);

        // 用用户自定义分类关联数据替换匹配的平台关联数据
        if ($userCatRelations !== null && $platformRelations !== null) {
            foreach ($userCatRelations as $userRelation) {
                // 在平台关联数据中查找匹配项
                foreach ($platformRelations as $index => $platformRelation) {
                    // 匹配条件：platform_id 且 third_platform_id 值相同
                    if ($platformRelation['platform_id'] == $userRelation['platform_id'] &&
                        $platformRelation['third_platform_id'] == $userRelation['third_platform_id']) {
                        // 将整个数组元素替换为用户自定义关联数据
                        $platformRelations[$index] = $userRelation;
                        break; // 找到匹配项后跳出内层循环
                    }
                }
            }
        }

        // 获取价格调整信息
        $priceAdjustmentInfo = $this->getPriceAdjustmentInfo($goods);

        return [
            'id' => $goods->id,
            'type' => $goods->type,
            'type_name' => $goods->getTypeName(),
            'source_url' => $goods->source_url,
            'mall_id' => $goods->mall_id,
            'goods_id' => $goods->goods_id,
            'goods_name' => $goods->goods_name,
            'goods_thumb' => $goods_thumb,
            'goods_pic' => $goods_pic,
            'goods_video' => $goods->goods_video,
            'goods_sku_num' => $goods->goods_sku_num,
            'first_sku' => $first_sku,
            'first_sku_price' => $first_sku_price,
            'first_sku_currentcy' => $first_sku_currentcy,
            'first_sku_thumb_url' => $first_sku_thumb_url,
            'first_sku_thumb_url_h500' => $first_sku_thumb_url_h500,
            'formatted_skus' => $formattedSkus,
            'goods_property' => $goods->goods_property,
            'goods_score' => $goods->goods_score,
            'goods_sold_quantity' => $goods->goods_sold_quantity,
            'status' => $goods->status,
            'cat_id' => $goods->cat_id,
            'cat_name' => $goods->frontCategory2?->name ?? '',
            'front_cat_id_2' => $goods->front_cat_id_2,
            'front_cat_desc' => $goods->front_cat_desc,
            'front_cat_2_path_name' => $front_cat_2_path_name,
            'platform_relations' => $platformRelations,
            //'user_cat_relations' => $userCatRelations,
            'is_price_modified' => $goods->is_price_modified,
            'price_adjustment_info' => $priceAdjustmentInfo,
            'created_at' => $goods->created_at,
            'updated_at' => $goods->updated_at,
            'operator_info' => $this->getOperatorInfo($goods),
        ];
    }

    /**
     * 格式化商品详情数据
     */
    private function formatGoodsDetailData($goods): array
    {
        return [
            'id' => $goods->id,
            'user_id' => $goods->user_id,
            'type' => $goods->type,
            'type_name' => $goods->getTypeName(),
            'source_url' => $goods->source_url,
            'cat_id' => $goods->cat_id,
            'front_cat_id_1' => $goods->front_cat_id_1,
            'front_cat_id_2' => $goods->front_cat_id_2,
            'front_cat_desc' => $goods->front_cat_desc,
            'front_cat_1_path_name' => $goods->frontCategory1?->path_name ?? '',
            'front_cat_2_path_name' => $goods->frontCategory2?->path_name ?? '',
            'mall_id' => $goods->mall_id,
            'goods_id' => $goods->goods_id,
            'goods_name' => $goods->goods_name,
            'goods_detail' => $goods->goods_detail,
            'goods_video' => $goods->goods_video,
            'goods_pic' => $goods->goods_pic,
            'goods_sku' => $goods->goods_sku,
            'goods_sku_num' => $goods->goods_sku_num,
            'goods_property' => $goods->goods_property,
            'status' => $goods->status,
            'skus' => $goods->skus->map(function($sku) {
                return [
                    'id' => $sku->id,
                    'sku_id' => $sku->sku_id,
                    'thumb_url' => $sku->thumb_url,
                    'currentcy' => $sku->currentcy,
                    'price' => $sku->price,
                    'spec_key_values' => $sku->spec_key_values,
                    'spec_values' => $sku->spec_values,
                    'skc_gallery' => $sku->skc_gallery,
                    'is_skc_gallery' => $sku->is_skc_gallery,
                    'url' => $sku->url,
                    'created_at' => $sku->created_at,
                    'updated_at' => $sku->updated_at,
                ];
            }),
            'created_at' => $goods->created_at,
            'updated_at' => $goods->updated_at,
        ];
    }

    /**
     * 获取操作者信息
     */
    private function getOperatorInfo($goods): array
    {
        $operatorType = '主账号操作';
        $operatorName = '';
        
        if ($goods->user_sub_id && $goods->subAccount) {
            $operatorType = '子账号操作';
            $name = $goods->subAccount->name ?? '';
            $phone = $goods->subAccount->phone ?? '';
            
            if ($name && $phone) {
                $operatorName = $name . '【' . $phone . '】';
            } elseif ($name) {
                $operatorName = $name;
            } elseif ($phone) {
                $operatorName = $phone;
            }
        }

        return [
            'type' => $operatorType,
            'name' => $operatorName,
            'created_at' => $goods->created_at,
            'updated_at' => $goods->updated_at,
        ];
    }

    /**
     * 获取需要处理图片的商品ID列表
     */
    public function getGoodsNeedImageProcess(int $userId, int $directoryId): array
    {
        $goodsIds = GoodsModel::byUserId($userId)
            ->byDirectoryId($directoryId)
            ->byStatus(1) // 只获取正常状态的商品
            ->where('img_local_status', 0) // 图片未处理完成
            ->pluck('id')
            ->toArray();

        return [
            'goods_ids' => $goodsIds,
            'total_count' => count($goodsIds)
        ];
    }

    /**
     * 获取价格调整信息
     */
    private function getPriceAdjustmentInfo($goods): array
    {
        if (!$goods->is_price_modified) {
            return [
                'is_adjusted' => false,
                'display_text' => '-',
                'latest_adjustment_time' => null
            ];
        }

        // 获取最新的价格调整记录
        $latestLog = GoodsPriceAdjustmentLogModel::byGoodsId($goods->id)
            ->orderByModified('desc')
            ->first();

        if (!$latestLog) {
            return [
                'is_adjusted' => false,
                'display_text' => '-',
                'latest_adjustment_time' => null
            ];
        }

        return [
            'is_adjusted' => true,
            'display_text' => '已调整 ' . $latestLog->modified_at,
            'latest_adjustment_time' => $latestLog->modified_at
        ];
    }

    /**
     * 获取用户商品分类关联信息
     */
    private function formatUserCatRelations($userCatRelations): ?array
    {
        if ($userCatRelations->isEmpty()) {
            return null;
        }

        $relations = [];
        foreach ($userCatRelations as $relation) {
            $relationData = [
                'id' => $relation->id,
                'cat_type' => 'user_cat',
                'platform_id' => 1, // 固定为1，表示TEMU平台
                'third_platform_id' => $relation->third_platform_id,
                'cat_third_ids' => [$relation->third_platform_cat_id], // 转换为数组格式，与formatPlatformRelations保持一致
                'third_platform_categories' => null,
            ];

            // 如果是N11平台(third_platform_id = 2)，构建分类详情
            if ($relation->third_platform_id == 2) {
                $relationData['third_platform_categories'] = [[
                    'id' => $relation->third_platform_cat_id,
                    'name' => $relation->third_platform_cat_name,
                    'name_tl' => $relation->third_platform_cat_name_tl,
                    'path_name' => str_ireplace(',', '->', $relation->third_platform_cat_path ?? ''),
                    'path_name_tl' => str_ireplace(',', '->', $relation->third_platform_cat_path_tl ?? ''),
                ]];
            }

            $relations[] = $relationData;
        }

        return $relations;
    }

    /**
     * 格式化平台关联信息
     */
    private function formatPlatformRelations($catRelations): ?array
    {
        if ($catRelations->isEmpty()) {
            return null;
        }

        $relations = [];
        foreach ($catRelations as $relation) {
            $relationData = [
                'id' => $relation->id,
                'cat_type' => 'system_cat',
                'platform_id' => $relation->platform_id,
                'third_platform_id' => $relation->third_platform_id,
                'cat_third_ids' => $relation->cat_third_ids,
                'third_platform_categories' => null,
            ];

            // 如果是N11平台(third_platform_id = 2)，获取分类详情
            if ($relation->third_platform_id == 2) {
                $relationData['third_platform_categories'] = $relation->n11_categories->map(function($category) {
                    return [
                        'id' => $category->id,
                        'name' => $category->name,
                        'name_tl' => $category->name_tl,
                        'path_name' => str_ireplace(',', '->', $category->path_name ?? ''),
                        'path_name_tl' => str_ireplace(',', '->', $category->path_name_tl ?? ''),
                    ];
                })->toArray();
            }

            $relations[] = $relationData;
        }

        return $relations;
    }

    /**
     * 获取商品统计信息
     */
    public function getGoodsStatistics(int $userId, array $params): array
    {
        $directoryId = $params['directory_id'] ?? 0;
        $timeRange = $params['time_range'] ?? 'today';
        $dayStart = $params['day_start'] ?? null;
        $dayEnd = $params['day_end'] ?? null;
        $goodsIds = $params['goods_ids'] ?? null; // 新增：选中商品ID列表
        $publishCurrency = $params['publish_currency'] ?? 'TL';
        $exchangeRateCurrency = $params['exchange_rate_currency'] ?? 'TL';
        $priceMin = $params['price_min'] ?? null; // 最低价格
        $priceMax = $params['price_max'] ?? null; // 最高价格

        // 构建查询
        $query = GoodsModel::byUserId($userId)->byStatus(1);

        // 如果传递了商品ID列表，直接按ID筛选，忽略其他筛选条件
        if (!empty($goodsIds) && is_array($goodsIds)) {
            $query->whereIn('id', $goodsIds);
        } else {
            // 按目录ID筛选
            if (is_numeric($directoryId)) {
                $query->byDirectoryId($directoryId);
            }

            // 按时间范围筛选
            $this->applyTimeRangeFilter($query, $timeRange, $dayStart, $dayEnd);
        }

        // 价格区间筛选：通过关联SKU表进行价格范围过滤（只有大于0的价格才进行筛选）
        if (($priceMin !== null && $priceMin > 0) || ($priceMax !== null && $priceMax > 0)) {
            $query->whereHas('skus', function ($skuQuery) use ($priceMin, $priceMax) {
                if ($priceMin !== null && $priceMin > 0) {
                    $skuQuery->where('price', '>=', $priceMin);
                }
                if ($priceMax !== null && $priceMax > 0) {
                    $skuQuery->where('price', '<=', $priceMax);
                }
            });
        }

        // 获取商品数量
        $goodsCount = $query->count();

        // 获取SKU数量：如果有价格区间筛选，需要单独统计符合价格条件的SKU数量
        if (($priceMin !== null && $priceMin > 0) || ($priceMax !== null && $priceMax > 0)) {
            // 先获取符合条件的商品ID列表
            $finalGoodsIds = $query->pluck('id')->toArray();

            // 如果商品ID数量过多，分批处理
            if (count($finalGoodsIds) > self::BATCH_SIZE) {
                $skuCount = $this->getSkuCountInBatches($finalGoodsIds, $priceMin, $priceMax);
            } else {
                $skuQuery = GoodsSkuModel::whereIn('user_goods_id', $finalGoodsIds);
                if ($priceMin !== null && $priceMin > 0) {
                    $skuQuery->where('price', '>=', $priceMin);
                }
                if ($priceMax !== null && $priceMax > 0) {
                    $skuQuery->where('price', '<=', $priceMax);
                }
                $skuCount = $skuQuery->count();
            }
        } else {
            $skuCount = $query->sum('goods_sku_num');
        }

        // 获取符合条件的商品ID列表，用于查询SKU货币信息（分批处理大量ID）
        $finalGoodsIds = $this->getGoodsIdsInBatches($query);

        // 统计货币信息：如果有价格区间筛选，需要传递价格条件
        $currencyStats = $this->getCurrencyStatistics($finalGoodsIds, $priceMin, $priceMax);
        $currencyStats['currencies'] = array_unique($currencyStats['currencies']);
        $currencyStats['currencies'] = array_values($currencyStats['currencies']);
        
        // 如果 $ 不在数组中，则追加 $
        if (!in_array('$', $currencyStats['currencies'])) {
            $currencyStats['currencies'][] = '$';
        }
        

        // 查询汇率信息
        $exchangeRates = $this->getExchangeRates($userId, $currencyStats['currencies'], $exchangeRateCurrency);

        return [
            'goods_count' => $goodsCount,
            'sku_count' => (int)$skuCount,
            'currency_count' => $currencyStats['currency_count'],
            'currencies' => $currencyStats['currencies'],
            'exchange_rates' => $exchangeRates,
            'exchange_rate_currency' => $exchangeRateCurrency,
            'directory_id' => $directoryId,
            'time_range' => $timeRange,
            'day_start' => $dayStart,
            'day_end' => $dayEnd
        ];
    }

    /**
     * 应用时间范围筛选
     */
    private function applyTimeRangeFilter($query, string $timeRange, ?string $dayStart = null, ?string $dayEnd = null): void
    {
        switch ($timeRange) {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'yesterday':
                $query->whereDate('created_at', today()->subDay());
                break;
            case 'lastweek':
                $query->whereBetween('created_at', [
                    today()->subWeek()->startOfDay(),
                    today()->endOfDay()
                ]);
                break;
            case 'custom':
                if ($dayStart && $dayEnd) {
                    $query->whereBetween('created_at', [
                        $dayStart . ' 00:00:00',
                        $dayEnd . ' 23:59:59'
                    ]);
                }
                break;
            case 'all':
            default:
                // 不添加时间筛选
                break;
        }
    }

    /**
     * 获取货币统计信息
     */
    private function getCurrencyStatistics(array $goodsIds, $priceMin = null, $priceMax = null): array
    {
        // 如果没有商品ID，返回空统计
        if (empty($goodsIds)) {
            return [
                'currency_count' => 0,
                'currencies' => []
            ];
        }

        // 如果商品ID数量过多，分批处理
        if (count($goodsIds) > self::BATCH_SIZE) {
            return $this->getCurrencyStatisticsInBatches($goodsIds, $priceMin, $priceMax);
        }

        // 查询这些商品对应的所有SKU中的货币信息
        $query = GoodsSkuModel::whereIn('user_goods_id', $goodsIds);

        // 如果有价格区间筛选，添加价格条件（只有大于0的价格才进行筛选）
        if ($priceMin !== null && $priceMin > 0) {
            $query->where('price', '>=', $priceMin);
        }
        if ($priceMax !== null && $priceMax > 0) {
            $query->where('price', '<=', $priceMax);
        }

        $currencies = $query->distinct()
            ->pluck('currentcy')
            ->filter() // 过滤掉空值
            ->values() // 重新索引
            ->toArray();

        return [
            'currency_count' => count($currencies),
            'currencies' => $currencies
        ];
    }

    /**
     * 分批获取货币统计信息
     */
    private function getCurrencyStatisticsInBatches(array $goodsIds, $priceMin = null, $priceMax = null, $batchSize = self::BATCH_SIZE): array
    {
        $allCurrencies = [];
        $batches = array_chunk($goodsIds, $batchSize);

        foreach ($batches as $batch) {
            $query = GoodsSkuModel::whereIn('user_goods_id', $batch);

            // 如果有价格区间筛选，添加价格条件
            if ($priceMin !== null && $priceMin > 0) {
                $query->where('price', '>=', $priceMin);
            }
            if ($priceMax !== null && $priceMax > 0) {
                $query->where('price', '<=', $priceMax);
            }

            $batchCurrencies = $query->distinct()
                ->pluck('currentcy')
                ->filter()
                ->toArray();

            $allCurrencies = array_merge($allCurrencies, $batchCurrencies);
        }

        // 去重并重新索引
        $uniqueCurrencies = array_values(array_unique($allCurrencies));

        return [
            'currency_count' => count($uniqueCurrencies),
            'currencies' => $uniqueCurrencies
        ];
    }

    /**
     * 获取用户的汇率信息
     *
     * @param int $userId 用户ID
     * @param array $currencies 货币数组
     * @param string $exchangeRateCurrency 汇率基准货币
     * @return array 汇率信息
     */
    private function getExchangeRates(int $userId, array $currencies, string $exchangeRateCurrency): array
    {
        $exchangeRates = [];
        
        // 遍历每个货币，查询对应的汇率
        foreach ($currencies as $currency) {
            // 如果货币与汇率基准货币相同，跳过
            if ($currency === $exchangeRateCurrency) {
                continue;
            }

            // 查询当前用户最新的汇率记录
            $latestRate = UserTaskCurrentcyModel::where('user_id', $userId)
                ->where('currentcy_from', $currency)
                ->where('currentcy_to', $exchangeRateCurrency)
                ->orderBy('id', 'desc')
                ->first();
            
            // 如果找到记录，使用记录中的汇率，否则使用默认值1
            $exchangeRates[$currency] = $latestRate ? (float)$latestRate->rate : 1.0;
        }
        
        return $exchangeRates;
    }

    /**
     * 分批获取商品ID列表，避免大量ID的内存问题
     */
    private function getGoodsIdsInBatches($query, $batchSize = self::BATCH_SIZE): array
    {
        $allIds = [];
        $offset = 0;

        do {
            $batchIds = $query->offset($offset)->limit($batchSize)->pluck('id')->toArray();
            $allIds = array_merge($allIds, $batchIds);
            $offset += $batchSize;
        } while (count($batchIds) === $batchSize);

        return $allIds;
    }

    /**
     * 分批统计SKU数量
     */
    private function getSkuCountInBatches(array $goodsIds, $priceMin = null, $priceMax = null, $batchSize = self::BATCH_SIZE): int
    {
        $totalCount = 0;
        $batches = array_chunk($goodsIds, $batchSize);

        foreach ($batches as $batch) {
            $skuQuery = GoodsSkuModel::whereIn('user_goods_id', $batch);

            if ($priceMin !== null && $priceMin > 0) {
                $skuQuery->where('price', '>=', $priceMin);
            }
            if ($priceMax !== null && $priceMax > 0) {
                $skuQuery->where('price', '<=', $priceMax);
            }

            $totalCount += $skuQuery->count();
        }

        return $totalCount;
    }



    /**
     * 调整SKU价格
     */
    public function adjustSkuPrice(int $userId, array $data): array
    {
        // 验证数据
        if (!isset($data['goods_id']) || !isset($data['sku_adjustments'])) {
            throw new MyException('参数错误');
        }

        $goodsId = (int)$data['goods_id'];
        $skuAdjustments = $data['sku_adjustments'];

        if (!is_array($skuAdjustments) || empty($skuAdjustments)) {
            throw new MyException('SKU价格调整数据不能为空');
        }

        // 验证商品所有权
        $goods = GoodsModel::find($goodsId);
        if (!$goods || !$goods->belongsToUser($userId)) {
            throw new MyException('商品不存在或无权限操作');
        }

        DB::beginTransaction();
        try {
            $adjustedCount = 0;
            $currentTime = now();

            foreach ($skuAdjustments as $adjustment) {
                if (!isset($adjustment['sku_id']) || !isset($adjustment['new_price'])) {
                    continue;
                }

                $skuId = (int)$adjustment['sku_id'];
                $newPrice = (float)$adjustment['new_price'];

                // 验证新价格
                if ($newPrice < 0.01) {
                    throw new MyException('商品价格必须大于等于0.01');
                }

                // 查找SKU
                $sku = GoodsSkuModel::where('id', $skuId)
                    ->where('user_goods_id', $goodsId)
                    ->first();

                if (!$sku) {
                    continue;
                }

                $oldPrice = (float)$sku->price;

                // 如果价格没有变化，跳过
                if (abs($oldPrice - $newPrice) < 0.01) {
                    continue;
                }

                // 更新SKU价格和修改标记
                $sku->update([
                    'price' => $newPrice,
                    'is_price_modified' => 1
                ]);

                // 记录价格调整日志
                GoodsPriceAdjustmentLogModel::create([
                    'goods_id' => $goodsId,
                    'sku_id' => $skuId,
                    'old_price' => $oldPrice,
                    'new_price' => $newPrice,
                    'modifier_id' => $userId,
                    'modified_at' => $currentTime
                ]);

                $adjustedCount++;
            }

            // 如果有SKU价格被调整，更新商品的修改标记和更新时间
            if ($adjustedCount > 0) {
                $goods->update([
                    'is_price_modified' => 1,
                    'updated_at' => $currentTime
                ]);
            }

            DB::commit();

            return [
                'message' => "成功调整 {$adjustedCount} 个SKU的价格",
                'adjusted_count' => $adjustedCount
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('价格调整失败：' . $e->getMessage());
        }
    }

    /**
     * 获取价格调整日志
     */
    public function getPriceAdjustmentLogs(int $userId, array $params): array
    {
        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));

        $goodsId = $params['goods_id'] ?? null;
        $skuId = $params['sku_id'] ?? null;

        if (!$goodsId) {
            throw new MyException('商品ID不能为空');
        }

        // 验证商品所有权
        $goods = GoodsModel::find($goodsId);
        if (!$goods || !$goods->belongsToUser($userId)) {
            throw new MyException('商品不存在或无权限查看');
        }

        // 构建查询
        $query = GoodsPriceAdjustmentLogModel::with(['sku'])
            ->byGoodsId($goodsId);

        if ($skuId) {
            $query->bySkuId($skuId);
        }

        // 排序
        $query->orderByModified('desc');

        // 分页查询
        $total = $query->count();
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;

        $logs = $query->offset($offset)
                     ->limit($pageSize)
                     ->get()
                     ->map(function($log) {
                         return [
                             'id' => $log->id,
                             'sku_id' => $log->sku_id,
                             'sku_spec_values' => $log->sku->spec_values ?? '',
                             'old_price' => (float)$log->old_price,
                             'new_price' => (float)$log->new_price,
                             'price_change' => $log->getPriceChangeAmount(),
                             'price_change_percentage' => $log->getPriceChangePercentage(),
                             'price_change_type' => $log->getPriceChangeType(),
                             'price_change_description' => $log->getPriceChangeDescription(),
                             'modifier_id' => $log->modifier_id,
                             'modifier_name' => '',
                             'modified_at' => $log->modified_at,
                             'created_at' => $log->created_at
                         ];
                     });

        return [
            'list' => $logs,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasNext' => $page < $totalPages,
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 重新计算受影响目录的商品数量
     *
     * @param array $affectedDirectories 受影响的目录信息数组，包含 user_id 和 directory_id
     */
    private function recalculateAffectedDirectoriesGoodsCount(array $affectedDirectories): void
    {
        // 去重处理，避免重复计算同一目录
        $uniqueDirectories = [];
        foreach ($affectedDirectories as $directory) {
            $key = $directory['user_id'] . '_' . $directory['directory_id'];
            if (!isset($uniqueDirectories[$key])) {
                $uniqueDirectories[$key] = $directory;
            }
        }

        // 对每个唯一的目录重新计算商品数量
        foreach ($uniqueDirectories as $directory) {
            $this->recalculateDirectoryGoodsCount($directory['user_id'], $directory['directory_id']);
        }
    }

    /**
     * 重新计算指定目录的商品数量
     *
     * @param int $userId 用户ID
     * @param int $directoryId 目录ID
     */
    private function recalculateDirectoryGoodsCount(int $userId, int $directoryId): void
    {
        // 统计该目录下的有效商品数量
        $goodsCount = GoodsModel::where('user_id', $userId)
            ->where('directory_id', $directoryId)
            ->where('status', 1) // 只统计有效商品
            ->count();

        // 更新目录的商品数量
        UserGoodsDirectoryModel::where('user_id', $userId)
            ->where('id', $directoryId)
            ->update(['goods_count' => $goodsCount]);
    }
}