<template>
    <div class="chrome_container">
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElNotification, ElMessageBox } from 'element-plus'
import { 
  userInfo as globalUserInfo,
  checkUserLoginStatus,
  initUserStore,
  cleanupUserStore,
  onUserInfoChange
} from '@/utils/userStore';
import { 
  getProductStatsAndCheckPageSize,
  getAllStockCodes,
  executeDeleteFlow,
  hasTableData
} from '@/utils/n11_dom/dom_operation/productListProcessor';
import { batchUpdateRejectedStatus } from '@/utils/n11_dom/requests/rejectedProductApi';

// 响应式状态
const isObserving = ref(false)
const sixthButtonActive = ref(false)
const processButtonExists = ref(false)
const debugMode = ref(true) // 可在开发时设为true

// 处理按钮状态管理
const isProcessing = ref(false) // 是否正在处理中
const lastClickTime = ref(0) // 上次点击时间，用于防抖
const CLICK_DEBOUNCE_TIME = 1000 // 防抖时间 1秒

// 通知管理
let currentNotification: any = null // 当前显示的通知实例

// 用户信息相关状态
const userInfo = ref<any>(null)
let userInfoChangeCleanup: (() => void) | null = null
let isCheckingUserLogin = ref(false) // 防止重复检查登录状态
let lastLoginCheckTime = 0 // 防抖时间戳
const LOGIN_CHECK_DEBOUNCE_TIME = 2000 // 2秒防抖

// MutationObserver实例
let observer: MutationObserver | null = null

// 处理按钮的标识
const PROCESS_BUTTON_ID = 'n11-process-button'

// 获取插件名称
const getPluginName = (): Promise<string> => {
  return new Promise((resolve) => {
    chrome.runtime.getManifest ? 
      resolve(chrome.runtime.getManifest().name) : 
      resolve('跨境蜂');
  });
};

/**
 * 显示通知（自动关闭上一个通知）
 */
const showNotification = (title: string, message: string, type: 'success' | 'warning' | 'info' | 'error' = 'info') => {
  // 关闭上一个通知
  if (currentNotification) {
    currentNotification.close()
  }
  
  // 显示新通知
  currentNotification = ElNotification({
    title,
    message,
    type,
    duration: 0, // 不自动关闭
    position: 'top-right'
  })
  
  return currentNotification
}

// 显示登录提示
const showLoginNotification = async () => {
  const pluginName = await getPluginName();
  ElNotification({
    title: pluginName,
    message: '请先登录',
    type: 'warning',
    duration: 5000,
    position: 'top-right'
  });
};

/**
 * 检查用户登录状态
 */
const checkAndHandleUserLogin = async (showErrorMessage = true): Promise<boolean> => {
  try {
    // 检查登录状态
    const loginStatus = await checkUserLoginStatus();
    if (!loginStatus.isLoggedIn) {
      showLoginNotification();
      return false;
    }

    // 使用全局用户信息
    userInfo.value = {
      name: globalUserInfo.phone,
      phone: globalUserInfo.phone,
      isVip: globalUserInfo.isVip,
      isAdmin: globalUserInfo.isAdmin,
      expiryDate: globalUserInfo.expiryDate
    };

    console.log('用户登录检查通过:', {
      phone: userInfo.value.phone,
      isVip: userInfo.value.isVip,
      isAdmin: userInfo.value.isAdmin
    });

    return true;
  } catch (error) {
    console.error('检查用户登录状态失败:', error);
    
    // 只在允许显示错误消息时才显示
    if (showErrorMessage) {
      ElMessage.error('检查用户登录状态失败');
    }
    
    return false;
  }
};

/**
 * 检查第六个按钮是否具有active类
 */
const checkSixthButtonActive = (): boolean => {
    try {
        const tabManager = document.querySelector('.tabManager')
        if (!tabManager) {
            console.log('未找到 .tabManager 容器')
            return false
        }

        const buttons = tabManager.querySelectorAll('button, .tab-button, [role="tab"]')
        if (buttons.length < 6) {
            console.log(`按钮数量不足，当前数量: ${buttons.length}`)
            return false
        }

        const sixthButton = buttons[5] as HTMLElement // 索引为5的是第六个按钮
        const hasActive = sixthButton.classList.contains('active')
        
        console.log(`第六个按钮active状态: ${hasActive}`)
        return hasActive
    } catch (error) {
        console.error('检查第六个按钮状态时发生错误:', error)
        return false
    }
}

/**
 * 添加处理商品列表按钮
 */
const addProcessButton = (): void => {
    try {
        // 检查按钮是否已存在
        if (document.getElementById(PROCESS_BUTTON_ID)) {
            console.log('处理商品列表按钮已存在，跳过添加')
            return
        }

        // 查找目标容器 - 优先查找Excel导入按钮附近的容器
        let container = document.querySelector('.table-top-buttons')
        
        if (!container) {
            // 查找Excel导入按钮的父容器
            const excelButton = document.querySelector('button[data-v-70104054]') ||
                               document.querySelector('button:contains("Excel ile İndir")') ||
                               document.querySelector('button:contains("Excel")') ||
                               document.querySelector('button:contains("导出")') ||
                               document.querySelector('button:contains("下载")')
            
            if (excelButton) {
                container = excelButton.parentElement
                console.log('找到Excel按钮容器:', container)
            }
        }
        
        if (!container) {
            // 备选容器查找
            container = document.querySelector('.toolbar') ||
                       document.querySelector('.action-buttons') ||
                       document.querySelector('[class*="button"]') ||
                       document.querySelector('.btn-group')
        }
        
        if (!container) {
            console.log('未找到合适的按钮容器')
            return
        }
        
        console.log('使用按钮容器:', container)

        // 创建处理商品列表按钮
        const processButton = document.createElement('button')
        processButton.id = PROCESS_BUTTON_ID
        processButton.className = 'btn btn-primary process-btn'
        processButton.textContent = isProcessing.value ? '处理中...' : '处理商品列表'
        processButton.type = 'button'
        processButton.disabled = isProcessing.value
        
        // 添加点击事件
        processButton.addEventListener('click', handleProcessClick)
        
        // 插入按钮到容器
        container.appendChild(processButton)
        
        processButtonExists.value = true
        console.log('处理商品列表按钮已添加')
    } catch (error) {
        console.error('添加处理商品列表按钮时发生错误:', error)
    }
}

/**
 * 移除处理商品列表按钮
 */
const removeProcessButton = (): void => {
    try {
        const existingButton = document.getElementById(PROCESS_BUTTON_ID)
        if (existingButton) {
            existingButton.removeEventListener('click', handleProcessClick)
            existingButton.remove()
            processButtonExists.value = false
            console.log('处理商品列表按钮已移除')
        }
    } catch (error) {
        console.error('移除处理商品列表按钮时发生错误:', error)
    }
}

/**
 * 更新处理按钮状态
 */
const updateProcessButtonState = (processing: boolean): void => {
    try {
        const button = document.getElementById(PROCESS_BUTTON_ID) as HTMLButtonElement
        if (button) {
            button.disabled = processing
            button.textContent = processing ? '处理中...' : '处理商品列表'
            
            if (processing) {
                button.classList.add('processing')
            } else {
                button.classList.remove('processing')
            }
        }
        isProcessing.value = processing
    } catch (error) {
        console.error('更新按钮状态时发生错误:', error)
    }
}

/**
 * 处理商品列表按钮点击事件处理器
 */
const handleProcessClick = async (event: Event): Promise<void> => {
    event.preventDefault()
    console.log('处理商品列表按钮被点击')
    
    // 防抖检查
    const currentTime = Date.now()
    if (currentTime - lastClickTime.value < CLICK_DEBOUNCE_TIME) {
        console.log('防抖：忽略重复点击')
        return
    }
    lastClickTime.value = currentTime
    
    // 检查是否正在处理中
    if (isProcessing.value) {
        console.log('正在处理中，忽略点击')
        showNotification('操作提示', '正在处理中，请耐心等待...', 'warning')
        return
    }
    
    // 首先检查用户登录状态
    const isLoggedIn = await checkAndHandleUserLogin()
    if (!isLoggedIn) {
        console.log('用户未登录，终止处理操作')
        return
    }
    
    // 显示确认对话框
    try {
        const confirmed = await ElMessageBox.confirm(
            '',
            "处理商品列表",
            {
                confirmButtonText: "确认处理",
                cancelButtonText: "取消",
                type: "warning",
                center: true,
                dangerouslyUseHTMLString: true,
                message: `
                    <div style="text-align: left; line-height: 1.6;">
                        <p style="margin-bottom: 12px; font-weight: 500;">确认要开始处理商品列表吗？系统将执行以下操作：</p>
                        <div style="margin: 12px 0; padding-left: 8px;">
                            <p style="margin: 8px 0;">1. 系统将扫描并识别当前页面所有审核未通过的商品</p>
                            <p style="margin: 8px 0;">2. 自动同步商品状态信息至后台任务管理系统</p>
                            <p style="margin: 8px 0;">3. 执行批量删除操作，从N11平台删除相关商品</p>
                            <p style="margin: 8px 0;">4. 处理完成后，您可前往任务管理页面重新发布这些商品</p>
                        </div>
                        <p style="margin-top: 16px; color: #e74c3c; font-weight: 500;">⚠️ 注意：此操作不可撤销，请谨慎确认！</p>
                    </div>
                `
            }
        )
        
        if (!confirmed) {
            return
        }
    } catch (error) {
        // 用户点击取消
        console.log('用户取消了操作')
        return
    }
    
    // 开始处理
    updateProcessButtonState(true)
    
    try {
        const pluginName = await getPluginName()
        
        // 显示开始处理的通知
        showNotification(
            `${pluginName} - 商品处理`, 
            '🚀 开始执行商品处理任务，正在进行初始化操作...', 
            'info'
        )
        
        console.log('=== 开始处理商品列表 ===')
        console.log('步骤1: 获取待处理商品数量')
        console.log('步骤2: 检查并设置页面大小为100条/页')
        
        // 执行商品统计和页面大小检查
        showNotification(
            `${pluginName} - 处理商品列表`, 
            '⚙️ 正在统计待处理商品数量...', 
            'info'
        )
        
        const setupResult = await getProductStatsAndCheckPageSize()
        
        if (!setupResult.success) {
            console.error('基础设置失败:', setupResult.message)
            showNotification(
                `${pluginName} - 处理商品列表失败`, 
                `❌ 处理商品列表失败：${setupResult.message}\n请刷新页面后重试`, 
                'error'
            )
            return
        }
        
        const totalProducts = setupResult.data?.totalProducts || 0
        const finalPageSize = setupResult.data?.finalPageSize || setupResult.data?.currentPageSize || 0
        
        console.log('基础设置完成:', setupResult.message)
        console.log('步骤3: 开始循环处理商品')
        
        showNotification(
            `${pluginName} - 处理商品列表`, 
            `✅ 初始化操作完成！\n📊 检测到待处理商品：${totalProducts}个\n📄 商品列表${finalPageSize}条/页\n\n🔄 即将启动自动化批处理流程...`, 
            'success'
        )
        
        // 循环处理：获取stock_code → 调用后端接口 → 删除操作
        let processRound = 1
        const maxProcessRounds = 20000000 // 防止无限循环，最多处理20000000轮
        let previousStockCodes: string[] = [] // 保存上一轮的stock_code列表，用于检测数据是否真正更新
        
        while (hasTableData() && processRound <= maxProcessRounds) {
            console.log(`=== 第 ${processRound} 轮处理操作 ===`)
            
            // 3.1 获取当前页面的所有 stock_code
            console.log(`第 ${processRound} 轮 - 步骤1: 获取当前页面商品的 stock_code`)
            
            showNotification(
                `${pluginName} - 第${processRound}轮处理`, 
                `🔍 第${processRound}轮批处理启动\n正在扫描当前页面商品数据...`, 
                'info'
            )
            
            const stockCodesResult = getAllStockCodes()
            
            if (!stockCodesResult.success) {
                console.error(`第 ${processRound} 轮获取商品列表失败:`, stockCodesResult.message)
                showNotification(
                    `${pluginName} - 第${processRound}轮失败`, 
                    `❌ 第${processRound}轮商品数据获取失败：${stockCodesResult.message}\n处理已中断`, 
                    'error'
                )
                break
            }
            
            const stockCodes = stockCodesResult.data?.stockCodes || []
            console.log(`第 ${processRound} 轮获取到 ${stockCodes.length} 个商品的 stock_code:`, stockCodes)
            
            if (stockCodes.length === 0) {
                console.log(`第 ${processRound} 轮没有商品数据，结束处理`)
                showNotification(
                    `${pluginName} - 处理完成`, 
                    `✅ 第${processRound}轮批处理完成！\n📋 当前页面已无待处理商品，所有处理任务已完成`, 
                    'success'
                )
                break
            }
            
            // 检查是否是真正的新数据（从第2轮开始检查）
            if (processRound > 1 && previousStockCodes.length > 0) {
                const currentFirstCode = stockCodes[0]
                const isDataChanged = !previousStockCodes.includes(currentFirstCode)
                
                if (!isDataChanged) {
                    console.log(`第 ${processRound} 轮 - 检测到数据未更新（第一个stock_code: ${currentFirstCode} 仍在上轮数据中），可能删除操作还未完成，需要继续等待...`)
                    showNotification(
                        `${pluginName} - 第${processRound}轮等待`, 
                        `⏳ 第${processRound}轮：正在等待页面数据更新...`, 
                        'info'
                    )
                    
                    // 继续等待数据真正更新
                    let waitForNewDataTime = 0
                    const maxWaitForNewDataTime = 120000 // 最多等待120秒等待新数据
                    const checkNewDataInterval = 2000 // 每2秒检查一次
                    let foundNewData = false
                    
                    while (waitForNewDataTime < maxWaitForNewDataTime) {
                        await new Promise(resolve => setTimeout(resolve, checkNewDataInterval))
                        waitForNewDataTime += checkNewDataInterval
                        
                        console.log(`第 ${processRound} 轮 - 等待新数据已 ${waitForNewDataTime/1000} 秒，重新检查数据状态...`)
                        
                        // 重新获取数据检查
                        const newStockCodesResult = getAllStockCodes()
                        if (newStockCodesResult.success) {
                            const newStockCodes = newStockCodesResult.data?.stockCodes || []
                            if (newStockCodes.length === 0) {
                                console.log(`第 ${processRound} 轮 - 检测到页面已无数据，处理完成`)
                                showNotification(
                                    `${pluginName} - 处理完成`, 
                                    `✅ 所有商品处理完成！`, 
                                    'success'
                                )
                                stockCodes.length = 0 // 清空数组，标记无数据
                                foundNewData = true
                                break
                            } else {
                                const newFirstCode = newStockCodes[0]
                                const isNewDataReady = !previousStockCodes.includes(newFirstCode)
                                if (isNewDataReady) {
                                    console.log(`第 ${processRound} 轮 - 检测到真正的新数据（第一个stock_code: ${newFirstCode} 不在上轮数据中），可以继续处理`)
                                    // 更新当前轮的stock_codes为最新数据
                                    stockCodes.length = 0 // 清空原数组
                                    stockCodes.push(...newStockCodes) // 添加新数据
                                    foundNewData = true
                                    break
                                } else {
                                    console.log(`第 ${processRound} 轮 - 数据仍未更新（第一个stock_code: ${newFirstCode} 仍在上轮数据中），继续等待...`)
                                }
                            }
                        }
                    }
                    
                    // 如果等待超时且没有找到新数据，强制继续（可能是特殊情况）
                    if (!foundNewData && waitForNewDataTime >= maxWaitForNewDataTime) {
                        /* console.warn(`第 ${processRound} 轮 - 等待新数据超时（${maxWaitForNewDataTime/1000}秒），强制继续处理`)
                        showNotification(
                            `${pluginName} - 第${processRound}轮超时`, 
                            `⚠️ 第${processRound}轮：等待新数据超时，强制继续处理\n可能存在数据不一致风险`, 
                            'warning'
                        ) */
                    }
                    
                    // 如果等待后仍无数据，跳出主循环
                    if (stockCodes.length === 0) {
                        break
                    }
                }
            }
            
            // 3.2 调用后端接口批量更新商品状态
            console.log(`第 ${processRound} 轮 - 步骤2: 调用后端接口批量更新商品状态`)
            
            showNotification(
                `${pluginName} - 第${processRound}轮数据同步`, 
                `📡 第${processRound}轮：正在将${stockCodes.length}个商品状态信息同步至后台任务管理系统\n数据传输中，请耐心等待处理完成...`, 
                'info'
            )
            await new Promise(resolve => setTimeout(resolve, 1500))
            
            const updateResult = await batchUpdateRejectedStatus(stockCodes)
            
            if (!updateResult.success) {
                console.error(`第 ${processRound} 轮后端接口调用失败:`, updateResult.message)
                showNotification(
                    `${pluginName} - 第${processRound}轮同步失败`, 
                    `❌ 第${processRound}轮数据同步异常：${updateResult.message}\n批处理已中断，请检查网络连接状态或联系客服反馈问题`, 
                    'error'
                )
                break // 如果后端接口失败，终止所有后续操作
            }
            
            console.log(`第 ${processRound} 轮后端接口调用成功:`, updateResult.message)
            
            showNotification(
                `${pluginName} - 第${processRound}轮同步成功`, 
                `✅ 第${processRound}轮数据同步完成！\n📊 已成功更新${stockCodes.length}个商品状态信息\n💡 ${updateResult.message}`, 
                'success'
            )
            
            // 3.3 执行删除操作
            console.log(`第 ${processRound} 轮 - 步骤3: 执行删除操作`)
            
            showNotification(
                `${pluginName} - 第${processRound}轮清理操作`, 
                `🗑️ 第${processRound}轮：正在执行N11平台商品清理任务\n操作流程：全选 → 批量删除 → 确认删除\n请勿手动干预页面操作...`, 
                'info'
            )
            
            const deleteResult = await executeDeleteFlow()
            
            if (!deleteResult.success) {
                console.error(`第 ${processRound} 轮删除失败:`, deleteResult.message)
                showNotification(
                    `${pluginName} - 第${processRound}轮清理失败`, 
                    `❌ 第${processRound}轮清理操作异常：${deleteResult.message}\n请联系客服反馈问题`, 
                    'error'
                )
                break
            }
            
            console.log(`第 ${processRound} 轮删除操作完成`)
            
            showNotification(
                `${pluginName} - 第${processRound}轮完成`, 
                `🎉 第${processRound}轮批处理执行成功！\n✅ 数据同步至任务系统完成\n✅ 平台商品清理完成\n\n⏳ 等待页面数据加载后继续下一轮处理...`, 
                'success'
            )
            
            // 保存当前轮的stock_code列表，用于下一轮的数据更新检测
            previousStockCodes = [...stockCodes]
            console.log(`第 ${processRound} 轮 - 已保存当前轮stock_code列表，用于下轮数据检测`)
            
            // 基础等待时间，确保删除操作开始执行
            console.log(`第 ${processRound} 轮 - 基础等待删除操作执行...`)
            await new Promise(resolve => setTimeout(resolve, 2000)) // 基础等待2秒
            
            processRound++
        }
        
        // 最终状态处理
        if (processRound > maxProcessRounds) {
            console.warn('达到最大处理轮数限制，停止处理操作')
            showNotification(
                `${pluginName} - 处理限制`, 
                `⚠️ 已达到最大批处理轮数限制（${maxProcessRounds}轮）\n为确保系统稳定性，自动处理已停止\n请检查页面状态，如需继续可重新启动处理任务`, 
                'warning'
            )
        } else if (!hasTableData()) {
            console.log('=== 所有商品处理完成 ===')
            showNotification(
                `${pluginName} - 全部完成`, 
                `🎊 恭喜！所有商品处理完成！\n✅ 所有被拒绝的商品已成功处理\n✅ 数据已同步到任务系统\n✅ 商品已从N11平台删除`, 
                'success'
            )
        }
        
        console.log('=== 商品列表处理完成 ===')
        
    } catch (error: any) {
        console.error('处理商品列表时发生异常:', error)
        const pluginName = await getPluginName()
        showNotification(
            `${pluginName} - 处理异常`, 
            `💥 处理过程中发生异常：${error.message || '未知错误'}\n请联系客服反馈问题`, 
            'error'
        )
    } finally {
        // 恢复按钮状态
        updateProcessButtonState(false)
        console.log('按钮状态已恢复')
    }
}

/**
 * 处理DOM变化
 */
const handleDomChange = (): void => {
    try {
        const currentSixthButtonActive = checkSixthButtonActive()
        const currentProcessButtonExists = !!document.getElementById(PROCESS_BUTTON_ID)
        
        console.log(`DOM变化检测 - 第六个按钮active: ${currentSixthButtonActive}, 处理按钮存在: ${currentProcessButtonExists}`)
        
        // 更新状态
        sixthButtonActive.value = currentSixthButtonActive
        processButtonExists.value = currentProcessButtonExists
        
        // 根据状态添加或移除处理按钮
        if (currentSixthButtonActive && !currentProcessButtonExists) {
            console.log('需要添加处理商品列表按钮')
            addProcessButton()
        } else if (!currentSixthButtonActive && currentProcessButtonExists) {
            console.log('需要移除处理商品列表按钮')
            removeProcessButton()
        }
    } catch (error) {
        console.error('处理DOM变化时发生错误:', error)
    }
}

/**
 * 设置MutationObserver
 */
const setupMutationObserver = (): void => {
    try {
        // 清理现有观察器
        if (observer) {
            observer.disconnect()
        }

        // 创建新的观察器
        observer = new MutationObserver((mutations) => {
            let shouldCheck = false
            
            // 检查是否有相关的DOM变化
            for (const mutation of mutations) {
                if (mutation.type === 'childList' || 
                    mutation.type === 'attributes') {
                    
                    const target = mutation.target as Element
                    
                    // 检查变化是否涉及tabManager或其子元素
                    if (target.closest('.tabManager') || 
                        target.classList.contains('tabManager') ||
                        target.querySelector('.tabManager')) {
                        shouldCheck = true
                        console.log('检测到tabManager DOM变化:', mutation.type, target)
                        break
                    }
                    
                    // 也检查是否是按钮的class变化
                    if (mutation.type === 'attributes' && 
                        mutation.attributeName === 'class' &&
                        target.tagName === 'BUTTON') {
                        const tabManager = target.closest('.tabManager')
                        if (tabManager) {
                            shouldCheck = true
                            console.log('检测到tabManager内按钮class变化:', target)
                            break
                        }
                    }
                }
            }
            
            if (shouldCheck) {
                console.log('触发DOM变化处理')
                // 使用setTimeout避免频繁触发
                setTimeout(handleDomChange, 50)
            }
        })

        // 开始观察
        const targetElement = document.body
        observer.observe(targetElement, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class'] // 只监听class属性变化
        })

        isObserving.value = true
        console.log('MutationObserver已启动')
        
        // 初始检查
        handleDomChange()
    } catch (error) {
        console.error('设置MutationObserver时发生错误:', error)
    }
}

/**
 * 清理MutationObserver
 */
const cleanupObserver = (): void => {
    try {
        if (observer) {
            observer.disconnect()
            observer = null
            isObserving.value = false
            console.log('MutationObserver已清理')
        }
        
        // 清理添加的按钮
        removeProcessButton()
    } catch (error) {
        console.error('清理MutationObserver时发生错误:', error)
    }
}

// 生命周期钩子
onMounted(async () => {
    console.log('N11产品列表DOM页面组件已挂载')
    
    // 初始化用户信息管理
    await initUserStore()
    
    // 监听用户信息变化
    userInfoChangeCleanup = onUserInfoChange(() => {
        console.log('用户信息发生变化，重新检查登录状态')
        
        // 防抖处理：避免短时间内重复检查
        const now = Date.now()
        if (now - lastLoginCheckTime < LOGIN_CHECK_DEBOUNCE_TIME) {
            console.log('防抖：跳过重复的用户信息变化检查')
            return
        }
        lastLoginCheckTime = now
        
        // 防止重复检查
        if (isCheckingUserLogin.value) {
            console.log('正在检查用户登录状态中，跳过重复检查')
            return
        }
        
        // 当用户信息变化时，重新检查登录状态
        if (globalUserInfo.isLogin) {
            // 异步执行，避免阻塞
            setTimeout(async () => {
                try {
                    isCheckingUserLogin.value = true
                    
                    // 检查用户信息是否真的发生了变化
                    const currentUserPhone = userInfo.value?.phone
                    const newUserPhone = globalUserInfo.phone
                    
                    if (currentUserPhone !== newUserPhone) {
                        console.log('用户信息确实发生变化，更新本地状态')
                        await checkAndHandleUserLogin(false) // 不显示错误消息，避免死循环
                    } else {
                        console.log('用户信息未发生实质变化，跳过更新')
                    }
                } catch (error) {
                    console.error('用户信息变化后检查登录状态失败:', error)
                } finally {
                    isCheckingUserLogin.value = false
                }
            }, 100)
        } else {
            // 用户退出登录，清理状态
            userInfo.value = null
            isCheckingUserLogin.value = false
            console.log('用户已退出登录，清理用户信息状态')
        }
    })
    
    // 先检查用户登录状态
    try {
        isCheckingUserLogin.value = true
        const isLoggedIn = await checkAndHandleUserLogin()
        if (isLoggedIn) {
            // 只有在用户已登录的情况下才启动DOM监听
            console.log('用户已登录，启动DOM监听')
            
            // 延迟启动观察器，确保页面完全加载
            setTimeout(setupMutationObserver, 1000)
            
            // 添加额外的点击监听器作为备用检测机制
            setTimeout(() => {
                const tabManager = document.querySelector('.tabManager')
                if (tabManager) {
                    tabManager.addEventListener('click', (event) => {
                        console.log('检测到tabManager点击事件:', event.target)
                        // 延迟检查状态变化
                        setTimeout(handleDomChange, 200)
                    })
                    console.log('已添加tabManager点击监听器')
                }
            }, 1500)
        } else {
            console.log('用户未登录，不启动DOM监听')
        }
    } catch (error) {
        console.error('初始化检查用户登录状态失败:', error)
        ElMessage.error('初始化用户登录状态失败，请刷新页面重试')
    } finally {
        isCheckingUserLogin.value = false
    }
})

onUnmounted(async () => {
    console.log('N11产品列表DOM页面组件即将卸载')
    
    // 清理用户信息变化监听器
    if (userInfoChangeCleanup) {
        userInfoChangeCleanup()
    }
    
    // 清理用户信息管理
    cleanupUserStore()
    
    // 清理观察器
    cleanupObserver()
    
    // 清理通知
    if (currentNotification) {
        currentNotification.close()
        currentNotification = null
    }
})
</script>

<style scoped>
.chrome_container {
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.debug-info {
    margin-top: 20px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
}

.debug-info p {
    margin: 5px 0;
}

/* 处理商品列表按钮样式 */
:global(.process-btn) {
    background-color: #fff;
    color: #5d3ebc;
    border: 1px solid #5d3ebc;
    padding: 5px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-left: 10px;
    transition: background-color 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    writing-mode: horizontal-tb;
    text-orientation: mixed;
    direction: ltr;
    min-width: auto;
    height: auto;
    line-height: normal;
    vertical-align: middle;
}

:global(.process-btn:hover) {
    color: #fff;
    background-color: #5d3ebc;
}

:global(.process-btn:active) {
    color: #fff;
    background-color: #4a2c9a;
}

:global(.process-btn:disabled) {
    color: #fff;
    background-color: #6c757d;
    cursor: not-allowed;
}

:global(.process-btn.processing) {
    color: #fff;
    background-color: #17a2b8;
    cursor: not-allowed;
    position: relative;
}

:global(.process-btn.processing::after) {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    margin-left: -8px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
