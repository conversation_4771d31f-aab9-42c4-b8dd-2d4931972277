<?php

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\UserGoodsCatRelationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 用户商品分类关联控制器
 */
class UserGoodsCatRelationController extends Controller
{
    protected UserGoodsCatRelationService $service;

    public function __construct(UserGoodsCatRelationService $service)
    {
        $this->service = $service;
        parent::__construct();
    }

    /**
     * 获取商品的分类关联信息
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getGoodsCatRelation(Request $request): JsonResponse
    {
        try {
            $goodsId = $request->input('goods_id');
            $thirdPlatformId = $request->input('third_platform_id', 2); // 默认为N11平台

            if (!$goodsId) {
                return $this->apiError('商品ID不能为空');
            }

            $user = $request->attributes->get('user');
            $userId = $user['id'];
            $relation = $this->service->getGoodsCatRelation($userId, $goodsId, $thirdPlatformId);

            return $this->apiSuccess($relation);
        } catch (\Exception $e) {
            return $this->apiError($e->getMessage());
        }
    }

    /**
     * 保存或更新商品分类关联
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function saveGoodsCatRelation(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'goods_id' => 'required|integer|min:1',
                'temu_cat_id' => 'required|integer|min:1',
                'third_platform_id' => 'required|integer|min:1',
                'third_platform_cat_id' => 'required|integer|min:1',
                'third_platform_cat_name' => 'required|string|max:255',
                'third_platform_cat_name_tl' => 'nullable|string|max:255',
                'third_platform_cat_path' => 'nullable|string|max:500',
                'third_platform_cat_path_tl' => 'nullable|string|max:500',
            ]);
            $user = $request->attributes->get('user');
            $data['user_id'] = $user['id'];
            
            $result = $this->service->saveGoodsCatRelation($data);

            return $this->apiSuccess($result, '保存成功');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->apiError('参数验证失败', 422, $e->errors());
        } catch (\Exception $e) {
            return $this->apiError($e->getMessage());
        }
    }

    /**
     * 删除商品分类关联
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteGoodsCatRelation(Request $request): JsonResponse
    {
        try {
            $goodsId = $request->input('goods_id');
            $thirdPlatformId = $request->input('third_platform_id', 2);

            if (!$goodsId) {
                return $this->apiError('商品ID不能为空');
            }

            $user = $request->attributes->get('user');
            $userId = $user['id'];
            $this->service->deleteGoodsCatRelation($userId, $goodsId, $thirdPlatformId);

            return $this->apiSuccess(null, '删除成功');
        } catch (\Exception $e) {
            return $this->apiError($e->getMessage());
        }
    }

    /**
     * 批量获取商品的分类关联信息
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchGetGoodsCatRelations(Request $request): JsonResponse
    {
        try {
            $goodsIds = $request->input('goods_ids', []);
            $thirdPlatformId = $request->input('third_platform_id', 2);

            if (empty($goodsIds) || !is_array($goodsIds)) {
                return $this->apiError('商品ID列表不能为空');
            }

            $user = $request->attributes->get('user');
            $userId = $user['id'];
            $relations = $this->service->batchGetGoodsCatRelations($userId, $goodsIds, $thirdPlatformId);

            return $this->apiSuccess($relations);
        } catch (\Exception $e) {
            return $this->apiError($e->getMessage());
        }
    }
}
