<template>
  <div class="card-usage">
    <div class="page-header">
      <h2>卡密激活</h2>
      <p class="subtitle">输入卡密获取VIP权益或积分</p>
    </div>

    <!-- 卡密激活表单 -->
    <el-card class="usage-card">
      <div class="usage-form">
        <el-form :model="usageForm" :rules="usageRules" ref="usageFormRef" label-width="0px" class="centered-form">
          <el-form-item prop="card_code" class="centered-form-item">
            <div class="input-wrapper">
              <el-input
                v-model="usageForm.card_code"
                placeholder="请输入卡密，格式如：KJF-XXXXXXXX"
                clearable
                size="large"
                :disabled="loading"
                class="centered-input"
              />
            </div>
          </el-form-item>
          <el-form-item class="centered-form-item">
            <div class="button-wrapper">
              <el-button
                type="primary"
                size="large"
                @click="handleUseCardWithConfirm"
                :loading="loading"
                class="centered-button"
              >
                {{ loading ? '处理中...' : '立即激活' }}
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 使用结果 -->
    <el-card v-if="usageResult" class="result-card">
      <div class="result-content">
        <div class="result-header">
          <el-icon class="success-icon"><SuccessFilled /></el-icon>
          <h3>卡密激活操作成功！</h3>
        </div>

        <div class="result-details">
          <div class="detail-item">
            <span class="label">卡密：</span>
            <span class="value">{{ usageResult.card_code }}</span>
          </div>
          <div v-if="usageResult.points_added > 0" class="detail-item">
            <span class="label">获得积分：</span>
            <span class="value highlight">+{{ usageResult.points_added }}</span>
          </div>
          <div v-if="usageResult.vip_days_added > 0" class="detail-item">
            <span class="label">VIP延长：</span>
            <span class="value highlight">+{{ usageResult.vip_days_added }}{{ getVipDaysUnitText(usageResult.vip_days_unit) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">积分变化：</span>
            <span class="value">{{ usageResult.user_points_before }} → {{ usageResult.user_points_after }}</span>
          </div>
          <div v-if="usageResult.user_vip_end_time_after" class="detail-item">
            <span class="label">VIP到期时间：</span>
            <span class="value">{{ usageResult.user_vip_end_time_after }}</span>
          </div>
        </div>

        <!-- 倒计时和立即刷新 -->
        <div class="countdown-section">
          <div class="countdown-info">
            <el-icon class="clock-icon"><Clock /></el-icon>
            <span class="countdown-text">{{ countdown }}秒后自动刷新用户信息</span>
          </div>
          <el-button
            type="primary"
            size="small"
            @click="handleImmediateRefresh"
            :loading="refreshing"
            class="immediate-refresh-btn"
          >
            {{ refreshing ? '刷新中...' : '立即刷新' }}
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 记录查看 -->
    <el-card class="records-card">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="卡密激活记录" name="usage">
          <div class="tab-header">
            <div class="date-filter">
              <el-date-picker
                v-model="usageDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleUsageDateChange"
                clearable
              />
              <el-button @click="loadUsageRecords" :loading="recordsLoading">刷新</el-button>
            </div>
          </div>

          <el-table
            :data="usageRecords"
            v-loading="recordsLoading"
            stripe
            empty-text="暂无使用记录"
          >
            <el-table-column label="卡密" width="250">
              <template #default="scope">
                <div class="card-code-cell">
                  <div class="card-code-content">
                    {{ scope.row.card_code }}
                  </div>
                  <el-button
                    type="text"
                    size="small"
                    @click="copyCardCode(scope.row.card_code)"
                    class="copy-btn"
                    :icon="CopyDocument"
                    title="复制卡密"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="points_added" label="获得积分" width="100">
              <template #default="scope">
                <span class="points">+{{ scope.row.points_added }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="vip_days_added" label="VIP时长" width="120">
              <template #default="scope">
                <span v-if="scope.row.vip_days_added > 0" class="vip-days">
                  +{{ scope.row.vip_days_added }}{{ getVipDaysUnitText(scope.row.vip_days_unit) }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="used_at" label="激活时间" width="160" />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="recordsPagination.total > 0">
            <el-pagination
              v-model:current-page="recordsPagination.page"
              v-model:page-size="recordsPagination.pageSize"
              :page-sizes="[10, 20, 50]"
              :total="recordsPagination.total"
              layout="total, sizes, prev, pager, next"
              @size-change="handleRecordsPageSizeChange"
              @current-change="handleRecordsPageChange"
            />
          </div>
        </el-tab-pane>

        <el-tab-pane label="积分扣减记录" name="points">
          <div class="tab-header">
            <div class="date-filter">
              <el-date-picker
                v-model="pointsDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handlePointsDateChange"
                clearable
              />
              <el-button @click="loadPointsLogs" :loading="pointsLoading">刷新</el-button>
            </div>
          </div>

          <el-table
            :data="pointsLogs"
            v-loading="pointsLoading"
            stripe
            empty-text="暂无积分扣减记录"
          >
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="description" label="内容" min-width="200" />
            <el-table-column label="扣减积分" width="100" align="center">
              <template #default="scope">
                <span class="points-deducted-simple">-{{ scope.row.points_deducted }}</span>
              </template>
            </el-table-column>
            <el-table-column label="积分变化" width="150" align="center">
              <template #default="scope">
                <div class="points-change">
                  <span class="points-before">{{ scope.row.points_before }}</span>
                  <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                  <span class="points-after">{{ scope.row.points_after }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="时间" width="160" />
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="pointsPagination.total > 0">
            <el-pagination
              v-model:current-page="pointsPagination.page"
              v-model:page-size="pointsPagination.pageSize"
              :page-sizes="[10, 20, 50]"
              :total="pointsPagination.total"
              layout="total, sizes, prev, pager, next"
              @size-change="handlePointsPageSizeChange"
              @current-change="handlePointsPageChange"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { SuccessFilled, Clock, CopyDocument, ArrowRight } from '@element-plus/icons-vue'
import { useCardCode, getUserUsageRecords } from '../utils/cardCodeApi'
import { getUserPointsLogs } from '../utils/pointsApi'
import { fetchAndUpdateUserInfo } from '../utils/userStore'

// 响应式数据
const loading = ref(false)
const recordsLoading = ref(false)
const pointsLoading = ref(false)
const usageFormRef = ref()
const usageResult = ref(null)
const usageRecords = ref([])
const pointsLogs = ref([])
const countdown = ref(5)
const refreshing = ref(false)
const activeTab = ref('usage')
const pointsDateRange = ref(null)
const usageDateRange = ref(null)

// 使用表单
const usageForm = reactive({
  card_code: ''
})

// 表单验证规则
const usageRules = {
  card_code: [
    { required: true, message: '请输入卡密', trigger: 'blur' },
    { min: 5, max: 50, message: '卡密长度应在5-50字符之间', trigger: 'blur' },
    { pattern: /^[a-zA-Z1-9][a-zA-Z0-9]{0,2}-[A-Z0-9]+$/, message: '卡密格式不正确', trigger: 'blur' }
  ]
}

// 使用记录分页
const recordsPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 积分记录分页
const pointsPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 防抖计时器
let debounceTimer: NodeJS.Timeout | null = null

// 倒计时计时器
let countdownTimer: NodeJS.Timeout | null = null

// 获取VIP时长单位文本
const getVipDaysUnitText = (unit: string) => {
  switch (unit) {
    case 'year': return '年'
    case 'month': return '月'
    case 'day': return '天'
    default: return '天'
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 5

  if (countdownTimer) {
    clearInterval(countdownTimer)
  }

  countdownTimer = setInterval(() => {
    countdown.value--

    if (countdown.value <= 0) {
      clearInterval(countdownTimer!)
      countdownTimer = null
      handleAutoRefresh()
    }
  }, 1000)
}

// 停止倒计时
const stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 自动刷新用户信息
const handleAutoRefresh = async () => {
  await refreshUserInfo()
}

// 立即刷新用户信息
const handleImmediateRefresh = async () => {
  stopCountdown()
  await refreshUserInfo()
}

// 刷新用户信息的通用方法
const refreshUserInfo = async () => {
  if (refreshing.value) return

  refreshing.value = true
  try {
    await fetchAndUpdateUserInfo()
    console.log('用户信息已刷新')
    ElMessage.success('用户信息已刷新')

    // 隐藏使用结果卡片
    usageResult.value = null
  } catch (error) {
    console.error('刷新用户信息失败:', error)
    ElMessage.error('刷新用户信息失败')
  } finally {
    refreshing.value = false
  }
}

// 复制卡密到剪切板
const copyCardCode = async (cardCode: string) => {
  try {
    await navigator.clipboard.writeText(cardCode)
    ElMessage.success('卡密已复制到剪切板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 带确认框的卡密激活方法
const handleUseCardWithConfirm = async () => {
  const formRef = usageFormRef.value
  if (!formRef) return

  try {
    // 先验证表单，只有验证通过才弹出确认框
    await formRef.validate()

    // 验证通过后弹出确认框
    await ElMessageBox.confirm(
      '确认要使用此卡密进行升级吗？',
      '确认升级',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    handleUseCardWithDebounce()
  } catch (error) {
    // 如果是表单验证失败，不做任何操作（Element Plus会自动显示验证错误信息）
    // 如果是用户取消确认框，也不做任何操作
    console.log('操作被取消或验证失败:', error)
  }
}

// 防抖处理的卡密激活方法
const handleUseCardWithDebounce = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  debounceTimer = setTimeout(() => {
    handleUseCard()
  }, 300) // 300ms防抖
}

// 卡密激活
const handleUseCard = async () => {
  const formRef = usageFormRef.value
  if (!formRef) return

  try {
    await formRef.validate()
    loading.value = true

    const response = await useCardCode(usageForm.card_code)
    console.log('卡密激活成功:', response)

    usageResult.value = response
    ElMessage.success('卡密激活操作成功！')

    // 清空表单
    usageForm.card_code = ''
    formRef.resetFields()

    // 刷新使用记录
    await loadUsageRecords()

    // 开始倒计时，5秒后自动刷新用户信息
    startCountdown()

    // 发送消息通知popup更新用户信息
    try {
      // 发送消息到background script，再转发给popup
      chrome.runtime.sendMessage({
        type: 'CARD_USAGE_SUCCESS',
        timestamp: Date.now(),
        cardUsageResult: response
      }).catch(() => {
        // 忽略发送失败的情况
      });
      console.log('卡密激活成功通知已发送');
    } catch (error) {
      console.error('发送卡密激活成功通知失败:', error);
    }

  } catch (error) {
    console.error('卡密激活失败:', error)
    usageResult.value = null
  } finally {
    loading.value = false
  }
}

// 加载使用记录
const loadUsageRecords = async () => {
  recordsLoading.value = true
  try {
    const params: any = {
      page: recordsPagination.page,
      pageSize: recordsPagination.pageSize
    }

    // 添加时间筛选参数
    if (usageDateRange.value && usageDateRange.value.length === 2) {
      params.start_date = usageDateRange.value[0]
      params.end_date = usageDateRange.value[1]
    }

    const response = await getUserUsageRecords(params)
    usageRecords.value = response.list || []
    recordsPagination.total = response.pagination?.total || 0

    console.log('使用记录加载成功:', response)
  } catch (error) {
    console.error('加载使用记录失败:', error)
    ElMessage.error('加载使用记录失败')
    usageRecords.value = []
  } finally {
    recordsLoading.value = false
  }
}

// 卡密激活记录日期范围变化
const handleUsageDateChange = () => {
  recordsPagination.page = 1
  loadUsageRecords()
}

// 分页大小变化
const handleRecordsPageSizeChange = (size: number) => {
  recordsPagination.pageSize = size
  recordsPagination.page = 1
  loadUsageRecords()
}

// 当前页变化
const handleRecordsPageChange = (page: number) => {
  recordsPagination.page = page
  loadUsageRecords()
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  if (tabName === 'points') {
    loadPointsLogs()
  }
}

// 加载积分记录
const loadPointsLogs = async () => {
  pointsLoading.value = true
  try {
    const params: any = {
      page: pointsPagination.page,
      page_size: pointsPagination.pageSize
    }

    // 添加时间筛选参数
    if (pointsDateRange.value && pointsDateRange.value.length === 2) {
      params.start_date = pointsDateRange.value[0]
      params.end_date = pointsDateRange.value[1]
    }

    const response = await getUserPointsLogs(params)
    pointsLogs.value = response.data || []
    pointsPagination.total = response.total || 0

    console.log('积分记录加载成功:', response)
  } catch (error) {
    console.error('加载积分记录失败:', error)
    ElMessage.error('加载积分记录失败')
    pointsLogs.value = []
  } finally {
    pointsLoading.value = false
  }
}

// 积分记录日期范围变化
const handlePointsDateChange = () => {
  pointsPagination.page = 1
  loadPointsLogs()
}

// 积分记录分页大小变化
const handlePointsPageSizeChange = (size: number) => {
  pointsPagination.pageSize = size
  pointsPagination.page = 1
  loadPointsLogs()
}

// 积分记录当前页变化
const handlePointsPageChange = (page: number) => {
  pointsPagination.page = page
  loadPointsLogs()
}

// 组件挂载时加载数据
onMounted(() => {
  loadUsageRecords()
})
</script>

<style scoped>
.card-usage {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
}

.subtitle {
  color: #909399;
  font-size: 16px;
  margin: 0;
}

.usage-card {
  margin-bottom: 30px;
}

.usage-card .el-card__body {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 20px;
}

.usage-form {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.centered-form {
  width: 100%;
}

.centered-form-item {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin-bottom: 20px !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.centered-form-item:last-child {
  margin-bottom: 0 !important;
}

.centered-form-item .el-form-item__content {
  margin-left: 0 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
}

.input-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.button-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.centered-input {
  width: 550px !important;
  max-width: 100% !important;
}

.centered-button {
  width: 200px !important;
  max-width: 100% !important;
}

.result-card {
  margin-bottom: 30px;
  border: 2px solid #67C23A;
}

.result-content {
  text-align: center;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.success-icon {
  font-size: 24px;
  color: #67C23A;
  margin-right: 10px;
}

.result-header h3 {
  margin: 0;
  color: #67C23A;
  font-size: 20px;
}

.result-details {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #EBEEF5;
}

.detail-item:last-child {
  border-bottom: none;
}

.label {
  color: #606266;
  font-weight: 500;
}

.value {
  color: #303133;
}

.value.highlight {
  color: #67C23A;
  font-weight: bold;
}

.countdown-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.countdown-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
}

.clock-icon {
  font-size: 16px;
  color: #409EFF;
}

.countdown-text {
  font-weight: 500;
}

.immediate-refresh-btn {
  flex-shrink: 0;
}

.records-card {
  margin-bottom: 20px;
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.records-header h3 {
  margin: 0;
  color: #303133;
}

.points {
  color: #67C23A;
  font-weight: bold;
}

.vip-days {
  color: #E6A23C;
  font-weight: bold;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.card-code-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-code-content {
  flex: 1;
  word-break: break-all;
  line-height: 1.4;
  min-height: 20px;
  max-width: 200px;
}

.copy-btn {
  flex-shrink: 0;
  padding: 4px 8px !important;
  min-width: auto !important;
  height: 24px !important;
  color: #409EFF !important;
}

.copy-btn:hover {
  background-color: #ecf5ff !important;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 15px;
}

.date-filter .el-date-editor {
  width: 300px;
}

/* 积分变化样式 */
.points-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.points-before {
  color: #909399;
  font-size: 14px;
  font-weight: 500;
}

.points-after {
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.arrow-icon {
  color: #409EFF;
  font-size: 12px;
}

/* 扣减积分简洁样式 */
.points-deducted-simple {
  color: #F56C6C;
  font-size: 14px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-usage {
    padding: 15px;
  }

  .usage-form {
    padding: 15px;
  }

  .result-details {
    max-width: 100%;
  }

  .records-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .countdown-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 10px;
  }

  .countdown-info {
    justify-content: center;
  }

  .tab-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .date-filter {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .date-filter .el-date-editor {
    width: 100%;
  }
}
</style>
