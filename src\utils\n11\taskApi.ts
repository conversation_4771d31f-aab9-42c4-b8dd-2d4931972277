/**
 * 任务API - Chrome扩展版本
 * 用于处理任务相关的API调用
 */
import config from '../../config'

/**
 * 通过background页面发送请求
 */
function sendRequestViaBackground(request: any): Promise<any> {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      url: request.url,
      method: request.method || 'get',
      pramas: request.data || request.params || {},
      headers: request.headers || {},
      auth: request.auth || false,
      encrypto: request.encrypto || false,
      timeout: request.timeout || 59000
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }

      if (response && response[0] && response[0].data) {
        const result = response[0].data;
        if (result.code == 1) {
          resolve({ success: true, data: result.data });
        } else {
          reject(new Error(`请求失败: ${result.status}`));
        }
      } else {
        reject(new Error('请求未返回有效数据'));
      }
    });
  });
}

// 任务更新参数接口
export interface TaskUpdateParams {
  task_detail_id: number
  task_id: number
  third_task_id?: number
  third_type?: string
  third_status?: string
  third_result?: string
  task_over: number
  task_num: number
}

// 任务开始响应接口
export interface TaskStartResponse {
  task_id?: number
  goods_name?: string
  thumb_url?: string
  price_third?: string
  price_list_third?: string
  currentcy?: string
  category_id?: number
  product_main_id?: string
  stock_code?: string
  spec_key_values?: string
  images?: Array<{
    url: string
    order: number
  }>
  goods_info?: {
    goods_property?: string
  }
  store_info?: {
    app_key: string
    app_secret: string
    integrator_name: string
    preparing_day: number
    shipment_template: string
    quantity: number
    vat_rate: number
    brand: string
  }
}

/**
 * 更新任务状态
 * @param params 更新参数
 */
export const updateTask = async (params: TaskUpdateParams): Promise<void> => {
  try {
    const response = await sendRequestViaBackground({
      funName: 'updateTask',
      url: config.apiTaskUpdateUrl,
      method: 'post',
      data: params,
      auth: true,
      encrypto: true
    })

    if (!response.success) {
      throw new Error(response.message || '更新任务状态失败')
    }

    console.log('任务状态更新成功:', params)
  } catch (error) {
    console.error('更新任务状态失败:', error)
    throw error
  }
}

/**
 * 获取重新上传参数
 * @param taskDetailId 任务详情ID
 * @returns 任务数据
 */
export const getRetryUploadParams = async (taskDetailId: number): Promise<TaskStartResponse> => {
  try {
    const response = await sendRequestViaBackground({
      funName: 'getRetryUploadParams',
      url: config.apiTaskRetryUploadParamsUrl,
      method: 'get',
      params: { task_detail_id: taskDetailId },
      auth: true,
      encrypto: true
    })

    if (!response.success || !response.data) {
      throw new Error(response.message || '获取重新上传参数失败')
    }

    console.log('获取重新上传参数成功:', response.data)
    return response.data as TaskStartResponse
  } catch (error) {
    console.error('获取重新上传参数失败:', error)
    throw error
  }
}

/**
 * 保存上传参数
 * @param taskDetailId 任务详情ID
 * @param platform 平台类型
 * @param params 参数JSON字符串
 */
export const saveUploadParams = async (
  taskDetailId: number,
  platform: string,
  params: string
): Promise<void> => {
  try {
    const response = await sendRequestViaBackground({
      funName: 'saveUploadParams',
      url: config.apiTaskSaveUploadParamsUrl,
      method: 'post',
      data: {
        task_detail_id: taskDetailId,
        platform,
        params
      },
      auth: true,
      encrypto: true
    })

    if (!response.success) {
      throw new Error(response.message || '保存上传参数失败')
    }

    console.log('保存上传参数成功')
  } catch (error) {
    console.error('保存上传参数失败:', error)
    throw error
  }
}
