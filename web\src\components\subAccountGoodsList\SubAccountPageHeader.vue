<template>
  <div class="page-header">
    <div class="header-left">
      <div class="custom-breadcrumb">
        <el-button type="text" @click="goBack" class="breadcrumb-back-button">
          <el-icon><ArrowLeft /></el-icon>
          <span>商品目录</span>
        </el-button>
        <template v-if="currentDirectoryName">
          <span class="breadcrumb-separator">/</span>
          <span class="breadcrumb-current-page">{{ currentDirectoryName }}</span>
        </template>
        <template v-else-if="currentSubAccountName">
          <span class="breadcrumb-separator">/</span>
          <span class="breadcrumb-current-page">{{ currentSubAccountName }}的商品</span>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft } from '@element-plus/icons-vue'

interface Props {
  currentDirectoryName?: string
  currentSubAccountName?: string
  selectedGoods: any[]
}

defineProps<Props>()

const emit = defineEmits<{
  goBack: []
  batchOperation: []
  batchDelete: []
  moveOperation: []
}>()

const goBack = () => {
  emit('goBack')
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
}

.custom-breadcrumb {
  display: flex;
  align-items: center;
  font-size: 18px;
  margin-right: 20px; /* 与右侧信息隔开一些距离 */
}

.breadcrumb-back-button {
  font-size: 18px; /* 统一返回按钮字体大小 */
  color: #409EFF; /* Element Plus 主题蓝色 */
  padding: 0; /* 移除默认padding */
  margin-right: 8px;
}

.breadcrumb-back-button .el-icon {
  margin-right: 4px;
}

.breadcrumb-back-button:hover,
.breadcrumb-back-button:focus {
  color: #66b1ff; /* 悬停颜色变浅 */
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #C0C4CC; /* 分隔符颜色 */
}

.breadcrumb-current-page {
  font-weight: 600; /* 当前页面名称加粗 */
  color: #303133; /* 当前页面文字颜色 */
  line-height: 1.2; /* 调整行高，减少占据的高度 */
  max-height: 24px; /* 限制最大高度 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超长文本显示省略号 */
  white-space: nowrap; /* 不换行 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}
</style>