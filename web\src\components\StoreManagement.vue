<template>
  <div class="store-management">
    <div class="page-header">
      <h2>店铺管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          新增店铺
        </el-button>
        <el-button type="warning" @click="showBatchDialog" :disabled="selectedStores.length === 0">
          <el-icon><Setting /></el-icon>
          批量设置
        </el-button>
      </div>
    </div>

    <!-- 搜索条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="店铺类型" class="store-type-item">
          <el-select
            v-model="searchForm.account_type"
            placeholder="请选择店铺类型"
            clearable
            popper-append-to-body
          >
            <el-option label="n11" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺名称">
          <el-input v-model="searchForm.account_name" placeholder="请输入店铺名称" clearable />
        </el-form-item>
        <el-form-item label="品牌名称">
          <el-input v-model="searchForm.brand" placeholder="请输入品牌名称" clearable />
        </el-form-item>
        <!-- <el-form-item label="集成商名称">
          <el-input v-model="searchForm.integrator_name" placeholder="请输入集成商名称" clearable />
        </el-form-item> -->
        <el-form-item label="状态" class="status-item">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            popper-append-to-body
          >
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="storeList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="80" :index="getIndex" />
        <el-table-column label="店铺信息" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div>类型: <el-tag :type="getStoreTypeTagType(row.account_type)">{{ getStoreTypeName(row.account_type) }}</el-tag></div>
            <div>名称: {{ row.account_name }}</div>
            <div>品牌: {{ row.brand }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="集成商名称" prop="integrator_name" min-width="120" show-overflow-tooltip /> -->
        <el-table-column label="价格设置" width="150">
          <template #default="{ row }">
            <div class="price-info-line">原价 ×{{ row.price_rate }}</div>
            <div v-if="row.price_add > 0" class="price-info-line">+ {{ row.price_add }}</div>
            <div v-if="row.price_subtract > 0" class="price-info-line">- {{ row.price_subtract }}</div>
          </template>
        </el-table-column>
        <el-table-column label="物流/库存/税率/备货" min-width="180">
          <template #default="{ row }">
            <div class="combined-info-line">物流模板: {{ row.shipment_template || '-' }}</div>
            <div class="combined-info-line">库存数量: {{ row.quantity || 0 }}</div>
            <div class="combined-info-line">增值税率: {{ row.vat_rate || 0 }}%</div>
            <div class="combined-info-line">备货天数: {{ row.preparing_day || 0 }}天</div>
          </template>
        </el-table-column>
        <el-table-column label="接口凭证" min-width="250" show-overflow-tooltip>
          <template #default="{ row }">
            <div>AppKey: {{ row.app_key }}</div>
            <div class="secret-text">AppSecret: {{ maskSecret(row.app_secret) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="created_at" width="160" />
        <el-table-column label="状态" prop="status" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              v-if="row.account_type === 2"
              type="success"
              size="small"
              @click="handleTestConnection(row)"
              :loading="testingStoreId === row.id"
            >
              接口测试
            </el-button>
            <!-- 明确注释掉删除 请不要恢复 <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      :before-close="handleDialogClose"
      append-to-body
    >
      <el-form
        ref="storeFormRef"
        :model="storeForm"
        :rules="storeFormRules"
        label-width="100px"
      >
        <el-form-item label="店铺类型" prop="account_type">
          <el-select
            v-model="storeForm.account_type"
            placeholder="请选择店铺类型"
            clearable
            popper-append-to-body
          >
            <el-option label="n11" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="店铺名称" prop="account_name">
          <el-input v-model="storeForm.account_name" placeholder="请输入店铺名称" />
        </el-form-item>
        <el-form-item label="品牌名称" prop="brand">
          <el-input v-model="storeForm.brand" placeholder="请输入品牌名称,发布商品时使用" />
        </el-form-item>
        <el-form-item label="集成商名称" prop="integrator_name">
          <el-input v-model="storeForm.integrator_name" placeholder="请输入集成商名称" />
        </el-form-item>
        <el-form-item label="价格设置" class="price-setting-item">
          <div class="price-formula">
            <span class="formula-text">原价 ×</span>
            <el-input
              v-model="storeForm.price_rate"
              placeholder="倍数"
              class="price-input-underline"
              type="number"
              step="0.01"
              min="0"
              max="999.99"
            />
            <span class="formula-text formula-operator">+</span>
            <el-input
              v-model="storeForm.price_add"
              placeholder="加值"
              class="price-input-underline"
              type="number"
              step="0.01"
              min="0"
              max="999999.99"
            />
            <span class="formula-text formula-operator">−</span>
            <el-input
              v-model="storeForm.price_subtract"
              placeholder="减值"
              class="price-input-underline"
              type="number"
              step="0.01"
              min="0"
              max="999999.99"
            />
          </div>
          <div class="price-tip">例如：原价100元，设置为 ×1.1 +5 −2，最终价格为 100×1.1+5−2=113元</div>
        </el-form-item>
        <el-form-item label="物流模板" prop="shipment_template">
          <el-input v-model="storeForm.shipment_template" placeholder="请输入物流模板名称" />
        </el-form-item>
        <el-form-item label="库存数量" prop="quantity">
          <el-input-number
            v-model="storeForm.quantity"
            :min="1"
            :max="9999"
            :precision="0"
            placeholder="请输入商品库存数量"
          />
        </el-form-item>
        <el-form-item label="增值税率" prop="vat_rate">
          <el-select
            v-model="storeForm.vat_rate"
            placeholder="请选择增值税率"
            popper-append-to-body
          >
            <el-option label="0%" :value="0" />
            <el-option label="1%" :value="1" />
            <el-option label="10%" :value="10" />
            <el-option label="20%" :value="20" />
          </el-select>
        </el-form-item>
        <el-form-item label="备货天数" prop="preparing_day">
          <el-input-number
            v-model="storeForm.preparing_day"
            :min="1"
            :max="365"
            :precision="0"
            placeholder="请输入备货天数"
          />
          <span class="form-tip">天</span>
        </el-form-item>
        <el-form-item label="AppKey" prop="app_key">
          <el-input v-model="storeForm.app_key" placeholder="请输入AppKey" />
        </el-form-item>
        <el-form-item label="AppSecret" prop="app_secret">
          <el-input v-model="storeForm.app_secret" placeholder="请输入AppSecret" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="storeForm.status">
            <el-radio :value="1">正常</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量设置对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量设置"
      width="700px"
      class="batch-dialog"
    >
      <div class="batch-form-container">
        <el-form
          ref="batchFormRef"
          :model="batchForm"
          :rules="batchFormRules"
          label-width="120px"
        >
          <el-form-item label="状态">
            <el-select
              v-model="batchForm.status"
              placeholder="请选择状态（不选择则不修改）"
              clearable
              popper-append-to-body
            >
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="价格设置" class="batch-price-setting">
            <div class="price-formula">
              <span class="formula-text">原价 ×</span>
              <el-input
                v-model="batchForm.price_rate"
                placeholder="倍数"
                class="price-input-underline-small"
                type="number"
                step="0.01"
                min="0"
                max="999.99"
              />
              <span class="formula-text formula-operator">+</span>
              <el-input
                v-model="batchForm.price_add"
                placeholder="加值"
                class="price-input-underline-small"
                type="number"
                step="0.01"
                min="0"
                max="999999.99"
              />
              <span class="formula-text formula-operator">−</span>
              <el-input
                v-model="batchForm.price_subtract"
                placeholder="减值"
                class="price-input-underline-small"
                type="number"
                step="0.01"
                min="0"
                max="999999.99"
              />
            </div>
          </el-form-item>
          <el-form-item label="应用价格设置">
            <el-switch v-model="batchForm.apply_price_rate" />
          </el-form-item>
          <el-form-item label="库存数量">
            <el-input-number
              v-model="batchForm.quantity"
              :min="1"
              :max="9999"
              :precision="0"
              placeholder="请输入商品库存数量"
            />
          </el-form-item>
          <el-form-item label="应用库存数量">
            <el-switch v-model="batchForm.apply_quantity" />
          </el-form-item>
          <el-form-item label="增值税率">
            <el-select
              v-model="batchForm.vat_rate"
              placeholder="请选择增值税率"
              popper-append-to-body
            >
              <el-option label="0%" :value="0" />
              <el-option label="1%" :value="1" />
              <el-option label="10%" :value="10" />
              <el-option label="20%" :value="20" />
            </el-select>
          </el-form-item>
          <el-form-item label="应用增值税率">
            <el-switch v-model="batchForm.apply_vat_rate" />
          </el-form-item>
          <el-form-item label="集成商名称">
            <el-input v-model="batchForm.integrator_name" placeholder="请输入集成商名称" />
          </el-form-item>
          <el-form-item label="应用集成商名称">
            <el-switch v-model="batchForm.apply_integrator_name" />
          </el-form-item>
          <el-form-item label="备货天数">
            <el-input-number
              v-model="batchForm.preparing_day"
              :min="1"
              :max="365"
              :precision="0"
              placeholder="请输入备货天数"
            />
            <span class="form-tip">天</span>
          </el-form-item>
          <el-form-item label="应用备货天数">
            <el-switch v-model="batchForm.apply_preparing_day" />
          </el-form-item>
          <div class="batch-info">
            <p>将对以下 {{ selectedStores.length }} 个店铺进行批量设置：</p>
            <ul class="selected-stores">
              <li v-for="store in selectedStores" :key="store.id">
                {{ store.account_name }}
              </li>
            </ul>
          </div>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchSubmit" :loading="batchSubmitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { Plus, Setting, Search, Refresh } from '@element-plus/icons-vue'
import {
  getStoreList,
  createStore,
  updateStore,
  deleteStore,
  batchUpdateStores,
  type Store as StoreType,
  type StoreListParams
} from '../utils/storeApi'
import { testN11StoreConnection } from '../utils/n11TestApi'

// 接口定义
interface Store extends StoreType {
  id: number
  createTime: string
}

interface SearchForm {
  account_type: number | null | undefined
  account_name: string
  brand: string
  integrator_name: string
  status: number | null | undefined  // 新增状态搜索字段
}

interface StoreForm {
  id?: number
  account_type: number
  account_name: string
  brand: string
  price_rate: number
  price_add: number
  price_subtract: number
  shipment_template: string
  quantity: number
  vat_rate: number
  preparing_day: number
  app_key: string
  app_secret: string
  status: number  // 新增状态字段
  integrator_name: string  // 新增集成商名称字段
}

interface BatchForm {
  price_rate: number
  price_add: number
  price_subtract: number
  status?: number | null // 新增批量状态设置
  apply_price_rate: boolean // 新增控制是否应用价格设置
  shipment_template: string
  apply_shipment_template: boolean
  quantity: number
  apply_quantity: boolean
  vat_rate: number
  apply_vat_rate: boolean
  preparing_day: number
  apply_preparing_day: boolean
  integrator_name: string  // 新增集成商名称字段
  apply_integrator_name: boolean  // 新增控制是否应用集成商名称
}

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const batchSubmitting = ref(false)
const validating = ref(false)
const testingStoreId = ref<number | null>(null) // 新增：正在测试的店铺ID
const storeList = ref<Store[]>([])
const selectedStores = ref<Store[]>([])
const credentialStatus = ref<{ valid: boolean; message: string } | null>(null)

// 搜索表单
const searchForm = reactive<SearchForm>({
  account_type: null,
  account_name: '',
  brand: '',
  integrator_name: '',
  status: null  // 新增状态搜索字段
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 对话框状态
const dialogVisible = ref(false)
const batchDialogVisible = ref(false)
const isEdit = ref(false)

// 表单数据
const storeForm = reactive<StoreForm>({
  id: undefined,
  account_type: 2,  // 修改默认值为有效的店铺类型
  account_name: '',
  brand: '',
  price_rate: 1.00,
  price_add: 0.00,
  price_subtract: 0.00,
  shipment_template: '',
  quantity: 10,
  vat_rate: 0,
  preparing_day: 5,
  app_key: '',
  app_secret: '',
  status: 1,  // 默认状态为正常
  integrator_name: ''  // 新增集成商名称字段
})

const batchForm = reactive<BatchForm>({
  price_rate: 1.00,
  price_add: 0.00,
  price_subtract: 0.00,
  status: undefined, // 批量状态设置，默认不设置
  apply_price_rate: false, // 默认不应用价格设置
  shipment_template: '',
  apply_shipment_template: false,
  quantity: 10,
  apply_quantity: false,
  vat_rate: 0,
  apply_vat_rate: false,
  preparing_day: 5,
  apply_preparing_day: false,
  integrator_name: '',  // 新增集成商名称字段
  apply_integrator_name: false  // 新增控制是否应用集成商名称
})

// 表单引用
const storeFormRef = ref()
const batchFormRef = ref()

// 表单验证规则
const storeFormRules = {
  account_type: [
    {
      required: true,
      validator: (rule: any, value: number, callback: any) => {
        if (!value || value === 0) {
          callback(new Error('请选择店铺类型'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  account_name: [{ required: true, message: '请输入店铺名称', trigger: 'blur' }],
  brand: [{ required: true, message: '请输入品牌名称', trigger: 'blur' }],
  price_rate: [{ required: true, message: '请输入价格倍数', trigger: 'blur' }],
  price_add: [{ required: true, message: '请输入价格加值', trigger: 'blur' }],
  price_subtract: [{ required: true, message: '请输入价格减值', trigger: 'blur' }],
  shipment_template: [{ required: true, message: '请输入物流模板名称', trigger: 'blur' }],
  quantity: [{ required: true, message: '请输入默认商品数量', trigger: 'blur' }],
  vat_rate: [{ required: true, message: '请输入增值税率', trigger: 'blur' }],
  preparing_day: [{ required: true, message: '请输入备货天数', trigger: 'blur' }],
  app_key: [{ required: true, message: '请输入AppKey', trigger: 'blur' }],
  app_secret: [{ required: true, message: '请输入AppSecret', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  integrator_name: [{ required: true, message: '请输入集成商名称', trigger: 'blur' }]
}

const batchFormRules = {
  // 移除 price_rate 的 required 验证
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑店铺' : '新增店铺')

// 获取序号
const getIndex = (index: number) => {
  return (pagination.currentPage - 1) * pagination.pageSize + index + 1
}

// 获取店铺类型名称
const getStoreTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    2: 'n11',
  }
  return typeMap[type] === undefined ? type : typeMap[type]
}

// 获取店铺类型标签类型
const getStoreTypeTagType = (type: string) => {
  return 'primary'
}

// 获取状态名称
const getStatusName = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '正常',
    0: '禁用'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  return status === 1 ? 'success' : 'danger'
}

// 隐藏AppSecret
const maskSecret = (secret: string) => {
  if (!secret) return ''
  return secret.length > 8 ? secret.substring(0, 4) + '****' + secret.substring(secret.length - 4) : '****'
}


// 加载店铺列表
const loadStoreList = async () => {
  loading.value = true
  try {
    const params: StoreListParams = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      account_type: (searchForm.account_type === null || searchForm.account_type === undefined || searchForm.account_type === 0) ? undefined : searchForm.account_type,
      account_name: searchForm.account_name || '',
      brand: searchForm.brand || '',
      integrator_name: searchForm.integrator_name || '',
      status: (searchForm.status === null || searchForm.status === undefined) ? undefined : searchForm.status  // 支持传递0值
    }

    const response = await getStoreList(params)
    console.log('获取店铺列表成功:', response)
    storeList.value = response.list as Store[]
    pagination.total = response.pagination.total
    pagination.totalPages = response.pagination.totalPages
    pagination.hasNext = response.pagination.hasNext
    pagination.hasPrevious = response.pagination.hasPrevious
  } catch (error) {
    console.log('获取店铺列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadStoreList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    account_type: undefined,
    account_name: '',
    brand: '',
    integrator_name: '',
    status: undefined  // 重置状态搜索
  })
  pagination.currentPage = 1
  loadStoreList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadStoreList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadStoreList()
}

// 选择变化
const handleSelectionChange = (selection: Store[]) => {
  selectedStores.value = selection
}

// 显示新增对话框
const showAddDialog = () => {
  isEdit.value = false
  resetStoreForm()
  dialogVisible.value = true
}

// 显示批量设置对话框
const showBatchDialog = () => {
  // 重置批量表单数据
  Object.assign(batchForm, {
    price_rate: 1.00,
    price_add: 0.00,
    price_subtract: 0.00,
    status: undefined,
    apply_price_rate: false,
    shipment_template: '',
    apply_shipment_template: false,
    quantity: 10,
    apply_quantity: false,
    vat_rate: 0,
    apply_vat_rate: false,
    preparing_day: 5,
    apply_preparing_day: false,
    integrator_name: '',
    apply_integrator_name: false
  })

  // 清理表单验证
  batchFormRef.value?.clearValidate()

  batchDialogVisible.value = true
}

// 编辑
const handleEdit = (row: Store) => {
  isEdit.value = true
  Object.assign(storeForm, row as StoreForm)
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: Store) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除店铺"${row.account_name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteStore(row.id)
    ElMessage.success('删除成功')
    loadStoreList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}


// 重置表单
const resetStoreForm = () => {
  Object.assign(storeForm, {
    id: undefined,
    account_type: 2,  // 修改默认值为有效的店铺类型
    account_name: '',
    brand: '',
    price_rate: 1.00,
    price_add: 0.00,
    price_subtract: 0.00,
    shipment_template: '',
    quantity: 10,
    vat_rate: 0,
    preparing_day: 5,
    app_key: '',
    app_secret: '',
    status: 1,  // 重置时默认为正常状态
    integrator_name: ''  // 重置时集成商名称字段
  })
  storeFormRef.value?.clearValidate()
}

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false
  resetStoreForm()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await storeFormRef.value.validate()

    submitting.value = true

    if (isEdit.value && storeForm.id) {
      // 编辑店铺
      await updateStore({
        id: storeForm.id,
        account_type: storeForm.account_type,
        account_name: storeForm.account_name,
        brand: storeForm.brand,
        price_rate: storeForm.price_rate,
        price_add: storeForm.price_add,
        price_subtract: storeForm.price_subtract,
        shipment_template: storeForm.shipment_template,
        quantity: storeForm.quantity,
        vat_rate: storeForm.vat_rate,
        preparing_day: storeForm.preparing_day,
        app_key: storeForm.app_key,
        app_secret: storeForm.app_secret,
        status: storeForm.status,
        integrator_name: storeForm.integrator_name
      })
      ElMessage.success('编辑成功')
    } else {
      // 新增店铺
      await createStore({
        account_type: storeForm.account_type,
        account_name: storeForm.account_name,
        brand: storeForm.brand,
        price_rate: storeForm.price_rate,
        price_add: storeForm.price_add,
        price_subtract: storeForm.price_subtract,
        shipment_template: storeForm.shipment_template,
        quantity: storeForm.quantity,
        vat_rate: storeForm.vat_rate,
        preparing_day: storeForm.preparing_day,
        app_key: storeForm.app_key,
        app_secret: storeForm.app_secret,
        status: storeForm.status,
        integrator_name: storeForm.integrator_name
      })
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
    resetStoreForm()
    loadStoreList()
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    submitting.value = false
  }
}

// 批量设置提交
const handleBatchSubmit = async () => {
  try {
    await batchFormRef.value.validate()

    // 检查是否至少选择了一项进行设置
    if (!batchForm.apply_price_rate &&
        (batchForm.status === undefined || batchForm.status === null) &&
        !batchForm.apply_shipment_template &&
        !batchForm.apply_quantity &&
        !batchForm.apply_vat_rate &&
        !batchForm.apply_preparing_day &&
        !batchForm.apply_integrator_name) {
        ElMessage.warning('请至少选择一项进行批量设置')
        return // 停止提交
    }

    batchSubmitting.value = true

    const ids = selectedStores.value.map(store => store.id)
    const updateData: any = {
      ids,
    }

    // 只有选择了应用价格设置才添加到更新数据中
    if (batchForm.apply_price_rate) {
      updateData.price_rate = batchForm.price_rate
      updateData.price_add = batchForm.price_add
      updateData.price_subtract = batchForm.price_subtract
    }

    // 只有选择了状态才添加到更新数据中
    if (batchForm.status !== undefined && batchForm.status !== null) {
      updateData.status = batchForm.status
    }

    // 只有选择了应用物流模板才添加到更新数据中
    if (batchForm.apply_shipment_template) {
      updateData.shipment_template = batchForm.shipment_template
    }

    // 只有选择了应用默认数量才添加到更新数据中
    if (batchForm.apply_quantity) {
      updateData.quantity = batchForm.quantity
    }

    // 只有选择了应用增值税率才添加到更新数据中
    if (batchForm.apply_vat_rate) {
      updateData.vat_rate = batchForm.vat_rate
    }

    // 只有选择了应用备货天数才添加到更新数据中
    if (batchForm.apply_preparing_day) {
      updateData.preparing_day = batchForm.preparing_day
    }

    // 只有选择了应用集成商名称才添加到更新数据中
    if (batchForm.apply_integrator_name) {
      updateData.integrator_name = batchForm.integrator_name
    }

    await batchUpdateStores(updateData)

    // 构建成功消息
    const appliedSettings = []
    if (batchForm.apply_price_rate) {
      appliedSettings.push(`价格设置为 ×${batchForm.price_rate} +${batchForm.price_add} -${batchForm.price_subtract}`)
    }
    if (batchForm.status !== undefined && batchForm.status !== null) {
      const statusText = batchForm.status === 1 ? '正常' : '禁用'
      appliedSettings.push(`状态为${statusText}`)
    }
    if (batchForm.apply_shipment_template) {
      appliedSettings.push(`物流模板为 ${batchForm.shipment_template}`)
    }
    if (batchForm.apply_quantity) {
      appliedSettings.push(`库存数量为 ${batchForm.quantity}`)
    }
    if (batchForm.apply_vat_rate) {
      appliedSettings.push(`增值税率为 ${batchForm.vat_rate}%`)
    }
    if (batchForm.apply_preparing_day) {
      appliedSettings.push(`备货天数为 ${batchForm.preparing_day}天`)
    }
    if (batchForm.apply_integrator_name) {
      appliedSettings.push(`集成商为 ${batchForm.integrator_name}`)
    }

    const message = `已成功设置 ${selectedStores.value.length} 个店铺的${appliedSettings.join('、')}。`

    ElNotification({
      title: '批量设置成功',
      message,
      type: 'success'
    })

    batchDialogVisible.value = false
    selectedStores.value = []
    loadStoreList()
  } catch (error) {
    console.error('批量设置失败:', error)
    ElMessage.error('批量设置失败')
  } finally {
    batchSubmitting.value = false
  }
}

// 测试店铺连接
const handleTestConnection = async (row: Store) => {
  try {
    // 检查是否为N11店铺类型
    if (row.account_type !== 2) {
      ElMessage.warning('只有N11店铺支持接口测试')
      return
    }

    // 检查AppKey是否存在
    if (!row.app_key) {
      ElMessage.warning('请先设置AppKey')
      return
    }

    testingStoreId.value = row.id

    const result = await testN11StoreConnection(row.app_key)

    if (result.success) {
      ElMessage.success(result.message)
    } else {
      ElMessage.error(result.message)
    }

  } catch (error) {
    console.error('测试店铺连接失败:', error)
    ElMessage.error('测试失败，请稍后重试')
  } finally {
    testingStoreId.value = null
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadStoreList()
})
</script>

<style scoped>
.store-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.price-adjustment {
  color: #e6a23c;
  font-weight: bold;
}

.secret-text {
  font-family: monospace;
  color: #909399;
}

.form-tip {
  margin-left: 8px;
  color: #909399;
}

.batch-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.batch-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-weight: bold;
}

.selected-stores {
  margin: 0;
  padding-left: 20px;
  max-height: 150px;
  overflow-y: auto;
}

.selected-stores li {
  color: #409eff;
  margin-bottom: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.credential-status {
  margin-left: 10px;
  font-size: 14px;
  font-weight: bold;
}

.credential-status.valid {
  color: #67c23a;
}

.credential-status.invalid {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

.store-type-item .el-select {
  width: 180px;
}

.status-item .el-select {
  width: 120px;
}

.batch-dialog .batch-form-container {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
}

/* 确保下拉选项不被容器裁剪 - 使用:deep()穿透样式作用域 */
:deep(.batch-dialog .el-select-dropdown) {
  z-index: 99999 !important;
}

:deep(.batch-dialog .el-select) {
  position: relative;
  z-index: 1;
}

/* 修复所有弹窗中的下拉框问题 - 使用:deep()穿透样式作用域 */
:deep(.el-dialog .el-select-dropdown) {
  z-index: 99999 !important;
}

:deep(.el-dialog .el-select) {
  position: relative;
  z-index: 1;
}

/* 全局修复ElementPlus下拉框z-index问题 - 使用:deep()穿透样式作用域 */
:deep(.el-popper.el-select__popper) {
  z-index: 99999 !important;
}

/* 修复ElementPlus下拉框在任何容器中的z-index问题 */
:deep(.el-select-dropdown) {
  z-index: 99999 !important;
}

/* 确保下拉框弹出层正确显示 */
:deep(.el-popper) {
  z-index: 99999 !important;
}

.batch-dialog .batch-form-container::-webkit-scrollbar {
  width: 6px;
}

.batch-dialog .batch-form-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.batch-dialog .batch-form-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.batch-dialog .batch-form-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.price-formula {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.formula-text {
  color: #606266;
  font-weight: bold;
  white-space: nowrap;
  font-size: 14px;
}

.formula-operator {
  color: #409eff;
  font-size: 18px;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
  background: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d1ecf1;
}

.price-input-underline {
  width: 120px;
}

.price-input-underline-small {
  width: 110px;
}

.price-input-underline :deep(.el-input__wrapper),
.price-input-underline-small :deep(.el-input__wrapper) {
  border: none;
  border-bottom: 2px solid #dcdfe6;
  border-radius: 0;
  box-shadow: none;
  padding: 8px 0;
  background: transparent;
  transition: border-color 0.3s;
}

.price-input-underline :deep(.el-input__wrapper:hover),
.price-input-underline-small :deep(.el-input__wrapper:hover) {
  border-bottom-color: #c0c4cc;
}

.price-input-underline :deep(.el-input__wrapper.is-focus),
.price-input-underline-small :deep(.el-input__wrapper.is-focus) {
  border-bottom-color: #409eff;
}

.price-input-underline :deep(.el-input__inner),
.price-input-underline-small :deep(.el-input__inner) {
  text-align: center;
  font-weight: 500;
  color: #303133;
}

.price-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.price-info-line,
.combined-info-line {
  line-height: 1.5; /* Adjust as needed for spacing */
  margin-bottom: 4px; /* Small margin between lines */
  font-size: 13px; /* Smaller font size for combined info */
  color: #606266;
}

.price-info-line:last-child,
.combined-info-line:last-child {
  margin-bottom: 0; /* Remove margin for the last line */
}
</style>
