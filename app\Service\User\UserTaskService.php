<?php
namespace App\Service\User;

use Carbon\Carbon;
use App\Utils\Jwt\Jwt;
use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Models\System\CatRelationSystemModel;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use App\Models\User\UserTaskModel;
use App\Models\User\UserAccountModel;
use Illuminate\Support\Facades\Cache;
use App\Models\User\UserTaskDetailModel;
use App\Models\User\UserTaskDetailUpParamsModel;
use App\Models\User\UserTaskDetailGoodsNameModel;
use App\Models\User\UserTaskCurrentcyModel;
use App\Utils\GoodsNameTrait;
use App\Utils\GoodsNameAiRewriter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserTaskService extends BaseService{
    use GoodsNameTrait;

    protected UserTaskModel $userTaskModel;
    protected UserAccountModel $userAccountModel;
    protected GoodsModel $goodsModel;
    protected UserTaskDetailModel $userTaskDetailModel;
    protected GoodsSkuModel $goodsSkuModel;
    protected GoodsService $goodsService;
    protected GoodsNameAiRewriter $goodsNameAiRewriter;
    protected UserTaskDetailGoodsNameModel $userTaskDetailGoodsNameModel;
    protected UserTaskDetailUpParamsModel $userTaskDetailUpParamsModel;
    protected UserTaskCurrentcyModel $userTaskCurrentcyModel;
    public function __construct(
        Jwt $jwtService,
        UserTaskModel $userTaskModel,
        UserAccountModel $userAccountModel,
        GoodsModel $goodsModel,
        UserTaskDetailModel $userTaskDetailModel,
        GoodsSkuModel $goodsSkuModel,
        GoodsService $goodsService,
        GoodsNameAiRewriter $goodsNameAiRewriter,
        UserTaskDetailGoodsNameModel $userTaskDetailGoodsNameModel,
        UserTaskDetailUpParamsModel $userTaskDetailUpParamsModel,
        UserTaskCurrentcyModel $userTaskCurrentcyModel
    )
    {
        $this->userTaskModel = $userTaskModel;
        $this->userAccountModel = $userAccountModel;
        $this->goodsModel = $goodsModel;
        $this->userTaskDetailModel = $userTaskDetailModel;
        $this->goodsSkuModel = $goodsSkuModel;
        $this->goodsService = $goodsService;
        $this->goodsNameAiRewriter = $goodsNameAiRewriter;
        $this->userTaskDetailGoodsNameModel = $userTaskDetailGoodsNameModel;
        $this->userTaskDetailUpParamsModel = $userTaskDetailUpParamsModel;
        $this->userTaskCurrentcyModel = $userTaskCurrentcyModel;
        parent::__construct($jwtService);
    }

    public function updateTask($user_id,$params){
        $task_id = $params['task_id'];
        $task_detail_id = $params['task_detail_id'] ?? 0;
        $task_over = $params['task_over'] ?? 0;
        if($task_detail_id > 0){
            $task_detail = $this->userTaskDetailModel->where('user_id',$user_id)->where('id',$task_detail_id)->first();
            if(!$task_detail){
                throw new MyException('任务详情不存在');
            }
            $third_task_id = $params['third_task_id']??0;
            $third_type = $params['third_type']??'';
            $third_status = $params['third_status']??'';
            $third_result = $params['third_result']??'';
            $task_num = $params['task_num'] ?? 0;
            
            $update = [];
            $update['third_type'] = $third_type;
            $update['third_status'] = $third_status;
            $update['third_result'] = $third_result;
            if($third_task_id > 0){
                $update['third_task_id'] = $third_task_id;
            }
            if($third_type == 'PRODUCT_CREATE' && $third_status == 'IN_QUEUE'){
                $update['memo'] = "商品已提交,等待验证结果";
                $update['status'] = 2;
            }
            if($third_type == 'PRODUCT_CREATE' && $third_status == 'FAIL'){
                $update['memo'] = "商品上传失败";
                $update['status'] = 3;
            }
            if($third_type == 'PRODUCT_CREATE' && $third_status == 'SUCCESS'){
                $update['memo'] = "商品上传成功";
                $update['status'] = 1;
            }   
            if(!isset($update['status'])){
                if($third_task_id > 0){
                    $update['memo'] = "商品已提交,等待验证结果";
                    $update['status'] = 2;
                }else{
                    $update['memo'] = "商品上传失败";
                    $update['status'] = 3;
                }
            }
            $this->userTaskDetailModel->where('id',$task_detail_id)->update($update);
        }

        

        // 统计审核未通过的数量 (status=6)
        $rejected_count = $this->userTaskDetailModel
            ->where('user_id', $user_id)
            ->where('task_id', $task_id)
            ->where('status', 6)
            ->count();

        if($task_over == 1){
            $task_count = $this->userTaskDetailModel->where('task_id',$task_id)->count();
            $updateData = [
                'task_over' => 1,
                'task_count' => $task_count,
                'task_num' => $task_count,
                'rejected_count' => $rejected_count,
                'latest_time' => now(),
                'updated_at' => now()
            ];
            if (isset($task_detail->user_goods_id)) {
                $updateData['latest_goods_id'] = $task_detail->user_goods_id;
            }
            $this->userTaskModel->where('id',$task_id)->update($updateData);
        }else{
            $updateData = [
                'task_num' => $task_num+1,
                'rejected_count' => $rejected_count,
                'latest_time' => now(),
                'updated_at' => now()
            ];
            if (isset($task_detail->user_goods_id)) {
                $updateData['latest_goods_id'] = $task_detail->user_goods_id;
            }
            $this->userTaskModel->where('id',$task_id)->update($updateData);
        }
        return [];
    }

    /**
     * 根据时间范围类型获取日期范围
     * @param string $time_range 时间范围类型
     * @param array $custom_date_range 自定义日期范围（当$time_range为'custom'时需要传入）
     * @param int $directory_id 目录ID（当$time_range为'all'时用于查询该目录下最早的商品创建时间）
     * @param int $user_id 用户ID（当$time_range为'all'时用于查询该用户的商品）
     * @return array 返回包含开始和结束日期的数组，格式为 ['day_start' => 'Y-m-d', 'day_end' => 'Y-m-d']
     */
    protected function getDateRange($time_range, $custom_date_range = [], $directory_id = 0, $user_id = 0) {
        $today = date('Y-m-d');
        
        switch($time_range) {
            case 'all':
                $day_start = '2025-05-30'; // 默认值
                
                // 如果提供了目录ID和用户ID，查询该目录下最早的商品创建时间
                if ($directory_id > 0 && $user_id > 0) {
                    $earliestGoods = $this->goodsModel
                        ->where('user_id', $user_id)
                        ->where('directory_id', $directory_id)
                        ->where('status', 1)
                        ->orderBy('id', 'asc')
                        ->first();
                    
                    if ($earliestGoods && $earliestGoods->created_at) {
                        $day_start = date('Y-m-d', strtotime($earliestGoods->created_at));
                    }
                }
                
                return [
                    'day_start' => $day_start,
                    'day_end' => $today
                ];
            case 'today':
                return [
                    'day_start' => $today,
                    'day_end' => $today
                ];
            case 'yesterday':
                $yesterday = date('Y-m-d', strtotime('-1 day'));
                return [
                    'day_start' => $yesterday,
                    'day_end' => $yesterday
                ];
            case 'last3days':
                return [
                    'day_start' => date('Y-m-d', strtotime('-2 days')),
                    'day_end' => $today
                ];
            case 'lastweek':
                return [
                    'day_start' => date('Y-m-d', strtotime('-6 days')),
                    'day_end' => $today
                ];
            case 'custom':
                $start = $custom_date_range[0];
                $end = $custom_date_range[1];
                
                $start = date('Y-m-d', strtotime($start));
                $end = date('Y-m-d', strtotime($end));
                
                if (strtotime($start) > strtotime($end)) {
                    return [
                        'day_start' => $end,
                        'day_end' => $start
                    ];
                }
                
                return [
                    'day_start' => $start,
                    'day_end' => $end
                ];
            default:
                throw new MyException('无效的时间范围类型');
        }
    }

    public function addTask($user_id,$params){
        // 生成请求唯一标识
        $requestHash = md5(json_encode([
            'user_id' => $user_id,
            'params' => $params,
            'timestamp' => $params['timestamp'] ?? time()
        ]));
        
        // 检查是否正在处理相同的请求
        $cacheKey = "addTask_processing_{$requestHash}";
        if (Cache::has($cacheKey)) {
            throw new MyException('请求正在处理中，请勿重复提交');
        }
        
        // 设置处理标记，5秒后自动过期
        Cache::put($cacheKey, true, 5);
        
        try {

            /* $user_task = $this->userTaskModel->where('user_id',$user_id)->where('task_over',0)->first();
            if($user_task){
                throw new MyException('您有未完成的任务,请先完成任务');
            } */

            
            $selectedStores = $params['selectedStores'] ?? [];
            if(empty($selectedStores)){
                throw new MyException('请选择要发布的店铺');
            }
            
            // 对selectedStores进行去重处理
            $selectedStores = array_values(array_unique(array_map('intval', $selectedStores)));
            
            $store_list_cn = $this->userAccountModel->where('user_id',$user_id)->where('status',1)->whereIn('id',$selectedStores)->count();
            if($store_list_cn != count($selectedStores)){
                throw new MyException('您选择发布的店铺不符合要求,请重新选择');
            }
            $execute_time = $params['execute_time'] ?? "manual";//manual immediate
            if(!in_array($execute_time,['manual','immediate'])){
                throw new MyException('执行时间不符合要求');
            }

            $publish_currency = $params['publish_currency'] ?? "TL";
            $publish_currency = strtoupper($publish_currency);

            $exchange_rate_currency = $params['exchange_rate_currency'] ?? "TL";
            $exchange_rate_currency = strtoupper($exchange_rate_currency);

            $exchange_rates = $params['exchange_rates'] ?? [];
            if(is_array($exchange_rates) && count($exchange_rates) > 0){
                //遍历去掉key是publish_currency的元素
                foreach($exchange_rates as $key => $value){
                    if(strtoupper($key) == strtoupper($exchange_rate_currency)){
                        unset($exchange_rates[$key]);
                    }
                }
            }

            $is_selected = 0;
            $selected_count = 0;
            $selected_ids = $params['selected_ids'] ?? [];
            if(is_array($selected_ids) && count($selected_ids) > 0){
                $selected_ids = array_map('intval', $selected_ids);
                $selected_ids = array_values(array_filter(array_unique($selected_ids)));
                $goods_list_cn = $this->goodsModel->where('user_id',$user_id)->where('status',1)->whereIn('id',$selected_ids)->count();
                if($goods_list_cn != count($selected_ids)){
                    throw new MyException('您选择发布的商品不符合要求,请重新选择');
                }
                $is_selected = 1;
                $params['timeRange'] = "today";
                $selected_count = count($selected_ids);
                if($selected_count > 100){
                    throw new MyException('您选择发布的商品数量超过100条,请重新选择');
                }
                $selected_ids = json_encode($selected_ids,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
            }else{
                $selected_ids = "";
            }


            $sort_order = $params['sort_order'] ?? "desc";//asc desc
            if(!in_array($sort_order,['asc','desc'])){
                throw new MyException('排序不符合要求');
            }
            $time_range = $params['timeRange'] ?? "";
            if(!in_array($time_range,['all','today','yesterday','last3days','lastweek','custom'])){
                throw new MyException('时间范围不符合要求');
            }
            $custom_date_range = $params['customDateRange'] ?? [];
            if($time_range == 'custom' && (empty($custom_date_range) || count($custom_date_range) != 2)){
                throw new MyException('自定义时间范围不符合要求');
            }

            // 获取目录ID
            $directory_id = $params['directory_id'] ?? 0;

            // 获取价格区间参数
            $price_min = $params['price_min'] ?? null;
            $price_max = $params['price_max'] ?? null;

            // 验证价格区间参数
            if ($price_min !== null && $price_min < 0) {
                throw new MyException('最低价格不能小于0');
            }
            if ($price_max !== null && $price_max < 0) {
                throw new MyException('最高价格不能小于0');
            }
            if ($price_min !== null && $price_max !== null && $price_min > 0 && $price_max > 0 && $price_min > $price_max) {
                throw new MyException('最低价格不能大于最高价格');
            }

            $date_range = $this->getDateRange($time_range, $custom_date_range, $directory_id, $user_id);
            
            // 获取发布设置类型和相关参数
            $account_setting_type = $params['account_setting_type'] ?? 1;
            $price_rate = $params['price_rate'] ?? 1.00;
            $price_add = $params['price_add'] ?? 0.00;
            $price_subtract = $params['price_subtract'] ?? 0.00;
            $quantity = $params['quantity'] ?? 1000;
            $vat_rate = $params['vat_rate'] ?? 10;
            $preparing_day = $params['preparing_day'] ?? 3;
            
            // 验证发布设置类型
            if (!in_array($account_setting_type, [1, 2])) {
                throw new MyException('发布设置类型不符合要求');
            }
            
            // 如果是自定义设置，验证参数
            if ($account_setting_type == 2) {
                if ($price_rate < 0 || $price_rate > 999.99) {
                    throw new MyException('价格倍数不符合要求');
                }
                if ($price_add < 0 || $price_add > 999999.99) {
                    throw new MyException('价格加值不符合要求');
                }
                if ($price_subtract < 0 || $price_subtract > 999999.99) {
                    throw new MyException('价格减值不符合要求');
                }
                if ($quantity < 1 || $quantity > 9999) {
                    throw new MyException('库存数量不符合要求');
                }
                if (!in_array($vat_rate, [0, 1, 10, 20])) {
                    throw new MyException('增值税率不符合要求');
                }
                if ($preparing_day < 1 || $preparing_day > 365) {
                    throw new MyException('备货天数不符合要求');
                }
            }

            // 计算商品和SKU统计信息
            $goodsCount = 0;
            $skuCount = 0;
            
            if ($is_selected == 1) {
                // 选中商品模式：在现有的whereIn基础上增加价格区间关联查询
                $goodsQuery = $this->goodsModel->where('user_id', $user_id)
                    ->where('status', 1)
                    ->whereIn('id', json_decode($selected_ids, true));

                // 如果有价格区间筛选，添加关联查询（只有大于0的价格才进行筛选）
                if (($price_min !== null && $price_min > 0) || ($price_max !== null && $price_max > 0)) {
                    $goodsQuery->whereHas('skus', function ($skuQuery) use ($price_min, $price_max) {
                        if ($price_min !== null && $price_min > 0) {
                            $skuQuery->where('price', '>=', $price_min);
                        }
                        if ($price_max !== null && $price_max > 0) {
                            $skuQuery->where('price', '<=', $price_max);
                        }
                    });
                }

                $goodsCount = $goodsQuery->count();

                // 统计SKU数量：如果有价格区间筛选，需要单独统计符合价格条件的SKU数量
                if (($price_min !== null && $price_min > 0) || ($price_max !== null && $price_max > 0)) {
                    $goodsIds = $goodsQuery->pluck('id')->toArray();
                    $skuQuery = $this->goodsSkuModel->whereIn('user_goods_id', $goodsIds);

                    if ($price_min !== null && $price_min > 0) {
                        $skuQuery->where('price', '>=', $price_min);
                    }
                    if ($price_max !== null && $price_max > 0) {
                        $skuQuery->where('price', '<=', $price_max);
                    }
                    $skuCount = $skuQuery->count();
                } else {
                    $skuCount = $goodsQuery->sum('goods_sku_num');
                }
            } else {
                // 时间范围模式：在statisticsParams中增加价格区间参数传递
                $statisticsParams = [
                    'directory_id' => $directory_id,
                    'time_range' => $time_range,
                    'day_start' => $date_range['day_start'],
                    'day_end' => $date_range['day_end']
                ];

                // 添加价格区间参数（只有大于0的价格才传递）
                if ($price_min !== null && $price_min > 0) {
                    $statisticsParams['price_min'] = $price_min;
                }
                if ($price_max !== null && $price_max > 0) {
                    $statisticsParams['price_max'] = $price_max;
                }

                $statistics = $this->goodsService->getGoodsStatistics($user_id, $statisticsParams);
                $goodsCount = $statistics['goods_count'];
                $skuCount = $statistics['sku_count'];
            }

            if($goodsCount <= 0){
                throw new MyException('您选择发布的商品数量为0,请重新选择');
            }
            
            // 准备汇率数据收集数组
            $exchangeRateData = [];
            // 逐个插入任务数据以获取任务ID
            foreach($selectedStores as $store_id) {
                $taskData = [
                    'is_selected' => $is_selected,
                    'selected_ids' => $selected_ids,
                    'user_id' => $user_id,
                    'time_range' => $time_range,
                    'day_start' => $date_range['day_start'],
                    'day_end' => $date_range['day_end'],
                    'sort_order' => $sort_order,
                    'execute_type' => $execute_time,
                    'directory_id' => $directory_id,
                    'user_account_id' => $store_id,
                    'account_setting_type' => $account_setting_type,
                    'currentcy' => $publish_currency,
                    'exchange_rate_currency' => $exchange_rate_currency,
                    'price_rate' => $price_rate,
                    'price_add' => $price_add,
                    'price_subtract' => $price_subtract,
                    'quantity' => $quantity,
                    'vat_rate' => $vat_rate,
                    'preparing_day' => $preparing_day,
                    'task_count' => $skuCount,
                    'task_num' => 0,   // 初始完成数为0
                    'task_over' => 0,  // 未完成状态
                    'goods_count' => $goodsCount,
                    'sku_count' => $skuCount,
                    'price_min' => $price_min ?? 0.00,
                    'price_max' => $price_max ?? 0.00,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
                
                // 插入任务并获取ID
                $taskId = $this->userTaskModel->insertGetId($taskData);
                
                // 如果有汇率数据，收集起来
                if(is_array($exchange_rates) && count($exchange_rates) > 0) {
                    foreach($exchange_rates as $currency_from => $rate) {
                        $exchangeRateData[] = [
                            'user_id' => $user_id,
                            'task_id' => $taskId,
                            'currentcy_from' => $currency_from,
                            'currentcy_to' => $exchange_rate_currency,
                            'rate' => $rate,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }
                }
            }
            
            // 批量插入汇率数据
            if (!empty($exchangeRateData)) {
                $this->userTaskCurrentcyModel->insert($exchangeRateData);
            }
            
            // 清除处理标记
            Cache::forget($cacheKey);
            
            return true;
        } catch (\Exception $e) {
            // 出错时也要清除缓存标记
            Cache::forget($cacheKey);
            throw new MyException('任务添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取任务列表（分页）
     * @param int $userId 用户ID
     * @param array $params 查询参数
     * @return array
     */
    public function getTaskList(int $userId, array $params): array
    {
        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));
        
        // 构建查询
        $query = $this->userTaskModel->where('user_id', $userId)
            ->with(['store','directory']); // 预加载店铺信息和目录信息

        // 按任务完成状态筛选
        if (isset($params['task_over'])) {
            $query->where('task_over', (int)$params['task_over']);
        }

        // 按创建时间范围筛选
        if (!empty($params['day_start'])) {
            $query->whereDate('created_at', '>=', $params['day_start']);
        }
        if (!empty($params['day_end'])) {
            $query->whereDate('created_at', '<=', $params['day_end']);
        }

        // 按审核未通过状态筛选
        if (!empty($params['has_rejected']) && $params['has_rejected'] === true) {
            $query->where('rejected_count', '>', 0);
        }

        // 按创建时间倒序排序
        $query->orderBy('id', 'desc');

        // 分页查询
        $total = $query->count();
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;
        
        $tasks = $query->offset($offset)
            ->limit($pageSize)
            ->get()
            ->map(function($task) {
                return $this->formatTaskData($task);
            });

        return [
            'list' => $tasks,
            'pagination' => [
                'current' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasNext' => $page < $totalPages,
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 格式化任务数据（列表用）
     * @param UserTaskModel $task 任务模型
     * @return array 格式化后的任务数据
     */
    private function formatTaskData($task): array
    {
        return [
            'id' => $task->id,
            'user_id' => $task->user_id,
            'is_selected' => $task->is_selected,
            'selected_ids' => $task->selected_ids ? json_decode($task->selected_ids, true) : [],
            'time_range' => $task->time_range,
            'day_start' => $task->day_start,
            'day_end' => $task->day_end,
            'sort_order' => $task->sort_order,
            'execute_type' => $task->execute_type,
            'directory_id' => $task->directory_id,
            'user_account_id' => $task->user_account_id,
            'account_setting_type' => $task->account_setting_type,
            'price_rate' => $task->price_rate,
            'price_add' => $task->price_add,
            'price_subtract' => $task->price_subtract,
            'price_min' => $task->price_min,
            'price_max' => $task->price_max,
            'currentcy' => $task->currentcy,
            'exchange_rate_currency' => $task->exchange_rate_currency,
            'quantity' => $task->quantity,
            'vat_rate' => $task->vat_rate,
            'preparing_day' => $task->preparing_day,
            'store_name' => $task->store ? $task->store->account_name : '',
            'store_logo' => $task->store ? $task->store->account_logo : '',
            'directory_name' => $task->directory ? $task->directory->name : '',
            'task_count' => $task->task_count,
            'task_num' => $task->task_num,
            'latest_goods_id' => $task->latest_goods_id,
            'latest_time' => $task->latest_time,
            'task_over' => $task->task_over,
            'goods_count' => $task->goods_count,
            'sku_count' => $task->sku_count,
            'rejected_count' => $task->rejected_count,
            'goods_ai_name_status' => $task->goods_ai_name_status,
            'latest_ai_goods_id' => $task->latest_ai_goods_id,
            'memo' => $task->memo,
            'created_at' => $task->created_at,
            'updated_at' => $task->updated_at,
        ];
    }

    /**
     * 开启任务
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @return array 返回第一条任务详情记录
     * @throws MyException
     */
    public function startTask(int $userId, int $taskId): array
    {
        // 设置更长的执行时间限制，避免AI请求导致超时
        set_time_limit(120); // 设置为2分钟
        
        $task = $this->userTaskModel->where('id', $taskId)
            ->where('user_id', $userId)
            ->first();
            
        if (!$task) {
            throw new MyException('任务不存在');
        }

        if($task->goods_ai_name_status != 1){
            throw new MyException('AI改写标题任务未完成');
        }

        // 检查是否有其他任务正在执行或执行完成未超过1分钟
        $oneMinuteAgo = now()->subMinute();
        $executingTask = $this->userTaskModel
            ->where('user_id', $userId)
            ->where('id', '!=', $taskId)
            ->where(function ($query) use ($oneMinuteAgo) {
                $query->where('latest_time', '>', $oneMinuteAgo);
                $query->where('task_over', 0);
            })
            ->first();

        if ($executingTask) {
            $remainingSeconds = 0;
            if ($executingTask->latest_time) {
                $finishTime = Carbon::parse($executingTask->latest_time)->addMinute();
                $remainingSeconds = now()->diffInSeconds($finishTime, false);
            }
            
            if ($remainingSeconds > 0) {
                throw new MyException('其它任务正在执行，请' . $remainingSeconds . '秒后再试');
            } else {
                throw new MyException('其它任务执行完成没有超过1分钟，请1分钟后再试');
            }
        }
        // 2. 检查任务是否已完成
        if ($task->task_over == 1) {
            throw new MyException('任务已完成，无需重复开启');
        }

        // 3. 检查是否已有待处理的任务详情记录
        $existingDetails = $this->userTaskDetailModel
            ->where('user_id', $userId)
            ->where('task_id', $taskId)
            ->where('status', 0)
            ->exists();     

        // 4. 如果没有待处理记录，表示任务已完成
        if (!$existingDetails) {
            $task_count = $this->userTaskDetailModel->where('user_id', $userId)->where('task_id', $taskId)->count();
            $this->userTaskModel->where('id', $taskId)->update([
                                    'task_over' => 1,
                                    'task_count' => $task_count,
                                    'task_num' => $task_count,
                                    'updated_at' => now()
                                ]);
            return ['task_over' => 1,'task_exist' => 0,'goods_no_cat_relation' => 0,'task_count' => $task_count,'task_num' => $task_count];
        }

        // 5. 获取第一条任务详情记录并返回 记录肯定存在
        $firstDetail = $this->userTaskDetailModel
            ->where('user_id', $userId)
            ->where('task_id', $taskId)
            ->where('status', 0)
            ->with(['goods','goodsSku', 'store'])
            ->orderBy('id', 'asc')
            ->first();

        //任务进行中
        $task_count = $task->sku_count;       
        if($task->sku_count <= 0){
            $task_count = $this->userTaskDetailModel->where('task_id', $taskId)->count();
        }
        //task_num 表示当前进行第几个任务 从0开始
        $task_num = $this->userTaskDetailModel->where('task_id', $taskId)->where('status','<>', 0)->count();
        $formattedData = $this->formatTaskDetailData($firstDetail);
        $formattedData['task_count'] = $task_count;
        $formattedData['task_num'] = $task_num;
        $formattedData['task_id'] = $taskId;
        $formattedData['task_detail_id'] = $firstDetail->id;
        $this->userTaskModel->where('id', $taskId)->update(['latest_time' => now(),'updated_at' => now()]); // 任务完成后，更新latest_time为当前时间
        return $formattedData;
    }

    

    /**
     * 生成带前缀的随机字符串
     * 
     * @param string $prefix 字符串前缀
     * @param int $length 随机部分的长度，默认为10
     * @param bool $digits_only 是否仅生成数字，默认为false
     * @return string 前缀+随机字符串
     */
    private function generatePrefixedRandomString(string $prefix = '', int $length = 10, bool $digits_only = false): string
    {
        $randomPart = '';
        
        if ($digits_only) {
            // 仅生成数字，确保不以0开头
            for ($i = 0; $i < $length; $i++) {
                if ($i === 0) {
                    // 第一位从1-9中选择，避免以0开头
                    $characters = '123456789';
                } else {
                    // 其余位从0-9中选择
                    $characters = '0123456789';
                }
                $randomPart .= $characters[rand(0, strlen($characters) - 1)];
            }
        } else {
            // 生成随机字符串，包含字母和数字
            $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            for ($i = 0; $i < $length; $i++) {
                $randomPart .= $characters[rand(0, strlen($characters) - 1)];
            }
        }
        
        return $prefix . $randomPart;
    }
    
    /**
     * 该方法已经不再使用 文件 UserTaskNewService.php中 generateTaskDetails 方法替代了该方法的实现
     * 生成任务详情记录  
     * @param UserTaskModel $task 任务模型
     * @throws MyException
     */
    private function generateTaskDetails(UserTaskModel $task): array
    {
        $goodsQuery = $this->goodsModel
            ->where('user_id',$task->user_id)
            ->where('directory_id',$task->directory_id);

        // 根据is_selected判断查询条件
        if ($task->is_selected == 1) {
            // 指定商品ID发布
            $selectedIds = json_decode($task->selected_ids, true);
            if (empty($selectedIds)) {
                throw new MyException('指定商品ID为空');
            }
            $user_goods_id_arr = $this->userTaskDetailModel::where('task_id', $task->id)->pluck('user_goods_id')->toArray();
            $selectedIds = array_diff($selectedIds, $user_goods_id_arr);
            if (empty($selectedIds)) {
                return ['task_over' => 1];
            }
            $goodsQuery->whereIn('id', $selectedIds);
        } else {
            // 按时间范围查询
            $goodsQuery->whereDate('created_at', '>=', $task->day_start)
                ->whereDate('created_at', '<=', $task->day_end);

            if ($task->latest_goods_id > 0) {
                if ($task->sort_order == 'desc') {
                    $goodsQuery->where('id', '<', $task->latest_goods_id);
                } else {
                    $goodsQuery->where('id', '>', $task->latest_goods_id);
                }
            }
        }
        $goodsQuery->where('status', 1);
        // 按排序规则排序并限制500条
        $goodsQuery->orderBy('id', $task->sort_order)
            ->limit(500);

        $goodsList = $goodsQuery->get();

        if ($goodsList->isEmpty()) {
            $this->userTaskModel->where('id', $task->id)->update(['task_over' => 1]);
            return ['task_over' => 1];
        }

        // 准备批量插入的数据
        $insertData = [];
        $taskCount = 0;
        $store_info = UserAccountModel::where('user_id', $task->user_id)->where('id', $task->user_account_id)->first();
        if(!$store_info){
            throw new MyException('店铺信息不存在');
        }

        // 根据account_setting_type决定使用哪个价格配置，并存储到局部变量
        if ($task->account_setting_type == 1) {
            // 使用店铺配置
            $current_price_rate = $store_info->price_rate;
            $current_price_add = $store_info->price_add;
            $current_price_subtract = $store_info->price_subtract;
            $quantity = $store_info->quantity;
            $vat_rate = $store_info->vat_rate;
            $preparing_day = $store_info->preparing_day;

        } else {
            // 使用任务配置
            $current_price_rate = $task->price_rate;
            $current_price_add = $task->price_add;
            $current_price_subtract = $task->price_subtract;
            $quantity = $task->quantity;
            $vat_rate = $task->vat_rate;
            $preparing_day = $task->preparing_day;
        }

        foreach ($goodsList as $goods) {
            // 获取商品的所有SKU
            $skuList = $this->goodsSkuModel->where('user_goods_id', $goods->id)->get();
            if ($skuList->isEmpty()) {
                continue;
            } else {
                //同一个商品使用上传到相同的分类ID
                $category_id = 0;
                if ($goods->cat_id > 0) {
                    $cat_third_ids = CatRelationSystemModel::query()->where('platform_id', 1)
                        ->where('third_platform_id', 2)
                        ->where('cat_platform_id', $goods->cat_id)
                        ->value('cat_third_ids');
                        
                    if (!empty($cat_third_ids)) {
                        $cat_third_ids = json_decode($cat_third_ids, true);
                        $cat_third_ids = array_values(array_filter(array_unique($cat_third_ids)));
                        if (count($cat_third_ids) > 1) {
                            $category_id = $cat_third_ids[array_rand($cat_third_ids, 1)];
                        } else {
                            $category_id = $cat_third_ids[0];
                        }
                        if (!$category_id) {
                            $category_id = 0;
                        }
                    }
                }
                if ($category_id <= 0) {
                    continue;
                }
                // 为每个SKU创建一条记录
                foreach ($skuList as $sku) {
                    // 生成带前缀的随机ID
                    $product_main_id = $this->generatePrefixedRandomString('', 15, true); // 仅生成数字
                    $stock_code = $this->generatePrefixedRandomString('sc_', 15, false); // 默认字母数字混合
                    

                    $goods_name = $goods->goods_name;
                    //不再处理标题 后续已经改成使用deepseek改写标题
                    /* if(!empty($sku->spec_values)){
                        $goods_name = '【'.str_replace(',', ' ', $sku->spec_values) . '】 ' . $goods_name;
                    }
                    
                    // 验证并截取商品名称长度，确保不超过255个字符
                    $goods_name = $this->truncateGoodsName($goods_name, 255); */
                    // 计算第三方平台价格：原价 * 倍数 + 加值 - 减值
                    $price_third_calculated = round(($sku->price * $current_price_rate) + $current_price_add - $current_price_subtract, 2);
                    $price_list_third_calculated = round($price_third_calculated * (1 + mt_rand(10, 15) / 100), 2);
                    $insertData[] = [
                        'user_id' => $task->user_id,
                        'task_id' => $task->id,
                        'directory_id' => $task->directory_id,
                        'user_account_id' => $task->user_account_id,
                        'user_goods_id' => $goods->id,
                        'user_goods_sku_id' => $sku->id,
                        'goods_id' => $sku->goods_id,
                        'goods_name' => $goods_name,
                        'thumb_url' => uploadFilePath($sku->thumb_url),
                        'currentcy' => $sku->currentcy,
                        'price' => $sku->price,
                        'price_third' => $price_third_calculated,
                        'price_list_third' => $price_list_third_calculated,
                        'quantity' => $quantity,
                        'vat_rate' => $vat_rate,
                        'preparing_day' => $preparing_day,
                        'spec_key_values' => $sku->spec_key_values,
                        'spec_values' => $sku->spec_values,
                        'is_skc_gallery' => $sku->is_skc_gallery,
                        'category_id' => $category_id,
                        'product_main_id' => $product_main_id,
                        'stock_code' => $stock_code,
                        'status' => 0,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                    $taskCount++;
                }
            }
        }

        // 分批插入任务详情记录
        if (!empty($insertData)) {
            $this->batchInsertTaskDetails($insertData);
        }
        if($taskCount > 0){
            return ['task_over' => 0];
        }else{
            return ['task_over' => 1];
        }
    }

    /**
     * 格式化任务详情数据
     * @param UserTaskDetailModel $detail 任务详情模型
     * @return array 格式化后的数据
     */
    public function formatTaskDetailData($detail): array
    {
        
        if($detail->goodsSku->is_skc_gallery == 1){
            $skc_gallery = $detail->goodsSku->skc_gallery;
        }else{
            $skc_gallery = $detail->goods->goods_pic;
        }
        
        // 安全地处理JSON解码和数组去重
        $decodedGallery = json_decode($skc_gallery, true);
        if (!is_array($decodedGallery)) {
            $decodedGallery = [];
        }
        
        // 确保所有元素都是字符串，过滤掉非字符串元素
        $filteredGallery = array_filter($decodedGallery, function($item) {
            return is_string($item) && !empty(trim($item));
        });
        
        // 现在可以安全地使用array_unique
        $skc_gallery = array_values(array_unique($filteredGallery));
        
        if($detail->goodsSku->thumb_url){
            $skc_gallery = array_merge([$detail->goodsSku->thumb_url], $skc_gallery);
        }

        $ordered_skc_gallery = [];
        $order = 0;
        foreach ($skc_gallery as $item) {
            $ordered_skc_gallery[] = [
                'url' => uploadFilePath($item),
                'order' => $order++,
            ];
        }
        $skc_gallery = $ordered_skc_gallery;

        $goods_name = $detail->goods_name_ai;
        if(empty($goods_name)){
            throw new MyException('商品名称不存在');
        }
        $goods_property = $detail->goods_property_ai;
        if(empty($goods_property)){
            $goods_property = $detail->goods_property;
        }
        
        return [
            'task_over' => 0,
            'task_exist' => 0,
            'goods_no_cat_relation' => 0,
            'id' => $detail->id,
            'user_id' => $detail->user_id,
            'task_id' => $detail->task_id,
            'user_account_id' => $detail->user_account_id,
            'user_goods_id' => $detail->user_goods_id,
            'user_goods_sku_id' => $detail->user_goods_sku_id,
            'goods_id' => $detail->goods_id,
            'goods_name' => $goods_name,
            'thumb_url' => $detail->goodsSku->thumb_url ? uploadFilePath($detail->goodsSku->thumb_url) : uploadFilePath($detail->thumb_url),
            'images' => $skc_gallery,
            'currentcy' => $detail->currentcy,
            'currentcy_goods' => $detail->currentcy_goods,//商品原价货币单位
            'price' => $detail->price,
            'price_third' => $detail->price_third,
            'price_list_third' => $detail->price_list_third,
            'quantity' => $detail->quantity,
            'vat_rate' => $detail->vat_rate,
            'preparing_day' => $detail->preparing_day,
            'spec_key_values' => $detail->spec_key_values,
            'spec_values' => $detail->spec_values,
            'is_skc_gallery' => $detail->is_skc_gallery,
            'category_id' => $detail->category_id,
            'product_main_id' => $detail->product_main_id,
            'stock_code' => $detail->stock_code,
            'status' => $detail->status,
            'goods_info' => $detail->goods ? [
                'id' => $detail->goods->id,
                'goods_name' => $goods_name,
                'goods_property' => $goods_property,
                'cat_id' => $detail->goods->cat_id,
            ] : null,
            'sku_info' => $detail->goodsSku ? [
                'id' => $detail->goodsSku->id,
                'sku_id' => $detail->goodsSku->sku_id,
                'url' => $detail->goodsSku->url,
                'skc_gallery' => $detail->goodsSku->skc_gallery,
            ] : null,
            'store_info' => $detail->store ? [
                'id' => $detail->store->id,
                'account_name' => $detail->store->account_name,
                'account_type' => $detail->store->account_type,
                'brand' => $detail->store->brand,
                'price_rate' => $detail->store->price_rate,
                'shipment_template' => $detail->store->shipment_template,
                'quantity' => $detail->quantity,//取详情配置 前端不做调整
                'vat_rate' => $detail->vat_rate,//取详情配置 前端不做调整
                'preparing_day' => $detail->preparing_day,//取详情配置 前端不做调整
                'integrator_name' => $detail->store->integrator_name,
                'app_key' => $detail->store->app_key,
                'app_secret' => $detail->store->app_secret,
            ] : null,
            'created_at' => $detail->created_at,
            'updated_at' => $detail->updated_at,
        ];
    }

    /**
     * 分批插入任务详情数据
     * @param array $batchInsertData 要插入的数据数组
     * @param int $batchSize 每批次插入的数量，默认1000条
     * @return int 成功插入的记录数
     * @throws \Exception
     */
    private function batchInsertTaskDetails(array $batchInsertData, ?int $batchSize = null): int
    {
        if (empty($batchInsertData)) {
            return 0;
        }

        // 如果没有指定批次大小，从配置中获取
        if ($batchSize === null) {
            $batchSize = config('jk.batch_insert.task_details_batch_size', 1000);
        }

        $totalCount = count($batchInsertData);
        $chunks = array_chunk($batchInsertData, $batchSize);
        $processedCount = 0;
        $failedBatches = [];

        foreach ($chunks as $index => $chunk) {
            try {
                $this->userTaskDetailModel->insertOrIgnore($chunk);
                $processedCount += count($chunk);
            } catch (\Exception $e) {
                $failedBatches[] = [
                    'batch_index' => $index + 1,
                    'batch_size' => count($chunk),
                    'error' => $e->getMessage()
                ];
                // 如果是占位符过多的错误，尝试更小的批次
                if (strpos($e->getMessage(), 'too many placeholders') !== false) {
                    $smallerBatchSize = max(100, intval($batchSize / 2));
                    $smallerChunks = array_chunk($chunk, $smallerBatchSize);

                    foreach ($smallerChunks as $smallChunk) {
                        try {
                            $this->userTaskDetailModel->insertOrIgnore($smallChunk);
                            $processedCount += count($smallChunk);
                        } catch (\Exception $retryException) {
                            \Log::error("小批次插入仍然失败：" . $retryException->getMessage());
                        }
                    }
                } else {
                }
            }
        }

        if (!empty($failedBatches)) {
            \Log::warning("批量插入任务详情部分失败：" . count($failedBatches) . " 个批次失败", [
                'failed_batches' => $failedBatches,
                'success_count' => $processedCount,
                'total_count' => $totalCount
            ]);
        }
        return $processedCount;
    }

}