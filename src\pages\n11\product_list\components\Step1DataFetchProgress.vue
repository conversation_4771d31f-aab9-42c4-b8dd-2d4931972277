<template>
  <div class="step1-progress">
    <!-- 页面进度详情 -->
    <div class="page-progress-section">
      <div class="section-title">
        <i class="el-icon-document"></i>
        数据获取进度
      </div>
      
      <div class="page-progress-content">
        <!-- 页面进度大数字显示 -->
        <div class="page-numbers">
          <div class="current-page">
            <span class="number">{{ progress.currentPage }}</span>
            <span class="label">当前页</span>
          </div>
          <div class="divider">/</div>
          <div class="total-pages">
            <span class="number">{{ progress.totalPages }}</span>
            <span class="label">总页数</span>
          </div>
        </div>
        
        <!-- 详细统计 -->
        <div class="page-details">
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">剩余页数:</span>
              <span class="value">{{ remainingPages }}</span>
            </div>
            <div class="detail-item">
              <span class="label">每页数量:</span>
              <span class="value">100条</span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">已获取商品:</span>
              <span class="value">{{ getActualFetchedProducts() }}</span>
            </div>
            <div class="detail-item">
              <span class="label">商品总数:</span>
              <span class="value">{{ progress.totalProducts || (progress.totalPages * 100) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据状态指示 -->
    <div class="network-status-section">
      <div class="section-title">
        <i class="el-icon-connection"></i>
        数据状态
      </div>
      
      <div class="network-status-content">
        <div class="status-indicator">
          <div class="status-icon" :class="getNetworkStatusClass()">
            <i :class="getNetworkStatusIcon()"></i>
          </div>
          <div class="status-text">
            <div class="status-main">{{ getNetworkStatusText() }}</div>
          </div>
        </div>
        
        <!-- 批次进度 -->
        <div class="batch-progress" v-if="progress.currentBatch > 0">
          <div class="batch-info">
            <span>当前批次: {{ progress.currentBatch }}</span>
            <span>批次大小: {{ getBatchSize() }}页</span>
          </div>
          <el-progress
            :percentage="getBatchProgress()"
            :stroke-width="6"
            :show-text="false"
            class="batch-progress-bar"
          />
        </div>
      </div>
    </div>

    <!-- 实时日志 -->
    <div class="log-section">
      <div class="section-title">
        <i class="el-icon-document-copy"></i>
        实时日志
      </div>
      
      <div class="log-content">
        <div class="log-item" v-for="(log, index) in recentLogs" :key="index" :class="log.type">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'

// Props接口
export interface FetchProgress {
  mode: 'rpc' | 'api' | 'fallback'
  currentPage: number
  totalPages: number
  currentBatch: number
  totalProducts: number
  networkStatus: 'idle' | 'requesting' | 'success' | 'error'
  speed: number
  estimatedTime: number
}

interface LogItem {
  time: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
}

// Props定义
const props = defineProps<{
  progress: FetchProgress
}>()

// 响应式数据
const recentLogs = ref<LogItem[]>([])
const hasLoggedTotalPages = ref(false) // 标记是否已记录总页数日志

// 计算属性
const remainingPages = computed(() => {
  return Math.max(0, props.progress.totalPages - props.progress.currentPage)
})

// 方法
const getNetworkStatusClass = () => {
  switch (props.progress.networkStatus) {
    case 'requesting': return 'status-requesting'
    case 'success': return 'status-success'
    case 'error': return 'status-error'
    default: return 'status-idle'
  }
}

const getNetworkStatusIcon = () => {
  switch (props.progress.networkStatus) {
    case 'requesting': return 'el-icon-loading'
    case 'success': return 'el-icon-success'
    case 'error': return 'el-icon-error'
    default: return 'el-icon-time'
  }
}

const getNetworkStatusText = () => {
  switch (props.progress.networkStatus) {
    case 'requesting': return '正在请求数据...'
    case 'success': return '数据获取成功'
    case 'error': return '网络请求失败'
    default: return '等待开始'
  }
}

const getBatchProgress = () => {
  if (props.progress.totalPages === 0) return 0
  
  const batchSize = getBatchSize()
  
  // 如果当前页数等于总页数，进度条应该是100%
  if (props.progress.currentPage >= props.progress.totalPages) {
    return 100
  }
  
  // 计算当前批次内的进度
  const currentBatchNumber = Math.ceil(props.progress.currentPage / batchSize)
  const currentBatchStartPage = (currentBatchNumber - 1) * batchSize + 1
  const currentBatchEndPage = Math.min(currentBatchNumber * batchSize, props.progress.totalPages)
  const pagesInCurrentBatch = props.progress.currentPage - currentBatchStartPage + 1
  const totalPagesInCurrentBatch = currentBatchEndPage - currentBatchStartPage + 1
  
  return Math.round((pagesInCurrentBatch / totalPagesInCurrentBatch) * 100)
}

const getBatchSize = () => {
  // 如果总页数小于默认批次大小5，则使用总页数作为批次大小
  return Math.min(5, props.progress.totalPages || 5)
}

const getActualFetchedProducts = () => {
  // 如果有实际的商品总数，则根据当前页数和总页数计算实际获取的商品数
  if (props.progress.totalProducts > 0 && props.progress.totalPages > 0) {
    // 计算实际获取的商品数，不能超过总商品数
    const estimatedFetched = Math.min(
      props.progress.currentPage * 100,
      props.progress.totalProducts
    )
    return estimatedFetched
  }
  
  // 如果没有总商品数，则使用页数乘以每页数量
  return props.progress.currentPage * 100
}

const addLog = (message: string, type: LogItem['type'] = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  recentLogs.value.unshift({
    time,
    message,
    type
  })
  
  // 只保留最近10条日志
  if (recentLogs.value.length > 10) {
    recentLogs.value = recentLogs.value.slice(0, 10)
  }
}

// 监听总页数变化 - 优先处理
watch(() => props.progress.totalPages, (newTotal, oldTotal) => {
  console.log('[UI组件] 接收到总页数变化:', { newTotal, oldTotal })
  if (newTotal > 0 && oldTotal === 0 && !hasLoggedTotalPages.value) {
    addLog(`当前共 ${newTotal} 页数据`, 'info')
    hasLoggedTotalPages.value = true
  }
  
  // 如果总页数重置为0，清除日志和重置状态
  if (newTotal === 0 && oldTotal > 0) {
    recentLogs.value = []
    hasLoggedTotalPages.value = false
    console.log('[UI组件] 重置日志和状态')
  }
})

// 监听进度变化，添加日志 - 延迟处理确保总页数日志先显示
watch(() => props.progress.currentPage, (newPage, oldPage) => {
  console.log('[UI组件] 接收到页数变化:', { newPage, oldPage, totalPages: props.progress.totalPages })
  if (newPage > oldPage) {
    // 使用 nextTick 确保总页数日志先显示
    nextTick(() => {
      addLog(`正在获取第 ${newPage} 页数据...`, 'info')
    })
  }
})

// 监听整个进度对象的变化（用于调试）
watch(() => props.progress, (newProgress) => {
  console.log('[UI组件] 进度对象完整更新:', {
    currentPage: newProgress.currentPage,
    totalPages: newProgress.totalPages,
    networkStatus: newProgress.networkStatus,
    mode: newProgress.mode,
    totalProducts: newProgress.totalProducts,
    speed: newProgress.speed,
    estimatedTime: newProgress.estimatedTime
  })
}, { deep: true })

watch(() => props.progress.networkStatus, (newStatus, oldStatus) => {
  if (newStatus === 'requesting' && oldStatus !== 'requesting') {
    addLog('开始获取数据...', 'info')
  } else if (newStatus === 'success' && oldStatus === 'requesting') {
    addLog('数据获取成功', 'success')
  } else if (newStatus === 'error' && oldStatus === 'requesting') {
    addLog('数据获取失败', 'error')
  }
})

watch(() => props.progress.mode, (newMode) => {
  const modeText = {
    'rpc': 'RPC模式',
    'api': 'API模式',
    'fallback': '回退到API模式'
  }
  //addLog(`使用 ${modeText[newMode]} 获取数据`, newMode === 'fallback' ? 'warning' : 'info')
})
</script>

<style scoped>
.step1-progress {
  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    
    i {
      color: #409eff;
    }
  }
  
  .page-progress-section {
    margin-bottom: 24px;
    
    .page-progress-content {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      
      .page-numbers {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;
        margin-bottom: 20px;
        
        .current-page, .total-pages {
          text-align: center;
          
          .number {
            display: block;
            font-size: 32px;
            font-weight: 700;
            color: #409eff;
            line-height: 1;
          }
          
          .label {
            display: block;
            font-size: 14px;
            color: #606266;
            margin-top: 4px;
          }
        }
        
        .divider {
          font-size: 24px;
          color: #dcdfe6;
          font-weight: 300;
        }
      }
      
      .page-details {
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .detail-item {
            display: flex;
            gap: 8px;
            
            .label {
              color: #909399;
              font-size: 14px;
            }
            
            .value {
              color: #303133;
              font-size: 14px;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
  
  .network-status-section {
    margin-bottom: 24px;
    
    .network-status-content {
      .status-indicator {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
        
        .status-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          i {
            font-size: 20px;
          }
          
          &.status-idle {
            background: #f0f0f0;
            color: #909399;
          }
          
          &.status-requesting {
            background: #e6f7ff;
            color: #409eff;
            
            i {
              animation: rotate 1s linear infinite;
            }
          }
          
          &.status-success {
            background: #f0f9ff;
            color: #67c23a;
          }
          
          &.status-error {
            background: #fef0f0;
            color: #f56c6c;
          }
        }
        
        .status-text {
          .status-main {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
          }
          
          .status-sub {
            font-size: 14px;
            color: #606266;
            margin-top: 2px;
          }
        }
      }
      
      .batch-progress {
        .batch-info {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          color: #606266;
          margin-bottom: 8px;
        }
        
        .batch-progress-bar {
          margin-bottom: 0;
        }
      }
    }
  }
  
  .log-section {
    .log-content {
      background: #fafafa;
      border-radius: 6px;
      padding: 12px;
      max-height: 200px;
      overflow-y: auto;
      
      .log-item {
        display: flex;
        gap: 12px;
        padding: 4px 0;
        font-size: 13px;
        
        .log-time {
          color: #909399;
          font-family: monospace;
          min-width: 60px;
        }
        
        .log-message {
          color: #606266;
        }
        
        &.success .log-message {
          color: #67c23a;
        }
        
        &.warning .log-message {
          color: #e6a23c;
        }
        
        &.error .log-message {
          color: #f56c6c;
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
