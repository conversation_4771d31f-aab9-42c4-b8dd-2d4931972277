// Background Script RPC 适配器
// 专门为解决 "document is not defined" 问题而设计

declare const chrome: any;

// 静态导入页面控制器模块，避免动态导入问题
import { getAllRejectedProductsViaRPC } from '@/utils/rpc/pageController';

// 静态导入DOM操作模块，避免动态导入问题
import * as domOperations from '@/utils/rpc/domOperations';

/**
 * Background Script 安全的 RPC 适配器
 * 重构版本：使用静态导入避免Service Worker中的动态导入问题
 */
export class BackgroundRpcAdapter {

  /**
   * 安全地获取所有重新上传商品
   */
  static async getAllRejectedProducts(
    onProgress?: (current: number, total: number) => void
  ): Promise<any[]> {
    try {
      // 基础环境检查
      if (!chrome?.tabs || !chrome?.scripting) {
        throw new Error('Chrome API 环境不完整');
      }

      // 使用静态导入的页面控制器模块
      console.log('Background: 使用静态导入的pageController模块');
      return await getAllRejectedProductsViaRPC(onProgress);
    } catch (error: any) {
      console.error('Background RPC Adapter: 获取重新上传商品失败:', error);
      throw new Error(`获取数据失败: ${error.message}`);
    }
  }

  /**
   * 安全地检查N11页面
   */
  static async checkN11Page(): Promise<boolean> {
    try {
      if (!chrome?.tabs || !chrome?.scripting) {
        return false;
      }

      // 获取活动标签页
      const tabs = await new Promise<any[]>((resolve, reject) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
          chrome.runtime.lastError ? reject(chrome.runtime.lastError) : resolve(result);
        });
      });

      if (!tabs?.length) return false;

      const { url, id: tabId } = tabs[0];
      if (!url?.includes('so.n11.com')) return false;

      // 执行页面检查脚本
      try {
        const results = await chrome.scripting.executeScript({
          target: { tabId },
          func: () => {
            try {
              const currentUrl = window.location.href;
              const isCorrectUrl = currentUrl.includes('so.n11.com') && 
                                  (currentUrl.includes('product') || currentUrl.includes('urun'));
              
              const hasProductElements = !!(
                document.querySelector('.product-list') ||
                document.querySelector('[class*="product"]') ||
                document.querySelector('.tabManager')
              );
              
              return isCorrectUrl && hasProductElements;
            } catch (error) {
              return false;
            }
          }
        });
        
        return !!(results?.[0]?.result);
      } catch (scriptError) {
        console.warn('页面检查脚本执行失败，回退到URL检查:', scriptError);
        return url.includes('so.n11.com') && (url.includes('product') || url.includes('urun'));
      }
    } catch (error) {
      console.error('Background RPC Adapter: 检查N11页面失败:', error);
      return false;
    }
  }

  /**
   * 安全地获取RPC状态
   */
  static async getRPCStatus(): Promise<any> {
    try {
      const status = {
        supported: false,
        chromeAPIs: {
          scripting: !!(chrome?.scripting?.executeScript),
          debugger: !!(chrome?.debugger?.attach),
          tabs: !!(chrome?.tabs?.query)
        },
        currentPage: {
          isN11: false,
          url: undefined,
          tabId: undefined
        },
        permissions: {
          debugger: false,
          scripting: false,
          activeTab: false
        }
      };

      // 检查权限
      if (chrome?.permissions) {
        try {
          const permissions = await new Promise<any>((resolve, reject) => {
            chrome.permissions.getAll((result: any) => {
              chrome.runtime.lastError ? reject(chrome.runtime.lastError) : resolve(result);
            });
          });

          status.permissions = {
            debugger: permissions.permissions?.includes('debugger') || false,
            scripting: permissions.permissions?.includes('scripting') || false,
            activeTab: permissions.permissions?.includes('activeTab') || false
          };
        } catch (permError) {
          console.warn('权限检查失败:', permError);
        }
      }

      // 检查当前页面
      if (chrome?.tabs) {
        try {
          const tabs = await new Promise<any[]>((resolve, reject) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
              chrome.runtime.lastError ? reject(chrome.runtime.lastError) : resolve(result);
            });
          });

          if (tabs?.length) {
            const activeTab = tabs[0];
            status.currentPage = {
              url: activeTab.url,
              tabId: activeTab.id,
              isN11: !!(activeTab.url?.includes('so.n11.com') && 
                       (activeTab.url.includes('product') || activeTab.url.includes('urun')))
            };
          }
        } catch (tabError) {
          console.warn('获取页面信息失败:', tabError);
        }
      }

      // 判断是否支持
      status.supported = Object.values(status.chromeAPIs).every(Boolean) && 
                        Object.values(status.permissions).some(Boolean);  // 至少有一个权限

      return status;
    } catch (error: any) {
      console.error('Background RPC Adapter: 获取状态失败:', error);
      return {
        supported: false,
        message: '状态检查失败',
        error: error.message
      };
    }
  }

  /**
   * 安全地执行DOM操作
   */
  static async executeDomOperation(operation: string, tabId?: number, params?: any): Promise<any> {
    try {
      if (!chrome?.tabs || !chrome?.scripting) {
        return { success: false, message: 'Chrome API 环境不完整' };
      }

      // 获取目标标签页
      let targetTabId = tabId;
      if (!targetTabId) {
        const tabs = await new Promise<any[]>((resolve, reject) => {
          chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
            chrome.runtime.lastError ? reject(chrome.runtime.lastError) : resolve(result);
          });
        });
        
        if (!tabs?.length) {
          return { success: false, message: '未找到活动标签页' };
        }
        
        targetTabId = tabs[0].id;
      }

      // 使用静态导入的DOM操作模块
      console.log('Background: 使用静态导入的domOperations模块');

      // 执行对应操作
      const operationMap: { [key: string]: Function } = {
        setPageSize100: domOperations.setPageSize100,
        clickNextPage: domOperations.clickNextPage,
        getPageStatus: domOperations.getPageStatus,
        isN11ProductListPage: domOperations.isN11ProductListPage,
        waitForElement: domOperations.waitForElement,
        scrollToPosition: domOperations.scrollToPosition
      };

      const operationFunc = operationMap[operation];
      if (!operationFunc) {
        return { success: false, message: `不支持的DOM操作: ${operation}` };
      }

      return await operationFunc(targetTabId, params);
    } catch (error: any) {
      console.error('Background RPC Adapter: DOM操作失败:', error);
      return { success: false, message: `DOM操作失败: ${error.message}` };
    }
  }
}

export default BackgroundRpcAdapter; 