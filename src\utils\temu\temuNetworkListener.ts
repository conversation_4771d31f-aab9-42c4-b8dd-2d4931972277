// Temu 搜索网络监听器
declare const chrome: any;

/**
 * Temu 搜索网络监听器配置
 */
export interface TemuSearchListenerConfig {
  tabId: number;
  timeout?: number;
}

/**
 * Temu 搜索网络监听结果
 */
export interface TemuSearchListenerResult {
  success: boolean;
  message: string;
  data?: any;
}

/**
 * 捕获的 Temu 搜索响应数据
 */
export interface TemuSearchResponseData {
  url: string;
  responseBody: any;
  timestamp: number;
}

/**
 * Temu 搜索网络监听器类
 */
export class TemuSearchNetworkListener {
  private tabId: number;
  private isAttached: boolean = false;
  private isListening: boolean = false;
  private responseCallback?: (data: TemuSearchResponseData) => void;
  private timeout: number;
  private pendingRequests: Map<string, { url: string, timestamp: number }> = new Map();

  constructor(config: TemuSearchListenerConfig) {
    this.tabId = config.tabId;
    this.timeout = config.timeout || 30000;
  }

  /**
   * 启动网络监听
   */
  async start(responseCallback?: (data: TemuSearchResponseData) => void): Promise<TemuSearchListenerResult> {
    try {
      this.responseCallback = responseCallback;

      // 附加到标签页
      const attachResult = await this.attachToTab();
      if (!attachResult.success) {
        return attachResult;
      }

      // 启用网络域
      const enableResult = await this.enableNetworkDomain();
      if (!enableResult.success) {
        await this.detach();
        return enableResult;
      }

      // 设置事件监听器
      this.setupEventListeners();

      this.isListening = true;
      console.log(`Temu 搜索网络监听器已启动，监听标签页 ${this.tabId}`);

      return { success: true, message: 'Temu 搜索网络监听器启动成功' };

    } catch (error: any) {
      console.error('启动 Temu 搜索网络监听器失败:', error);
      await this.cleanup();
      return { success: false, message: `启动失败: ${error.message}` };
    }
  }

  /**
   * 停止网络监听
   */
  async stop(): Promise<TemuSearchListenerResult> {
    try {
      await this.cleanup();
      console.log('Temu 搜索网络监听器已停止');
      return { success: true, message: 'Temu 搜索网络监听器停止成功' };
    } catch (error: any) {
      console.error('停止 Temu 搜索网络监听器失败:', error);
      return { success: false, message: `停止失败: ${error.message}` };
    }
  }

  /**
   * 检查标签页是否有效且可访问
   */
  private async checkTabValidity(): Promise<boolean> {
    return new Promise((resolve) => {
      chrome.tabs.get(this.tabId, (tab: any) => {
        if (chrome.runtime.lastError) {
          console.warn('标签页无效或不存在:', chrome.runtime.lastError);
          resolve(false);
          return;
        }
        
        // 检查标签页是否正在加载或已完成加载
        const isValid = tab && (tab.status === 'complete' || tab.status === 'loading');
        console.log(`标签页 ${this.tabId} 状态检查:`, { status: tab?.status, url: tab?.url, valid: isValid });
        resolve(isValid);
      });
    });
  }

  /**
   * 附加到标签页
   */
  private async attachToTab(): Promise<TemuSearchListenerResult> {
    return new Promise(async (resolve) => {
      // 首先检查标签页是否有效
      const isTabValid = await this.checkTabValidity();
      if (!isTabValid) {
        resolve({
          success: false,
          message: '标签页无效或不可访问，可能已被关闭或正在重新加载'
        });
        return;
      }

      // 先尝试直接附加
      const directAttachResult = await this.tryAttachDirectly();
      if (directAttachResult.success) {
        resolve(directAttachResult);
        return;
      }

      // 如果直接附加失败，检查是否是调试器冲突
      const errorMessage = directAttachResult.message || '';
      if (errorMessage.includes('Another debugger is already attached')) {
        console.log('检测到调试器冲突，尝试先分离再重新附加...');

        // 先尝试分离现有的调试器
        const detachResult = await this.forceDetachExistingDebugger();
        if (detachResult.success) {
          // 等待一小段时间让分离操作完成
          await new Promise(resolve => setTimeout(resolve, 500));

          // 重新检查标签页有效性
          const isStillValid = await this.checkTabValidity();
          if (!isStillValid) {
            resolve({
              success: false,
              message: '标签页在重新附加过程中变为无效'
            });
            return;
          }

          // 重新尝试附加
          const retryAttachResult = await this.tryAttachDirectly();
          resolve(retryAttachResult);
        } else {
          resolve({
            success: false,
            message: `调试器冲突处理失败: ${detachResult.message}`
          });
        }
      } else {
        // 其他类型的错误
        resolve(directAttachResult);
      }
    });
  }

  /**
   * 尝试直接附加调试器
   */
  private async tryAttachDirectly(): Promise<TemuSearchListenerResult> {
    return new Promise((resolve) => {
      chrome.debugger.attach({ tabId: this.tabId }, '1.3', () => {
        if (chrome.runtime.lastError) {
          const errorMessage = chrome.runtime.lastError.message || '';
          console.error('附加 debugger 失败:', chrome.runtime.lastError);
          resolve({
            success: false,
            message: `附加失败: ${errorMessage}`
          });
          return;
        }

        this.isAttached = true;
        console.log(`成功附加到标签页 ${this.tabId}`);
        resolve({ success: true, message: '附加成功' });
      });
    });
  }

  /**
   * 强制分离现有的调试器
   */
  private async forceDetachExistingDebugger(): Promise<TemuSearchListenerResult> {
    return new Promise((resolve) => {
      chrome.debugger.detach({ tabId: this.tabId }, () => {
        if (chrome.runtime.lastError) {
          const errorMessage = chrome.runtime.lastError.message || '';
          console.warn('分离现有调试器时出现警告:', chrome.runtime.lastError);
          // 即使分离时出现错误，也可能是因为调试器已经被分离了
          // 所以我们仍然认为这是成功的
          resolve({ success: true, message: '分离操作已尝试' });
        } else {
          console.log('成功分离现有调试器');
          resolve({ success: true, message: '分离成功' });
        }
      });
    });
  }

  /**
   * 启用网络域
   */
  private async enableNetworkDomain(): Promise<TemuSearchListenerResult> {
    return new Promise((resolve) => {
      // 添加延迟确保debugger连接稳定
      setTimeout(() => {
        chrome.debugger.sendCommand(
          { tabId: this.tabId },
          'Network.enable',
          {},
          () => {
            if (chrome.runtime.lastError) {
              const errorMessage = chrome.runtime.lastError.message || '';
              console.error('启用 Network 域失败:', chrome.runtime.lastError);
              
              // 如果是因为debugger已分离，尝试重新附加
              if (errorMessage.includes('Detached while handling command') || 
                  errorMessage.includes('not attached')) {
                console.log('检测到debugger已分离，尝试重新附加...');
                this.isAttached = false;
                
                // 重新附加并启用网络域
                this.retryAttachAndEnable().then(resolve);
                return;
              }
              
              resolve({
                success: false,
                message: `启用 Network 域失败: ${errorMessage}`
              });
              return;
            }

            console.log('Network 域已启用');
            resolve({ success: true, message: 'Network 域启用成功' });
          }
        );
      }, 200); // 延迟200ms确保连接稳定
    });
  }

  /**
   * 重新附加并启用网络域
   */
  private async retryAttachAndEnable(): Promise<TemuSearchListenerResult> {
    try {
      console.log('开始重新附加debugger...');
      
      // 先尝试分离可能存在的连接
      await this.forceDetachExistingDebugger();
      
      // 等待一段时间确保分离完成
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 重新附加
      const attachResult = await this.tryAttachDirectly();
      if (!attachResult.success) {
        return attachResult;
      }
      
      // 等待一段时间确保附加稳定
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 重新启用网络域
      return new Promise((resolve) => {
        chrome.debugger.sendCommand(
          { tabId: this.tabId },
          'Network.enable',
          {},
          () => {
            if (chrome.runtime.lastError) {
              console.error('重新启用 Network 域失败:', chrome.runtime.lastError);
              resolve({
                success: false,
                message: `重新启用 Network 域失败: ${chrome.runtime.lastError.message}`
              });
              return;
            }

            console.log('重新启用 Network 域成功');
            resolve({ success: true, message: 'Network 域重新启用成功' });
          }
        );
      });
      
    } catch (error: any) {
      console.error('重新附加和启用失败:', error);
      return {
        success: false,
        message: `重新附加失败: ${error.message}`
      };
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 使用绑定的事件处理器，便于后续移除
    chrome.debugger.onEvent.addListener(this.handleDebuggerEvent);
    chrome.debugger.onDetach.addListener(this.handleDebuggerDetach);
  }

  /**
   * 处理页面刷新的情况
   */
  private async handlePageRefresh(): Promise<void> {
    if (!this.isListening) {
      console.log('监听器已停止，跳过重新连接');
      return;
    }

    console.log('开始处理页面刷新重连...');
    
    // 等待页面加载完成
    await this.waitForPageLoad();
    
    // 尝试重新连接
    const maxRetries = 3;
    let retryCount = 0;
    
    while (retryCount < maxRetries && this.isListening) {
      retryCount++;
      console.log(`尝试重新连接 (${retryCount}/${maxRetries})...`);
      
      try {
        // 检查标签页是否仍然有效
        const isValid = await this.checkTabValidity();
        if (!isValid) {
          console.log('标签页无效，停止重连尝试');
          break;
        }
        
        // 尝试重新附加和启用网络域
        const attachResult = await this.attachToTab();
        if (attachResult.success) {
          const enableResult = await this.enableNetworkDomain();
          if (enableResult.success) {
            console.log('页面刷新后重新连接成功');
            return;
          }
        }
        
        // 如果失败，等待一段时间后重试
        if (retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
        
      } catch (error) {
        console.error(`重连尝试 ${retryCount} 失败:`, error);
        if (retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
      }
    }
    
    console.warn('页面刷新后重新连接失败，已达到最大重试次数');
  }

  /**
   * 等待页面加载完成
   */
  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      const checkPageStatus = () => {
        chrome.tabs.get(this.tabId, (tab: any) => {
          if (chrome.runtime.lastError) {
            console.warn('检查页面状态时出错:', chrome.runtime.lastError);
            resolve();
            return;
          }
          
          if (tab && tab.status === 'complete') {
            console.log('页面加载完成');
            resolve();
          } else {
            console.log('页面仍在加载中，继续等待...');
            setTimeout(checkPageStatus, 500);
          }
        });
      };
      
      // 开始检查，但最多等待10秒
      checkPageStatus();
      setTimeout(() => {
        console.log('等待页面加载超时，继续执行');
        resolve();
      }, 10000);
    });
  }

  /**
   * 处理响应接收事件
   */
  private handleResponseReceived(params: any): void {
    try {
      const { requestId, response } = params;
      const url = response.url;

      // 只关注 Temu 搜索 API 的响应
      if (!url.includes('/api/poppy/v1/search') || !url.includes('scene=search')) {
        return;
      }
      // 不关注搜索建议
      if(url.includes('/api/poppy/v1/search_suggest')){
        return;
      }

      console.log('捕获到 Temu 搜索响应:', response.status, url);

      // 检查是否是成功响应
      if (response.status >= 200 && response.status < 300) {
        // 记录待处理的请求，等待 loadingFinished 事件
        this.pendingRequests.set(requestId, {
          url: url,
          timestamp: Date.now()
        });
        
        console.log('记录待处理请求:', requestId, url);
      }

    } catch (error) {
      console.error('处理响应接收事件失败:', error);
    }
  }

  /**
   * 处理加载完成事件
   */
  private handleLoadingFinished(params: any): void {
    try {
      const { requestId } = params;
      
      // 检查是否是我们关注的请求
      const pendingRequest = this.pendingRequests.get(requestId);
      if (!pendingRequest) {
        return;
      }

      console.log('请求加载完成:', requestId, pendingRequest.url);

      // 移除待处理请求记录
      this.pendingRequests.delete(requestId);

      // 现在可以安全地获取响应体
      this.getResponseBody(requestId, pendingRequest.url);

    } catch (error) {
      console.error('处理加载完成事件失败:', error);
    }
  }

  /**
   * 获取响应体
   */
  private getResponseBody(requestId: string, url: string): void {
    chrome.debugger.sendCommand(
      { tabId: this.tabId },
      'Network.getResponseBody',
      { requestId },
      (result: any) => {
        // 检查 Chrome runtime 错误
        if (chrome.runtime.lastError) {
          console.warn('获取响应体失败:', chrome.runtime.lastError.message);
          console.warn('请求ID:', requestId, 'URL:', url);
          
          // 常见错误处理
          if (chrome.runtime.lastError.message?.includes('No data found for resource')) {
            console.warn('响应体数据不可用，可能已被浏览器清理或请求类型不支持');
          } else if (chrome.runtime.lastError.message?.includes('not attached')) {
            console.warn('Debugger 已断开连接');
          }
          return;
        }

        // 检查 result 是否存在
        if (!result) {
          console.warn('获取响应体返回空结果:', { requestId, url });
          return;
        }

        try {
          // 检查是否有响应体数据
          if (!result.hasOwnProperty('body')) {
            console.warn('响应结果中没有 body 字段:', result);
            return;
          }

          let responseBody = result.body;
          // 检查响应体是否为空
          if (!responseBody) {
            console.warn('响应体为空:', { requestId, url });
            return;
          }

          // 如果响应体是 base64 编码的，需要解码
          if (result.base64Encoded) {
            try {
              responseBody = atob(responseBody);
            } catch (decodeError) {
              console.error('Base64 解码失败:', decodeError);
              return;
            }
          }

          // 尝试解析 JSON
          let parsedBody: any;
          try {
            parsedBody = JSON.parse(responseBody);
          } catch (e) {
            console.error('解析响应 JSON 失败:', e);
            console.error('原始响应体:', responseBody);
            return;
          }

          // 检查是否是有效的 Temu 搜索响应
          if (this.isValidTemuSearchResponse(parsedBody)) {
            const capturedResponse: TemuSearchResponseData = {
              url,
              responseBody: parsedBody,
              timestamp: Date.now()
            };

            console.log('成功捕获 Temu 搜索数据:', parsedBody?.result?.data?.goods_list?.length || 0, '条商品');

            // 调用回调函数
            if (this.responseCallback) {
              this.responseCallback(capturedResponse);
            }
          } else {
            console.warn('响应体不是有效的 Temu 搜索响应:', parsedBody);
          }

        } catch (error) {
          console.error('处理响应体失败:', error);
          console.error('错误详情:', { requestId, url, result });
        }
      }
    );
  }

  /**
   * 检查是否是有效的 Temu 搜索响应
   */
  private isValidTemuSearchResponse(responseBody: any): boolean {
    try {
      return responseBody &&
             responseBody.success === true &&
             responseBody.result &&
             responseBody.result.data &&
             Array.isArray(responseBody.result.data.goods_list);
    } catch (error) {
      return false;
    }
  }

  /**
   * 分离 debugger
   */
  private async detach(): Promise<void> {
    if (!this.isAttached) return;

    return new Promise((resolve) => {
      chrome.debugger.detach({ tabId: this.tabId }, () => {
        if (chrome.runtime.lastError) {
          console.error('分离 debugger 失败:', chrome.runtime.lastError);
        } else {
          console.log(`已从标签页 ${this.tabId} 分离 debugger`);
        }
        this.isAttached = false;
        resolve();
      });
    });
  }

  /**
   * 清理资源
   */
  private async cleanup(): Promise<void> {
    this.isListening = false;
    this.responseCallback = undefined;
    
    // 清理待处理的请求
    this.pendingRequests.clear();
    
    // 移除事件监听器
    try {
      chrome.debugger.onEvent.removeListener(this.handleDebuggerEvent);
      chrome.debugger.onDetach.removeListener(this.handleDebuggerDetach);
    } catch (error) {
      console.warn('移除事件监听器时出错:', error);
    }
    
    await this.detach();
  }

  /**
   * debugger事件处理器（绑定到实例）
   */
  private handleDebuggerEvent = (source: any, method: string, params: any) => {
    if (source.tabId !== this.tabId) return;

    if (method === 'Network.responseReceived') {
      this.handleResponseReceived(params);
    } else if (method === 'Network.loadingFinished') {
      this.handleLoadingFinished(params);
    }
  };

  /**
   * debugger分离事件处理器（绑定到实例）
   */
  private handleDebuggerDetach = (source: any, reason: string) => {
    if (source.tabId === this.tabId) {
      console.log(`Debugger从标签页 ${this.tabId} 分离，原因: ${reason}`);
      this.isAttached = false;
      
      // 如果是因为页面导航或刷新导致的分离，尝试重新连接
      if (reason === 'target_closed' || reason === 'replaced_with_devtools') {
        console.log('检测到页面刷新或导航，尝试重新连接...');
        this.handlePageRefresh();
      }
    }
  };
}

/**
 * 创建 Temu 搜索网络监听器实例
 */
export function createTemuSearchNetworkListener(config: TemuSearchListenerConfig): TemuSearchNetworkListener {
  return new TemuSearchNetworkListener(config);
}

/**
 * 获取当前标签页 ID
 */
export async function getCurrentTabId(): Promise<number | null> {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    return tabs[0]?.id || null;
  } catch (error) {
    console.error('获取当前标签页 ID 失败:', error);
    return null;
  }
}
