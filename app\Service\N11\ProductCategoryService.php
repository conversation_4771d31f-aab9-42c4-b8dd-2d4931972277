<?php

namespace App\Service\N11;

use App\Service\BaseService;
use App\Models\N11\ProductCategoryN11Model;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class ProductCategoryService extends BaseService{

    /**
     * 当前使用的翻译服务
     * 可选值：mymemory, google
     *
     * @var string
     */
    protected $currentTranslator = 'mymemory';

    /**
     * 从JSON文件采集N11商品分类并保存到数据库
     *
     * @param bool $isConsole 是否是命令行执行
     * @param object|null $command 命令行对象，用于输出进度信息
     * @param string $translator 指定的翻译服务，可选值：mymemory, google
     * @return array 返回采集结果
     */
    public function collectCategories($isConsole = false, $command = null, $translator = 'mymemory')
    {
        // 设置当前使用的翻译服务
        $this->currentTranslator = $translator;
        
        $jsonFilePath = storage_path('n11/categories.json');

        // 检查文件是否存在
        if (!File::exists($jsonFilePath)) {
            $message = "分类数据文件不存在: {$jsonFilePath}";
            if ($isConsole && $command) {
                $command->error($message);
            }
            return [
                'status' => 0,
                'message' => $message
            ];
        }

        try {
            // 读取JSON文件内容
            $jsonContent = File::get($jsonFilePath);
            $categoriesData = json_decode($jsonContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $message = "JSON解析错误: " . json_last_error_msg();
                if ($isConsole && $command) {
                    $command->error($message);
                }
                return [
                    'status' => 0,
                    'message' => $message
                ];
            }

            // 检查数据结构是否符合预期
            if (!isset($categoriesData['categories']) || !is_array($categoriesData['categories'])) {
                $message = "JSON数据结构不符合预期";
                if ($isConsole && $command) {
                    $command->error($message);
                }
                return [
                    'status' => 0,
                    'message' => $message
                ];
            }

            // 开始处理分类数据
            $categoryCount = 0;
            $categories = $categoriesData['categories'];
            $totalCategories = count($categories);

            if ($isConsole && $command) {
                $command->info("开始处理N11分类数据，共 {$totalCategories} 个顶级分类");
            }

            foreach ($categories as $index => $category) {
                // 保存顶级分类到数据库
                $savedCategory = $this->saveCategoryToDatabase($category, 0);
                $categoryCount++;

                if ($isConsole && $command) {
                    $command->newLine();
                    $command->info("处理顶级分类 [".($index+1)."/{$totalCategories}]: {$category['name']} (ID: {$savedCategory->id})");
                }

                // 如果有子分类，则递归处理
                if (isset($category['subCategories']) && is_array($category['subCategories']) && count($category['subCategories']) > 0) {
                    if ($isConsole && $command) {
                        $command->info("开始处理 {$category['name']} 的子分类...");
                    }
                    
                    $childrenCount = $this->processChildCategories(
                        $savedCategory->id,
                        $category['name'],
                        $category['subCategories'],
                        $isConsole,
                        $command
                    );

                    $categoryCount += $childrenCount;

                    if ($isConsole && $command) {
                        $command->info("完成 {$category['name']} 的子分类采集，共 {$childrenCount} 个子分类");
                    }
                }
            }

            return [
                'status' => 1,
                'message' => 'N11分类数据采集完成',
                'count' => $categoryCount
            ];

        } catch (\Exception $e) {
            Log::error('N11分类数据采集失败: ' . $e->getMessage());

            if ($isConsole && $command) {
                $command->error('N11分类数据采集失败: ' . $e->getMessage());
            }

            return [
                'status' => 0,
                'message' => 'N11分类数据采集失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 递归处理子分类
     *
     * @param int $parentId 父分类ID
     * @param string $pathName 分类路径名称
     * @param array $childCategories 子分类数组
     * @param bool $isConsole 是否是命令行执行
     * @param object|null $command 命令行对象，用于输出进度信息
     * @return int 处理的分类数量
     */
    protected function processChildCategories($parentId, $pathName, $childCategories, $isConsole = false, $command = null)
    {
        $count = 0;
        $totalChildren = count($childCategories);

        if ($isConsole && $command) {
            $command->line("  正在处理 {$pathName} 的 {$totalChildren} 个子分类...");
        }

        // 如果子分类数量较多，显示进度条
        if ($isConsole && $command && $totalChildren > 10) {
            $childBar = $command->getOutput()->createProgressBar($totalChildren);
            $childBar->start();
        }

        foreach ($childCategories as $index => $category) {
            // 构建当前分类的完整路径名称（土耳其语）
            $currentPathName = $pathName . ',' . $category['name'];

            // 保存分类到数据库
            $savedCategory = $this->saveCategoryToDatabase($category, $parentId, $currentPathName);
            $count++;

            if ($isConsole && $command && $totalChildren <= 10) {
                $command->line("    处理子分类 [".($index+1)."/{$totalChildren}]: {$category['name']} (ID: {$savedCategory->id})");
            }

            // 如果有子分类，则递归处理
            if (isset($category['subCategories']) && is_array($category['subCategories']) && count($category['subCategories']) > 0) {
                $childrenCount = $this->processChildCategories(
                    $savedCategory->id,
                    $currentPathName,
                    $category['subCategories'],
                    $isConsole,
                    $command
                );
                $count += $childrenCount;

                if ($isConsole && $command && $totalChildren <= 10) {
                    $command->line("    完成子分类 {$category['name']} 的子分类采集，共 {$childrenCount} 个子分类");
                }
            }

            if ($isConsole && $command && isset($childBar) && $totalChildren > 10) {
                $childBar->advance();
            }
        }

        if ($isConsole && $command && isset($childBar) && $totalChildren > 10) {
            $childBar->finish();
            $command->newLine();
        }

        return $count;
    }

    /**
     * 保存分类到数据库
     *
     * @param array $category 分类数据
     * @param int $parentId 父分类ID
     * @param string|null $pathName 分类路径名称（土耳其语）
     * @return ProductCategoryN11Model 保存的分类模型
     */
    protected function saveCategoryToDatabase($category, $parentId, $pathName = null)
    {
        // 确定分类层级
        $level = 1;
        if ($parentId > 0) {
            // 获取父分类的层级
            $parentCategory = ProductCategoryN11Model::query()->where('id', $parentId)->first();
            if ($parentCategory) {
                $level = $parentCategory->level + 1;
            }
        }

        // 检查记录是否已经存在且name_tl不为空
        $existingCategory = ProductCategoryN11Model::query()->where('id', $category['id'])->first();
        $nameCn = '';

        if ($existingCategory && !empty($existingCategory->name_tl)) {
            // 如果记录已存在且name_tl不为空，直接使用现有的name(中文名称)
            $nameCn = $existingCategory->name;
        } else {
            // 将土耳其语名称翻译为中文
            $nameCn = $this->translateToChineseFromTurkish($category['name']);
        }

        // 构建分类路径
        $path = (string)$category['id'];
        $pathNameTl = $category['name']; // 土耳其语名称
        $pathNameCn = $nameCn; // 中文名称

        if ($parentId > 0) {
            $parentCategory = ProductCategoryN11Model::query()->where('id', $parentId)->first();
            if ($parentCategory && !empty($parentCategory->path)) {
                $path = $parentCategory->path . ',' . $category['id'];
                
                // 如果有父分类路径名称
                if ($pathName !== null) {
                    $pathNameTl = $pathName;
                } else if (!empty($parentCategory->path_name_tl)) {
                    $pathNameTl = $parentCategory->path_name_tl . ',' . $category['name'];
                }
                
                if (!empty($parentCategory->path_name)) {
                    $pathNameCn = $parentCategory->path_name . ',' . $nameCn;
                }
            }
        }

        // 判断是否为叶子节点
        $isLeaf = (!isset($category['subCategories']) || $category['subCategories'] === null) ? 1 : 0;

        // 准备保存的数据
        $data = [
            'id' => $category['id'],
            'name' => $nameCn, // 中文名称
            'name_en' => '', // 默认为空字符串
            'name_tl' => $category['name'], // 土耳其语名称
            'parent_id' => $parentId,
            'is_leaf' => $isLeaf,
            'level' => $level,
            'path' => $path,
            'path_name' => $pathNameCn, // 中文路径
            'path_name_tl' => $pathNameTl, // 土耳其语路径
            'sort_order' => 0, // 保持原始顺序
            'status' => 1, // 默认启用
        ];

        // 使用updateOrCreate避免重复插入
        return ProductCategoryN11Model::query()->updateOrCreate(
            ['id' => $category['id']],
            $data
        );
    }

    /**
     * 将土耳其语翻译为中文
     * 根据设置选择翻译服务
     *
     * @param string $text 需要翻译的文本
     * @return string 翻译后的中文文本
     */
    protected function translateToChineseFromTurkish($text)
    {
        // 根据设置选择翻译服务
        switch ($this->currentTranslator) {
            case 'mymemory':
                // 使用MyMemory翻译服务
                $myMemoryResult = $this->translateUsingMyMemory($text);
                if (!empty($myMemoryResult) && $myMemoryResult !== $text) {
                    return $myMemoryResult;
                }
                break;

            case 'google':
                // 只使用谷歌翻译
                $googleResult = $this->translateUsingGoogle($text);
                if (!empty($googleResult) && $googleResult !== $text) {
                    return $googleResult;
                }
                break;
        }

        // 所有翻译方法都失败，返回原文
        Log::warning('所有翻译方法都失败，返回原文: ' . $text);
        return $text;
    }

    /**
     * 使用MyMemory翻译API将土耳其语翻译为中文
     *
     * @param string $text 需要翻译的文本
     * @return string 翻译后的中文文本
     */
    protected function translateUsingMyMemory($text)
    {
        try {
            $client = new Client([
                'timeout' => 10,
                'verify' => false
            ]);

            // 使用MyMemory翻译API
            $response = $client->get('https://api.mymemory.translated.net/get', [
                'query' => [
                    'q' => $text,
                    'langpair' => 'tr|zh-CN'
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            // 解析翻译结果
            if (isset($data['responseData']['translatedText']) && !empty($data['responseData']['translatedText'])) {
                return $data['responseData']['translatedText'];
            }

            // 如果翻译失败，返回原文
            return $text;
        } catch (\Exception $e) {
            Log::warning('MyMemory翻译失败: ' . $e->getMessage() . ', 原文: ' . $text);
            return $text; // 翻译失败时返回原文
        }
    }

    /**
     * 使用谷歌翻译API将土耳其语翻译为中文
     *
     * @param string $text 需要翻译的文本
     * @return string 翻译后的中文文本
     */
    protected function translateUsingGoogle($text)
    {
        try {
            $client = new Client([
                'timeout' => 10,
                'verify' => false
            ]);

            $response = $client->get('https://translate.googleapis.com/translate_a/single', [
                'query' => [
                    'client' => 'gtx',
                    'sl' => 'tr', // 土耳其语
                    'tl' => 'zh-CN', // 中文
                    'dt' => 't',
                    'q' => $text
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            // 解析翻译结果
            if (isset($data[0][0][0])) {
                return $data[0][0][0];
            }

            // 如果翻译失败，返回原文
            return $text;
        } catch (\Exception $e) {
            Log::warning('谷歌翻译失败: ' . $e->getMessage() . ', 原文: ' . $text);
            return $text; // 翻译失败时返回原文
        }
    }
}