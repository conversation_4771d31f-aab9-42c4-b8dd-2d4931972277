#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API客户端
统一处理HTTP请求，包括固定API前缀、错误处理和响应解析
"""

import requests
import json
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urljoin


class APIClient:
    """API客户端类"""

    def __init__(self, timeout: int = 30):
        """
        初始化API客户端
        
        Args:
            timeout: 请求超时时间（秒）
        """
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'TradeShopAssistant/1.0.0'
        })
    
    def _build_url(self, endpoint: str) -> str:
        """
        构建完整的API URL

        Args:
            endpoint: API端点

        Returns:
            完整的URL
        """
        from config.settings import AppSettings
        # 使用动态配置获取API基础URL
        base_url = AppSettings.get_api_base_url()
        # 确保endpoint不以/开头，避免urljoin替换整个路径
        endpoint = endpoint.lstrip('/')
        full_url = urljoin(base_url + '/', endpoint)
        return full_url
    
    def _prepare_headers(self, appid: str, appsecret: str, additional_headers: Dict[str, str] = None) -> Dict[str, str]:
        """
        准备请求头
        
        Args:
            appid: 应用ID
            appsecret: 应用密钥
            additional_headers: 额外的请求头
            
        Returns:
            完整的请求头字典
        """
        headers = {
            'X-App-Id': appid,
            'X-App-Secret': appsecret
        }
        
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    def _handle_response(self, response: requests.Response) -> Tuple[bool, Dict[str, Any], str]:
        """
        处理HTTP响应

        Args:
            response: HTTP响应对象

        Returns:
            (是否成功, 响应数据, 错误消息)
        """
        try:
            # 检查HTTP状态码
            if response.status_code == 200:
                try:
                    data = response.json()
                    return True, data, ""
                except json.JSONDecodeError:
                    return True, {"raw_content": response.text}, ""
            else:
                error_msg = f"HTTP {response.status_code}: {response.reason}"
                try:
                    error_data = response.json()
                    # 处理Laravel的错误响应格式
                    if 'message' in error_data:
                        error_msg = error_data['message']
                        # 如果有详细的验证错误信息，也包含进来
                        if 'errors' in error_data:
                            error_details = []
                            errors = error_data['errors']
                            if isinstance(errors, dict):
                                for field, messages in errors.items():
                                    if isinstance(messages, list):
                                        error_details.extend([f"{field}: {msg}" for msg in messages])
                                    else:
                                        error_details.append(f"{field}: {messages}")
                            if error_details:
                                error_msg += f" - {'; '.join(error_details)}"
                    elif 'error' in error_data:
                        error_msg = error_data['error']
                except json.JSONDecodeError:
                    pass

                return False, {}, error_msg

        except Exception as e:
            return False, {}, f"响应处理错误: {str(e)}"
    
    def get(self, endpoint: str, appid: str, appsecret: str, params: Dict[str, Any] = None, 
            headers: Dict[str, str] = None) -> Tuple[bool, Dict[str, Any], str]:
        """
        发送GET请求
        
        Args:
            endpoint: API端点
            appid: 应用ID
            appsecret: 应用密钥
            params: 查询参数
            headers: 额外的请求头
            
        Returns:
            (是否成功, 响应数据, 错误消息)
        """
        try:
            url = self._build_url(endpoint)
            request_headers = self._prepare_headers(appid, appsecret, headers)
            
            response = self.session.get(
                url,
                params=params,
                headers=request_headers,
                timeout=self.timeout
            )
            return self._handle_response(response)
            
        except requests.exceptions.Timeout:
            return False, {}, "请求超时"
        except requests.exceptions.ConnectionError:
            return False, {}, "连接错误，请检查网络"
        except requests.exceptions.RequestException as e:
            return False, {}, f"请求错误: {str(e)}"
        except Exception as e:
            return False, {}, f"未知错误: {str(e)}"
    
    def post(self, endpoint: str, appid: str, appsecret: str, data: Dict[str, Any] = None,
             json_data: Dict[str, Any] = None, headers: Dict[str, str] = None) -> Tuple[bool, Dict[str, Any], str]:
        """
        发送POST请求
        
        Args:
            endpoint: API端点
            appid: 应用ID
            appsecret: 应用密钥
            data: 表单数据
            json_data: JSON数据
            headers: 额外的请求头
            
        Returns:
            (是否成功, 响应数据, 错误消息)
        """
        try:
            url = self._build_url(endpoint)
            request_headers = self._prepare_headers(appid, appsecret, headers)
            
            response = self.session.post(
                url,
                data=data,
                json=json_data,
                headers=request_headers,
                timeout=self.timeout
            )
            
            return self._handle_response(response)
            
        except requests.exceptions.Timeout:
            return False, {}, "请求超时"
        except requests.exceptions.ConnectionError:
            return False, {}, "连接错误，请检查网络"
        except requests.exceptions.RequestException as e:
            return False, {}, f"请求错误: {str(e)}"
        except Exception as e:
            return False, {}, f"未知错误: {str(e)}"
    
    def put(self, endpoint: str, appid: str, appsecret: str, data: Dict[str, Any] = None,
            json_data: Dict[str, Any] = None, headers: Dict[str, str] = None) -> Tuple[bool, Dict[str, Any], str]:
        """
        发送PUT请求
        
        Args:
            endpoint: API端点
            appid: 应用ID
            appsecret: 应用密钥
            data: 表单数据
            json_data: JSON数据
            headers: 额外的请求头
            
        Returns:
            (是否成功, 响应数据, 错误消息)
        """
        try:
            url = self._build_url(endpoint)
            request_headers = self._prepare_headers(appid, appsecret, headers)
            
            response = self.session.put(
                url,
                data=data,
                json=json_data,
                headers=request_headers,
                timeout=self.timeout
            )
            
            return self._handle_response(response)
            
        except requests.exceptions.Timeout:
            return False, {}, "请求超时"
        except requests.exceptions.ConnectionError:
            return False, {}, "连接错误，请检查网络"
        except requests.exceptions.RequestException as e:
            return False, {}, f"请求错误: {str(e)}"
        except Exception as e:
            return False, {}, f"未知错误: {str(e)}"

    def post_file(self, endpoint: str, appid: str, appsecret: str, data: Dict[str, Any] = None,
                  files: Dict[str, Any] = None, headers: Dict[str, str] = None) -> Tuple[bool, Dict[str, Any], str]:
        """
        发送文件上传POST请求

        Args:
            endpoint: API端点
            appid: 应用ID
            appsecret: 应用密钥
            data: 表单数据
            files: 文件数据
            headers: 额外的请求头

        Returns:
            (是否成功, 响应数据, 错误消息)
        """
        try:
            url = self._build_url(endpoint)
            request_headers = self._prepare_headers(appid, appsecret, headers)

            # 文件上传时不能设置Content-Type，让requests自动处理multipart/form-data
            # 从headers中移除Content-Type，如果存在的话
            if 'Content-Type' in request_headers:
                del request_headers['Content-Type']

            # 创建临时session，不包含默认的Content-Type
            temp_session = requests.Session()
            temp_session.headers.update({
                'Accept': 'application/json',
                'User-Agent': 'TradeShopAssistant/1.0.0'
            })

            response = temp_session.post(
                url,
                data=data,
                files=files,
                headers=request_headers,
                timeout=self.timeout
            )

            return self._handle_response(response)

        except requests.exceptions.Timeout:
            return False, {}, "请求超时"
        except requests.exceptions.ConnectionError:
            return False, {}, "连接错误，请检查网络"
        except requests.exceptions.RequestException as e:
            return False, {}, f"请求错误: {str(e)}"
        except Exception as e:
            return False, {}, f"未知错误: {str(e)}"
