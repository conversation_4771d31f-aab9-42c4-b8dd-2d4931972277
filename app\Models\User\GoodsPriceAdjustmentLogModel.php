<?php
declare(strict_types=1);

namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GoodsPriceAdjustmentLogModel extends BaseModel
{
    protected $table = 'user_goods_price_adjustment_log';
    
    protected $fillable = [
        'goods_id',
        'sku_id',
        'old_price',
        'new_price',
        'modifier_id',
        'modified_at',
    ];

    /**
     * 关联商品
     */
    public function goods(): BelongsTo
    {
        return $this->belongsTo(GoodsModel::class, 'goods_id', 'id');
    }

    /**
     * 关联SKU
     */
    public function sku(): BelongsTo
    {
        return $this->belongsTo(GoodsSkuModel::class, 'sku_id', 'id');
    }

    /**
     * 关联修改人
     */
    public function modifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'modifier_id', 'id');
    }

    /**
     * 作用域：按商品ID筛选
     */
    public function scopeByGoodsId($query, int $goodsId)
    {
        return $query->where('goods_id', $goodsId);
    }

    /**
     * 作用域：按SKU ID筛选
     */
    public function scopeBySkuId($query, int $skuId)
    {
        return $query->where('sku_id', $skuId);
    }

    /**
     * 作用域：按修改人ID筛选
     */
    public function scopeByModifierId($query, int $modifierId)
    {
        return $query->where('modifier_id', $modifierId);
    }

    /**
     * 作用域：按修改时间范围筛选
     */
    public function scopeByModifiedDateRange($query, $startDate = null, $endDate = null)
    {
        if ($startDate) {
            $query->where('modified_at', '>=', $startDate);
        }
        if ($endDate) {
            $query->where('modified_at', '<=', $endDate . ' 23:59:59');
        }
        return $query;
    }

    /**
     * 作用域：按修改时间排序
     */
    public function scopeOrderByModified($query, string $direction = 'desc')
    {
        return $query->orderBy('modified_at', $direction);
    }

    /**
     * 获取价格变化金额
     */
    public function getPriceChangeAmount(): float
    {
        return (float)($this->new_price - $this->old_price);
    }

    /**
     * 获取价格变化百分比
     */
    public function getPriceChangePercentage(): float
    {
        if ($this->old_price == 0) {
            return 0;
        }
        return round(($this->new_price - $this->old_price) / $this->old_price * 100, 2);
    }

    /**
     * 是否为价格上涨
     */
    public function isPriceIncrease(): bool
    {
        return $this->new_price > $this->old_price;
    }

    /**
     * 是否为价格下降
     */
    public function isPriceDecrease(): bool
    {
        return $this->new_price < $this->old_price;
    }

    /**
     * 获取价格变化类型
     */
    public function getPriceChangeType(): string
    {
        if ($this->isPriceIncrease()) {
            return 'increase';
        } elseif ($this->isPriceDecrease()) {
            return 'decrease';
        } else {
            return 'unchanged';
        }
    }

    /**
     * 获取价格变化描述
     */
    public function getPriceChangeDescription(): string
    {
        $change = $this->getPriceChangeAmount();
        $percentage = $this->getPriceChangePercentage();
        
        if ($change > 0) {
            return "上涨 {$change} ({$percentage}%)";
        } elseif ($change < 0) {
            return "下降 " . abs($change) . " (" . abs($percentage) . "%)";
        } else {
            return "无变化";
        }
    }
}
