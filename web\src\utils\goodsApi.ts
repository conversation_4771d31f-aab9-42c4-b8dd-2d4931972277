/**
 * 商品管理API接口
 * 参考storeApi.ts的结构，通过background页面发送请求
 */
import { sendRequestViaBackground } from './api'
import { getApiUrl } from './apiConfig'

// 商品接口类型定义
export interface Goods {
  id?: number
  type_name: string
  source_url: string
  mall_id: number
  goods_id: number
  goods_name: string
  goods_thumb:string
  goods_pic:Array<string>
  goods_video:string
  goods_sku_num:number
  first_sku:string
  first_sku_price:number
  first_sku_currentcy:string
  formatted_skus:Array<{
    id:number
    sku:string
    price:number
    currentcy:string
    thumb_url?:string
  }>
  cat_id:number
  cat_name:string,
  front_cat_id_1:number
  front_cat_id_2:number
  front_cat_desc:string
  front_cat_2_path_name:string
  status: number  // 状态：1正常 0禁用
  is_price_modified?: number  // 是否修改过价格：1是 0否
  price_adjustment_info?: {
    is_adjusted: boolean
    display_text: string
    latest_adjustment_time: string | null
  }
  operator_info?: {
    type: string
    name: string
    created_at: string
    updated_at: string
  }
  created_at?: string
  updated_at?: string
}

export interface GoodsListParams {
  page: number
  pageSize: number
  goods_name?: string
  goods_id?: number
  status?: number  // 状态搜索参数：1正常 0禁用
  directory_id?: number  // 目录ID筛选参数
  price_adjusted?: string  // 价格调整状态：1已调整 0未调整
  cat_adjusted?: string  // 分类调整状态：1已调整 0未调整
  time_type?: string  // 时间类型：created_at/updated_at
  start_date?: string  // 开始日期
  end_date?: string  // 结束日期
  sub_account_name?: string  // 子账号姓名
  sub_account_phone?: string  // 子账号手机号
  only_sub_account?: boolean  // 是否只显示子账号商品
  sort_field?: string  // 排序字段
  sort_order?: string  // 排序方向
}

export interface GoodsListResponse {
  list: Goods[]
  pagination: {
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
    currentPage: number
    pageSize: number
  }
}

export interface BatchUpdateParams {
  ids: number[]
  status?: number  // 批量状态设置
  goods_price?: number  // 批量价格设置
}

export interface SkuPriceAdjustment {
  sku_id: number
  new_price: number
}

export interface AdjustSkuPriceParams {
  goods_id: number
  sku_adjustments: SkuPriceAdjustment[]
}

export interface PriceAdjustmentLog {
  id: number
  sku_id: number
  sku_spec_values: string
  old_price: number
  new_price: number
  price_change: number
  price_change_percentage: number
  price_change_type: string
  price_change_description: string
  modifier_id: number
  modifier_name: string
  modified_at: string
  created_at: string
}

export interface PriceAdjustmentLogsParams {
  goods_id: number
  sku_id?: number
  page: number
  pageSize: number
}

export interface PriceAdjustmentLogsResponse {
  list: PriceAdjustmentLog[]
  pagination: {
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
    currentPage: number
    pageSize: number
  }
}



/**
 * 获取商品列表
 * @param params 查询参数
 * @returns 商品列表响应
 */
export const getGoodsList = async (params: GoodsListParams): Promise<GoodsListResponse> => {
  const url = await getApiUrl('apiGoodsListUrl');
  console.log('获取商品列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getGoodsList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 更新商品
 * @param goods 商品信息
 * @returns 更新结果
 */
export const updateGoods = async (goods: Partial<Goods>): Promise<Goods> => {
  const baseUrl = await getApiUrl('apiGoodsUpdateUrl');
  const url = `${baseUrl}`;

  return sendRequestViaBackground({
    funName: 'updateGoods',
    url,
    method: 'post',
    data: goods,
    auth: true
  });
};

/**
 * 批量更新商品
 * @param params 批量更新参数
 * @returns 更新结果
 */
export const batchUpdateGoods = async (params: BatchUpdateParams): Promise<void> => {
  const url = await getApiUrl('apiGoodsBatchUpdateUrl');

  return sendRequestViaBackground({
    funName: 'batchUpdateGoods',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};

/**
 * 获取商品详情
 * @param id 商品ID
 * @returns 商品详情
 */
export const getGoodsDetail = async (id: number): Promise<Goods> => {
  const baseUrl = await getApiUrl('apiGoodsDetailUrl');
  const url = `${baseUrl}/${id}`;

  return sendRequestViaBackground({
    funName: 'getGoodsDetail',
    url,
    method: 'get',
    auth: true
  });
};

/**
 * 删除商品
 * @param id 商品ID
 * @returns 删除结果
 */
export const deleteGoods = async (id: number): Promise<any> => {
  const url = await getApiUrl('apiGoodsDeleteUrl');

  return sendRequestViaBackground({
    funName: 'deleteGoods',
    url,
    method: 'post',
    data: { id },
    auth: true
  });
};

/**
 * 批量删除商品
 * @param ids 商品ID数组
 * @returns 删除结果
 */
export const batchDeleteGoods = async (ids: number[]): Promise<any> => {
  const url = await getApiUrl('apiGoodsBatchDeleteUrl');

  return sendRequestViaBackground({
    funName: 'batchDeleteGoods',
    url,
    method: 'post',
    data: { ids },
    auth: true
  });
};

/**
 * 调整SKU价格
 * @param params 价格调整参数
 * @returns 调整结果
 */
export const adjustSkuPrice = async (params: AdjustSkuPriceParams): Promise<{message: string, adjusted_count: number}> => {
  const url = await getApiUrl('apiGoodsAdjustSkuPriceUrl');

  return sendRequestViaBackground({
    funName: 'adjustSkuPrice',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};

/**
 * 获取价格调整日志
 * @param params 查询参数
 * @returns 价格调整日志列表
 */
export const getPriceAdjustmentLogs = async (params: PriceAdjustmentLogsParams): Promise<PriceAdjustmentLogsResponse> => {
  const url = await getApiUrl('apiGoodsPriceAdjustmentLogsUrl');

  return sendRequestViaBackground({
    funName: 'getPriceAdjustmentLogs',
    url,
    method: 'get',
    params,
    auth: true
  });
};

