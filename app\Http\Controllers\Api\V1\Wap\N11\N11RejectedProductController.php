<?php

namespace App\Http\Controllers\Api\V1\Wap\N11;

use App\Http\Controllers\Api\Controller;
use App\Service\N11\N11RejectedProductService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Validator;

class N11RejectedProductController extends Controller
{
    protected $n11RejectedProductService;

    public function __construct(N11RejectedProductService $n11RejectedProductService)
    {
        $this->n11RejectedProductService = $n11RejectedProductService;
    }

    /**
     * 批量保存重新上传商品
     */
    public function batchSave(Request $request): JsonResponse
    {

        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $products = $request->input('products');
        $result = $this->n11RejectedProductService->batchSaveRejectedProducts($userId, $products);
        return $this->apiSuccess($result);
    }

    /**
     * 获取待处理商品数量
     */
    public function getPendingCount(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $count = $this->n11RejectedProductService->getPendingCount($userId);
        return $this->apiSuccess(['pending_count' => $count]);
    }

    /**
     * 获取下一个待处理商品
     */
    public function getNextPending(Request $request): JsonResponse
    {

        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $product = $this->n11RejectedProductService->getNextPendingProduct($userId);
        if (!$product) {
            return $this->apiSuccess(['product' => null, 'message' => '没有待处理的商品']);
        }
        return $this->apiSuccess(['product' => $product]);

    }

    /**
     * 更新商品处理状态
     */
    public function updateStatus(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $productId = $request->input('product_id');
        $status = $request->input('status');
        $this->n11RejectedProductService->updateProductStatus($userId, $productId, $status);
        return $this->apiSuccess(['message' => '状态更新成功']);

    }

    /**
     * 批量更新商品状态为拒绝状态
     */
    public function batchUpdateStatus(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $stockCodes = $request->input('stock_codes');
        if(!is_array($stockCodes) || empty($stockCodes)){
            $stockCodes = [];
        }
        // 调用服务层方法批量更新状态
        $result = $this->n11RejectedProductService->batchUpdateRejectedStatus($userId, $stockCodes);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取处理统计信息
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $statistics = $this->n11RejectedProductService->getProcessingStatistics($userId);
        return $this->apiSuccess($statistics);
    }
} 