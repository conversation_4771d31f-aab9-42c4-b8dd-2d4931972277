<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class CleanDuplicateTaskDetailRecordsCommand extends Command
{
    /**
     * 命令名称和参数
     *
     * @var string
     */
    protected $signature = 'task:clean-duplicate-records';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '清理user_task_detail表中的重复记录，为添加唯一索引做准备';

    /**
     * 统计信息
     */
    private $stats = [
        'duplicate_groups' => 0,
        'total_duplicate_records' => 0,
        'records_to_keep' => 0,
        'records_to_delete' => 0,
        'deleted_detail_records' => 0,
        'deleted_goods_name_records' => 0,
        'deleted_up_params_records' => 0,
        'affected_tasks' => 0,
        'updated_completed_tasks' => 0,
        'failed_groups' => [],
        'duplicate_data' => [],
        'task_ids' => [],
        'sample_records' => []
    ];

    /**
     * 执行命令
     */
    public function handle()
    {
        try {
            $this->info('开始清理user_task_detail表中的重复记录...');
            
            // 1. 查找并分析重复记录
            if (!$this->findDuplicateRecords()) {
                return Command::FAILURE;
            }

            // 2. 显示统计信息并确认
            if (!$this->confirmExecution()) {
                $this->info('操作已取消');
                return Command::SUCCESS;
            }

            // 3. 执行清理操作
            $this->executeCleanup();

            // 4. 更新任务统计信息
            $this->updateTaskStatistics();

            // 5. 显示结果统计
            $this->showResults();

            // 6. 提示添加索引
            $this->showIndexCommand();

            return Command::SUCCESS;
            
        } catch (Exception $e) {
            $this->error("执行过程中发生错误: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 查找重复记录
     */
    private function findDuplicateRecords(): bool
    {
        $this->info('正在查找重复记录...');
        
        try {
            // 查找所有重复的记录组合（基于新的索引条件）
            $duplicateGroups = DB::table('user_task_detail')
                ->select('user_id', 'user_account_id', 'user_goods_id', 'user_goods_sku_id', DB::raw('COUNT(*) as count'))
                ->groupBy('user_id', 'user_account_id', 'user_goods_id', 'user_goods_sku_id')
                ->having('count', '>', 1)
                ->get();

            $this->stats['duplicate_groups'] = $duplicateGroups->count();
            
            if ($this->stats['duplicate_groups'] == 0) {
                $this->info('没有找到重复记录，可以直接添加唯一索引');
                return false;
            }

            $this->info("找到 {$this->stats['duplicate_groups']} 个重复记录组合");

            // 分析每个重复组合，决定保留哪些记录
            $this->analyzeDuplicateGroups($duplicateGroups);

            return true;
            
        } catch (Exception $e) {
            $this->error("查找重复记录时发生错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 分析重复记录组合
     */
    private function analyzeDuplicateGroups($duplicateGroups): void
    {
        $this->info('正在分析重复记录组合...');
        
        $progressBar = $this->output->createProgressBar($duplicateGroups->count());
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');
        $progressBar->setMessage('准备开始分析...');
        $progressBar->start();

        foreach ($duplicateGroups as $group) {
            $progressBar->setMessage("分析组合: user_id={$group->user_id}, user_account_id={$group->user_account_id}");
            
            // 获取这个组合的所有记录
            $records = DB::table('user_task_detail')
                ->where('user_id', $group->user_id)
                ->where('user_account_id', $group->user_account_id)
                ->where('user_goods_id', $group->user_goods_id)
                ->where('user_goods_sku_id', $group->user_goods_sku_id)
                ->orderBy('id')
                ->get();

            $this->stats['total_duplicate_records'] += $records->count();

            // 收集涉及的任务ID
            foreach ($records as $record) {
                if (!in_array($record->task_id, $this->stats['task_ids'])) {
                    $this->stats['task_ids'][] = $record->task_id;
                }
            }

            // 决定保留哪个记录（保留ID最小的）
            $keepRecord = $this->decideKeepRecord($records);
            $deleteRecords = $records->filter(function ($record) use ($keepRecord) {
                return $record->id != $keepRecord->id;
            });

            $this->stats['records_to_keep']++;
            $this->stats['records_to_delete'] += $deleteRecords->count();

            // 保存分析结果
            $this->stats['duplicate_data'][] = [
                'group' => $group,
                'keep_record' => $keepRecord,
                'delete_records' => $deleteRecords->toArray()
            ];

            // 收集前10个重复记录组合的详细信息用于人工核验
            if (count($this->stats['sample_records']) < 10) {
                $this->stats['sample_records'][] = [
                    'group' => $group,
                    'keep_record' => $keepRecord,
                    'delete_records' => $deleteRecords->toArray(),
                    'total_in_group' => $records->count()
                ];
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);
        $this->info('重复记录分析完成');
        
        $this->stats['affected_tasks'] = count($this->stats['task_ids']);
        $this->info("涉及的任务数量: {$this->stats['affected_tasks']}");
    }

    /**
     * 决定保留哪个记录
     */
    private function decideKeepRecord($records)
    {
        // 直接返回ID最小的记录
        return $records->sortBy('id')->first();
    }

    /**
     * 确认执行
     */
    private function confirmExecution(): bool
    {
        $this->newLine();
        $this->info('=== 清理统计 ===');
        $this->info("重复记录组合数: {$this->stats['duplicate_groups']}");
        $this->info("总重复记录数: {$this->stats['total_duplicate_records']}");
        $this->info("将保留的记录数: {$this->stats['records_to_keep']}");
        $this->info("将删除的记录数: {$this->stats['records_to_delete']}");
        $this->info("涉及的任务数量: {$this->stats['affected_tasks']}");
        $this->newLine();

        // 显示重复记录样本供人工核验
        $this->showSampleRecords();
        
        $this->warn('此操作将会：');
        $this->warn('1. 删除重复的任务详情记录');
        $this->warn('2. 删除相关的商品名称记录');
        $this->warn('3. 删除相关的上传参数记录');
        $this->warn('4. 更新已完成任务的统计信息');
        $this->warn('5. 为添加唯一索引做准备');
        $this->newLine();
        
        return $this->confirm('确认要执行清理操作吗？此操作不可逆！');
    }

    /**
     * 执行清理操作
     */
    private function executeCleanup(): void
    {
        $this->info('开始执行清理操作...');
        
        $progressBar = $this->output->createProgressBar(count($this->stats['duplicate_data']));
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');
        $progressBar->setMessage('准备开始清理...');
        $progressBar->start();

        foreach ($this->stats['duplicate_data'] as $data) {
            $group = $data['group'];
            $deleteRecords = $data['delete_records'];
            
            $progressBar->setMessage("清理组合: user_id={$group->user_id}, user_account_id={$group->user_account_id}");
            
            DB::beginTransaction();
            
            try {
                $deleteIds = array_column($deleteRecords, 'id');
                
                if (!empty($deleteIds)) {
                    // 删除 user_task_detail_goods_name 中的关联记录
                    $deletedGoodsName = DB::table('user_task_detail_goods_name')
                        ->whereIn('task_detail_id', $deleteIds)
                        ->delete();
                    $this->stats['deleted_goods_name_records'] += $deletedGoodsName;

                    // 删除 user_task_detail_up_params 中的关联记录
                    $deletedUpParams = DB::table('user_task_detail_up_params')
                        ->whereIn('task_detail_id', $deleteIds)
                        ->delete();
                    $this->stats['deleted_up_params_records'] += $deletedUpParams;

                    // 删除重复的主记录
                    $deletedDetailRecords = DB::table('user_task_detail')
                        ->whereIn('id', $deleteIds)
                        ->delete();
                    $this->stats['deleted_detail_records'] += $deletedDetailRecords;
                }

                DB::commit();
                
            } catch (Exception $e) {
                DB::rollback();
                $this->stats['failed_groups'][] = [
                    'group' => $group,
                    'error' => $e->getMessage()
                ];
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);
        $this->info('清理操作完成');
    }

    /**
     * 显示重复记录样本供人工核验
     */
    private function showSampleRecords(): void
    {
        if (empty($this->stats['sample_records'])) {
            return;
        }

        $this->info('=== 重复记录样本（前10个组合）===');
        $this->newLine();

        foreach ($this->stats['sample_records'] as $index => $sample) {
            $group = $sample['group'];
            $keepRecord = $sample['keep_record'];
            $deleteRecords = $sample['delete_records'];
            $totalInGroup = $sample['total_in_group'];

            $this->info("样本 " . ($index + 1) . ":");
            $this->info("  重复条件: user_id={$group->user_id}, user_account_id={$group->user_account_id}, user_goods_id={$group->user_goods_id}, user_goods_sku_id={$group->user_goods_sku_id}");
            $this->info("  该组合共有 {$totalInGroup} 条重复记录");
            
            $this->info("  🟢 将保留的记录:");
            $this->info("    ID: {$keepRecord->id}, task_id: {$keepRecord->task_id}, status: {$keepRecord->status}, 商品名称: " . mb_substr($keepRecord->goods_name, 0, 30) . "...");
            
            $this->info("  🔴 将删除的记录:");
            foreach ($deleteRecords as $deleteRecord) {
                $this->info("    ID: {$deleteRecord->id}, task_id: {$deleteRecord->task_id}, status: {$deleteRecord->status}, 商品名称: " . mb_substr($deleteRecord->goods_name, 0, 30) . "...");
            }
            
            $this->newLine();
        }

        $this->warn('请仔细核验以上样本记录，确认删除逻辑是否正确！');
        $this->newLine();
    }

    /**
     * 更新任务统计信息
     */
    private function updateTaskStatistics(): void
    {
        if (empty($this->stats['task_ids'])) {
            return;
        }

        $this->info('开始更新任务统计信息...');
        
        // 查找已完成的任务
        $completedTasks = DB::table('user_task')
            ->whereIn('id', $this->stats['task_ids'])
            ->where('task_over', 1)
            ->get();

        if ($completedTasks->isEmpty()) {
            $this->info('没有需要更新统计的已完成任务');
            return;
        }

        $progressBar = $this->output->createProgressBar($completedTasks->count());
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');
        $progressBar->setMessage('准备开始更新统计...');
        $progressBar->start();

        foreach ($completedTasks as $task) {
            $progressBar->setMessage("更新任务统计: task_id={$task->id}");
            
            try {
                // 统计该任务下不同user_goods_id的数量作为goods_count
                $goodsCount = DB::table('user_task_detail')
                    ->where('task_id', $task->id)
                    ->distinct('user_goods_id')
                    ->count('user_goods_id');

                // 统计该任务下的详细记录数量作为sku_count和task_count
                $detailCount = DB::table('user_task_detail')
                    ->where('task_id', $task->id)
                    ->count();

                // 更新任务统计
                DB::table('user_task')
                    ->where('id', $task->id)
                    ->update([
                        'goods_count' => $goodsCount,
                        'sku_count' => $detailCount,
                        'task_count' => $detailCount,
                        'updated_at' => now()
                    ]);

                $this->stats['updated_completed_tasks']++;
                
            } catch (Exception $e) {
                $this->warn("更新任务 {$task->id} 统计信息失败: " . $e->getMessage());
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);
        $this->info('任务统计信息更新完成');
    }

    /**
     * 显示结果统计
     */
    private function showResults(): void
    {
        $this->info('=== 清理结果统计 ===');
        $this->info("删除的任务详情记录: {$this->stats['deleted_detail_records']}");
        $this->info("删除的商品名称记录: {$this->stats['deleted_goods_name_records']}");
        $this->info("删除的上传参数记录: {$this->stats['deleted_up_params_records']}");
        $this->info("涉及的任务数量: {$this->stats['affected_tasks']}");
        $this->info("更新统计的已完成任务数量: {$this->stats['updated_completed_tasks']}");
        
        if (!empty($this->stats['failed_groups'])) {
            $this->warn("处理失败的组合数: " . count($this->stats['failed_groups']));
            foreach ($this->stats['failed_groups'] as $failed) {
                $group = $failed['group'];
                $this->warn("失败组合: user_id={$group->user_id}, user_account_id={$group->user_account_id} - {$failed['error']}");
            }
        }
        
        // 计算成功率
        $totalGroups = count($this->stats['duplicate_data']);
        $successGroups = $totalGroups - count($this->stats['failed_groups']);
        if ($totalGroups > 0) {
            $successRate = round($successGroups / $totalGroups * 100, 2);
            $this->info("处理成功率: {$successRate}%");
        }
        
        $this->newLine();
        $this->info('清理操作已完成！');
    }

    /**
     * 显示添加索引的命令
     */
    private function showIndexCommand(): void
    {
        $this->newLine();
        $this->info('=== 添加唯一索引 ===');
        $this->info('现在可以执行以下SQL命令添加唯一索引：');
        $this->newLine();
        $this->line('ALTER TABLE `user_task_detail` ADD UNIQUE INDEX(`user_id`, `user_account_id`, `user_goods_id`, `user_goods_sku_id`);');
        $this->newLine();
        $this->warn('请在数据库中手动执行上述SQL命令！');
    }
} 