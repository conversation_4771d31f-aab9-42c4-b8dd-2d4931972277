// 导入Chrome扩展消息发送函数
declare const chrome: any;

/**
 * 通过background页面发送请求
 */
function sendRequestViaBackground(url: string, options: any): Promise<any> {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      url: url,
      method: options.method || 'get',
      pramas: options.body ? JSON.parse(options.body) : {},
      headers: options.headers || {},
      auth: options.auth || false,
      encrypto: options.encrypto || false,
      timeout: 59000
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }

      if (response && response[0]) {
        const result = response[0];
        if (result.status >= 200 && result.status < 300) {
          resolve({ success: true, data: result.data });
        } else {
          reject(new Error(`请求失败: ${result.status}`));
        }
      } else {
        reject(new Error('请求未返回有效数据'));
      }
    });
  });
}

// N11重新上传商品数据类型定义
export interface N11RejectedProduct {
  id: string
  title: string
  commission: number
  imageUrl: string
  brand: string
  commissionRateValue: number
  stockCode: string
  barcode: string
  productMainId: string
  salesPrice: number
  listPrice: number
  quantity: number
  statusOriginal: string
  groupId: string
  categoryName: string
  preparingTime: number
  catalogId: string
  shipmentTemplate: string
  inApprovalReason: any[]
  rejectInfo: string
  vatRate: number
  skuId: string
  productInfo: any
}

// 查询响应数据类型
export interface N11RejectedProductsResponse {
  content: N11RejectedProduct[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

/**
 * 查询N11重新上传商品列表
 * @param pageNumber 页码（从0开始）
 * @param pageSize 每页大小，默认100
 * @returns 查询结果
 */
export async function queryN11RejectedProducts(
  pageNumber: number,
  pageSize: number = 100
): Promise<N11RejectedProductsResponse> {
  const requestData = {
    page: pageNumber,
    size: pageSize,
    sort: 'id,desc',
    tab: 'catalog_rejected', // 重新上传商品标签
    searchText: '',
    categoryId: '',
    brandId: '',
    priceMin: '',
    priceMax: '',
    stockMin: '',
    stockMax: ''
  }

  const options = {
    method: 'post',
    headers: {
      'Accept': 'application/json, text/plain, */*',
      'Content-Type': 'application/json',
      'Origin': 'https://so.n11.com',
      'Referer': 'https://so.n11.com/magaza/urun-listesi',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    },
    body: JSON.stringify(requestData),
    auth: false,
    encrypto: false
  }

  try {
    const response = await sendRequestViaBackground(
      'https://so.n11.com/api/seller-product-query/api/v1/product/internal/search',
      options
    )

    if (response.success && response.data) {
      return response.data
    } else {
      throw new Error(response.message || '查询重新上传商品失败')
    }
  } catch (error) {
    console.error('查询N11重新上传商品失败:', error)
    throw error
  }
}

/**
 * 获取所有重新上传商品（原API方式）
 * @param onProgress 进度回调函数
 * @returns 所有重新上传商品数据
 */
export async function getAllRejectedProducts(
  onProgress?: (current: number, total: number) => void
): Promise<N11RejectedProduct[]> {
  try {
    // 首先获取第一页，确定总页数
    const firstPageResponse = await queryN11RejectedProducts(0, 100)
    const totalPages = firstPageResponse.totalPages
    const totalElements = firstPageResponse.totalElements

    console.log(`发现 ${totalElements} 个重新上传商品，共 ${totalPages} 页`)

    if (totalPages === 0) {
      return []
    }

    let allProducts: N11RejectedProduct[] = []
    allProducts.push(...firstPageResponse.content)

    // 报告第一页进度
    if (onProgress) {
      onProgress(1, totalPages)
    }

    // 获取剩余页面
    for (let page = 1; page < totalPages; page++) {
      try {
        const response = await queryN11RejectedProducts(page, 100)
        allProducts.push(...response.content)

        // 报告进度
        if (onProgress) {
          onProgress(page + 1, totalPages)
        }

        // 添加小延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 100))
      } catch (error) {
        console.error(`获取第 ${page + 1} 页数据失败:`, error)
        // 继续处理下一页，不中断整个流程
      }
    }

    console.log(`成功获取 ${allProducts.length} 个重新上传商品`)
    return allProducts

  } catch (error) {
    console.error('获取所有重新上传商品失败:', error)
    throw error
  }
}

/**
 * 通过RPC方式获取所有重新上传商品
 * @param onProgress 进度回调函数
 * @returns 所有重新上传商品数据
 */
export async function getAllRejectedProductsViaRPC(
  onProgress?: (current: number, total: number) => void
): Promise<N11RejectedProduct[]> {
  try {
    console.log('开始通过RPC方式获取重新上传商品数据...');

    // 检查RPC环境支持
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      throw new Error('RPC环境不支持：未检测到Chrome扩展环境');
    }

    // 创建一个唯一的请求ID用于进度回调
    const requestId = `rpc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 设置进度监听器
    let progressListener: ((message: any) => void) | null = null;
    if (onProgress) {
      progressListener = (message: any) => {
        console.log('[Content Script] 接收到消息:', message);
        if (message.type === 'rpc_progress' && message.requestId === requestId) {
          console.log(`[Content Script] 处理进度消息: ${message.current}/${message.total}`);
          onProgress(message.current, message.total);
        }
      };
      chrome.runtime.onMessage.addListener(progressListener);
      console.log(`[Content Script] 已设置进度监听器，requestId: ${requestId}`);
    }

    try {
      // 通过background发送RPC请求
      const result = await new Promise<N11RejectedProduct[]>((resolve, reject) => {
        chrome.runtime.sendMessage({
          funType: 'rpcPageControl',
          action: 'getAllRejectedProducts',
          requestId: requestId
        }, (response: any) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
            return;
          }

          if (response && Array.isArray(response)) {
            resolve(response);
          } else if (response && response.success === false) {
            reject(new Error(response.message || 'RPC获取数据失败'));
          } else {
            reject(new Error('RPC返回数据格式不正确'));
          }
        });
      });

      console.log(`RPC方式成功获取 ${result.length} 个重新上传商品`);
      return result;
    } finally {
      // 清理进度监听器
      if (progressListener) {
        chrome.runtime.onMessage.removeListener(progressListener);
      }
    }

  } catch (error) {
    console.error('RPC方式获取重新上传商品失败:', error);
    throw error;
  }
}

/**
 * 检测当前环境是否支持RPC方式（简化版，只检查基本Chrome环境）
 */
export function isRPCSupported(): boolean {
  try {
    console.log('=== RPC支持检查开始 ===');

    // 只检查基本Chrome环境，具体API检查交给Background
    if (typeof chrome === 'undefined') {
      console.log('❌ chrome对象不存在');
      return false;
    }
    console.log('✅ chrome对象存在');

    if (!chrome.runtime) {
      console.log('❌ chrome.runtime不存在');
      return false;
    }
    console.log('✅ chrome.runtime存在');

    console.log('✅ 基本Chrome环境支持，具体API支持将由Background检查');
    console.log('=== RPC支持检查结束 ===');

    return true; // 基本环境支持，具体API支持交给Background判断
  } catch (error: any) {
    console.error('RPC支持检查异常:', error);
    return false;
  }
}

/**
 * 通过Background获取RPC环境状态（不在Content Script中直接检查API）
 */
export async function getRPCStatusViaBackground(): Promise<any> {
  try {
    console.log('通过Background获取RPC状态...');

    if (!isRPCSupported()) {
      return { supported: false, message: 'Chrome环境不支持' };
    }

    const result = await new Promise<any>((resolve) => {
      chrome.runtime.sendMessage({
        funType: 'rpcPageControl',
        action: 'getRPCStatus'
      }, (response: any) => {
        if (chrome.runtime.lastError) {
          console.error('Background RPC状态检查失败:', chrome.runtime.lastError);
          resolve({ supported: false, message: chrome.runtime.lastError.message });
          return;
        }
        resolve(response || { supported: false, message: '未知状态' });
      });
    });

    console.log('Background返回的RPC状态:', result);
    return result;
  } catch (error: any) {
    console.error('获取Background RPC状态失败:', error);
    return { supported: false, message: error.message };
  }
}

/**
 * 获取RPC功能状态（保持向后兼容）
 */
export async function getRPCStatus(): Promise<any> {
  return await getRPCStatusViaBackground();
}

/**
 * 检测当前是否在N11页面环境
 */
export async function isN11Environment(): Promise<boolean> {
  try {
    if (!isRPCSupported()) {
      return false;
    }

    const result = await new Promise<boolean>((resolve) => {
      chrome.runtime.sendMessage({
        funType: 'rpcPageControl',
        action: 'checkN11Page'
      }, (response: boolean) => {
        if (chrome.runtime.lastError) {
          resolve(false);
          return;
        }
        resolve(!!response);
      });
    });

    return result;
     } catch (error: any) {
     console.error('检测N11环境失败:', error);
     return false;
   }
}

/**
 * 获取所有重新上传商品（带回退机制）
 * 优先使用RPC方式，在不支持的环境下回退到原API方式
 * @param onProgress 进度回调函数
 * @param forceRPC 是否强制使用RPC方式
 * @returns 所有重新上传商品数据
 */
export async function getAllRejectedProductsWithFallback(
  onProgress?: (current: number, total: number) => void,
  forceRPC: boolean = false
): Promise<N11RejectedProduct[]> {
  try {
    // 检查RPC支持和N11环境
    const rpcSupported = isRPCSupported();
    const isN11Env = true;//强制赋值为true 这里不要调用     const isN11Env = await isN11Environment();

    console.log('环境检测结果:', { rpcSupported, isN11Env, forceRPC });
    if(forceRPC){
      console.log('强制使用RPC方式获取数据...');
    }
    // 如果强制使用RPC或者环境支持RPC且在N11页面，则使用RPC方式
    if (forceRPC || (rpcSupported && isN11Env)) {
      console.log('使用RPC方式获取数据...');
      try {
        return await getAllRejectedProductsViaRPC(onProgress);
      } catch (rpcError) {
        console.error('RPC方式失败:', rpcError);

        if (forceRPC) {
          throw rpcError; // 强制RPC模式下，直接抛出错误
        }

        console.log('回退到API方式...');
        // 回退到API方式
        return await getAllRejectedProducts(onProgress);
      }
    } else {
      console.log('使用传统API方式获取数据...');
      return await getAllRejectedProducts(onProgress);
    }

  } catch (error) {
    console.error('获取重新上传商品失败:', error);
    throw error;
     }
}
