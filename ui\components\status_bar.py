#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
状态栏组件
"""

from PyQt5.QtWidgets import QStatusBar, QLabel
from PyQt5.QtCore import Qt, QTimer


class StatusBarComponent(QStatusBar):
    """状态栏组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        # 创建左侧状态标签（常规日志信息）
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("QLabel { padding: 2px 5px; }")
        self.addWidget(self.status_label)

        # 创建右侧信息提示标签（用于友好提示信息）
        self.info_label = QLabel("")
        self.info_label.setStyleSheet("""
            QLabel {
                color: #0066cc;
                font-weight: normal;
                padding: 2px 8px;
                background-color: #e6f3ff;
                border: 1px solid #b3d9ff;
                border-radius: 3px;
                margin: 1px;
            }
        """)
        self.info_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.info_label.setVisible(False)  # 初始隐藏
        self.info_label.setMaximumWidth(400)  # 限制最大宽度
        self.info_label.setWordWrap(False)  # 不换行，使用省略号
        self.addPermanentWidget(self.info_label)

        # 创建右侧错误信息标签
        self.error_label = QLabel("")
        self.error_label.setStyleSheet("""
            QLabel {
                color: #ff0000;
                font-weight: bold;
                padding: 2px 8px;
                background-color: #ffe6e6;
                border: 1px solid #ffcccc;
                border-radius: 3px;
                margin: 1px;
            }
        """)
        self.error_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.error_label.setVisible(False)  # 初始隐藏
        self.error_label.setMaximumWidth(400)  # 限制最大宽度
        self.error_label.setWordWrap(False)  # 不换行，使用省略号
        self.addPermanentWidget(self.error_label)

        # 创建错误信息显示定时器
        self.error_timer = QTimer()
        self.error_timer.setSingleShot(True)
        self.error_timer.timeout.connect(self._hide_error_message)

        # 创建信息提示显示定时器
        self.info_timer = QTimer()
        self.info_timer.setSingleShot(True)
        self.info_timer.timeout.connect(self._hide_info_message)

        # 设置状态栏样式
        self.setStyleSheet("""
            QStatusBar {
                border-top: 1px solid #cccccc;
                background-color: #f0f0f0;
            }
        """)

    def update_status(self, message: str, level: str):
        """更新状态栏显示"""
        if level == "ERROR":
            # 错误信息显示在右侧错误标签
            if self.error_label:
                # 先隐藏信息提示，避免冲突
                if self.info_label and self.info_label.isVisible():
                    self._hide_info_message()
                    if self.info_timer.isActive():
                        self.info_timer.stop()

                # 处理长文本，如果超过50个字符则截断并添加省略号
                display_message = message
                if len(message) > 50:
                    display_message = message[:47] + "..."

                self.error_label.setText(display_message)
                self.error_label.setToolTip(message)  # 完整信息显示在工具提示中
                self.error_label.setVisible(True)
                # 设置8秒后自动隐藏错误信息（在5-10秒范围内）
                self.error_timer.start(8000)

            # 左侧状态栏仍显示常规样式的错误信息
            if self.status_label:
                self.status_label.setText(message)
                self.status_label.setStyleSheet("QLabel { color: #cc0000; padding: 2px 5px; }")
        else:
            # 非错误信息正常显示在左侧
            if self.status_label:
                self.status_label.setText(message)

                # 根据日志级别设置颜色
                if level == "WARNING":
                    self.status_label.setStyleSheet("QLabel { color: #ff8800; padding: 2px 5px; }")
                elif level == "SUCCESS":
                    self.status_label.setStyleSheet("QLabel { color: #008800; padding: 2px 5px; }")
                elif level == "INFO":
                    self.status_label.setStyleSheet("QLabel { color: #0066cc; padding: 2px 5px; }")
                else:
                    self.status_label.setStyleSheet("QLabel { color: black; padding: 2px 5px; }")

    def _hide_error_message(self):
        """隐藏错误信息"""
        if self.error_label:
            self.error_label.setVisible(False)
            self.error_label.setText("")
            self.error_label.setToolTip("")  # 清除工具提示

    def _hide_info_message(self):
        """隐藏信息提示"""
        if self.info_label:
            self.info_label.setVisible(False)
            self.info_label.setText("")
            self.info_label.setToolTip("")  # 清除工具提示

    def hide_error_message_manually(self):
        """手动隐藏错误信息"""
        if self.error_timer.isActive():
            self.error_timer.stop()
        self._hide_error_message()

    def hide_info_message_manually(self):
        """手动隐藏信息提示"""
        if self.info_timer.isActive():
            self.info_timer.stop()
        self._hide_info_message()

    def show_error_message(self, message: str, auto_hide_seconds: int = 8):
        """显示错误信息

        Args:
            message: 错误信息
            auto_hide_seconds: 自动隐藏时间（秒），默认8秒
        """
        if self.error_label:
            # 处理长文本
            display_message = message
            if len(message) > 50:
                display_message = message[:47] + "..."

            self.error_label.setText(display_message)
            self.error_label.setToolTip(message)
            self.error_label.setVisible(True)

            # 重新启动定时器
            if self.error_timer.isActive():
                self.error_timer.stop()
            self.error_timer.start(auto_hide_seconds * 1000)

    def show_info_message(self, message: str, auto_hide_seconds: int = 10):
        """显示信息提示

        Args:
            message: 信息提示内容
            auto_hide_seconds: 自动隐藏时间（秒），默认10秒
        """
        if self.info_label:
            # 先隐藏错误信息，避免冲突
            if self.error_label and self.error_label.isVisible():
                self._hide_error_message()
                if self.error_timer.isActive():
                    self.error_timer.stop()

            # 处理长文本
            display_message = message
            if len(message) > 50:
                display_message = message[:47] + "..."

            self.info_label.setText(display_message)
            self.info_label.setToolTip(message)
            self.info_label.setVisible(True)

            # 重新启动定时器
            if self.info_timer.isActive():
                self.info_timer.stop()
            self.info_timer.start(auto_hide_seconds * 1000)
