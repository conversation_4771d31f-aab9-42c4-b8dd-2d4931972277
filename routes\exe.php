<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\Exe\ExeGoodsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| EXE API Routes
|--------------------------------------------------------------------------
|
| 这里定义了EXE程序专用的API路由
| 所有路由都使用api.auth中间件进行认证
|
*/

Route::middleware('api.auth')->prefix('exe/v1')->group(function () {
    
    // 商品相关接口
    Route::prefix('goods')->group(function () {
        
        // 获取待处理商品信息
        Route::get('pending', [ExeGoodsController::class, 'getPendingGoods'])
            ->name('exe.goods.pending');
        
        // 文件上传接口
        Route::post('upload-file', [ExeGoodsController::class, 'uploadFile'])
            ->name('exe.goods.upload-file');
        
        // 商品状态更新接口
        Route::put('update-status', [ExeGoodsController::class, 'updateStatus'])
            ->name('exe.goods.update-status');

        // 设置商品图片本地化错误状态接口
        Route::post('set-image-local-error', [ExeGoodsController::class, 'setImageLocalError'])
            ->name('exe.goods.set-image-local-error');
    });
});