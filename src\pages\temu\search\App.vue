<template>
  <div class="temu-search-container">
    <!-- 进度条覆盖层 -->
    <ProgressBarOverlay
      :visible="isShowingFilterProgress"
      :title="filterProgressTitle"
      :percentage="filterProgressPercentage"
      :status="filterProgressStatus"
      :current-step="filterCurrentStep"
      :step-info="filterStepInfo"
      :total="filterTotalCount"
      :current="filterCurrentCount"
    />

    <!-- 网络监听失败通知 -->
    <NetworkErrorNotification
      :visible="showNetworkErrorNotification"
      @close="closeNetworkErrorNotification"
    />

    <!-- 设置面板 -->
    <SettingsPanel
      v-if="isAuthorizedUser"
      :plugin-name="pluginName"
      :multi-select-enabled="multiSelectEnabled"
      :is-exporting="isExporting"
      :is-deduplicating="isDeduplicating"
      :deduplicated-count="deduplicatedCount"
      :filter-settings="filterSettings"
      @multi-select-change="onMultiSelectChange"
      @export="exportToExcel"
      @deduplicate="deduplicateSelectedGoods"
      @filter-settings-change="onFilterSettingsChange"
    />

    <!-- 登录提示 -->
    <LoginPrompt v-if="!isLoggedIn" />

    <!-- 确认导出对话框 -->
    <ExportConfirmDialog
      v-model:visible="showConfirmDialog"
      :total-product-count="totalProductCount"
      :selected-product-count="selectedProductCount"
      @confirm="confirmExport"
      @cancel="showConfirmDialog = false"
    />

    <!-- 文件名输入对话框 -->
    <FileNameInputDialog
      v-model:visible="showFileNameDialog"
      :default-file-name="defaultFileName"
      :is-exporting="isExporting"
      @confirm="confirmFileName"
      @cancel="cancelExport"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage } from 'element-plus';
import { initMessageManager } from '@/utils/temu/messageManager';
import { isUserAuthorizedForBatchCollection } from '@/utils/userValidation';

// 全局消息实例跟踪
let currentProgressMessage: any = null;
let currentFeedbackMessage: any = null;
import { checkUserLoginStatus, onUserInfoChange, userInfo } from '@/utils/userStore';
import {
  observeSearchPageData,
  startContinuousNetworkListening,
  type SearchPageData,
  type GoodsLinkMap
} from '@/utils/temu/temuSearchUtils';

// 导入组件
import NetworkErrorNotification from './components/NetworkErrorNotification.vue';
import SettingsPanel from './components/SettingsPanel.vue';
import ExportConfirmDialog from './components/ExportConfirmDialog.vue';
import FileNameInputDialog from './components/FileNameInputDialog.vue';
import LoginPrompt from './components/LoginPrompt.vue';
import ProgressBarOverlay from './components/ProgressBarOverlay.vue';

// 导入工具函数
import {
  addCheckboxesToExistingProducts,
  setupDOMObserver,
  stopDOMObserver,
  removeAllCheckboxes,
  getSelectedProductLinks,
  getTotalProductCount
} from '@/utils/temu/checkboxManager';
import { batchCollectionManager } from '@/utils/temu/batchCollectionManager';
import { exportLinksToExcel } from '@/utils/temu/excelExporter';
import { deduplicateProducts } from '@/utils/temu/productDeduplicator';
import { loadSettings, saveSettings, type TemuSearchSettings, type ProductFilterSettings } from '@/utils/temu/settingsManager';
import { generateDefaultFileName } from '@/utils/temu/dateUtils';
import {
  createFilterSummary,
  shouldExecuteFilter,
  validateFilterDeduplicationIntegration,
  getRemainingProductCount,
  createIntegratedProcessingReport,
  createUserFeedbackMessage,
  createProgressMessage,
  handleFilterError,
  globalFilterExecutor,
  type FilterProgress,
  type UserFeedbackMessage
} from '@/utils/temu/productFilter';

// 响应式数据
const isLoggedIn = ref(false);
const currentUserId = ref<number | undefined>(undefined);
const isAuthorizedUser = ref(false);
const multiSelectEnabled = ref(false);
const isExporting = ref(false);
const isDeduplicating = ref(false);
const pluginName = ref('跨境蜂');
const showConfirmDialog = ref(false);
const showFileNameDialog = ref(false);
const totalProductCount = ref(0);
const selectedProductCount = ref(0);
const deduplicatedCount = ref(0);
const pendingExportData = ref<string[]>([]);
const showNetworkErrorNotification = ref(false);
const defaultFileName = ref('');

// 新增：筛选设置状态管理
const filterSettings = ref<ProductFilterSettings>({
  priceFilter: {
    minPrice: undefined,
    maxPrice: undefined
  },
  salesFilter: {
    minSales: undefined,
    maxSales: undefined
  },
  removeLocalProducts: true
});

// 新增：筛选进度和反馈状态
const filterProgress = ref<FilterProgress | null>(null);
const isShowingFilterProgress = ref(false);
const filterProgressMessage = ref('');
const filterProgressTitle = ref('商品处理进度');
const filterProgressPercentage = ref(0);
const filterProgressStatus = ref<'success' | 'exception' | 'warning' | undefined>(undefined);
const filterCurrentStep = ref('');
const filterStepInfo = ref('');
const filterTotalCount = ref(0);
const filterCurrentCount = ref(0);

// 监听器和清理函数
let userInfoChangeCleanup: (() => void) | null = null;
let domObserver: MutationObserver | null = null;

// 新增：商品数据相关
const searchPageData = ref<SearchPageData | null>(null);
const goodsLinkMap = ref<GoodsLinkMap>({});
let searchDataObserverCleanup: (() => void) | null = null;
let currentNetworkListener: any = null;

// 从manifest.json获取插件名称
const getPluginName = async (): Promise<string> => {
  try {
    if (chrome.runtime?.getManifest) {
      const manifest = chrome.runtime.getManifest();
      return manifest.name || '跨境蜂';
    }
    return '跨境蜂';
  } catch (error) {
    console.warn('获取插件名称失败:', error);
    return '跨境蜂';
  }
};

// 检查用户登录状态
const checkLogin = async () => {
  try {
    const loginStatus = await checkUserLoginStatus();
    isLoggedIn.value = loginStatus.isLoggedIn;

    if (loginStatus.isLoggedIn) {
      // 获取用户ID
      currentUserId.value = userInfo.userId;

      // 使用统一的用户权限验证方法
      isAuthorizedUser.value = isUserAuthorizedForBatchCollection();

      console.log('用户登录状态:', loginStatus.isLoggedIn, '用户ID:', currentUserId.value, '授权状态:', isAuthorizedUser.value);
    } else {
      currentUserId.value = undefined;
      isAuthorizedUser.value = false;
    }
  } catch (error) {
    console.error('检查登录状态失败:', error);
    isLoggedIn.value = false;
    currentUserId.value = undefined;
    isAuthorizedUser.value = false;
  }
};

// 加载用户设置
const loadUserSettings = async () => {
  try {
    const settings = await loadSettings();
    multiSelectEnabled.value = settings.multiSelectEnabled;
    // 加载筛选设置
    filterSettings.value = settings.filterSettings;
    console.log('加载筛选设置:', filterSettings.value);
  } catch (error) {
    console.error('加载设置失败:', error);
  }
};

// 保存用户设置
const saveUserSettings = async () => {
  try {
    const settings: TemuSearchSettings = {
      multiSelectEnabled: multiSelectEnabled.value,
      filterSettings: filterSettings.value
    };
    await saveSettings(settings);
    console.log('保存设置成功:', settings);
  } catch (error) {
    console.error('保存设置失败:', error);
  }
};


// 多选开关变化处理
const onMultiSelectChange = async (enabled: boolean) => {
  multiSelectEnabled.value = enabled;
  console.log('多选开关状态:', enabled);

  if (enabled) {
    // 开启多选：为现有商品添加复选框并设置监听器
    addCheckboxesToExistingProducts();
    domObserver = setupDOMObserver();
  } else {
    // 关闭多选：移除所有复选框并停止监听器
    removeAllCheckboxes();
    stopDOMObserver(domObserver);
    domObserver = null;
  }

  // 保存设置
  await saveUserSettings();
};

// 筛选设置变更处理
const onFilterSettingsChange = async (newFilterSettings: ProductFilterSettings) => {
  filterSettings.value = newFilterSettings;
  console.log('筛选设置变更:', newFilterSettings);

  // 保存设置到本地缓存
  await saveUserSettings();
};

// 初始化搜索页面数据提取
const initializeSearchDataExtraction = async () => {
  if (!isAuthorizedUser.value) {
    console.log('用户未授权，跳过搜索数据提取');
    return;
  }

  console.log('开始初始化搜索页面数据提取...');

  // 观察页面数据
  searchDataObserverCleanup = observeSearchPageData((data) => {
    console.log('获取到搜索页面数据:', data);
    searchPageData.value = data;
    goodsLinkMap.value = { ...goodsLinkMap.value, ...data.goodsLinkMap };
  });

  // 启动持续的网络监听
  currentNetworkListener = await startContinuousNetworkListening((newGoodsData) => {
    console.log('从网络监听获取到新的商品数据:', Object.keys(newGoodsData).length, '个');

    // 合并到现有数据
    goodsLinkMap.value = { ...goodsLinkMap.value, ...newGoodsData };

    console.log('当前总商品数据:', Object.keys(goodsLinkMap.value).length, '个');
  });

  if (!currentNetworkListener) {
    console.warn('网络监听器启动失败，将只使用页面初始数据');
    // 显示持久通知提醒用户
    showNetworkErrorNotification.value = true;
  }
};

// 清理搜索数据相关资源
const cleanupSearchDataExtraction = () => {
  if (searchDataObserverCleanup) {
    searchDataObserverCleanup();
    searchDataObserverCleanup = null;
  }

  if (currentNetworkListener) {
    currentNetworkListener.stop();
    currentNetworkListener = null;
  }

  // 清空数据
  searchPageData.value = null;
  goodsLinkMap.value = {};
};


// 导出到Excel
const exportToExcel = async () => {
  if (isExporting.value) {
    return;
  }

  // 获取选中的商品链接
  const selectedLinks = getSelectedProductLinks();
  const totalCount = getTotalProductCount();

  if (selectedLinks.length === 0) {
    ElMessage.warning('请选择至少一个商品');
    return;
  }

  // 设置统计数据并显示确认对话框
  totalProductCount.value = totalCount;
  selectedProductCount.value = selectedLinks.length;
  pendingExportData.value = selectedLinks;
  showConfirmDialog.value = true;
};

// 确认导出
const confirmExport = () => {
  showConfirmDialog.value = false;

  // 生成默认文件名
  defaultFileName.value = generateDefaultFileName();
  showFileNameDialog.value = true;
};

// 确认文件名并执行导出
const confirmFileName = async (fileName: string) => {
  if (!fileName.trim()) {
    ElMessage.warning('请输入文件名');
    return;
  }

  try {
    isExporting.value = true;

    const selectedLinks = pendingExportData.value;
    console.log('选中的商品链接:', selectedLinks);

    // 使用工具函数导出Excel
    await exportLinksToExcel(
      selectedLinks,
      fileName,
      searchPageData.value,
      goodsLinkMap.value
    );

    ElMessage.success({
      message: `成功导出 ${selectedLinks.length} 个商品链接`,
      duration: 4000
    });

    // 关闭对话框并清理数据
    showFileNameDialog.value = false;
    pendingExportData.value = [];

  } catch (error) {
    console.error('导出Excel失败:', error);
    ElMessage.error('导出失败，请重试');
  } finally {
    isExporting.value = false;
  }
};

// 取消导出
const cancelExport = () => {
  showFileNameDialog.value = false;
  pendingExportData.value = [];
};

// 筛选进度回调处理
const handleFilterProgress = (progress: FilterProgress) => {
  filterProgress.value = progress;
  filterProgressMessage.value = createProgressMessage(progress);

  // 显示进度消息
  if (progress.stage === 'initializing') {
    isShowingFilterProgress.value = true;
    // 关闭之前的进度消息
    if (currentProgressMessage) {
      currentProgressMessage.close();
    }
    currentProgressMessage = ElMessage.info({
      message: filterProgressMessage.value,
      duration: 1000
    });
  } else if (progress.stage === 'filtering' && progress.processedCount % 20 === 0) {
    // 每处理20个商品更新一次进度消息
    // 关闭之前的进度消息
    if (currentProgressMessage) {
      currentProgressMessage.close();
    }
    currentProgressMessage = ElMessage.info({
      message: filterProgressMessage.value,
      duration: 1000
    });
  } else if (progress.stage === 'completed') {
    isShowingFilterProgress.value = false;
    // 关闭之前的进度消息
    if (currentProgressMessage) {
      currentProgressMessage.close();
      currentProgressMessage = null;
    }
  }
};

// 显示用户反馈消息
const showUserFeedback = (feedbackMessage: UserFeedbackMessage) => {
  // 关闭之前的反馈消息
  if (currentFeedbackMessage) {
    currentFeedbackMessage.close();
  }

  const messageOptions = {
    message: feedbackMessage.message,
    duration: feedbackMessage.duration || 3000
  };

  switch (feedbackMessage.type) {
    case 'success':
      currentFeedbackMessage = ElMessage.success(messageOptions);
      break;
    case 'warning':
      currentFeedbackMessage = ElMessage.warning(messageOptions);
      break;
    case 'error':
      currentFeedbackMessage = ElMessage.error(messageOptions);
      break;
    case 'info':
    default:
      currentFeedbackMessage = ElMessage.info(messageOptions);
      break;
  }

  // 如果有详细信息，在控制台输出
  if (feedbackMessage.details && feedbackMessage.details.length > 0) {
    console.log(`${feedbackMessage.title} - 详细信息:`, feedbackMessage.details);
  }
};

// 商品去重（集成筛选功能）
const deduplicateSelectedGoods = async () => {
  isDeduplicating.value = true;
  deduplicatedCount.value = 0;

  try {
    // 首先验证筛选与去重的集成环境
    const validation = validateFilterDeduplicationIntegration();
    if (!validation.isValid) {
      ElMessage.error(validation.message);
      console.error('集成验证失败:', validation);
      return;
    }

    console.log('集成验证通过:', validation.message);

    let filterResult: any = null;
    let deduplicationResult: any = null;

    // 第一步：执行筛选逻辑（如果有筛选条件启用）
    if (shouldExecuteFilter(filterSettings.value)) {
      console.log('开始执行商品筛选...');

      try {
        // 设置进度回调
        globalFilterExecutor.setProgressCallback(handleFilterProgress);

        // 执行筛选
        filterResult = await globalFilterExecutor.execute(filterSettings.value);

        // 创建用户反馈消息
        const feedbackMessage = createUserFeedbackMessage(filterResult);
        showUserFeedback(feedbackMessage);

        if (filterResult.success) {
          const filterSummary = createFilterSummary(filterResult);
          console.log('筛选完成:', filterSummary);

          // 确认筛选后的商品列表能正确传递给去重功能
          const remainingCount = getRemainingProductCount();
          console.log(`筛选后剩余商品数量: ${remainingCount}，准备执行去重`);

        } else {
          console.error('筛选失败:', filterResult.error);
          // 筛选失败时仍然继续执行去重
        }

      } catch (filterError) {
        // 处理筛选错误
        const { error, userMessage } = handleFilterError(filterError, '商品筛选');
        console.error('筛选执行异常:', error);
        showUserFeedback(userMessage);

        // 筛选失败时仍然继续执行去重
        filterResult = {
          success: false,
          error: error.getUserMessage(),
          filteredCount: 0,
          remainingCount: 0
        };
      }
    } else {
      console.log('未启用筛选条件，跳过筛选步骤');
    }

    // 第二步：执行去重逻辑（复用现有的商品DOM获取逻辑）
    console.log('开始执行商品去重...');

    // 显示去重进度反馈
    if (filterResult?.filteredCount > 0) {
      ElMessage.info('筛选完成，正在进行去重检查...');
    } else {
      ElMessage.info('正在检查重复商品...');
    }

    try {
      deduplicationResult = await deduplicateProducts();

      if (deduplicationResult.success) {
        deduplicatedCount.value = deduplicationResult.deletedCount;

        // 使用集成报告生成综合反馈
        const report = createIntegratedProcessingReport(filterResult, deduplicationResult);

        console.log('商品处理完成统计:', report.details);

        // 提供综合的处理结果反馈
        if (report.details.total.processedCount > 0) {
          ElMessage.success({
            message: report.summary,
            duration: 5000 // 更长的显示时间以便用户阅读详细信息
          });
        } else {
          ElMessage.info('处理完成：没有发现需要删除的商品');
        }

      } else {
        ElMessage.error({
          message: `去重失败：${deduplicationResult.error || '未知错误'}`,
          duration: 4000
        });
      }
    } catch (deduplicationError) {
      console.error('去重执行异常:', deduplicationError);
      ElMessage.error({
        message: `去重失败：${deduplicationError instanceof Error ? deduplicationError.message : '未知错误'}`,
        duration: 4000
      });
    }

  } catch (error) {
    console.error('商品处理失败:', error);

    // 使用统一的错误处理
    const { userMessage } = handleFilterError(error, '商品处理');
    showUserFeedback(userMessage);

  } finally {
    isDeduplicating.value = false;
    isShowingFilterProgress.value = false;
    filterProgress.value = null;
    filterProgressMessage.value = '';
  }
};

// 关闭网络错误通知
const closeNetworkErrorNotification = () => {
  showNetworkErrorNotification.value = false;
};

// 组件挂载
onMounted(async () => {
  console.log('Temu搜索页面组件已挂载');

  // 获取插件名称
  pluginName.value = await getPluginName();

  // 检查登录状态
  await checkLogin();

  // 只有授权用户才初始化消息管理器（用于批量采集）
  if (isUserAuthorizedForBatchCollection()) {
    console.log('用户有批量采集权限，初始化消息管理器');
    initMessageManager();
  } else {
    console.log('用户无批量采集权限，跳过消息管理器初始化');
  }

  // 加载设置
  await loadUserSettings();

  // 监听用户信息变化
  userInfoChangeCleanup = onUserInfoChange(async () => {
    const wasAuthorized = isAuthorizedUser.value;
    await checkLogin();

    // 如果授权状态发生变化，重新初始化相关功能
    if (wasAuthorized !== isAuthorizedUser.value) {
      if (isAuthorizedUser.value) {
        // 用户成为授权用户，初始化功能
        setTimeout(async () => {
          await initializeSearchDataExtraction();
        }, 1000);
      } else {
        // 用户失去授权，清理功能
        cleanupSearchDataExtraction();
      }
    }
  });

  // 如果是授权用户且开启了多选，初始化功能
  if (isAuthorizedUser.value && multiSelectEnabled.value) {
    setTimeout(() => {
      addCheckboxesToExistingProducts();
      setupDOMObserver();
    }, 1000); // 延迟1秒确保页面加载完成
  }

  // 如果是授权用户，初始化搜索数据提取
  if (isAuthorizedUser.value) {
    setTimeout(async () => {
      await initializeSearchDataExtraction();
    }, 1500); // 延迟1.5秒确保页面加载完成
  }
});

// 组件卸载
onBeforeUnmount(() => {
  // 清理监听器
  if (userInfoChangeCleanup) {
    userInfoChangeCleanup();
  }

  // 停止DOM监听器
  stopDOMObserver(domObserver);

  // 移除复选框
  removeAllCheckboxes();

  // 清理搜索数据提取
  cleanupSearchDataExtraction();
});
</script>

<style scoped>
.temu-search-container {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 全局样式 - 商品复选框 */
:global(.product-checkbox) {
  pointer-events: auto !important;
  user-select: none;
}

:global(.product-checkbox:hover .checkbox-visual) {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 覆盖ElementPlus默认样式 */
:global(.el-dialog) {
  border-radius: 12px !important;
}

:global(.el-dialog__header) {
  padding: 20px 20px 10px 20px !important;
  border-bottom: 1px solid #ebeef5 !important;
}

:global(.el-dialog__body) {
  padding: 20px !important;
}

:global(.el-dialog__footer) {
  padding: 10px 20px 20px 20px !important;
  border-top: 1px solid #ebeef5 !important;
}
</style>
