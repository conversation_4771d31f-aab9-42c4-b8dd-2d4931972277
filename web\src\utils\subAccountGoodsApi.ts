import { sendRequestViaBackground } from './api'
import { API_URLS, getApiUrl } from './apiConfig'

// 商品类型定义
export interface SubAccountGoods {
  id: number
  type: number
  type_name: string
  source_url: string
  mall_id: number
  goods_id: string
  goods_name: string
  goods_thumb: string
  goods_pic: string[]
  images: string[]
  goods_video: string
  goods_sku_num: number
  first_sku: string
  first_sku_price: number
  first_sku_currentcy: string
  first_sku_thumb_url: string
  first_sku_thumb_url_h500: string
  formatted_skus: any[]
  goods_property: string
  goods_score: number
  goods_sold_quantity: number
  status: number
  cat_id: number
  cat_name: string
  front_cat_id_2: number
  front_cat_desc: string
  front_cat_2_path_name: string
  platform_relations: any[]
  is_price_modified: boolean
  price_adjustment_info: any
  created_at: string
  updated_at: string
  operator_info: {
    type: string
    name: string
    created_at: string
    updated_at: string
  }
  user_sub_id: number
}

// 商品列表参数
export interface SubAccountGoodsListParams {
  page?: number
  pageSize?: number
  directory_id?: number
  goods_name?: string
  goods_id?: number | null
  status?: number | null
  time_type?: string
  start_date?: string
  end_date?: string
  sort_field?: string
  sort_order?: string
  sub_account_id?: number // 主账号查询特定子账号时使用
  // 主账号专属筛选参数
  price_adjusted?: string | null
  cat_adjusted?: string | null
  only_sub_account?: boolean
  // 子账号查询参数
  sub_account_name?: string
  sub_account_phone?: string
}

// 商品列表响应
export interface SubAccountGoodsListResponse {
  list: SubAccountGoods[]
  pagination: {
    total: number
    totalPages: number
    currentPage: number
    pageSize: number
    hasNext: boolean
    hasPrevious: boolean
  }
}

// 获取子账号商品列表
export const getSubAccountGoodsList = async (params: SubAccountGoodsListParams): Promise<SubAccountGoodsListResponse> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_GOODS_LIST);
  return sendRequestViaBackground({
    funName: 'getSubAccountGoodsList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

// 删除子账号商品
export const deleteSubAccountGoods = async (goodsId: number): Promise<void> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_GOODS_DELETE);
  return sendRequestViaBackground({
    funName: 'deleteSubAccountGoods',
    url,
    method: 'post',
    data: { goods_id: goodsId },
    auth: true
  });
};

// 批量删除子账号商品
export const batchDeleteSubAccountGoods = async (goodsIds: number[]): Promise<void> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_GOODS_DELETE);
  return sendRequestViaBackground({
    funName: 'batchDeleteSubAccountGoods',
    url,
    method: 'post',
    data: { goods_ids: goodsIds },
    auth: true
  });
};

// 获取商品详情
export const getSubAccountGoodsDetail = async (goodsId: number): Promise<SubAccountGoods> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_GOODS_LIST);
  return sendRequestViaBackground({
    funName: 'getSubAccountGoodsDetail',
    url: `${url}/${goodsId}`,
    method: 'get',
    auth: true
  });
};

// 更新商品状态
export const updateSubAccountGoodsStatus = async (goodsId: number, status: number): Promise<void> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_GOODS_LIST);
  return sendRequestViaBackground({
    funName: 'updateSubAccountGoodsStatus',
    url: `${url}/${goodsId}/status`,
    method: 'put',
    data: { status },
    auth: true
  });
};

// 批量更新商品状态
export const batchUpdateSubAccountGoodsStatus = async (goodsIds: number[], status: number): Promise<void> => {
  const url = await getApiUrl(API_URLS.SUB_ACCOUNT_GOODS_LIST);
  return sendRequestViaBackground({
    funName: 'batchUpdateSubAccountGoodsStatus',
    url: `${url}/batch/status`,
    method: 'put',
    data: { goods_ids: goodsIds, status },
    auth: true
  });
};