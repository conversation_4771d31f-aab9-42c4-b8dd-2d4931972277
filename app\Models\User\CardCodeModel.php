<?php

namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CardCodeModel extends BaseModel
{
    protected $table = 'card_codes';
    
    protected $fillable = [
        'card_code',
        'card_name',
        'card_type',
        'price',
        'points',
        'vip_days',
        'vip_days_unit',
        'vip_level',
        'status',
        'used_at',
        'used_by',
        'is_copied',
        'copied_at',
        'copied_by',
        'batch_no',
        'description',
        'valid_until',
        'admin_id'
    ];

    protected $casts = [
        'card_type' => 'integer',
        'price' => 'decimal:2',
        'points' => 'integer',
        'vip_days' => 'integer',
        'vip_level' => 'integer',
        'status' => 'integer',
        'is_copied' => 'integer',
        'admin_id' => 'integer',
        'used_by' => 'integer',
        'copied_by' => 'integer',
        'valid_until' => 'datetime',
        'used_at' => 'datetime',
        'copied_at' => 'datetime'
    ];

    // 卡密类型常量
    const TYPE_VIP_CARD = 1; // 有效期卡（包含积分和VIP有效期）
    const TYPE_POINTS_CARD = 2; // 纯积分卡（仅包含积分）

    // 卡密状态常量
    const STATUS_USED = 0; // 已使用
    const STATUS_UNUSED = 1; // 未使用
    const STATUS_DISABLED = 2; // 已禁用

    // VIP时长单位常量
    const VIP_UNIT_YEAR = 'year'; // 年
    const VIP_UNIT_MONTH = 'month'; // 月
    const VIP_UNIT_DAY = 'day'; // 天

    // 复制状态常量
    const COPY_STATUS_NOT_COPIED = 0; // 未复制
    const COPY_STATUS_COPIED = 1; // 已复制

    /**
     * 关联创建人（管理员）
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id', 'id');
    }

    /**
     * 关联使用记录
     */
    public function usageRecords(): HasMany
    {
        return $this->hasMany(CardUsageRecordModel::class, 'card_code_id', 'id');
    }

    /**
     * 关联操作日志
     */
    public function operationLogs(): HasMany
    {
        return $this->hasMany(CardOperationLogModel::class, 'card_code_id', 'id');
    }

    /**
     * 关联使用人
     */
    public function usedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'used_by');
    }

    /**
     * 关联复制人
     */
    public function copiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'copied_by');
    }

    /**
     * 按卡密类型查询
     */
    public function scopeByCardType(Builder $query, int $cardType): Builder
    {
        return $query->where('card_type', $cardType);
    }

    /**
     * 按复制状态查询
     */
    public function scopeByCopyStatus(Builder $query, int $copyStatus): Builder
    {
        return $query->where('is_copied', $copyStatus);
    }

    /**
     * 按VIP时长单位查询
     */
    public function scopeByVipDaysUnit(Builder $query, string $vipDaysUnit): Builder
    {
        return $query->where('vip_days_unit', $vipDaysUnit);
    }

    /**
     * 获取VIP时长单位选项
     */
    public static function getVipDaysUnitOptions(): array
    {
        return [
            self::VIP_UNIT_YEAR => '年',
            self::VIP_UNIT_MONTH => '月',
            self::VIP_UNIT_DAY => '天'
        ];
    }

    /**
     * 获取复制状态选项
     */
    public static function getCopyStatusOptions(): array
    {
        return [
            self::COPY_STATUS_NOT_COPIED => '未复制',
            self::COPY_STATUS_COPIED => '已复制'
        ];
    }

    /**
     * 按状态查询
     */
    public function scopeByStatus(Builder $query, int $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * 按批次号查询
     */
    public function scopeByBatchNo(Builder $query, string $batchNo): Builder
    {
        return $query->where('batch_no', $batchNo);
    }

    /**
     * 按管理员ID查询
     */
    public function scopeByAdminId(Builder $query, int $adminId): Builder
    {
        return $query->where('admin_id', $adminId);
    }

    /**
     * 按卡密查询（区分大小写）
     */
    public function scopeByCardCode(Builder $query, string $cardCode): Builder
    {
        return $query->whereRaw('BINARY card_code = ?', [$cardCode]);
    }

    /**
     * 按卡密查询（不区分大小写，用于兼容性）
     */
    public function scopeByCardCodeCaseInsensitive(Builder $query, string $cardCode): Builder
    {
        return $query->where('card_code', $cardCode);
    }

    /**
     * 按创建时间范围查询
     */
    public function scopeByDateRange(Builder $query, ?string $startDate = null, ?string $endDate = null): Builder
    {
        if ($startDate) {
            $query->where('created_at', '>=', $startDate . ' 00:00:00');
        }
        if ($endDate) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }
        return $query;
    }

    /**
     * 按时间类型和时间范围查询
     */
    public function scopeByTimeTypeAndRange(Builder $query, string $timeType = 'created_at', ?string $startDate = null, ?string $endDate = null): Builder
    {
        // 验证时间类型
        $allowedTimeTypes = ['created_at', 'used_at', 'copied_at'];
        if (!in_array($timeType, $allowedTimeTypes)) {
            $timeType = 'created_at';
        }

        if ($startDate) {
            $query->where($timeType, '>=', $startDate . ' 00:00:00');
        }
        if ($endDate) {
            $query->where($timeType, '<=', $endDate . ' 23:59:59');
        }
        return $query;
    }

    /**
     * 按使用时间范围查询
     */
    public function scopeByUsedDateRange(Builder $query, ?string $startDate = null, ?string $endDate = null): Builder
    {
        if ($startDate) {
            $query->where('used_at', '>=', $startDate . ' 00:00:00');
        }
        if ($endDate) {
            $query->where('used_at', '<=', $endDate . ' 23:59:59');
        }
        return $query;
    }

    /**
     * 按复制时间范围查询
     */
    public function scopeByCopiedDateRange(Builder $query, ?string $startDate = null, ?string $endDate = null): Builder
    {
        if ($startDate) {
            $query->where('copied_at', '>=', $startDate . ' 00:00:00');
        }
        if ($endDate) {
            $query->where('copied_at', '<=', $endDate . ' 23:59:59');
        }
        return $query;
    }

    /**
     * 查询有效的卡密（未使用且未禁用且未过期）
     */
    public function scopeValid(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_UNUSED)
                    ->where(function ($q) {
                        $q->whereNull('valid_until')
                          ->orWhere('valid_until', '>', now());
                    });
    }

    /**
     * 查询未使用的卡密
     */
    public function scopeUnused(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_UNUSED);
    }

    /**
     * 查询已使用的卡密
     */
    public function scopeUsed(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_USED);
    }

    /**
     * 查询已禁用的卡密
     */
    public function scopeDisabled(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_DISABLED);
    }

    /**
     * 按卡密名称模糊查询
     */
    public function scopeByCardName(Builder $query, string $cardName): Builder
    {
        return $query->where('card_name', 'like', "%{$cardName}%");
    }

    /**
     * 按价格范围查询
     */
    public function scopeByPriceRange(Builder $query, ?float $minPrice = null, ?float $maxPrice = null): Builder
    {
        if ($minPrice !== null) {
            $query->where('price', '>=', $minPrice);
        }
        if ($maxPrice !== null) {
            $query->where('price', '<=', $maxPrice);
        }
        return $query;
    }

    /**
     * 按积分范围查询
     */
    public function scopeByPointsRange(Builder $query, ?int $minPoints = null, ?int $maxPoints = null): Builder
    {
        if ($minPoints !== null) {
            $query->where('points', '>=', $minPoints);
        }
        if ($maxPoints !== null) {
            $query->where('points', '<=', $maxPoints);
        }
        return $query;
    }

    /**
     * 获取卡密类型文本
     */
    public function getCardTypeTextAttribute(): string
    {
        return match($this->card_type) {
            self::TYPE_VIP_CARD => '有效期卡',
            self::TYPE_POINTS_CARD => '积分卡',
            default => '未知类型'
        };
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            self::STATUS_USED => '已使用',
            self::STATUS_UNUSED => '未使用',
            self::STATUS_DISABLED => '已禁用',
            default => '未知状态'
        };
    }

    /**
     * 检查卡密是否有效（未使用且未过期且未禁用）
     */
    public function isValid(): bool
    {
        if ($this->status !== self::STATUS_UNUSED) {
            return false;
        }
        
        if ($this->valid_until && $this->valid_until->isPast()) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查是否可以删除（未使用和未复制的卡密才能删除）
     */
    public function canDelete(): bool
    {
        return $this->status === self::STATUS_UNUSED && $this->is_copied === self::COPY_STATUS_NOT_COPIED;
    }

    /**
     * 获取卡密类型选项
     */
    public static function getCardTypeOptions(): array
    {
        return [
            self::TYPE_VIP_CARD => '有效期卡',
            self::TYPE_POINTS_CARD => '积分卡'
        ];
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_USED => '已使用',
            self::STATUS_UNUSED => '未使用',
            self::STATUS_DISABLED => '已禁用'
        ];
    }
}
