#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传管理器
负责文件上传到Laravel服务器的操作
"""

import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Tuple, Dict, Any, List, Callable
from api.goods_service import GoodsService


class UploadManager:
    """文件上传管理器类"""
    
    # 最大重试次数
    MAX_RETRY_ATTEMPTS = 2
    
    def __init__(self, goods_service: GoodsService = None, file_dao=None):
        """
        初始化上传管理器

        Args:
            goods_service: 商品服务实例
            file_dao: 文件数据访问对象，用于更新上传状态
        """
        self.goods_service = goods_service or GoodsService()
        self.file_dao = file_dao
    
    def upload_file(self, appid: str, appsecret: str, goods_id: int, file_type: str,
                   field_name: str, local_file_path: str, original_url: str,
                   max_retries: int = None, goods_platform_id: int = None) -> Tuple[bool, str]:
        """
        上传单个文件到服务器

        Args:
            appid: 应用ID
            appsecret: 应用密钥
            goods_id: SQLite数据库中的商品ID
            file_type: 文件类型 (image, video, pdf)
            field_name: 字段名
            local_file_path: 本地文件路径
            original_url: 原始URL
            max_retries: 最大重试次数
            goods_platform_id: 平台商品ID (Laravel后端实际需要的值)

        Returns:
            (是否成功, 错误消息)
        """
        if max_retries is None:
            max_retries = self.MAX_RETRY_ATTEMPTS
        
        # 验证文件是否存在
        if not os.path.exists(local_file_path):
            return False, f"本地文件不存在: {local_file_path}"
        
        # 验证文件大小
        file_size = os.path.getsize(local_file_path)
        if file_size == 0:
            return False, "文件为空"
        
        # 重试上传
        last_error = ""
        for attempt in range(max_retries):
            try:
                success, response_data, error_msg = self.goods_service.upload_file(
                    appid, appsecret, goods_id, file_type, field_name,
                    local_file_path, original_url, goods_platform_id
                )
                
                if success:
                    # 检查响应数据
                    if isinstance(response_data, dict):
                        if response_data.get('code') == 200:
                            return True, "本地化成功"
                        else:
                            last_error = response_data.get('message', '本地化失败')
                    else:
                        return True, "本地化成功"
                else:
                    last_error = error_msg or "本地化失败"
                
                # 如果不是最后一次尝试，等待一段时间再重试
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    
            except Exception as e:
                last_error = f"本地化异常: {str(e)}"
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
        
        return False, f"本地化失败，已重试{max_retries}次: {last_error}"

    def upload_files_concurrent(self, upload_tasks: List[Dict], max_workers: int = 2,
                                progress_callback: Callable = None,
                                stop_flag: Callable = None) -> Tuple[int, int]:
        """
        并发上传多个文件

        Args:
            upload_tasks: 上传任务列表，每个任务包含上传所需的信息
            max_workers: 最大并发数
            progress_callback: 进度回调函数，接收参数 (completed_count, total_count, current_task, success)
            stop_flag: 停止标志检查函数，返回True时停止上传

        Returns:
            (成功上传数量, 总任务数量)
        """
        if not upload_tasks:
            return 0, 0

        total_tasks = len(upload_tasks)
        completed_count = 0
        success_count = 0

        # 限制最大并发数，避免系统过载
        max_workers = min(max_workers, 10)  # 最大不超过10
        max_workers = max(max_workers, 1)   # 最小为1

        # 使用线程锁保护计数器
        count_lock = threading.Lock()

        def upload_single_task(task):
            """上传单个任务"""
            nonlocal completed_count, success_count

            try:
                # 检查停止标志
                if stop_flag and stop_flag():
                    return False, "用户停止操作"

                # 提取上传参数
                appid = task.get('appid')
                appsecret = task.get('appsecret')
                goods_id = task.get('goods_id')
                goods_platform_id = task.get('goods_platform_id')
                file_type = task.get('file_type')
                field_name = task.get('field_name')
                local_path = task.get('local_path')
                original_url = task.get('original_url')

                if not all([appid, appsecret, goods_id, file_type, field_name, local_path, original_url]):
                    return False, "任务参数不完整"

                # 执行上传
                success, error_msg = self.upload_file(
                    appid, appsecret, goods_id, file_type,
                    field_name, local_path, original_url,
                    max_retries=None, goods_platform_id=goods_platform_id
                )

                # 更新数据库中的上传状态
                record_id = task.get('record_id')
                if record_id and self.file_dao:
                    try:
                        if success:
                            self.file_dao.update_upload_status(record_id, True, None)
                        else:
                            self.file_dao.update_upload_status(record_id, False, error_msg)
                    except Exception as db_error:
                        # 数据库更新失败不影响上传结果，但记录错误
                        print(f"更新数据库上传状态失败: {str(db_error)}")

                # 更新计数器
                with count_lock:
                    completed_count += 1
                    if success:
                        success_count += 1

                    # 调用进度回调
                    if progress_callback:
                        progress_callback(completed_count, total_tasks, task, success)

                return success, error_msg

            except Exception as e:
                with count_lock:
                    completed_count += 1
                    if progress_callback:
                        progress_callback(completed_count, total_tasks, task, False)
                return False, f"本地化异常: {str(e)}"

        # 使用线程池执行并发上传
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_task = {executor.submit(upload_single_task, task): task for task in upload_tasks}

            # 等待任务完成
            for future in as_completed(future_to_task):
                if stop_flag and stop_flag():
                    # 如果需要停止，取消剩余任务
                    for f in future_to_task:
                        if not f.done():
                            f.cancel()
                    break

                try:
                    future.result()  # 获取结果，如果有异常会抛出
                except Exception:
                    # 异常已在upload_single_task中处理
                    pass

        return success_count, total_tasks
    
    def upload_multiple_files(self, appid: str, appsecret: str, file_records: List[Dict[str, Any]],
                             progress_callback=None) -> Tuple[int, int, List[str]]:
        """
        批量上传多个文件
        
        Args:
            appid: 应用ID
            appsecret: 应用密钥
            file_records: 文件记录列表，每个记录包含上传所需的信息
            progress_callback: 进度回调函数 (current, total, file_info)
            
        Returns:
            (成功数量, 失败数量, 错误消息列表)
        """
        success_count = 0
        failed_count = 0
        error_messages = []
        total_files = len(file_records)
        
        for i, record in enumerate(file_records):
            try:
                # 调用进度回调
                if progress_callback:
                    progress_callback(i + 1, total_files, record)
                
                # 提取上传参数
                goods_id = record.get('goods_id')  # SQLite数据库中的商品ID
                goods_platform_id = record.get('goods_platform_id')  # 平台商品ID
                file_type = record.get('type')
                field_name = record.get('field')
                local_path = record.get('url_local')
                original_url = record.get('url')

                if not all([goods_id, goods_platform_id, file_type, field_name, local_path, original_url]):
                    error_msg = f"文件记录信息不完整: {record}"
                    error_messages.append(error_msg)
                    failed_count += 1
                    continue

                # 上传文件 - 传递两个ID参数
                success, error_msg = self.upload_file(
                    appid, appsecret, goods_id, file_type,
                    field_name, local_path, original_url,
                    max_retries=None, goods_platform_id=goods_platform_id
                )
                
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                    error_messages.append(f"文件 {local_path}: {error_msg}")
                    
            except Exception as e:
                failed_count += 1
                error_messages.append(f"处理文件记录时出错: {str(e)}")
        
        return success_count, failed_count, error_messages
    
    def validate_upload_requirements(self, file_records: List[Dict[str, Any]]) -> Tuple[bool, List[str]]:
        """
        验证上传要求
        
        Args:
            file_records: 文件记录列表
            
        Returns:
            (是否通过验证, 错误消息列表)
        """
        errors = []
        
        for i, record in enumerate(file_records):
            # 检查必需字段
            required_fields = ['goods_id', 'type', 'field', 'url_local', 'url']
            for field in required_fields:
                if field not in record or not record[field]:
                    errors.append(f"记录 {i+1}: 缺少必需字段 '{field}'")
            
            # 检查本地文件是否存在
            local_path = record.get('url_local')
            if local_path and not os.path.exists(local_path):
                errors.append(f"记录 {i+1}: 本地文件不存在 '{local_path}'")
            
            # 检查文件类型
            file_type = record.get('type')
            if file_type and file_type not in ['image', 'video', 'pdf']:
                errors.append(f"记录 {i+1}: 不支持的文件类型 '{file_type}'")
        
        return len(errors) == 0, errors
    
    def get_file_type_from_extension(self, file_path: str) -> str:
        """
        根据文件扩展名获取文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件类型 (image, video, pdf)
        """
        _, ext = os.path.splitext(file_path.lower())
        
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
        pdf_extensions = ['.pdf']
        
        if ext in image_extensions:
            return 'image'
        elif ext in video_extensions:
            return 'video'
        elif ext in pdf_extensions:
            return 'pdf'
        else:
            return 'image'  # 默认为图片类型
    
    def calculate_upload_statistics(self, file_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算上传统计信息
        
        Args:
            file_records: 文件记录列表
            
        Returns:
            统计信息字典
        """
        stats = {
            'total_files': len(file_records),
            'total_size': 0,
            'by_type': {},
            'ready_for_upload': 0,
            'missing_files': 0
        }
        
        for record in file_records:
            file_type = record.get('type', 'unknown')
            local_path = record.get('url_local')
            
            # 按类型统计
            if file_type not in stats['by_type']:
                stats['by_type'][file_type] = {'count': 0, 'size': 0}
            
            stats['by_type'][file_type]['count'] += 1
            
            # 检查文件状态
            if local_path and os.path.exists(local_path):
                file_size = os.path.getsize(local_path)
                stats['total_size'] += file_size
                stats['by_type'][file_type]['size'] += file_size
                stats['ready_for_upload'] += 1
            else:
                stats['missing_files'] += 1
        
        return stats
    
    def format_file_size(self, size_bytes: int) -> str:
        """
        格式化文件大小显示
        
        Args:
            size_bytes: 字节数
            
        Returns:
            格式化的大小字符串
        """
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
