<template>
  <div class="sub-account-management">
    <div class="page-header">
      <h2>子账号管理</h2>
      <p class="description">管理您的子账号，子账号仅有采集和查看商品目录的功能</p>
    </div>

    <!-- 统计信息 -->
    <div class="stats-card">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ pagination.total }}</div>
            <div class="stat-label">当前子账号数</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ subAccountLimit }}</div>
            <div class="stat-label">子账号上限</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ subAccountLimit - pagination.total }}</div>
            <div class="stat-label">剩余可创建</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选表单 -->
    <div class="filter-form">
      <el-form :inline="true" :model="filterForm" class="filter-form-inline">
        <el-form-item label="姓名">
          <el-input
            v-model="filterForm.name"
            placeholder="请输入姓名"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="filterForm.phone"
            placeholder="请输入手机号"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="filterForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="action-bar">
      <el-button
        type="primary"
        @click="showCreateDialog = true"
        :disabled="pagination.total >= subAccountLimit"
      >
        <el-icon><Plus /></el-icon>
        新增子账号
      </el-button>
      <el-button @click="loadSubAccountList">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 子账号列表 -->
    <div class="table-container">
      <el-table
        :data="subAccountList"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="phone" label="手机号" width="150" />
        <el-table-column prop="total_goods_count" label="商品总数" width="100" align="center" />
        <el-table-column prop="today_goods_count" label="今日商品" width="100" align="center" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template #default="scope">
            <el-button
              size="small"
              @click="handleEditSubAccount(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              :type="scope.row.status === 1 ? 'warning' : 'success'"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="handleViewGoods(scope.row)"
            >
              查看商品
            </el-button>
            <!-- 暂时注释删除按钮，保留代码便于后续维护 -->
            <!--
            <el-button
              size="small"
              type="danger"
              @click="handleDeleteSubAccount(scope.row)"
            >
              删除
            </el-button>
            -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handlePageSizeChange"
        @current-change="handleCurrentPageChange"
      />
    </div>

    <!-- 创建子账号对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新增子账号"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="createForm"
        :rules="createRules"
        ref="createFormRef"
        label-width="80px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="createForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="createForm.password"
            type="password"
            placeholder="请输入密码（6-20位）"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreateSubAccount" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑子账号对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑子账号"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="editForm"
        :rules="editRules"
        ref="editFormRef"
        label-width="80px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="editForm.password"
            type="password"
            placeholder="留空则不修改密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateSubAccount" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  getSubAccountList,
  createSubAccount,
  updateSubAccount,
  toggleSubAccountStatus,
  deleteSubAccount,
  type SubAccount,
  type CreateSubAccountParams,
  type UpdateSubAccountParams
} from '../utils/subAccountApi'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const subAccountList = ref<SubAccount[]>([])
const subAccountLimit = ref(2)
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 筛选表单数据
const filterForm = reactive({
  name: '',
  phone: '',
  status: ''
})

// 表单数据
const createForm = reactive({
  name: '',
  phone: '',
  password: ''
})

const editForm = reactive({
  id: 0,
  name: '',
  phone: '',
  password: ''
})

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { max: 50, message: '姓名不能超过50个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
  ]
}

const editRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { max: 50, message: '姓名不能超过50个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
  ]
}

// 表单引用
const createFormRef = ref()
const editFormRef = ref()

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 分页事件处理
const handlePageSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.current = 1 // 重置到第一页
  loadSubAccountList()
}

const handleCurrentPageChange = (page: number) => {
  pagination.value.current = page
  loadSubAccountList()
}

// 筛选事件处理
const handleSearch = () => {
  pagination.value.current = 1 // 重置到第一页
  loadSubAccountList()
}

const handleReset = () => {
  filterForm.name = ''
  filterForm.phone = ''
  filterForm.status = ''
  pagination.value.current = 1 // 重置到第一页
  loadSubAccountList()
}

// 加载子账号列表
const loadSubAccountList = async () => {
  loading.value = true
  try {
    const response = await getSubAccountList({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: filterForm.name || undefined,
      phone: filterForm.phone || undefined,
      status: filterForm.status !== '' ? Number(filterForm.status) : undefined
    })
    console.log('子账号列表响应:', response)

    // sendRequestViaBackground已自动处理成功/失败判断，成功时直接返回data内容
    subAccountList.value = response.list || []
    subAccountLimit.value = response.sub_num_limit || 2

    // 更新分页信息
    if (response.pagination) {
      pagination.value = response.pagination
    }
  } catch (error) {
    console.error('获取子账号列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 创建子账号
const handleCreateSubAccount = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()
    submitting.value = true

    const response = await createSubAccount(createForm)
    console.log('创建子账号响应:', response)

    // sendRequestViaBackground已自动处理成功/失败判断，成功时直接返回data内容
    ElMessage.success('子账号创建成功')
    showCreateDialog.value = false
    resetCreateForm()
    await loadSubAccountList()
  } catch (error) {
    console.error('创建子账号失败:', error)
  } finally {
    submitting.value = false
  }
}

// 重置创建表单
const resetCreateForm = () => {
  createForm.name = ''
  createForm.phone = ''
  createForm.password = ''
  createFormRef.value?.resetFields()
}

// 编辑子账号
const handleEditSubAccount = (row: any) => {
  editForm.id = row.id
  editForm.name = row.name
  editForm.phone = row.phone
  editForm.password = ''
  showEditDialog.value = true
}

// 更新子账号
const handleUpdateSubAccount = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    submitting.value = true

    const updateData: any = {
      id: editForm.id,
      name: editForm.name,
      phone: editForm.phone
    }

    // 只有输入了密码才更新密码
    if (editForm.password) {
      updateData.password = editForm.password
    }

    const response = await updateSubAccount(updateData)
    console.log('更新子账号响应:', response)

    // sendRequestViaBackground已自动处理成功/失败判断，成功时直接返回data内容
    ElMessage.success('子账号更新成功')
    showEditDialog.value = false
    await loadSubAccountList()
  } catch (error) {
    console.error('更新子账号失败:', error)
  } finally {
    submitting.value = false
  }
}

// 切换状态
const handleToggleStatus = async (row: any) => {
  const newStatus = row.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'

  try {
    await ElMessageBox.confirm(
      `确定要${action}子账号"${row.name}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await toggleSubAccountStatus({
      id: row.id,
      status: newStatus
    })
    console.log('切换子账号状态响应:', response)

    // sendRequestViaBackground已自动处理成功/失败判断，成功时直接返回data内容
    ElMessage.success(`${action}成功`)
    await loadSubAccountList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}子账号失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除子账号
const handleDeleteSubAccount = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除子账号"${row.name}"吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteSubAccount({
      id: row.id
    })
    console.log('删除子账号响应:', response)

    // sendRequestViaBackground已自动处理成功/失败判断，成功时直接返回data内容
    ElMessage.success('删除成功')
    await loadSubAccountList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除子账号失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 查看商品
const handleViewGoods = (row: SubAccount) => {
  // 跳转到子账号商品列表页面，传递子账号ID作为参数
  router.push({
    name: 'SubAccountGoodsListByUser',
    params: {
      subAccountId: row.id.toString()
    },
    query: { subAccountName: row.name }
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadSubAccountList()
})
</script>

<style scoped>
.sub-account-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
}

.description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.filter-form {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-form-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.action-bar {
  margin-bottom: 20px;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
