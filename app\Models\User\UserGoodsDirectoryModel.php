<?php

namespace App\Models\User;

use App\Models\BaseModel;
use App\Models\User\GoodsModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserGoodsDirectoryModel extends BaseModel
{
    protected $table = 'user_goods_directory';

    protected $fillable = [
        'user_id',
        'user_sub_id',
        'name',
        'description',
        'sort_order',
        'status_sub',//子账号是否可见 默认0 不可见   1可见
        'status',
        'goods_count'
    ];

    protected $casts = [
        'user_id' => 'integer',
        'user_sub_id' => 'integer',
        'sort_order' => 'integer',
        'status_sub' => 'integer',
        'status' => 'integer',
        'goods_count' => 'integer',
        // 'created_at' => 'datetime',
        // 'updated_at' => 'datetime',
    ];

    /**
     * 关联用户（创建者）
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联子账号用户
     */
    public function subUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_sub_id');
    }

    /**
     * 关联商品
     */
    public function goods(): HasMany
    {
        return $this->hasMany(GoodsModel::class, 'directory_id');
    }

    /**
     * 按用户ID筛选
     */
    public function scopeByUserId($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 按状态筛选
     */
    public function scopeByStatus($query, int $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 按创建时间范围筛选
     */
    public function scopeByDateRange($query, $startDate = null, $endDate = null)
    {
        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }
        if ($endDate) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }
        return $query;
    }

    /**
     * 按ID排序（默认）
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('id', 'desc');
    }

    /**
     * 检查目录是否属于指定用户
     */
    public function belongsToUser(int $userId): bool
    {
        return $this->user_id === $userId;
    }

    /**
     * 检查目录是否属于指定子账号用户
     */
    public function belongsToSubUser(int $userSubId): bool
    {
        return $this->user_sub_id === $userSubId;
    }

    /**
     * 更新商品数量
     */
    public function updateGoodsCount(): void
    {
        $count = $this->goods()->count();
        $this->update(['goods_count' => $count]);
    }

    /**
     * 批量更新指定用户所有目录的商品数量
     * 使用统计计算的方法，不使用increment/decrement
     * 
     * @param int $userId 用户ID
     */
    public static function updateAllGoodsCountByUser(int $userId): void
    {
        // 获取用户所有目录
        $directories = self::where('user_id', $userId)->get();
        
        foreach ($directories as $directory) {
            // 统计该目录下的有效商品数量
            $goodsCount = GoodsModel::where('user_id', $userId)
                ->where('directory_id', $directory->id)
                ->where('status', 1)
                ->count();
            
            // 更新目录商品数量
            $directory->update(['goods_count' => $goodsCount]);
        }
    }

    /**
     * 重新计算单个目录的商品数量
     * 
     * @param int $userId 用户ID
     * @param int $directoryId 目录ID
     */
    public static function recalculateGoodsCount(int $userId, int $directoryId): void
    {
        $goodsCount = GoodsModel::where('user_id', $userId)
            ->where('directory_id', $directoryId)
            ->where('status', 1)
            ->count();

        self::where('user_id', $userId)
            ->where('id', $directoryId)
            ->update(['goods_count' => $goodsCount]);
    }

    /**
     * 获取状态名称
     */
    public function getStatusName(): string
    {
        return $this->status === 1 ? '启用' : '禁用';
    }

    /**
     * 获取子账号可见状态名称
     */
    public function getStatusSubName(): string
    {
        return $this->status_sub === 1 ? '可见' : '不可见';
    }
}