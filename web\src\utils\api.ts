/**
 * API请求工具函数
 * 用于在web项目中发送带有认证token的请求
 */
import { getLocalStorage } from './storage';
import { handleApiResponse, handleN11ApiResponse, handleDeepSeekApiResponse } from './responseHandler';

interface RequestConfig {
  headers?: Record<string, string>;
  method?: string;
  body?: any;
  [key: string]: any;
}

interface BackgroundRequest {
  funName: string;
  url: string;
  method: string;
  data?: any;
  params?: any;
  auth?: boolean;
  encrypto?: boolean;
  [key: string]: any;
}

/**
 * 用户信息响应接口
 */
interface UserInfoResponse {
  id: number;           // 用户ID
  phone: string;        // 手机号
  is_vip: number;       // VIP状态 (1: 是VIP, 0: 不是VIP)
  vip_end_time: string; // VIP到期时间
  is_admin: number;     // 管理员状态 (1: 是管理员, 0: 不是管理员)
  is_card_admin: number; // 卡密管理员状态 (1: 是卡密管理员, 0: 不是卡密管理员)
  is_sub: number;       // 子账号状态 (1: 是子账号, 0: 不是子账号)
  appid: string;        // 应用ID
  appstatus: number;    // 应用状态
  token: string;        // 认证令牌
  points: number;       // 用户积分
}

/**
 * 创建带有认证头的请求配置
 * @param {RequestConfig} config 请求配置
 * @returns {Promise<RequestConfig>} 带有认证头的请求配置
 */
export const createAuthConfig = async (config: RequestConfig = {}): Promise<RequestConfig> => {
  try {
    // 从chrome.storage.local获取token
    const result = await getLocalStorage(['token']);

    // 创建headers对象（如果不存在）
    const headers = config.headers || {};

    // 如果有token，添加到Authorization头
    if (result.token) {
      headers.Authorization = `Bearer ${result.token}`;
    }

    return {
      ...config,
      headers
    };
  } catch (error) {
    console.error('创建认证配置失败:', error);
    return config;
  }
};

/**
 * 发送带有认证的API请求
 * @param {string} url 请求URL
 * @param {RequestConfig} options 请求选项
 * @returns {Promise<any>} 请求结果
 */
export const fetchWithAuth = async (url: string, options: RequestConfig = {}): Promise<any> => {
  try {
    // 获取带认证的配置
    const config = await createAuthConfig(options);

    // 发送请求
    const response = await fetch(url, config);

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status} ${response.statusText}`);
    }

    // 解析响应数据
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    } else {
      return await response.text();
    }
  } catch (error) {
    console.error('API请求失败:', error);
    throw error;
  }
};

/**
 * 通过background页面发送API请求
 * 使用chrome.runtime.sendMessage与background页面通信
 * @param {BackgroundRequest} request 请求对象
 * @returns {Promise<any>} 请求结果
 */
export const sendRequestViaBackground = (request: BackgroundRequest): Promise<any> => {
  return new Promise((resolve, reject) => {
    // 确保请求对象包含必要的字段
    console.log('发送请求:', request)
    if (!request.funName || !request.url || !request.method) {
      reject(new Error('请求缺少必要的字段: funName, url, method'));
      return;
    }

    // 默认添加auth: true，除非明确指定为false
    if (request.auth === undefined) {
      request.auth = true;
    }

    // 发送消息到background页面
    chrome.runtime.sendMessage({
      funType: 'axios',
      pramas: request.data || request.params || {},
      headers: request.headers || {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
      },
      method: request.method,
      url: request.url,
      auth: request.auth,
      encrypto: request.encrypto,
      timeout: request.timeout || 59000,
      ...request
    }, (response) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      console.log('response-------------', response);
      if (response && response[0]) {
        // 使用响应处理函数处理结果
        console.log('response', response);
        const statusCode = response[0].status;
        const result = response[0].data;

        const url = new URL(request.url);
        const hostname = url.hostname;

        let handledSuccessfully = false;
        if (hostname === 'api.n11.com') {
          handledSuccessfully = handleN11ApiResponse(result, statusCode);
        } else if (hostname === 'api.deepseek.com') {
          handledSuccessfully = handleDeepSeekApiResponse(result, statusCode);
        } else {
          handledSuccessfully = handleApiResponse(result, statusCode);
        }

        if (handledSuccessfully) {
          if (hostname === 'api.n11.com') {
            resolve(result);
          } else if (hostname === 'api.deepseek.com') {
            resolve(result);
          } else {
            resolve(result.data);
          }
        } else {
          console.warn('API响应处理失败，返回原始数据供调用方处理:', result);
          if (hostname === 'api.n11.com') {
            resolve(result);
          } else {
            reject(new Error('API响应处理失败'));
          }
        }
      } else {
        reject(new Error('请求未返回有效数据'));
      }
    });
  });
};

/**
 * 获取用户信息API
 * @returns {Promise<UserInfoResponse>} 用户信息，包含权限状态和应用信息
 */
export const fetchUserInfo = async (): Promise<UserInfoResponse> => {
  // 获取配置中的用户信息URL
  // 注意：这里需要从Chrome扩展的配置中获取URL
  // 由于web项目无法直接访问Chrome扩展的配置，我们需要通过background获取
  return new Promise((resolve, reject) => {
    // 首先获取配置
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey: 'apiUserInfoUrl'
    }, (configResponse) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }

      const apiUserInfoUrl = configResponse?.url || 'http://tsa.test.com/api/user/info'; // 默认开发环境URL

      // 发送用户信息请求
      sendRequestViaBackground({
        funName: 'fetchUserInfoRequest',
        url: apiUserInfoUrl,
        method: 'get',
        auth: true,
        encrypto: true // 启用加密
      }).then(resolve).catch(reject);
    });
  });
};
