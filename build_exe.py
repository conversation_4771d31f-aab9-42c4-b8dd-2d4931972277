#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXE打包脚本
使用PyInstaller将应用程序打包成可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller 已安装 (版本: {PyInstaller.__version__})")
        return True
    except ImportError:
        print("✗ PyInstaller 未安装")
        return False


def install_pyinstaller():
    """安装PyInstaller"""
    try:
        print("正在安装 PyInstaller...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "pyinstaller"
        ], capture_output=True, text=True, check=True)
        
        print("✓ PyInstaller 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ PyInstaller 安装失败: {e}")
        return False


def create_spec_file():
    """创建PyInstaller规格文件"""
    # 获取图标文件路径
    icon_path = os.path.join('resource', 'icons', 'app_icon.ico')
    if not os.path.exists(icon_path):
        print(f"⚠️  警告: 图标文件不存在: {icon_path}")
        icon_path = None
    else:
        print(f"✓ 找到图标文件: {icon_path}")

    # 构建spec文件内容
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'requests',
        'sqlite3',
        'json',
        'urllib3'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='跨境蜂助手',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,'''

    # 根据图标文件是否存在添加icon参数
    if icon_path:
        # 使用绝对路径确保图标正确嵌入到EXE中
        icon_path_abs = os.path.abspath(icon_path)
        icon_path_escaped = icon_path_abs.replace('\\', '\\\\')
        spec_content += f"\n    icon=r'{icon_path_escaped}',"
    else:
        spec_content += "\n    icon=None,"

    spec_content += "\n)\n"
    
    with open('app.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建 app.spec 文件")


def build_executable():
    """构建可执行文件"""
    try:
        print("开始构建可执行文件...")
        print("这可能需要几分钟时间，请耐心等待...")
        
        # 使用spec文件构建
        result = subprocess.run([
            sys.executable, "-m", "PyInstaller", 
            "--clean", 
            "app.spec"
        ], capture_output=True, text=True, check=True)
        
        print("✓ 可执行文件构建成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def create_directory_structure():
    """在dist目录中创建必要的目录结构"""
    try:
        dist_dir = 'dist'
        resource_dir = os.path.join(dist_dir, 'resource')
        db_dir = os.path.join(resource_dir, 'db')
        log_dir = os.path.join(dist_dir, 'log')
        attachment_dir = os.path.join(dist_dir, 'attachment')

        # 创建resource目录
        if not os.path.exists(resource_dir):
            os.makedirs(resource_dir)
            print(f"✓ 已创建目录: {resource_dir}")
        else:
            print(f"✓ 目录已存在: {resource_dir}")

        # 创建db目录
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
            print(f"✓ 已创建目录: {db_dir}")
        else:
            print(f"✓ 目录已存在: {db_dir}")

        # 创建log目录
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            print(f"✓ 已创建目录: {log_dir}")
        else:
            print(f"✓ 目录已存在: {log_dir}")

        # 创建attachment目录
        if not os.path.exists(attachment_dir):
            os.makedirs(attachment_dir)
            print(f"✓ 已创建目录: {attachment_dir}")
        else:
            print(f"✓ 目录已存在: {attachment_dir}")

        print("✓ 目录结构创建完成")

    except Exception as e:
        print(f"✗ 创建目录结构失败: {e}")


def clean_build_files():
    """清理构建文件"""
    dirs_to_remove = ['build', '__pycache__']
    files_to_remove = ['app.spec']

    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已清理 {dir_name} 目录")
            except Exception as e:
                print(f"✗ 清理 {dir_name} 失败: {e}")

    for file_name in files_to_remove:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✓ 已清理 {file_name} 文件")
            except Exception as e:
                print(f"✗ 清理 {file_name} 失败: {e}")


def main():
    """主函数"""
    print("跨境蜂助手 - EXE打包脚本")
    print("=" * 40)
    
    # 检查当前目录
    if not os.path.exists('main.py'):
        print("✗ 错误: 找不到 main.py 文件")
        print("请确保在项目根目录下运行此脚本")
        return False
    
    # 检查PyInstaller
    if not check_pyinstaller():
        print("正在安装 PyInstaller...")
        if not install_pyinstaller():
            print("✗ 无法安装 PyInstaller，请手动安装:")
            print("pip install pyinstaller")
            return False
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if build_executable():
        print("\n" + "=" * 40)
        print("✓ 打包完成！")

        # 检查输出文件
        exe_path = os.path.join('dist', '跨境蜂助手.exe')
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"可执行文件位置: {exe_path}")
            print(f"文件大小: {file_size:.1f} MB")

            # 创建必要的目录结构
            create_directory_structure()
        else:
            print("✗ 警告: 找不到生成的可执行文件")
        
        # 询问是否清理构建文件
        try:
            response = input("\n是否清理构建文件? (y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                clean_build_files()
        except KeyboardInterrupt:
            print("\n操作已取消")
        
        return True
    else:
        print("\n✗ 打包失败")
        return False


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n使用说明:")
        print("1. 可执行文件位于 dist 目录中")
        print("2. 可以将整个 dist 目录复制到其他计算机上运行")
        print("3. 程序会在 EXE 文件同目录下的 resource/db/ 文件夹中创建 kjf.db 数据库文件")
        print("4. 数据库文件会随着 EXE 文件一起保存，确保配置信息不会丢失")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
