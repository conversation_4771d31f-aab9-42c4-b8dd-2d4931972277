<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Helpers\Traits\ApiResponse;
use App\Models\User\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * API认证中间件
 * 用于EXE程序的API接口认证
 */
class ApiAuthMiddleware
{
    use ApiResponse;
    /**
     * 处理传入的请求
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            // 从HTTP请求头中获取appid和appsecret
            $appId = $request->header('X-App-Id');
            $appSecret = $request->header('X-App-Secret');

            // 验证必要参数是否存在
            if (empty($appId) || empty($appSecret)) {
                return $this->handleAuthenticationError('缺少必要的认证参数', $appId, $request);
            }

            // 根据appid查询用户信息
            $user = User::where('appid', $appId)->first();

            if (!$user) {
                return $this->handleAuthenticationError('无效的appid', $appId, $request);
            }

            // 验证用户状态和API状态
            if ($user->status != 1) {
                return $this->handleAuthenticationError('用户账号已被禁用', $appId, $request);
            }

            if ($user->appstatus != 1) {
                return $this->handleAuthenticationError('API功能已被禁用', $appId, $request);
            }

            // 验证appsecret
            if ($user->appsecret !== $appSecret) {
                return $this->handleAuthenticationError('无效的appsecret', $appId, $request);
            }

            // 验证通过，将用户信息写入request对象
            $request->attributes->set('api_user', $user);
            return $next($request);

        } catch (\Exception $e) {
            // 处理系统异常
            $this->logAuthFailure('系统错误: ' . $e->getMessage(), $appId ?? null, $request);
            return $this->exeErrorResponse(
                '系统错误',
                500,
                [
                    'type' => 'system_error',
                    'detail' => '服务器内部错误，请稍后重试'
                ]
            );
        }
    }

    /**
     * 处理认证错误，返回统一的JSON响应
     *
     * @param string $message
     * @param string|null $appId
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    private function handleAuthenticationError(string $message, ?string $appId, Request $request): \Illuminate\Http\JsonResponse
    {
        // 记录认证失败日志
        $this->logAuthFailure($message, $appId, $request);

        // 返回统一的错误响应格式
        return $this->exeErrorResponse(
            $message,
            401,
            [
                'type' => 'authentication_error',
                'detail' => '请检查appid和appsecret是否正确'
            ]
        );
    }

    /**
     * 记录认证失败日志
     *
     * @param string $reason
     * @param string|null $appId
     * @param Request $request
     */
    private function logAuthFailure(string $reason, ?string $appId, Request $request): void
    {
        Log::channel('api_auth')->warning('API认证失败', [
            'reason' => $reason,
            'appid' => $appId,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'route' => $request->route()?->getName() ?? $request->path(),
            'timestamp' => now()->toDateTimeString()
        ]);
    }
}