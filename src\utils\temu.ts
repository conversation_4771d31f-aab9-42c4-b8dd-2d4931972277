import { createDomObserver } from './domObserver';
import config from '@/config'; // 导入配置文件
import { getLocalStorageData } from '@/utils/new/utils';
import { handleApiResponse } from './responseHandler'; // 导入新的响应处理函数
import { ElNotification } from 'element-plus';
import { processGoodsImages } from './imageProcess';
import { isUserAuthorizedForBatchCollection } from '@/utils/userValidation';
import { messageManager } from './temu/messageManager';

// 媒体文件处理控制配置
// 0: 跳过图片、视频、PDF文件的处理流程
// 1: 保持现有的媒体文件处理流程不变
const SKIP_MEDIA_PROCESSING: 0 | 1 = 0;

// Function to generate a random string similar to verifyauthtoken
export const generateRandomVerifyAuthToken = (): string => {
  const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-';
  let randomString = '';
  for (let i = 0; i < 39; i++) {
    randomString += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return `pc-${randomString}`;
};

// Function to generate a random string similar to x-phan-data
export const generateRandomXPhanData = (): string => {
  const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let randomString = '';
  for (let i = 0; i < 50; i++) {
    randomString += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return randomString;
};

// Function to validate URL format
export const isTemuGoodsDetailPage = (): boolean => {
  const url = window.location.href;
  const regex = /https?:\/\/www\.temu\.com\/([\S]*-\d{11,}\.html[\S]*|goods\.html.*goods_id=.*)/;
  return regex.test(url);
};

export const extractRawDataFromHtml = (html: string): any => {
  const regex = /window\.rawData=({.*?});document\.dispatchEvent/s;
  const match = html.match(regex);
  if (match && match[1]) {
    try {
      return JSON.parse(match[1]);
    } catch (e) {
      console.error('Failed to parse JSON:', match[1]);
      return null;
    }
  }
  console.warn('No rawData found in HTML');
  return null;
};

export const hasGoodsInfo = (data: any): boolean => {
  return data.store && data.store.goods && data.store.sku && Array.isArray(data.store.sku) && data.store.sku.length > 0 && (
    Array.isArray(data.store.goods) ? data.store.goods.length > 0 :
    typeof data.store.goods === 'object' && data.store.goods !== null && Object.keys(data.store.goods).length > 0
  );
}

//从传递的JSON字符串中 提取商品数据
export const extractGoodsDataFromJson = (json: any): any => {
  const data = json.store;
  console.log('data----------',data);
  const goodsData = data.goods;
  const goodsId = goodsData.goodsId;
  const mallId = goodsData.mallId;
  const goodsName = goodsData.goodsName
  const catId = goodsData.catId;
  const goodsSoldQuantity = isNaN(Number(goodsData.soldQuantity)) ? 0 : Number(goodsData.soldQuantity);
  const goodsScore = isNaN(Number(data.review?.reviewData?.goodsScore)) ? 0 : Number(data.review?.reviewData?.goodsScore);
  const goodsHasInstruction = Object.prototype.hasOwnProperty.call(data.moduleMap.productDetailExtensionModule?.data?.instruction || {}, 'imgInstruction');

  const goodsHasGuideFile = Object.prototype.hasOwnProperty.call(data.moduleMap.productDetailExtensionModule?.data?.instruction || {}, 'guideFile');
  const goodsHasGuideFileSplit = Object.prototype.hasOwnProperty.call(data.moduleMap.productDetailExtensionModule?.data?.instruction || {}, 'guideFileSplit');

  let goodsPdf = '';
  if(goodsHasGuideFile){
    goodsPdf = data.moduleMap.productDetailExtensionModule?.data?.instruction?.guideFile?.url;
    console.log('goodsPdf111---------------------',goodsPdf);
  }

  if(!goodsPdf && goodsHasGuideFileSplit){
    goodsPdf = data.moduleMap.productDetailExtensionModule?.data?.instruction?.guideFileSplit?.guideFileList[0]?.url;
    console.log('goodsPdf222---------------------',goodsPdf);
  }


  //首页分类信息
  const crumbOptList = data.crumbOptList;
  const { nameString:optNameString, idNameMap:optIdNameMap } = processCrumbOptList(crumbOptList);
  const skuListData = data.sku;
  const skuInfoMapData = data.skuInfoMap;
  const skcData = goodsData.skc;
  const skc = processSkcData(skcData);
  const sku = processSkuList(skuListData,skuInfoMapData,skc);
  const galleryList = goodsData.gallery;

  let { nonSpecItems:pics, videoItem:video } = processGalleryList(galleryList, sku);

  video = goodsData.video ? goodsData.video : video;

  const goodsPropertyData = goodsData.goodsProperty;
  const goodsProperty = processGoodsProperty(goodsPropertyData);


  let goodsDetailData = data.productDetail?.floorList;
  console.log("goodsDetailData--------------------------------------------",goodsDetailData);

  // 从 floorList 中提取 type === 3 的项的 text 属性
  const floorListTextProperties = extractFloorListTextProperties(goodsDetailData);

  // 处理商品警告信息并添加到floorListTextProperties
  const warningTexts = extractProductWarningText(goodsData.productWarning || '');
  floorListTextProperties.push(...warningTexts);

  // 将提取的 text 属性添加到 goodsProperty 数组中
  if (floorListTextProperties.length > 0) {
    goodsProperty.push({
      key: 'floorListProperties',
      values: floorListTextProperties
    });
  }


  let goodsDetail = processGoodsDetailData(goodsDetailData);
  if(goodsDetail.length <= 0){
    goodsDetailData = data.productDetailFlatList || [];
    goodsDetail = processGoodsDetailData(goodsDetailData);
  }


  console.log('goodsDetail',goodsDetail);



  processVideoWithSkc(skc, video);

  return { goodsId, mallId, goodsName, catId, optNameString, optIdNameMap, sku, pics, video, goodsProperty, goodsDetail, skc, goodsSoldQuantity, goodsScore, goodsHasInstruction, goodsPdf};
};

// 新增函数处理 crumbOptList
export const processCrumbOptList = (crumbOptList: any[]): { nameString: string, idNameMap: { [key: string]: string } } => {
  const names: string[] = [];
  const idNameMap: { [key: string]: string } = {};

  if (!crumbOptList) {
    return { nameString: "", idNameMap: {} };
  }

  for (const item of crumbOptList) {
    if (item.optId > 0) {
      names.push(item.optName);
      idNameMap[item.optId] = item.optName;
    }
  }

  const nameString = names.join("->");

  return { nameString, idNameMap };
};

export const processSkuList = (skuList: any[], skuInfoMap: any[], skc: { [key: string]: any }): any[] => {
  const processedSkus: any[] = [];

  if (!skuList) {
    return processedSkus;
  }

  const presaleSkus: { [key: string]: any } = {};
  for (const key in skuInfoMap) {
    const item = skuInfoMap[key];
    if (item.selectSkuTip && item.selectSkuTip.dialog && item.selectSkuTip.dialog.title.includes('预售')) {
      presaleSkus[key] = item;
    }
  }

  for (const sku of skuList) {
    if (sku.isOnsale === 1) {
      if (sku.skuId && presaleSkus[sku.skuId]) {
        const presaleInfo = presaleSkus[sku.skuId].selectSkuTip;
        sku.presaleTitle = presaleInfo.dialog.title;  // 添加预售标题属性
        sku.presaleText = presaleInfo.text;  // 添加预售文本属性
        sku.isPresale = 1;
      }else{
        sku.presaleTitle = '';
        sku.presaleText = '';
        sku.isPresale = 0;
      }
      const skuId = sku.skuId;
      const goodsId = sku.goodsId;
      const thumbUrl = sku.thumbUrl;
      const limitQuantity = sku.limitQuantity;
      const galleryId = sku.galleryId;
      const presaleTitle = sku.presaleTitle;
      const presaleText = sku.presaleText;
      const isPresale = sku.isPresale;
      // 获取项 salePriceRich 遍历项salePriceRich 取项中type为currency的text 作为返回key为 currency的值 取type为 price 的text 作为返回key为price的值
      let currency = '';
      let price = '';
      if (sku.salePriceRich) {
        for (const priceItem of sku.salePriceRich) {
          if (priceItem.type === 'currency') {
            currency = priceItem.text;
          } else if (priceItem.type === 'price') {
            price = priceItem.text;
          }
        }
      }

      // 遍历specs项 取每项的 specKey 和 specValue的值 拼接2种格式值
      const specKeyValues: string[] = [];
      const specValues: string[] = [];
      const specValueIds: number[] = [];
      let mainSpecValueId = 0;
      let mainSpecValue = '';
      if (sku.specs) {
        for (const spec of sku.specs) {
          specKeyValues.push(`${spec.specKey}:${spec.specValue}`);
          specValues.push(spec.specValue);
          if (spec.specValueId !== undefined) {
            specValueIds.push(spec.specValueId);
          }
          if(mainSpecValueId == 0 && spec.specValueId !== undefined){
            mainSpecValueId = spec.specValueId;
            mainSpecValue = spec.specValue;
          }
        }
      }
      let gallery: any[] = [];
      if(skc[mainSpecValueId] && skc[mainSpecValueId].gallery && skc[mainSpecValueId].gallery.length > 0){
        gallery = skc[mainSpecValueId].gallery;
      }
      const specKeyValuesString = specKeyValues.join(',');
      const specValuesString = specValues.join(',');

      processedSkus.push({
        skuId,
        goodsId,
        thumbUrl,
        limitQuantity,
        galleryId,
        currency,
        price,
        specKeyValues: specKeyValuesString,
        specValues: specValuesString,
        specValueIds: specValueIds,
        presaleTitle,
        presaleText,
        isPresale,
        mainSpecValueId,
        mainSpecValue,
        skcGallery: gallery,
        isSkcGallery: gallery.length > 0 ? 1 : 0,
      });
    }
  }

  return processedSkus;
};

export const processGalleryList = (galleryList: any[], processedSkus: any[]): { nonSpecItems: any[], videoItem: any } => {
  const nonSpecItems: any[] = [];
  let videoItem: any = null;

  if (!galleryList) {
    return { nonSpecItems: [], videoItem: null };
  }

  for (const galleryItem of galleryList) {
    if (galleryItem && typeof galleryItem === 'object') {
      if (!galleryItem.showTextSpecs) {
        // 项不包含 showTextSpecs
        const itemInfo: any = {
          id: galleryItem.id,
          url: galleryItem.url,
        };

        if (galleryItem.video) {
          itemInfo.video = {
            url: galleryItem.video.url,
            videoUrl: galleryItem.video.videoUrl,
          };
          if (videoItem === null) {
              videoItem = itemInfo.video;
          }
        }
        console.log('galleryItem-------------', galleryItem)
        console.log('galleryItem.url--------------', galleryItem.url)
        console.log('galleryItem.videoUrl--------------', galleryItem.videoUrl)
        // 如果该项既有url也有videoUrl，则不push
        if (!(galleryItem.url && galleryItem.videoUrl)) {
          nonSpecItems.push(itemInfo);
        }
      } else {
        // 项包含 showTextSpecs
        const specId = galleryItem.id;
        if (specId !== undefined) {
          for (const sku of processedSkus) {
            if (sku.galleryId && sku.galleryId === specId) {
              sku.url = galleryItem.url;
            }
          }
        }
      }
    }
  }

  return { nonSpecItems, videoItem };
};

export const processGoodsProperty = (goodsPropertyList: any[]): any[] => {
  if (!Array.isArray(goodsPropertyList)) return [];
  // 使用 filter 和 map 方法简化代码
  return goodsPropertyList
    .filter(property => property && property.key !== undefined && property.values !== undefined)
    .map(property => ({
      key: property.key,
      values: property.values,
    }));
};

export const processGoodsDetailData = (goodsDetailData: any[]): any[] => {
  if (!Array.isArray(goodsDetailData)) return [];
  return goodsDetailData
    .filter(detail => detail && detail.url !== undefined)
    .map(detail => ({ url: detail.url }));
};

/**
 * 从 floorList 数组中提取 type === 3 的项的 text 属性
 * @param floorList floorList 数组数据
 * @returns 提取到的 text 内容数组
 */
export const extractFloorListTextProperties = (floorList: any[]): string[] => {
  const textProperties: string[] = [];

  // 安全检查：确保 floorList 是数组
  if (!Array.isArray(floorList)) {
    return textProperties;
  }

  // 遍历 floorList 数组
  for (const floorItem of floorList) {
    // 安全检查：确保项存在且 type === 3
    if (floorItem && floorItem.type === 3) {
      // 安全检查：确保 items 数组存在且有第一个元素
      if (Array.isArray(floorItem.items) && floorItem.items.length > 0) {
        const firstItem = floorItem.items[0];
        // 安全检查：确保第一个元素存在 text 属性
        if (firstItem && typeof firstItem.text === 'string') {
          textProperties.push(firstItem.text);
        }
      }
    }
  }

  return textProperties;
};

/**
 * 从商品警告信息中提取文本内容
 * @param productWarning 商品警告数据
 * @returns 提取到的警告文本数组
 */
export const extractProductWarningText = (productWarning: any): string[] => {
  const warningTexts: string[] = [];

  if (!productWarning) {
    return warningTexts;
  }

  try {
    const warningData = typeof productWarning === 'string' ? JSON.parse(productWarning) : productWarning;

    if (warningData.tips && Array.isArray(warningData.tips)) {
      for (const tip of warningData.tips) {
        if (tip.type === 1 && tip.richText && tip.richText.textRich && Array.isArray(tip.richText.textRich)) {
          for (const textItem of tip.richText.textRich) {
            if (textItem.type === 0 && textItem.value && !textItem.value.startsWith('Warning:')) {
              warningTexts.push(textItem.value);
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('Failed to parse product warning:', error);
  }

  return warningTexts;
};

export const processSkcData = (skcData: any[]): { [key: string]: any } => {
  const result: { [key: string]: any } = {};
  if (!Array.isArray(skcData)) return result;

  skcData
    .filter(skc => skc && skc.gallery !== undefined && skc.specId !== undefined)
    .forEach(skc => {
      result[skc.specId] = {
        skcId: skc.skcId,
        specId: skc.specId,
        specValue: skc.specValue,
        gallery: skc.gallery
      };
    });

  return result;
};

export const processVideoWithSkc = (skc: any, video: any): void => {
  if (!skc || !video || !video.videoUrl) {
    if (video) {
      video.isSkuVideo = 0;
      video.specId = 0;
      video.specValue = '';
    }
    return;
  }

  let isSkuVideoFound = false;
  let specId = 0;
  let specValue = '';
  for (const skcItem of Object.values(skc) as any[]) {
    if (skcItem && Array.isArray(skcItem.gallery)) {
      for (const galleryItem of skcItem.gallery) {
        if (galleryItem && galleryItem.video && galleryItem.video.videoUrl === video.videoUrl) {
          isSkuVideoFound = true;
          specId = skcItem.specId;
          specValue = skcItem.specValue;
          break;
        }
      }
    }
    if (isSkuVideoFound) {
      break;
    }
  }

  video.isSkuVideo = isSkuVideoFound ? 1 : 0;
  video.specId = specId;
  video.specValue = specValue;
};

/**
 * 观察Temu商品详情页并提取数据
 * @param callbacks 回调函数对象
 * @returns 清理函数
 */
export const observeTemuProductPage = ({
  onDataExtracted,
  onSecurityVerification,
  onError
}: {
  onDataExtracted: (goodsData: any) => void;
  onSecurityVerification?: () => void;
  onError?: (message: string) => void;
}) => {
  if (!isTemuGoodsDetailPage()) {
    console.log("不是Temu商品详情页");
    //if (onError) onError("不是Temu商品详情页");
    return { cleanup: () => {} };
  }

  console.log('Temu商品详情页，开始观察');
  //1分钟超时
  return createDomObserver({
    textPattern: 'window.rawData=',
    timeout: 60000,
    maxRetries: 10,
    initialDelay: 300,
    onSuccess: async () => {
      try {
        const htmlContent = document.documentElement.outerHTML;
        const rawData = extractRawDataFromHtml(htmlContent);

        if (rawData) {
          console.log('成功获取rawData数据');
          console.log('rawData', rawData);
          if (hasGoodsInfo(rawData)) {
            const goodsData = extractGoodsDataFromJson(rawData);

            // 如果存在介绍图片，则调用接口获取并添加到 goodsData
            if (goodsData.goodsHasInstruction) {
              try {
                const instructionImages = await fetchTemuInstructionImages(goodsData.goodsId);
                goodsData.instructionImages = instructionImages;
                console.log('Instruction images fetched:', instructionImages);
              } catch (imgError) {
                console.error('Error fetching instruction images:', imgError);
                // 即使图片获取失败，也继续处理其他数据
              }
            }

            onDataExtracted(goodsData);
          } else if (onSecurityVerification) {
            // 安全验证提示
            onSecurityVerification();
          }
        } else {
          throw new Error('无法解析rawData');
        }
      } catch (error) {
        console.error('处理数据时出错:', error);
        if (onError) onError('处理数据时出错: ' + (error instanceof Error ? error.message : String(error)));
      }
    },
    onTimeout: () => {
      console.log('提取数据超时');
      if (onError) onError('提取数据超时，请刷新页面重试');
    }
  });
};

/**
 * 提交商品数据到服务器
 * @param goodsData 商品数据
 * @param directoryName 目录名称，用于成功提示
 */
export const submitGoodsData = async (goodsData: any, directoryName?: string): Promise<void> => {
  const url = (config as any).apiTemuGoodsAddUrl;
  if (!url) {
    console.error('Temu goods add URL is not configured.');
    return;
  }

  return new Promise<void>(async (resolve, reject) => {
    try {
      console.log('Submitting goods data:', goodsData);
      // 获取本地存储的 token
      const localData = await getLocalStorageData(['token']) as { token?: string };
      const token = localData.token;

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = 'Bearer ' + token;
      }

      chrome.runtime.sendMessage({
        funType: 'axios', // Assuming 'axios' is the correct funType for network requests
        funName: 'submitTemuGoodsDataRequest', // A descriptive name for this request
        pramas: goodsData,
        headers: headers as any, // Cast to any for HeadersInit type compatibility
        method: 'post',
        url: url
      }, (response: any) => {
        if (response && response[0] && response[0].data) {
          const result = response[0].data;
          console.log('submitTemuGoodsDataRequest 返回结果:', result);

          // 使用新的响应处理函数
          if (handleApiResponse(result)) {
            // 如果 handleApiResponse 返回 true，表示响应正常或未处理错误
            console.log('Goods data submitted successfully:', result);

            // 发送采集完成消息（用于批量采集）
            const goodsId = result.data?.goods_id;
            const goodsName = goodsData?.goods_name || `商品${goodsId}`;
            if (goodsId) {
              try {
                // 检查用户是否有权限发送批量采集消息
                if (isUserAuthorizedForBatchCollection()) {
                  messageManager.sendCollectionComplete(goodsId.toString(), goodsName);
                  console.log('已发送采集完成消息:', { goodsId, goodsName });
                } else {
                  console.log('用户无批量采集权限，跳过发送采集完成消息');
                }
              } catch (error) {
                console.warn('发送采集完成消息失败:', error);
              }
            }

            // 根据配置决定是否进行媒体文件处理
            if (SKIP_MEDIA_PROCESSING === 0) {
              // 跳过媒体处理，直接显示采集成功消息
              console.log('跳过媒体文件处理，直接显示成功消息');
              showCompletionMessageWithoutMedia(result.data.goods_id, result.data.goods_sku_num, directoryName);
            } else {
              // 商品入库成功后，开始图片处理流程
              startImageProcessing(result.data.info_id, result.data.goods_id, result.data.goods_sku_num, directoryName);
            }
            resolve();
          } else {
            // 如果 handleApiResponse 返回 false，表示处理了错误通知
            // 这里不需要再次 reject，因为通知已经在 handleApiResponse 中处理
            // 如果需要进一步的错误处理（例如根据错误类型执行不同逻辑），可以在 handleApiResponse 内部或外部添加
            reject(new Error('API error: ' + (result && result.errMsg ? result.errMsg : 'Unknown error'))); // 仍然reject以便调用方可以捕获错误
          }
        } else {
          console.error('Failed to submit goods data - Invalid response structure:', response);
          reject(new Error('Invalid response structure'));
        }
      });
    } catch (error) {
      console.error('Failed to submit goods data:', error);
      reject(error);
    }
  });
};

/**
 * 开始图片处理流程
 * @param infoId 商品信息ID
 * @param goodsId 商品ID
 * @param skuNum SKU数量
 * @param directoryName 目录名称
 */
const startImageProcessing = async (infoId: number, goodsId: number, skuNum: number, directoryName?: string): Promise<void> => {
  let currentNotification: any = null; // 用于管理当前显示的通知

  try {
    // 显示处理开始通知
    currentNotification = ElNotification({
      title: '图片处理中',
      message: `商品ID[${goodsId}]正在获取图片处理信息...`,
      type: 'info',
      duration: 0
    });

    // 第一次请求获取处理信息
    const initialResult = await processGoodsImages(infoId, 1);

    if (!initialResult || !initialResult.data) {
      throw new Error('获取图片处理信息失败');
    }

    const processInfo = initialResult.data;
    let currentStep = 2; // 从第2步开始处理
    let isCompleted = processInfo.img_local_status === 1;

    // 关闭之前的通知，显示处理信息
    if (currentNotification) {
      currentNotification.close();
      currentNotification = null;
    }

    // 构建处理信息消息
    let processMessage = `商品ID[${goodsId}]开始处理`;
    const mediaInfo = [];
    if (processInfo.total_images > 0) {
      mediaInfo.push(`${processInfo.total_images}张图片`);
    }
    if (processInfo.total_videos > 0) {
      mediaInfo.push(`${processInfo.total_videos}个视频`);
    }
    if (processInfo.total_pdfs > 0) {
      mediaInfo.push(`${processInfo.total_pdfs}个PDF文件`);
    }
    if (mediaInfo.length > 0) {
      processMessage += `，共${mediaInfo.join('、')}`;
    }

    // 显示处理信息通知
    currentNotification = ElNotification({
      title: '开始图片处理',
      message: processMessage,
      type: 'info',
      duration: 0
    });

    // 如果已经处理完成，直接显示成功消息
    if (isCompleted) {
      if (currentNotification) {
        currentNotification.close();
        currentNotification = null;
      }
      showCompletionMessage(goodsId, skuNum, processInfo, directoryName);
      return;
    }

    // 计算总步骤数
    const totalSteps = processInfo.estimated_steps || 1;
    let processedSteps = 0;

    // 循环处理直到完成
    while (!isCompleted && processedSteps < totalSteps + 5) { // 添加最大循环次数防止无限循环
      try {
        const result = await processGoodsImages(infoId, currentStep);

        if (!result || !result.data) {
          throw new Error('图片处理请求失败');
        }

        const data = result.data;
        isCompleted = data.img_local_status === 1;

        // 更新进度通知
        const totalMedia = (data.total_images || 0) + (data.total_videos || 0) + (data.total_pdfs || 0);
        const processedMedia = (data.processed_images || 0) + (data.processed_videos || 0) + (data.processed_pdfs || 0);
        const progress = totalMedia > 0 ? Math.min(100, Math.round(processedMedia / totalMedia * 100)) : 0;
        if (currentNotification) {
          currentNotification.close();
          currentNotification = null;
        }

        if (!isCompleted) {
          currentNotification = ElNotification({
            title: '图片处理中',
            message: `商品ID[${goodsId}]图片处理进度: ${progress}% (${data.processed_images || 0}/${data.total_images || 0}张图片, ${data.processed_videos || 0}/${data.total_videos || 0}个视频, ${data.processed_pdfs || 0}/${data.total_pdfs || 0}个PDF文件)`,
            type: 'info',
            duration: 0
          });
        }

        currentStep++;
        processedSteps++;

        // 如果完成，显示成功消息
        if (isCompleted) {
          showCompletionMessage(goodsId, skuNum, data, directoryName);
          break;
        }

        // 短暂延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (stepError) {
        console.error(`图片处理步骤 ${currentStep} 失败:`, stepError);
        currentStep++;
        processedSteps++;

        // 如果连续失败多次，停止处理
        if (processedSteps >= totalSteps + 3) {
          break;
        }
      }
    }

    // 如果循环结束但未完成，显示警告
    if (!isCompleted) {
      if (currentNotification) {
        currentNotification.close();
        currentNotification = null;
      }
      ElNotification({
        title: '图片处理未完成',
        message: `商品ID[${goodsId}]图片处理可能未完全完成，请稍后在商品管理中查看`,
        type: 'warning',
        duration: 5000
      });
    }

  } catch (error) {
    console.error('图片处理流程失败:', error);
    if (currentNotification) {
      currentNotification.close();
      currentNotification = null;
    }
    ElNotification({
      title: '图片处理失败',
      message: `商品ID[${goodsId}]图片处理失败: ${error instanceof Error ? error.message : '未知错误'}`,
      type: 'error',
      duration: 5000
    });
  }
};

/**
 * 显示处理完成消息
 * @param goodsId 商品ID
 * @param skuNum SKU数量
 * @param processInfo 处理信息
 * @param directoryName 目录名称
 */
const showCompletionMessage = (goodsId: number, skuNum: number, processInfo: any, directoryName?: string): void => {
  const imageInfo = (processInfo.total_images || 0) > 0 ? `${processInfo.total_images}张图片` : '';
  const videoInfo = (processInfo.total_videos || 0) > 0 ? `${processInfo.total_videos}个视频` : '';
  const pdfInfo = (processInfo.total_pdfs || 0) > 0 ? `${processInfo.total_pdfs}个PDF文件` : '';

  let mediaInfo = '';
  const mediaItems = [imageInfo, videoInfo, pdfInfo].filter(item => item);
  if (mediaItems.length > 0) {
    mediaInfo = `，包含${mediaItems.join('、')}`;
  }

  const successMessage = directoryName
    ? `商品ID[${goodsId}]获取成功，共${skuNum}个SKU${mediaInfo}，已存储到目录[${directoryName}]`
    : `商品ID[${goodsId}]获取成功，共${skuNum}个SKU${mediaInfo}`;

  ElNotification({
    title: '商品采集完成',
    message: successMessage,
    type: 'success',
    duration: 0
  });
};

/**
 * 显示跳过媒体处理时的完成消息
 * @param goodsId 商品ID
 * @param skuNum SKU数量
 * @param directoryName 目录名称
 */
const showCompletionMessageWithoutMedia = (goodsId: number, skuNum: number, directoryName?: string): void => {
  const successMessage = directoryName
    ? `商品ID[${goodsId}]采集成功，共${skuNum}个SKU，已存储到目录[${directoryName}]`
    : `商品ID[${goodsId}]采集成功，共${skuNum}个SKU`;

  ElNotification({
    title: '商品采集完成',
    message: successMessage,
    type: 'success',
    duration: 0
  });
};

/**
 * 获取Temu商品介绍图片列表
 * @param goodsId 商品ID
 * @returns 介绍图片URL数组
 */
export const fetchTemuInstructionImages = async (goodsId: string): Promise<string[]> => {
  const apiUrl = 'https://www.temu.com/tr/api/oak/instructions/img_list';
  const referer = window.location.href;
  const verifyAuthToken = generateRandomVerifyAuthToken();
  const xPhanData = generateRandomXPhanData();

  const headers = {
    'authority': 'www.temu.com',
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'application/json;charset=UTF-8',
    'origin': 'https://www.temu.com',
    'referer': referer,
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
    'verifyauthtoken': verifyAuthToken,
    'x-phan-data': xPhanData,
  };

  const postData = {
    'goods_id': goodsId,
  };

  return new Promise<string[]>((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      funName: 'fetchTemuInstructionImagesRequest',
      pramas: postData,
      headers: headers as any,
      method: 'post',
      url: apiUrl,
      auth: false,
      encrypto: false
    }, (response: any) => {
      if (response && response[0] && response[0].data) {
        const result = response[0].data;
        console.log('fetchTemuInstructionImagesRequest 返回结果:', result);
        if (result.img_list && Array.isArray(result.img_list)) {
          const imgUrls = result.img_list.map((item: any) => item.url);
          resolve(imgUrls);
        } else {
          console.error('Failed to fetch instruction images - API error:', result.errMsg || 'Unknown error');
          reject(new Error('Failed to fetch instruction images: ' + (result.errMsg || 'Unknown error')));
        }
      } else {
        console.error('Failed to fetch instruction images - Invalid response structure:', response);
        reject(new Error('Invalid response structure from fetchTemuInstructionImagesRequest'));
      }
    });
  });
};


