<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\SubAccountService;
use Illuminate\Http\Request;

class SubAccountController extends Controller
{
    protected SubAccountService $subAccountService;

    public function __construct(SubAccountService $subAccountService)
    {
        $this->subAccountService = $subAccountService;
        parent::__construct();
    }

    /**
     * 获取子账号列表
     */
    public function list(Request $request)
    {
        $user = $request->attributes->get('user');
        // 检查是否为主账号
        if ($user['pid'] > 0) {
            return $this->apiError('您没有权限进行该操作');
        }
        
        $params = $request->only(['page', 'pageSize', 'name', 'phone', 'status']);
        $result = $this->subAccountService->getSubAccountList($user['id'], $params);
        return $this->apiSuccess($result);
    }

    /**
     * 创建子账号
     */
    public function create(Request $request)
    {
        $user = $request->attributes->get('user');

        if ($user['pid'] > 0) {
            return $this->apiError('您没有权限进行该操作');
        }

        $data = $request->validate([
            'name' => 'required|string|max:50',
            'phone' => 'required|string|max:50',
            'password' => 'required|string|min:6|max:20'
        ]);
        
        $result = $this->subAccountService->createSubAccount($user['id'], $data);
        return $this->apiSuccess($result, '子账号创建成功');
    }

    /**
     * 更新子账号
     */
    public function update(Request $request)
    {
        $user = $request->attributes->get('user');

        if ($user['pid'] > 0) {
            return $this->apiError('您没有权限进行该操作');
        }

        $data = $request->validate([
            'id' => 'required|integer',
            'name' => 'sometimes|string|max:50',
            'phone' => 'sometimes|string|max:50|unique:user,phone,' . $request->id,
            'password' => 'sometimes|string|min:6|max:20',
            'status' => 'sometimes|integer|in:0,1'
        ]);

        $result = $this->subAccountService->updateSubAccount($user['id'], $data);
        return $this->apiSuccess($result, '子账号更新成功');
    }

    /**
     * 删除子账号
     */
    public function delete(Request $request)
    {
        return $this->apiError('您没有权限进行该操作');
        $user = $request->attributes->get('user');
        if ($user['pid'] > 0) {
            return $this->apiError('您没有权限进行该操作');
        }
        $data = $request->validate([
            'id' => 'required|integer'
        ]);
        $result = $this->subAccountService->deleteSubAccount($user['id'], $data['id']);
        return $this->apiSuccess($result, '子账号删除成功');
    }

    /**
     * 启用/禁用子账号
     */
    public function toggleStatus(Request $request)
    {
        $user = $request->attributes->get('user');
        if ($user['pid'] > 0) {
            return $this->apiError('您没有权限进行该操作');
        }
        $data = $request->validate([
            'id' => 'required|integer',
            'status' => 'required|integer|in:0,1'
        ]);

        $result = $this->subAccountService->toggleSubAccountStatus($user['id'], $data['id'], $data['status']);
        return $this->apiSuccess($result, $data['status'] ? '子账号已启用' : '子账号已禁用');
    }

    /**
     * 获取子账号详情
     */
    public function detail(Request $request)
    {
        $user = $request->attributes->get('user');
        if ($user['pid'] > 0) {
            return $this->apiError('您没有权限进行该操作');
        }
        $data = $request->validate([
            'id' => 'required|integer'
        ]);

        $result = $this->subAccountService->getSubAccountDetail($user['id'], $data['id']);
        return $this->apiSuccess($result);
    }
}
