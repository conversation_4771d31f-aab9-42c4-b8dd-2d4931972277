<template>
  <el-dialog
    v-model="visible"
    :title="isBatchMode ? '批量生成卡密' : '生成卡密'"
    width="600px"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      :rules="formRules"
      ref="formRef"
      label-width="100px"
    >
      <!-- 批量模式下的生成数量 -->
      <el-form-item
        v-if="isBatchMode"
        label="生成数量"
        prop="quantity"
      >
        <el-input-number
          v-model="formData.quantity"
          :min="1"
          :max="1000"
          style="width: 240px;"
        />
      </el-form-item>

      <!-- 卡密名称 -->
      <el-form-item label="卡密名称" prop="card_name">
        <div style="display: flex; gap: 8px; align-items: center;">
          <el-input
            v-model="formData.card_name"
            placeholder="请输入卡密名称"
            style="width: 240px;"
          />
          <el-button size="small" @click="setCardName('年卡')">年卡</el-button>
          <el-button size="small" @click="setCardName('试用卡')">试用卡</el-button>
        </div>
      </el-form-item>

      <!-- 卡密类型 -->
      <el-form-item label="卡密类型" prop="card_type">
        <el-radio-group v-model="formData.card_type">
          <el-radio :label="1">有效期卡（积分+VIP）</el-radio>
          <el-radio :label="2">积分卡</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 价格 -->
      <el-form-item label="价格" prop="price">
        <div style="display: flex; align-items: center; width: 240px;">
          <el-input-number
            v-model="formData.price"
            :min="0"
            :precision="2"
            style="flex: 1;"
            :controls="false"
          />
          <span style="margin-left: 8px; color: #909399;">元</span>
        </div>
      </el-form-item>

      <!-- 积分 -->
      <el-form-item label="积分" prop="points">
        <div style="display: flex; align-items: center; width: 240px;">
          <el-input-number
            v-model="formData.points"
            :min="0"
            style="flex: 1;"
            :controls="false"
            @blur="handlePointsBlur"
          />
          <span style="margin-left: 8px; color: #909399;">分</span>
        </div>
      </el-form-item>

      <!-- VIP时长 - 仅在有效期卡类型时显示 -->
      <el-form-item
        v-if="formData.card_type === 1"
        label="VIP时长"
        prop="vip_days"
      >
        <div style="display: flex; gap: 8px; align-items: center;">
          <el-input-number
            v-model="formData.vip_days"
            :min="0"
            style="width: 160px;"
            :controls="false"
          />
          <el-select
            v-model="formData.vip_days_unit"
            style="width: 80px;"
            :disabled="vipDaysUnitDisabled"
          >
            <el-option label="年" value="year" />
            <el-option label="月" value="month" />
            <el-option label="天" value="day" />
          </el-select>
        </div>
      </el-form-item>

      <!-- 卡密前缀 -->
      <el-form-item label="卡密前缀" prop="prefix">
        <el-input
          v-model="formData.prefix"
          placeholder="默认为kjf"
          maxlength="3"
          style="width: 240px;"
        />
      </el-form-item>

      <!-- 有效期 这里强制不显示 不需要调整这里 保留 -->
      <el-form-item v-if="false" label="有效期" prop="valid_until">
        <el-date-picker
          v-model="formData.valid_until"
          type="date"
          placeholder="选择有效期（可选）"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 240px;"
        />
      </el-form-item>

      <!-- 描述 -->
      <el-form-item label="描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          rows="3"
          placeholder="请输入描述"
          style="width: 400px;"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
        :loading="loading"
      >
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

// Props定义
interface Props {
  modelValue: boolean
  isBatchMode?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isBatchMode: false,
  loading: false
})

// Emits定义
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  quantity: 10,
  card_name: '年卡',
  card_type: 1,
  price: 0,
  points: 200000, // 初次加载时积分默认值200000
  vip_days: 1,  // 默认VIP时长为1
  vip_days_unit: 'year', // 默认为年
  vip_level: 1, // 默认VIP等级为1，不在界面显示
  prefix: 'KJF',
  valid_until: '',
  description: ''
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单验证规则
const formRules = computed<FormRules>(() => {
  const rules: FormRules = {
    card_name: [{ required: true, message: '请输入卡密名称', trigger: 'blur' }],
    card_type: [{ required: true, message: '请选择卡密类型', trigger: 'change' }],
    price: [
      { required: true, message: '请输入价格', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          if (value <= 0) {
            callback(new Error('价格必须大于0'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    points: [
      { required: true, message: '请输入积分', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          if (value <= 0) {
            callback(new Error('积分必须大于0'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  // 有效期卡类型时，VIP时长也必须大于0
  if (formData.card_type === 1) {
    rules.vip_days = [
      { required: true, message: '请输入VIP时长', trigger: 'blur' },
      {
        validator: (_rule, value, callback) => {
          if (value <= 0) {
            callback(new Error('VIP时长必须大于0'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  // 批量模式下添加数量验证
  if (props.isBatchMode) {
    rules.quantity = [{ required: true, message: '请输入生成数量', trigger: 'blur' }]
  }

  return rules
})

// VIP时长单位是否禁用
const vipDaysUnitDisabled = computed(() => {
  return formData.card_name === '年卡'
})

// 监听对话框显示状态，重置表单
watch(visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 设置卡密名称
const setCardName = (name: string) => {
  formData.card_name = name

  // 根据卡密名称设置VIP时长单位和默认值，以及积分默认值
  if (name === '年卡') {
    formData.vip_days_unit = 'year'
    formData.vip_days = 1  // 年卡默认1年
    formData.points = 200000  // 年卡积分默认值200000
  } else if (name === '试用卡') {
    formData.vip_days_unit = 'day'
    formData.vip_days = 5  // 试用卡默认5天
    formData.points = 500  // 试用卡积分默认值500
  } else {
    // 自定义名称默认为年，默认值1
    formData.vip_days_unit = 'year'
    formData.vip_days = 1
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    quantity: 10,
    card_name: '年卡',
    card_type: 1,
    price: 0,
    points: 200000, // 初次加载时积分默认值200000
    vip_days: 1,  // 默认VIP时长为1
    vip_days_unit: 'year',
    vip_level: 1,
    prefix: 'KJF',
    valid_until: '',
    description: ''
  })

  // 清除表单验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 处理积分输入框失去焦点
const handlePointsBlur = () => {
  // 只有在积分卡类型时才进行自动命名
  if (formData.card_type !== 2) {
    return
  }

  // 检查当前卡密名称是否为年卡、试用卡或以积分加油包结尾
  const currentName = formData.card_name
  const shouldAutoName = currentName === '年卡' ||
                         currentName === '试用卡' ||
                         currentName.endsWith('积分加油包')

  if (!shouldAutoName) {
    return
  }

  // 验证积分值是否能被10000整除且结果大于等于1
  const points = formData.points
  if (points && points >= 10000 && points % 10000 === 0) {
    const wanPoints = points / 10000
    formData.card_name = `${wanPoints}万积分加油包`
  }
}

// 处理确认
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构建提交数据
    const submitData: any = {
      card_name: formData.card_name,
      card_type: formData.card_type,
      price: formData.price,
      points: formData.points,
      vip_days: formData.card_type === 1 ? formData.vip_days : 0,
      vip_days_unit: formData.card_type === 1 ? formData.vip_days_unit : 'day',
      vip_level: formData.card_type === 1 ? formData.vip_level : 0,
      description: formData.description,
      valid_until: formData.valid_until ? `${formData.valid_until} 23:59:59` : '',
      prefix: formData.prefix || 'KJF'
    }

    // 批量模式下添加数量
    if (props.isBatchMode) {
      submitData.quantity = formData.quantity
    }

    emit('confirm', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  visible.value = false
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}

// 暴露重置方法给父组件
defineExpose({
  resetForm
})
</script>

<style scoped>
/* 表单样式优化 */
.el-input-number {
  width: 100%;
}

.el-radio-group {
  width: 100%;
}

.el-radio {
  margin-right: 20px;
}

.el-form-item {
  margin-bottom: 18px;
}

/* 输入框单位样式 */
:deep(.el-input-group__append) {
  background-color: #f5f7fa;
  border-left: 1px solid #dcdfe6;
  color: #909399;
  padding: 0 15px;
  font-size: 14px;
  font-weight: 500;
}

/* 数字输入框去掉控制按钮后的样式优化 */
:deep(.el-input-number .el-input__inner) {
  text-align: left;
  padding-left: 15px;
  padding-right: 15px;
}
</style>
