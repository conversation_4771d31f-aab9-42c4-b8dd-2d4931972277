<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class SyncTaskDetailCurrentcyCommand extends Command
{
    /**
     * 命令名称和参数
     *
     * @var string
     */
    protected $signature = 'task:sync-currentcy';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '同步user_goods_sku表的currentcy到user_task_detail表的currentcy_goods字段，并将user_task表的currentcy设置为TL';

    /**
     * 统计信息
     */
    private $stats = [
        'total_task_detail_records' => 0,
        'total_task_records' => 0,
        'successfully_updated_details' => 0,
        'failed_detail_updates' => 0,
        'no_sku_found' => 0,
        'successfully_updated_tasks' => 0,
        'failed_task_updates' => 0,
        'failed_detail_records' => [],
        'failed_task_records' => [],
        'sample_records' => []
    ];

    /**
     * 执行命令
     */
    public function handle()
    {
        try {
            $this->info('开始同步货币单位数据...');
            
            // 1. 统计需要处理的记录数量
            if (!$this->countRecords()) {
                return Command::FAILURE;
            }

            // 2. 显示统计信息并确认
            if (!$this->confirmExecution()) {
                $this->info('操作已取消');
                return Command::SUCCESS;
            }

            // 3. 同步user_task_detail表的currentcy_goods字段
            $this->syncTaskDetailCurrentcy();

            // 4. 更新user_task表的currentcy字段
            $this->updateTaskCurrentcy();

            // 5. 显示结果统计
            $this->showResults();

            return Command::SUCCESS;
            
        } catch (Exception $e) {
            $this->error("执行过程中发生错误: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 统计需要处理的记录数量
     */
    private function countRecords(): bool
    {
        $this->info('正在统计需要处理的记录数量...');
        
        try {
            // 统计user_task_detail表的记录数量
            $this->stats['total_task_detail_records'] = DB::table('user_task_detail')->count();
            
            // 统计user_task表的记录数量
            $this->stats['total_task_records'] = DB::table('user_task')->count();
            
            if ($this->stats['total_task_detail_records'] == 0 && $this->stats['total_task_records'] == 0) {
                $this->info('没有找到需要处理的记录');
                return false;
            }

            $this->info("user_task_detail表记录数: {$this->stats['total_task_detail_records']}");
            $this->info("user_task表记录数: {$this->stats['total_task_records']}");

            // 收集前10个user_task_detail记录的详细信息用于人工核验
            $this->collectSampleRecords();

            return true;
            
        } catch (Exception $e) {
            $this->error("统计记录时发生错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 收集样本记录
     */
    private function collectSampleRecords(): void
    {
        $this->info('正在收集样本记录...');
        
        $sampleRecords = DB::table('user_task_detail')
            ->select('id', 'user_goods_sku_id', 'currentcy_goods', 'goods_name')
            ->limit(10)
            ->get();

        foreach ($sampleRecords as $record) {
            // 查找对应的user_goods_sku记录
            $skuRecord = DB::table('user_goods_sku')
                ->where('id', $record->user_goods_sku_id)
                ->first(['currentcy']);

            $this->stats['sample_records'][] = [
                'task_detail_id' => $record->id,
                'user_goods_sku_id' => $record->user_goods_sku_id,
                'current_currentcy_goods' => $record->currentcy_goods,
                'sku_currentcy' => $skuRecord ? $skuRecord->currentcy : '未找到SKU记录',
                'goods_name' => mb_substr($record->goods_name, 0, 50) . '...'
            ];
        }
    }

    /**
     * 确认执行
     */
    private function confirmExecution(): bool
    {
        $this->newLine();
        $this->info('=== 同步统计 ===');
        $this->info("user_task_detail表记录数: {$this->stats['total_task_detail_records']}");
        $this->info("user_task表记录数: {$this->stats['total_task_records']}");
        $this->newLine();

        // 显示样本记录供人工核验
        $this->showSampleRecords();
        
        $this->warn('此操作将会：');
        $this->warn('1. 从user_goods_sku表获取currentcy值');
        $this->warn('2. 更新user_task_detail表中的currentcy_goods字段');
        $this->warn('3. 将user_task表中的currentcy字段设置为"TL"');
        $this->newLine();
        
        return $this->confirm('确认要执行同步操作吗？');
    }

    /**
     * 显示样本记录供人工核验
     */
    private function showSampleRecords(): void
    {
        if (empty($this->stats['sample_records'])) {
            return;
        }

        $this->info('=== 样本记录（前10条）===');
        $this->newLine();

        foreach ($this->stats['sample_records'] as $index => $sample) {
            $this->info("样本 " . ($index + 1) . ":");
            $this->info("  task_detail_id: {$sample['task_detail_id']}");
            $this->info("  user_goods_sku_id: {$sample['user_goods_sku_id']}");
            $this->info("  当前currentcy_goods: {$sample['current_currentcy_goods']}");
            $this->info("  SKU的currentcy: {$sample['sku_currentcy']}");
            $this->info("  商品名称: {$sample['goods_name']}");
            $this->newLine();
        }

        $this->warn('请仔细核验以上样本记录，确认同步逻辑是否正确！');
        $this->newLine();
    }

    /**
     * 同步user_task_detail表的currentcy_goods字段
     */
    private function syncTaskDetailCurrentcy(): void
    {
        $this->info('开始同步user_task_detail表的currentcy_goods字段...');
        
        $batchSize = 1000; // 每批处理1000条记录
        $totalBatches = ceil($this->stats['total_task_detail_records'] / $batchSize);
        
        $progressBar = $this->output->createProgressBar($this->stats['total_task_detail_records']);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');
        $progressBar->setMessage('准备开始同步...');
        $progressBar->start();

        $processedCount = 0;
        
        DB::table('user_task_detail')
            ->orderBy('id')
            ->chunk($batchSize, function ($taskDetails) use ($progressBar, &$processedCount) {
                foreach ($taskDetails as $taskDetail) {
                    $processedCount++;
                    $this->processTaskDetail($taskDetail, $progressBar, $processedCount);
                }
            });

        $progressBar->finish();
        $this->newLine(2);
        $this->info('user_task_detail表同步完成');
    }

    /**
     * 处理单个任务详情记录
     */
    private function processTaskDetail($taskDetail, $progressBar, $current): void
    {
        $progressBar->setMessage("处理记录: ID={$taskDetail->id}");
        
        DB::beginTransaction();
        
        try {
            // 查找对应的user_goods_sku记录
            $skuRecord = DB::table('user_goods_sku')
                ->where('id', $taskDetail->user_goods_sku_id)
                ->first(['currentcy']);

            if (!$skuRecord) {
                $this->stats['no_sku_found']++;
                $remaining = $this->stats['total_task_detail_records'] - $current;
                $progressBar->setMessage("ID: {$taskDetail->id} | 状态: 未找到SKU记录 | 进度: {$current}/{$this->stats['total_task_detail_records']} | 剩余: {$remaining}");
                
                DB::commit();
                $progressBar->advance();
                return;
            }

            // 更新currentcy_goods字段
            $updated = DB::table('user_task_detail')
                ->where('id', $taskDetail->id)
                ->update([
                    'currentcy_goods' => $skuRecord->currentcy,
                    'updated_at' => now()
                ]);

            if ($updated) {
                $this->stats['successfully_updated_details']++;
            } else {
                $this->stats['failed_detail_updates']++;
                $this->stats['failed_detail_records'][] = [
                    'id' => $taskDetail->id,
                    'user_goods_sku_id' => $taskDetail->user_goods_sku_id,
                    'error' => '更新失败'
                ];
            }

            DB::commit();
            
            $remaining = $this->stats['total_task_detail_records'] - $current;
            $progressBar->setMessage("ID: {$taskDetail->id} | 状态: 更新成功 | currentcy: {$skuRecord->currentcy} | 进度: {$current}/{$this->stats['total_task_detail_records']} | 剩余: {$remaining}");
            
        } catch (Exception $e) {
            DB::rollback();
            $this->stats['failed_detail_updates']++;
            $this->stats['failed_detail_records'][] = [
                'id' => $taskDetail->id,
                'user_goods_sku_id' => $taskDetail->user_goods_sku_id,
                'error' => $e->getMessage()
            ];
            
            $remaining = $this->stats['total_task_detail_records'] - $current;
            $progressBar->setMessage("ID: {$taskDetail->id} | 状态: 异常错误 | 进度: {$current}/{$this->stats['total_task_detail_records']} | 剩余: {$remaining}");
        }
        
        $progressBar->advance();
    }

    /**
     * 更新user_task表的currentcy字段
     */
    private function updateTaskCurrentcy(): void
    {
        $this->info('开始更新user_task表的currentcy字段为TL...');
        
        try {
            $updated = DB::table('user_task')
                ->update([
                    'currentcy' => 'TL',
                    'updated_at' => now()
                ]);

            $this->stats['successfully_updated_tasks'] = $updated;
            $this->info("成功更新 {$updated} 条user_task记录");
            
        } catch (Exception $e) {
            $this->stats['failed_task_updates'] = $this->stats['total_task_records'];
            $this->stats['failed_task_records'][] = [
                'error' => $e->getMessage()
            ];
            $this->error("更新user_task表失败: " . $e->getMessage());
        }
    }

    /**
     * 显示结果统计
     */
    private function showResults(): void
    {
        $this->info('=== 同步结果统计 ===');
        $this->info("user_task_detail表处理结果:");
        $this->info("  总记录数: {$this->stats['total_task_detail_records']}");
        $this->info("  成功更新: {$this->stats['successfully_updated_details']}");
        $this->info("  更新失败: {$this->stats['failed_detail_updates']}");
        $this->info("  未找到SKU: {$this->stats['no_sku_found']}");
        
        $this->newLine();
        $this->info("user_task表处理结果:");
        $this->info("  总记录数: {$this->stats['total_task_records']}");
        $this->info("  成功更新: {$this->stats['successfully_updated_tasks']}");
        $this->info("  更新失败: {$this->stats['failed_task_updates']}");
        
        if (!empty($this->stats['failed_detail_records'])) {
            $this->warn("user_task_detail表处理失败的记录数: " . count($this->stats['failed_detail_records']));
            foreach (array_slice($this->stats['failed_detail_records'], 0, 5) as $failed) {
                $this->warn("失败记录: ID={$failed['id']}, user_goods_sku_id={$failed['user_goods_sku_id']} - {$failed['error']}");
            }
            if (count($this->stats['failed_detail_records']) > 5) {
                $this->warn("... 还有 " . (count($this->stats['failed_detail_records']) - 5) . " 条失败记录");
            }
        }
        
        if (!empty($this->stats['failed_task_records'])) {
            $this->warn("user_task表处理失败:");
            foreach ($this->stats['failed_task_records'] as $failed) {
                $this->warn("失败原因: {$failed['error']}");
            }
        }
        
        // 计算成功率
        if ($this->stats['total_task_detail_records'] > 0) {
            $successRate = round($this->stats['successfully_updated_details'] / $this->stats['total_task_detail_records'] * 100, 2);
            $this->info("user_task_detail表处理成功率: {$successRate}%");
        }
        
        if ($this->stats['total_task_records'] > 0) {
            $taskSuccessRate = round($this->stats['successfully_updated_tasks'] / $this->stats['total_task_records'] * 100, 2);
            $this->info("user_task表处理成功率: {$taskSuccessRate}%");
        }
        
        $this->newLine();
        $this->info('货币单位同步操作已完成！');
    }
} 