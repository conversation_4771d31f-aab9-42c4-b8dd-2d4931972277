<?php

namespace App\Service\User;

use App\Service\BaseService;
use App\Models\User\UserGoodsDirectoryModel;
use App\Models\User\GoodsModel;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Validator;

class UserGoodsDirectoryService extends BaseService
{
    /**
     * 获取目录列表（分页）
     */
    public $goodsDirectoryStartNumber;

    public function __construct()
    {
        $this->goodsDirectoryStartNumber = config('services.goods_directory.start_number');
    }

    public function getDirectoryList(int $userId, int $userPid, array $params): array
    {
        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));
        
        // 获取筛选参数
        $status = $params['status'] ?? null;
        $name = $params['name'] ?? null;
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;
        $directoryType = $params['directory_type'] ?? null;
        $creatorName = $params['creator_name'] ?? null;
        $creatorPhone = $params['creator_phone'] ?? null;
        $statusSub = $params['status_sub'] ?? null;

        // 构建查询
        $query = UserGoodsDirectoryModel::query();

        if ($userPid <= 0) {
            //当前是管理所有目录的功能 所以主账号查询所有的商品目录 包含子账号的创建的目录
            $query->where('user_id', $userId);
            // 主账号查询时，关联查询用户表获取子账号信息
            $query->with(['subUser']);
            
            // 主账号才能使用这些筛选条件
            if (is_numeric($directoryType)) {
                if ($directoryType == 0) {
                    // 系统目录
                    $query->where('user_sub_id', 0);
                } elseif ($directoryType == 1) {
                    // 子账号目录
                    $query->where('user_sub_id', '>', 0);
                }
            }
            
            // 按创建人姓名或手机号筛选
            if ($creatorName || $creatorPhone) {
                $query->whereHas('subUser', function($q) use ($creatorName, $creatorPhone) {
                    if ($creatorName) {
                        $q->where('name', 'like', "%{$creatorName}%");
                    }
                    if ($creatorPhone) {
                        $q->where('phone', 'like', "%{$creatorPhone}%");
                    }
                });
            }
            
        } else {
            // 子账号查询条件 仅查询状态是1且对子账号可见的自己的目录
            $query->where('status_sub', 1)->where('status',1);
            $query->where(function($q) use ($userPid, $userId) {
                $q->where('user_id', $userPid);
            });
        }
        if ($userPid <= 0) {
            // 主账号才允许按状态筛选
            // 按状态筛选
            if (is_numeric($status) && in_array($status, [0, 1])) {
                $query->byStatus($status);
            }

            // 按子账号可见状态筛选（仅主账号可用）
            if (is_numeric($statusSub) && in_array($statusSub, [0, 1])) {
                $query->where('status_sub', $statusSub);
            }
        }

        // 按名称筛选
        if ($name) {
            $query->where('name', 'like', "%{$name}%");
        }

        // 按创建时间范围筛选
        if ($startDate || $endDate) {
            $query->byDateRange($startDate, $endDate);
        }

        // 排序
        $query->ordered();

        // 分页查询
        $total = $query->count();
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;
        
        $directories = $query->offset($offset)
                            ->limit($pageSize)
                            ->get()
                            ->map(function($item) use ($userPid, $userId) {
                                return $this->formatDirectoryData($item, $userPid <= 0, $userPid, $userId);
                            });

        return [
            'list' => $directories,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasNext' => $page < $totalPages,
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 创建目录
     */
    public function createDirectory(int $userId, int $userPid, array $data): array
    {
        if ($userPid > 0) {
            throw new MyException('您没有权限创建目录');
        }

        // 子账号不能设置 status_sub 字段（虽然子账号不能创建目录，但为了安全起见）
        if ($userPid > 0 && isset($data['status_sub'])) {
            unset($data['status_sub']);
        }

        // 验证数据
        $this->validateDirectoryData($data, true);

        // 检查同一用户下是否已存在相同名称的目录
        $exists = UserGoodsDirectoryModel::query();
        if ($userPid <= 0) {
            $exists->where('user_id', $userId);
        } else {
            //子账号创建目录时会使用-子账号ID+数字来命名
            $dir_num = $this->goodsDirectoryStartNumber + $userId;
            $data['name'] = $data['name'] .'-' . strval($dir_num);
            $exists->where(function($q) use ($userPid, $userId) {
                $q->where('user_id', $userPid)
                  ->orWhere('user_sub_id', $userId);
            });
        }
        $exists = $exists->where('name', $data['name'])
            ->exists();   
        if ($exists) {
            throw new MyException('该目录名称已存在');
        }

        // 创建目录

        if ($userPid <= 0) {
            $user_id = $userId;
            $user_sub_id = 0;
        }else{
            $user_id = $userPid;
            $user_sub_id = $userId;
        }

        $directoryData = array_merge($data, [
            'user_id' => $user_id,
            'user_sub_id' => $user_sub_id,
            'status' => $data['status'] ?? 1,
            'status_sub' => $data['status_sub'] ?? 0,
            'sort_order' => $data['sort_order'] ?? 0,
            'goods_count' => 0,
        ]);

        $directory = UserGoodsDirectoryModel::create($directoryData);

        return [
            'id' => $directory->id,
            'message' => '目录创建成功'
        ];
    }

    /**
     * 更新目录
     */
    public function updateDirectory(int $userId,int $userPid, array $data): array
    {
        // 子账号不能修改 status_sub 字段
        if ($userPid > 0 && isset($data['status_sub'])) {
            unset($data['status_sub']);
        }

        // 验证数据
        $this->validateDirectoryData($data, false);

        if (!isset($data['id'])) {
            throw new MyException('目录ID不能为空');
        }

        // 查找目录并验证所有权
        $directory = UserGoodsDirectoryModel::find($data['id']);
        
        if (!$directory) {
            throw new MyException('目录不存在');
        }

        if($userPid<=0){
            if (!$directory->belongsToUser($userId)) {
                throw new MyException('您没有权限操作该目录');
            }
        }else{
            if (!$directory->belongsToSubUser($userId)) {
                throw new MyException('您没有权限操作该目录');
            }
        }
        // 检查是否是子账号操作系统目录
        if ($userPid > 0 && $directory->user_sub_id === 0) {
            throw new MyException('您没有权限操作该目录');
        }
        
        // 处理目录名称更新逻辑
        if (isset($data['name'])) {
            // 获取当前目录的显示名称（去掉后缀）
            $currentDisplayName = $directory->name;
            if (strpos($currentDisplayName, '-') !== false) {
                $currentDisplayName = explode('-', $currentDisplayName)[0];
            }

            // 只有当新名称与当前显示名称不同时才需要更新
            if ($data['name'] !== $currentDisplayName) {
                // 计算最终要保存到数据库的完整名称（包含后缀）
                $finalNameForDB = $data['name'];

                // 处理子账号创建的目录（无论是主账号还是子账号修改都要保留原后缀）
                if ($directory->user_sub_id > 0) {
                    // 提取原名称中的后缀（格式：名称-数字）
                    $originalName = $directory->name;
                    $lastDashPos = strrpos($originalName, '-');

                    if ($lastDashPos !== false) {
                        $suffix = substr($originalName, $lastDashPos + 1);
                        // 验证后缀是数字，确保是子账号目录的标准格式
                        if (is_numeric($suffix)) {
                            // 保留原后缀，确保子账号目录标识不丢失
                            $finalNameForDB = $data['name'] . '-' . $suffix;
                        }
                    }
                } else if ($userPid > 0) {
                    // 只有子账号创建新的系统目录时才需要添加后缀
                    // 这种情况实际上不应该发生，因为子账号不能修改系统目录
                    $dir_num = $this->goodsDirectoryStartNumber + $userId;
                    $finalNameForDB = $data['name'] . '-' . $dir_num;
                }

                // 使用最终名称进行重复检查
                $exists = UserGoodsDirectoryModel::query();
                if ($userPid <= 0) {
                    // 主账号检查所有目录
                    $exists->where('user_id', $userId);
                } else {
                    // 子账号检查范围
                    $exists->where(function($q) use ($userPid, $userId) {
                        $q->where('user_id', $userPid)
                          ->orWhere('user_sub_id', $userId);
                    });
                }
                $exists = $exists->where('name', $finalNameForDB)
                    ->where('id', '!=', $directory->id)
                    ->exists();

                if ($exists) {
                    throw new MyException('该目录名称已存在');
                }

                // 将最终名称设置到 data 中，供后续保存使用
                $data['name'] = $finalNameForDB;
            }else{
                unset($data['name']);
            }
        }

        // 更新目录
        $updateData = $data;
        unset($updateData['id']);

        $directory->update($updateData);

        return [
            'message' => '目录更新成功'
        ];
    }

    /**
     * 批量更新目录
     */
    public function batchUpdateDirectory(int $userId, int $userPid, array $data): array
    {
        if ($userPid > 0) {
            throw new MyException('您没有权限批量更新目录');
        }
        $isSubAccount = $userPid > 0;

        $directories = UserGoodsDirectoryModel::query()->whereIn('id', $data['ids'])->get();
        
        if ($directories->count() != count($data['ids'])) {
            throw new MyException('参数错误');
        }

        // 检查子账号是否有权限操作系统目录
        if ($isSubAccount) {
            $systemDirectories = $directories->filter(function($directory) {
                return $directory->user_sub_id === 0;
            });
            
            if ($systemDirectories->count() > 0) {
                throw new MyException('您没有权限操作所选目录,请重新选择操作的商品目录');
            }
        }else{
            $systemDirectories = $directories->filter(function($directory)use($userId) {
                return $directory->user_id !== $userId;
            });
            if ($systemDirectories->count() > 0) {
                throw new MyException('您没有权限操作所选目录,请重新选择操作的商品目录');
            }
        }
        
        $ids = $data['ids'];
        unset($data['ids']);
        
        if (count($data) == 0) {
            throw new MyException('请至少选择一项');
        }
        
        UserGoodsDirectoryModel::query()->whereIn('id', $ids)->update($data);
        
        return [
            'message' => '目录批量更新成功'
        ];
    }

    /**
     * 删除目录
     */
    public function deleteDirectory(int $userId, int $userPid, int $directoryId): array
    {
        if ($userPid > 0) {
            throw new MyException('您没有权限删除目录');
        }
        // 验证目录ID
        if (!$directoryId) {
            throw new MyException('目录ID不能为空');
        }

        // 查找目录并验证所有权
        $directory = UserGoodsDirectoryModel::find($directoryId);
        
        if (!$directory) {
            throw new MyException('目录不存在');
        }

        if($userPid<=0){
            if (!$directory->belongsToUser($userId)) {
                throw new MyException('无权限操作此目录');
            }
        }else{
            if (!$directory->belongsToSubUser($userId)) {
                throw new MyException('无权限操作此目录');
            }
        }

        // 检查是否是子账号操作系统目录
        if ($userPid > 0 && $directory->user_sub_id === 0) {
            throw new MyException('您没有权限删除该目录');
        }

        // 检查目录下是否有商品
        $goodsCount = GoodsModel::byUserId($userId)
            ->byDirectoryId($directoryId)
            ->count();

        if ($goodsCount > 0) {
            throw new MyException("该目录下还有 {$goodsCount} 个商品，无法删除");
        }

        // 删除目录
        $directory->delete();

        return [
            'message' => '目录删除成功'
        ];
    }

    /**
     * 获取目录详情
     */
    public function getDirectoryDetail(int $userId,int $userPid, int $directoryId): array
    {
        // 验证目录ID
        if (!$directoryId) {
            throw new MyException('目录ID不能为空');
        }

        // 查找目录并验证所有权
        $directory = UserGoodsDirectoryModel::find($directoryId);
        
        if (!$directory) {
            throw new MyException('目录不存在');
        }

        if($userPid<=0){
            if (!$directory->belongsToUser($userId)) {
                throw new MyException('无权限操作此目录');
            }
        }else{
            if (!$directory->belongsToSubUser($userId)) {
                throw new MyException('无权限操作此目录');
            }
        }

        return $this->formatDirectoryDetailData($directory);
    }

    /**
     * 更新目录商品数量
     * 使用统计计算的方法更新值，不使用increment/decrement
     */
    public function updateDirectoryGoodsCount(int $directoryId): void
    {
        $directory = UserGoodsDirectoryModel::find($directoryId);
        if ($directory) {
            // 统计该目录下的有效商品数量
            $goodsCount = GoodsModel::where('user_id', $directory->user_id)
                ->where('directory_id', $directoryId)
                ->where('status', 1)
                ->count();

            // 更新目录商品数量
            $directory->update(['goods_count' => $goodsCount]);
        }
    }

    /**
     * 获取子账号在指定目录下的商品数量
     */
    private function getSubAccountGoodsCount(int $directoryId, int $userSubId): int
    {
        return GoodsModel::where('directory_id', $directoryId)
            ->where('user_sub_id', $userSubId)
            ->where('status', 1)
            ->count();
    }

    /**
     * 批量更新所有目录的商品数量  该方法没有使用到
     * 使用统计计算的方法更新值，不使用increment/decrement
     */
    public function updateAllDirectoryGoodsCount(int $userId): void
    {
        UserGoodsDirectoryModel::updateAllGoodsCountByUser($userId);
    }

    /**
     * 验证目录数据
     */
    private function validateDirectoryData(array $data, bool $isCreate = false): void
    {
        $rules = [];
        
        if ($isCreate) {
            $rules = [
                'name' => 'required|string|max:100',
            ];
        } else {
            $rules = [
                'id' => 'required|integer',
                'name' => 'sometimes|string|max:100',
            ];
        }

        // 通用验证规则
        $commonRules = [
            'description' => 'nullable|string|max:500',
            'sort_order' => 'nullable|integer|min:0',
            'status' => 'sometimes|integer|in:0,1',
            'status_sub' => 'sometimes|integer|in:0,1',
        ];

        $rules = array_merge($rules, $commonRules);

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new MyException($validator->errors()->first());
        }
    }

    /**
     * 格式化目录数据（列表用）
     */
    private function formatDirectoryData($directory, bool $includeUserInfo = false, int $userPid = 0, int $userId = 0): array
    {
        // 处理目录名称，只返回"-"前面的内容
        $displayName = $directory->name;
        if (strpos($displayName, '-') !== false) {
            $displayName = explode('-', $displayName)[0];
        }

        $data = [
            'id' => $directory->id,
            'user_id' => $directory->user_id,
            'user_sub_id' => $directory->user_sub_id,
            'name' => $displayName,
            'description' => $directory->description,
            'sort_order' => $directory->sort_order,
            'status' => $directory->status,
            'status_name' => $directory->getStatusName(),
            'status_sub'  => $directory->status_sub,
            'status_sub_name' => $directory->getStatusSubName(),
            'goods_count' => $directory->goods_count,
            'created_at' => $directory->created_at,
            'updated_at' => $directory->updated_at,
        ];

        // 如果是子账号且目录是系统目录，添加子账号商品数量统计
        if ($userPid > 0 && $directory->user_sub_id === 0) {
            $subAccountGoodsCount = $this->getSubAccountGoodsCount($directory->id, $userId);
            $data['sub_account_goods_count'] = $subAccountGoodsCount;
        }

        // 主账号查询时，添加用户信息
        if ($includeUserInfo && $directory->user_sub_id > 0 && $directory->subUser) {
            $data['user_info'] = [
                'id' => $directory->subUser->id,
                'name' => $directory->subUser->name,
                'phone' => $directory->subUser->phone,
                'is_vip' => $directory->subUser->is_vip,
                'vip_level' => $directory->subUser->vip_level,
                'vip_end_time' => $directory->subUser->vip_end_time,
                'status' => $directory->subUser->status,
            ];
        }

        return $data;
    }

    /**
     * 格式化目录详情数据
     */
    private function formatDirectoryDetailData($directory): array
    {
        // 处理目录名称，只返回"-"前面的内容
        $displayName = $directory->name;
        if (strpos($displayName, '-') !== false) {
            $displayName = explode('-', $displayName)[0];
        }
        
        return [
            'id' => $directory->id,
            'user_id' => $directory->user_id,
            'name' => $displayName,
            'description' => $directory->description,
            'sort_order' => $directory->sort_order,
            'status' => $directory->status,
            'status_name' => $directory->getStatusName(),
            'status_sub'  => $directory->status_sub,
            'status_sub_name' => $directory->getStatusSubName(),
            'goods_count' => $directory->goods_count,
            'created_at' => $directory->created_at,
            'updated_at' => $directory->updated_at,
        ];
    }
}