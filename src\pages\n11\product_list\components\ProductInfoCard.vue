<template>
  <div class="product-info-card" :class="{ processing: processing, clickable: clickable }" @click="handleClick">
    <!-- 商品图片 -->
    <div class="product-image">
      <img
        :src="product.imageUrl || placeholderImage"
        :alt="product.title"
        @error="handleImageError"
        class="image"
      />
      <div v-if="processing" class="processing-overlay">
        <i class="el-icon-loading"></i>
      </div>
    </div>
    
    <!-- 商品信息 -->
    <div class="product-info">
      <div class="product-title" :title="product.title">
        {{ product.title }}
      </div>
      
      <div class="product-details">
        <div class="detail-row">
          <span class="label">价格:</span>
          <span class="value price">{{ formatPrice(product.price) }}</span>
        </div>
        <div class="detail-row">
          <span class="label">Stock Code:</span>
          <span class="value code">{{ product.stockCode }}</span>
        </div>
        <div class="detail-row" v-if="product.productId">
          <span class="label">商品ID:</span>
          <span class="value id">{{ product.productId }}</span>
        </div>
      </div>
    </div>
    
    <!-- 状态指示器 -->
    <div class="status-indicator" v-if="showStatus">
      <div class="status-icon" :class="getStatusClass()">
        <i :class="getStatusIcon()"></i>
      </div>
      <div class="status-text">{{ getStatusText() }}</div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="card-actions" v-if="showActions">
      <el-button
        size="small"
        type="text"
        @click.stop="$emit('view-details')"
        v-if="!processing"
      >
        查看详情
      </el-button>
      <el-button
        size="small"
        type="text"
        @click.stop="$emit('retry')"
        v-if="status === 'error' && !processing"
      >
        重试
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props接口
export interface ProductInfo {
  productId: string
  title: string
  imageUrl?: string
  price: string
  stockCode: string
}

// Props定义
const props = defineProps<{
  product: ProductInfo
  processing?: boolean
  status?: 'idle' | 'processing' | 'success' | 'error'
  clickable?: boolean
  showStatus?: boolean
  showActions?: boolean
}>()

// Emits定义
const emit = defineEmits<{
  'click': []
  'view-details': []
  'retry': []
}>()

// 计算属性
const placeholderImage = computed(() => {
  // 使用base64编码的占位图片，避免外部依赖
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0MFY0MEgyMFYyMFoiIGZpbGw9IiNEQ0RGRTYiLz4KPHBhdGggZD0iTTI1IDI4TDMwIDMzTDM1IDI4TDQwIDMzVjM3SDIwVjMzTDI1IDI4WiIgZmlsbD0iI0M4Q0JEMCIvPgo8Y2lyY2xlIGN4PSIzMCIgY3k9IjI2IiByPSIyIiBmaWxsPSIjQzhDQkQwIi8+Cjwvc3ZnPgo='
})

// 方法
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = placeholderImage.value
}

const handleClick = () => {
  if (props.clickable && !props.processing) {
    emit('click')
  }
}

const formatPrice = (price: string): string => {
  // 简单的价格格式化
  if (!price) return '未知'
  
  // 如果价格包含货币符号，直接返回
  if (price.includes('₺') || price.includes('TL') || price.includes('$') || price.includes('€')) {
    return price
  }
  
  // 否则添加土耳其里拉符号
  const numPrice = parseFloat(price)
  if (isNaN(numPrice)) return price
  
  return `${numPrice.toFixed(2)} `
}

const getStatusClass = () => {
  switch (props.status) {
    case 'processing': return 'status-processing'
    case 'success': return 'status-success'
    case 'error': return 'status-error'
    default: return 'status-idle'
  }
}

const getStatusIcon = () => {
  switch (props.status) {
    case 'processing': return 'el-icon-loading'
    case 'success': return 'el-icon-success'
    case 'error': return 'el-icon-error'
    default: return 'el-icon-time'
  }
}

const getStatusText = () => {
  switch (props.status) {
    case 'processing': return '处理中'
    case 'success': return '成功'
    case 'error': return '失败'
    default: return '等待'
  }
}
</script>

<style scoped>
.product-info-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &.processing {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  }
  
  &.clickable {
    cursor: pointer;
    
    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  .product-image {
    position: relative;
    flex-shrink: 0;
    
    .image {
      width: 60px;
      height: 60px;
      border-radius: 6px;
      object-fit: cover;
      border: 1px solid #dcdfe6;
    }
    
    .processing-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(64, 158, 255, 0.8);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      i {
        color: white;
        font-size: 20px;
        animation: rotate 1s linear infinite;
      }
    }
  }
  
  .product-info {
    flex: 1;
    min-width: 0;
    
    .product-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1.2;
    }
    
    .product-details {
      .detail-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          font-size: 14px;
          color: #909399;
          min-width: 80px;
        }
        
        .value {
          font-size: 14px;
          color: #606266;
          font-weight: 500;
          
          &.price {
            color: #e6a23c;
            font-weight: 600;
          }
          
          &.code {
            font-family: monospace;
            background: #f5f7fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
          }
          
          &.id {
            font-family: monospace;
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .status-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
    
    .status-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      i {
        font-size: 16px;
      }
      
      &.status-idle {
        background: #f0f0f0;
        color: #909399;
      }
      
      &.status-processing {
        background: #e6f7ff;
        color: #409eff;
        
        i {
          animation: rotate 1s linear infinite;
        }
      }
      
      &.status-success {
        background: #f0f9ff;
        color: #67c23a;
      }
      
      &.status-error {
        background: #fef0f0;
        color: #f56c6c;
      }
    }
    
    .status-text {
      font-size: 12px;
      color: #606266;
      font-weight: 500;
    }
  }
  
  .card-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex-shrink: 0;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
