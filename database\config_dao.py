#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置数据访问对象
负责config表的CRUD操作
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from .db_manager import DatabaseManager


class ConfigDAO:
    """配置数据访问对象"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化配置DAO
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
    
    def get_all_configs(self) -> List[Dict[str, Any]]:
        """
        获取所有配置

        Returns:
            配置列表
        """
        query = """
            SELECT id, appid, appsecret, memo, user_id, phone, is_default, concurrent_download_count, concurrent_upload_count, created_at, updated_at
            FROM config
            ORDER BY is_default DESC, updated_at DESC
        """
        rows = self.db_manager.execute_query(query)
        return [dict(row) for row in rows]
    
    def get_config_by_id(self, config_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取配置

        Args:
            config_id: 配置ID

        Returns:
            配置信息，如果不存在返回None
        """
        query = """
            SELECT id, appid, appsecret, memo, user_id, phone, is_default, concurrent_download_count, created_at, updated_at
            FROM config
            WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (config_id,))
        return dict(rows[0]) if rows else None
    
    def get_config_by_appid(self, appid: str) -> Optional[Dict[str, Any]]:
        """
        根据appid获取配置

        Args:
            appid: 应用ID

        Returns:
            配置信息，如果不存在返回None
        """
        query = """
            SELECT id, appid, appsecret, memo, user_id, phone, is_default, concurrent_download_count, created_at, updated_at
            FROM config
            WHERE appid = ?
        """
        rows = self.db_manager.execute_query(query, (appid,))
        return dict(rows[0]) if rows else None
    
    def insert_config(self, appid: str, appsecret: str, memo: str = "",
                     user_id: int = None, phone: str = None, is_default: bool = False,
                     concurrent_download_count: int = 5, concurrent_upload_count: int = 2) -> int:
        """
        插入新配置

        Args:
            appid: 应用ID
            appsecret: 应用密钥
            memo: 备注
            user_id: 用户ID
            phone: 手机号
            is_default: 是否为默认账号
            concurrent_download_count: 并发下载数量
            concurrent_upload_count: 并发上传数量

        Returns:
            新插入记录的ID
        """
        # 如果设置为默认账号，先清除其他账号的默认状态
        if is_default:
            self.clear_all_default()

        query = """
            INSERT INTO config (appid, appsecret, memo, user_id, phone, is_default, concurrent_download_count, concurrent_upload_count, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """
        return self.db_manager.execute_insert(query, (appid, appsecret, memo, user_id, phone, 1 if is_default else 0, concurrent_download_count, concurrent_upload_count))
    
    def update_config(self, config_id: int, appid: str = None, appsecret: str = None, memo: str = None,
                     user_id: int = None, phone: str = None, is_default: bool = None,
                     concurrent_download_count: int = None) -> bool:
        """
        更新配置

        Args:
            config_id: 配置ID
            appid: 应用ID（可选）
            appsecret: 应用密钥（可选）
            memo: 备注（可选）
            user_id: 用户ID（可选）
            phone: 手机号（可选）
            is_default: 是否为默认账号（可选）
            concurrent_download_count: 并发下载数量（可选）

        Returns:
            是否更新成功
        """
        # 如果设置为默认账号，先清除其他账号的默认状态
        if is_default:
            self.clear_all_default()

        # 构建动态更新语句
        update_fields = []
        params = []

        if appid is not None:
            update_fields.append("appid = ?")
            params.append(appid)

        if appsecret is not None:
            update_fields.append("appsecret = ?")
            params.append(appsecret)

        if memo is not None:
            update_fields.append("memo = ?")
            params.append(memo)

        if user_id is not None:
            update_fields.append("user_id = ?")
            params.append(user_id)

        if phone is not None:
            update_fields.append("phone = ?")
            params.append(phone)

        if is_default is not None:
            update_fields.append("is_default = ?")
            params.append(1 if is_default else 0)

        if concurrent_download_count is not None:
            update_fields.append("concurrent_download_count = ?")
            params.append(concurrent_download_count)

        if not update_fields:
            return False

        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        params.append(config_id)

        query = f"""
            UPDATE config
            SET {', '.join(update_fields)}
            WHERE id = ?
        """

        affected_rows = self.db_manager.execute_update(query, tuple(params))
        return affected_rows > 0
    
    def update_config_by_appid(self, appid: str, appsecret: str = None, memo: str = None,
                              user_id: int = None, phone: str = None, is_default: bool = None,
                              concurrent_download_count: int = None) -> bool:
        """
        根据appid更新配置

        Args:
            appid: 应用ID
            appsecret: 应用密钥（可选）
            memo: 备注（可选）
            user_id: 用户ID（可选）
            phone: 手机号（可选）
            is_default: 是否为默认账号（可选）
            concurrent_download_count: 并发下载数量（可选）

        Returns:
            是否更新成功
        """
        # 如果设置为默认账号，先清除其他账号的默认状态
        if is_default:
            self.clear_all_default()

        # 构建动态更新语句
        update_fields = []
        params = []

        if appsecret is not None:
            update_fields.append("appsecret = ?")
            params.append(appsecret)

        if memo is not None:
            update_fields.append("memo = ?")
            params.append(memo)

        if user_id is not None:
            update_fields.append("user_id = ?")
            params.append(user_id)

        if phone is not None:
            update_fields.append("phone = ?")
            params.append(phone)

        if is_default is not None:
            update_fields.append("is_default = ?")
            params.append(1 if is_default else 0)

        if concurrent_download_count is not None:
            update_fields.append("concurrent_download_count = ?")
            params.append(concurrent_download_count)

        if not update_fields:
            return False

        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        params.append(appid)

        query = f"""
            UPDATE config
            SET {', '.join(update_fields)}
            WHERE appid = ?
        """

        affected_rows = self.db_manager.execute_update(query, tuple(params))
        return affected_rows > 0
    
    def delete_config(self, config_id: int) -> bool:
        """
        删除配置
        
        Args:
            config_id: 配置ID
            
        Returns:
            是否删除成功
        """
        query = "DELETE FROM config WHERE id = ?"
        affected_rows = self.db_manager.execute_update(query, (config_id,))
        return affected_rows > 0
    
    def delete_config_by_appid(self, appid: str) -> bool:
        """
        根据appid删除配置
        
        Args:
            appid: 应用ID
            
        Returns:
            是否删除成功
        """
        query = "DELETE FROM config WHERE appid = ?"
        affected_rows = self.db_manager.execute_update(query, (appid,))
        return affected_rows > 0
    
    def config_exists(self, appid: str) -> bool:
        """
        检查配置是否存在

        Args:
            appid: 应用ID

        Returns:
            配置是否存在
        """
        query = "SELECT COUNT(*) as count FROM config WHERE appid = ?"
        rows = self.db_manager.execute_query(query, (appid,))
        return rows[0]['count'] > 0 if rows else False

    def get_default_config(self) -> Optional[Dict[str, Any]]:
        """
        获取默认配置

        Returns:
            默认配置信息，如果不存在返回None
        """
        query = """
            SELECT id, appid, appsecret, memo, user_id, phone, is_default, concurrent_download_count, created_at, updated_at
            FROM config
            WHERE is_default = 1
            LIMIT 1
        """
        rows = self.db_manager.execute_query(query)
        return dict(rows[0]) if rows else None

    def set_default_config(self, config_id: int) -> bool:
        """
        设置默认配置

        Args:
            config_id: 配置ID

        Returns:
            是否设置成功
        """
        # 先清除所有默认状态
        self.clear_all_default()

        # 设置指定配置为默认
        query = """
            UPDATE config
            SET is_default = 1, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """
        affected_rows = self.db_manager.execute_update(query, (config_id,))
        return affected_rows > 0

    def set_default_config_by_appid(self, appid: str) -> bool:
        """
        根据appid设置默认配置

        Args:
            appid: 应用ID

        Returns:
            是否设置成功
        """
        # 先清除所有默认状态
        self.clear_all_default()

        # 设置指定配置为默认
        query = """
            UPDATE config
            SET is_default = 1, updated_at = CURRENT_TIMESTAMP
            WHERE appid = ?
        """
        affected_rows = self.db_manager.execute_update(query, (appid,))
        return affected_rows > 0

    def clear_all_default(self) -> bool:
        """
        清除所有配置的默认状态

        Returns:
            是否清除成功
        """
        query = """
            UPDATE config
            SET is_default = 0, updated_at = CURRENT_TIMESTAMP
            WHERE is_default = 1
        """
        self.db_manager.execute_update(query)
        return True

    def get_concurrent_download_count(self, appid: str) -> int:
        """
        获取指定配置的并发下载数量

        Args:
            appid: 应用ID

        Returns:
            并发下载数量，如果配置不存在返回默认值5
        """
        query = """
            SELECT concurrent_download_count
            FROM config
            WHERE appid = ?
        """
        rows = self.db_manager.execute_query(query, (appid,))
        if rows and rows[0]['concurrent_download_count'] is not None:
            return rows[0]['concurrent_download_count']
        return 5  # 默认值

    def update_concurrent_download_count(self, appid: str, count: int) -> bool:
        """
        更新指定配置的并发下载数量

        Args:
            appid: 应用ID
            count: 并发下载数量

        Returns:
            是否更新成功
        """
        # 验证范围
        if count < 1 or count > 100:
            return False

        query = """
            UPDATE config
            SET concurrent_download_count = ?, updated_at = CURRENT_TIMESTAMP
            WHERE appid = ?
        """
        affected_rows = self.db_manager.execute_update(query, (count, appid))
        return affected_rows > 0

    def get_concurrent_upload_count(self, appid: str) -> int:
        """
        获取指定配置的并发上传数量

        Args:
            appid: 应用ID

        Returns:
            并发上传数量，如果配置不存在返回默认值2
        """
        query = """
            SELECT concurrent_upload_count
            FROM config
            WHERE appid = ?
        """
        rows = self.db_manager.execute_query(query, (appid,))
        if rows and rows[0]['concurrent_upload_count'] is not None:
            return rows[0]['concurrent_upload_count']
        return 2  # 默认值

    def update_concurrent_upload_count(self, appid: str, count: int) -> bool:
        """
        更新指定配置的并发上传数量

        Args:
            appid: 应用ID
            count: 并发上传数量

        Returns:
            是否更新成功
        """
        # 验证范围
        if count < 1 or count > 20:
            return False

        query = """
            UPDATE config
            SET concurrent_upload_count = ?, updated_at = CURRENT_TIMESTAMP
            WHERE appid = ?
        """
        affected_rows = self.db_manager.execute_update(query, (count, appid))
        return affected_rows > 0
