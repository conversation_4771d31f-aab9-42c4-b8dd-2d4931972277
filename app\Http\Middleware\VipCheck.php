<?php
declare(strict_types=1);

namespace App\Http\Middleware;

use App\Exceptions\MyException;
use Closure;
use Illuminate\Http\Request;

class VipCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取用户信息（由UserLogin中间件设置）
        $user = $request->attributes->get('user');
        
        if (!$user) {
            throw new MyException("用户信息不存在", 401);
        }

        // 检查VIP身份和积分
        $isSub = $user['pid'] > 0; //是不是子账号
        $isVip = $user['is_vip'] ?? 0;
        $points = $user['points'] ?? 0;
        $isAdmin = $user['is_admin'] ?? 0;
        
        // 管理员不进行VIP和积分检查 如果不是VIP或主账户的积分小于等于0，返回特定状态码
        //  子账号没有积分时 仍然可以使用 因为子账号仅负责采集功能
        if($isAdmin != 1){
            if ($isVip == 0 || (!$isSub && $points < 0)) {
                // 返回特定的状态码，前端将根据此状态码跳转到卡密激活页面
                return response()->json([
                    'status' => 403,
                    'code' => 1003, // 自定义错误码：VIP权限不足
                    'msg' => $isVip == 0 ? '您不是VIP会员，请升级会员后使用' : '您的积分不足，请充值后使用',
                    'data' => []
                ], 200);
            }
        }

        return $next($request);
    }
}
