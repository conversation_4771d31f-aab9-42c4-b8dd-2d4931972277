#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品数据访问对象
负责goods表的CRUD操作
"""

import json
from typing import Optional, List, Dict, Any
from datetime import datetime
from .db_manager import DatabaseManager


class GoodsDAO:
    """商品数据访问对象"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化商品DAO
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
    
    def insert_goods(self, user_id: int, phone: str, goods_id: int, goods_name: str,
                     goods_platform_id: int, directory_name: str, media_files: dict,
                     pending_total_count: int = 0) -> int:
        """
        插入新商品记录
        
        Args:
            user_id: 用户ID
            phone: 手机号
            goods_id: 商品ID
            goods_name: 商品名称
            goods_platform_id: 平台商品ID
            directory_name: 目录名称
            media_files: 媒体文件信息（字典）
            pending_total_count: 待处理商品总数
            
        Returns:
            新插入记录的ID
        """
        media_files_json = json.dumps(media_files, ensure_ascii=False)
        
        query = """
            INSERT OR REPLACE INTO goods 
            (user_id, phone, goods_id, goods_name, goods_platform_id, directory_name, 
             media_files_json, pending_total_count, is_all_files_downloaded, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """
        
        return self.db_manager.execute_insert(query, (
            user_id, phone, goods_id, goods_name, goods_platform_id, 
            directory_name, media_files_json, pending_total_count
        ))
    
    def get_goods_by_id(self, goods_id: int) -> Optional[Dict[str, Any]]:
        """
        根据商品ID获取商品信息
        
        Args:
            goods_id: 商品ID
            
        Returns:
            商品信息，如果不存在返回None
        """
        query = """
            SELECT id, user_id, phone, goods_id, goods_name, goods_platform_id, 
                   directory_name, media_files_json, pending_total_count, 
                   is_all_files_downloaded, created_at, updated_at
            FROM goods
            WHERE goods_id = ?
        """
        
        rows = self.db_manager.execute_query(query, (goods_id,))
        if rows:
            goods = dict(rows[0])
            # 解析JSON字段
            if goods['media_files_json']:
                try:
                    goods['media_files'] = json.loads(goods['media_files_json'])
                except json.JSONDecodeError:
                    goods['media_files'] = {}
            else:
                goods['media_files'] = {}
            return goods
        return None
    
    def update_files_downloaded_status(self, goods_id: int, is_downloaded: bool) -> int:
        """
        更新商品的文件下载完成状态
        
        Args:
            goods_id: 商品ID
            is_downloaded: 是否已下载完成
            
        Returns:
            受影响的行数
        """
        query = """
            UPDATE goods 
            SET is_all_files_downloaded = ?, updated_at = CURRENT_TIMESTAMP
            WHERE goods_id = ?
        """
        
        return self.db_manager.execute_update(query, (1 if is_downloaded else 0, goods_id))
    
    def get_all_goods(self) -> List[Dict[str, Any]]:
        """
        获取所有商品记录
        
        Returns:
            商品列表
        """
        query = """
            SELECT id, user_id, phone, goods_id, goods_name, goods_platform_id, 
                   directory_name, media_files_json, pending_total_count, 
                   is_all_files_downloaded, created_at, updated_at
            FROM goods
            ORDER BY created_at DESC
        """
        
        rows = self.db_manager.execute_query(query)
        goods_list = []
        for row in rows:
            goods = dict(row)
            # 解析JSON字段
            if goods['media_files_json']:
                try:
                    goods['media_files'] = json.loads(goods['media_files_json'])
                except json.JSONDecodeError:
                    goods['media_files'] = {}
            else:
                goods['media_files'] = {}
            goods_list.append(goods)
        
        return goods_list
    
    def delete_goods(self, goods_id: int) -> int:
        """
        删除商品记录
        
        Args:
            goods_id: 商品ID
            
        Returns:
            受影响的行数
        """
        query = "DELETE FROM goods WHERE goods_id = ?"
        return self.db_manager.execute_update(query, (goods_id,))
    
    def goods_exists(self, goods_id: int) -> bool:
        """
        检查商品是否存在
        
        Args:
            goods_id: 商品ID
            
        Returns:
            是否存在
        """
        query = "SELECT COUNT(*) as count FROM goods WHERE goods_id = ?"
        rows = self.db_manager.execute_query(query, (goods_id,))
        return rows[0]['count'] > 0 if rows else False
