<template>
  <el-dialog
    :model-value="visible"
    title="确认导出"
    width="400px"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @update:model-value="handleVisibilityChange"
  >
    <div class="confirm-content">
      <el-icon class="confirm-icon"><InfoFilled /></el-icon>
      <div class="confirm-text">
        <p>当前共 <strong>{{ totalProductCount }}</strong> 个商品链接</p>
        <p>已选中 <strong>{{ selectedProductCount }}</strong> 个</p>
        <p>确认要导出选中的商品链接吗？</p>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认导出</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElDialog, El<PERSON>utton, ElIcon } from 'element-plus';
import { InfoFilled } from '@element-plus/icons-vue';

interface Props {
  visible: boolean;
  totalProductCount: number;
  selectedProductCount: number;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const handleVisibilityChange = (value: boolean) => {
  emit('update:visible', value);
};

const handleConfirm = () => {
  emit('confirm');
};

const handleCancel = () => {
  emit('cancel');
};
</script>

<style scoped>
.confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 0;
}

.confirm-icon {
  font-size: 24px;
  color: #409eff;
  margin-top: 2px;
}

.confirm-text {
  flex: 1;
}

.confirm-text p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
}

.confirm-text strong {
  color: #409eff;
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>