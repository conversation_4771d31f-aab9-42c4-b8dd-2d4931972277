<?php
namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\UserAiKeyService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserAiKeyController extends Controller
{
    protected UserAiKeyService $aiKeyService;

    public function __construct(UserAiKeyService $aiKeyService)
    {
        $this->aiKeyService = $aiKeyService;
        parent::__construct();
    }

    /**
     * 获取AI Key列表（分页）
     */
    public function list(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $params = $request->only(['page', 'pageSize', 'status', 'ai_type', 'ai_key', 'start_date', 'end_date']);
        $result = $this->aiKeyService->getAiKeyList($userId, $params);
        
        return $this->apiSuccess($result);
    }

    /**
     * 创建AI Key
     */
    public function create(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'ai_key', 'description', 'sort_order', 'status', 'ai_type'
        ]);
        
        $result = $this->aiKeyService->createAiKey($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 更新AI Key
     */
    public function update(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'id', 'ai_key', 'description', 'sort_order', 'status', 'ai_type'
        ]);
        
        $result = $this->aiKeyService->updateAiKey($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 批量更新AI Key
     */
    public function batchUpdate(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only(['ids', 'status']);
        $result = $this->aiKeyService->batchUpdateAiKey($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 删除AI Key
     */
    public function delete(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $aiKeyId = (int)$request->input('id');
        $result = $this->aiKeyService->deleteAiKey($userId, $aiKeyId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取AI Key详情
     */
    public function detail(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $aiKeyId = (int)$request->input('id');
        $result = $this->aiKeyService->getAiKeyDetail($userId, $aiKeyId);
        
        return $this->apiSuccess($result);
    }
} 