import { createDomObserver } from '../domObserver';
// 移除了网络监听器的导入，现在通过 background script 处理

// 声明 Chrome API
declare const chrome: any;

/**
 * 商品链接存储对象
 */
export interface GoodsLinkMap {
  [goodsId: string]: string;
}

/**
 * 搜索页面数据提取结果
 */
export interface SearchPageData {
  sessionId: string;
  goodsLinkMap: GoodsLinkMap;
}

/**
 * 网络监听结果
 */
export interface NetworkSearchResult {
  goodsList: Array<{
    goods_id: number;
    seo_link_url: string;
  }>;
}

/**
 * 生成指定长度的随机字符串（包含 a-z、0-9）
 */
function generateRandomString(length: number): string {
  let result = '';
  const chars = '0123456789abcdefghijklmnopqrstuvwxyz';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * 36);
    result += chars[randomIndex];
  }

  return result;
}

/**
 * 生成包含前缀、时间戳和随机字符串的唯一标识符
 */
function generateUniqueId(prefix: string | number): string {
  // 生成从当前时间到前10分钟内的随机时间戳
  const now = Date.now();
  const tenMinutesInMs = 10 * 60 * 1000;
  const randomTimestamp = now - Math.floor(Math.random() * tenMinutesInMs);
  const randomStr = generateRandomString(10);
  return `${prefix}_${randomTimestamp}_${randomStr}`;
}

/**
 * 从 HTML 中提取 rawData
 */
export function extractRawDataFromSearchPage(html: string): any {
  const regex = /window\.rawData=({.*?});document\.dispatchEvent/s;
  const match = html.match(regex);
  if (match && match[1]) {
    try {
      return JSON.parse(match[1]);
    } catch (e) {
      console.error('Failed to parse rawData JSON:', e);
      return null;
    }
  }
  return null;
}

/**
 * 从 rawData 中提取商品链接数据
 */
export function extractGoodsDataFromRawData(rawData: any): SearchPageData | null {
  try {
    // 更安全的数据访问
    let sessionId = '';
    let goodsList: any[] = [];

    // 检查并提取 sessionId
    if (rawData && typeof rawData === 'object') {
      if (rawData.store && typeof rawData.store === 'object') {
        if (rawData.store.query && typeof rawData.store.query === 'object') {
          sessionId = rawData.store.query._x_sessn_id || '';
        }
        // 提取商品列表
        if (Array.isArray(rawData.store.goodsList)) {
          goodsList = rawData.store.goodsList;
        }
      }
    }

    console.log('提取到的数据:', { sessionId, goodsListCount: goodsList.length });

    const goodsLinkMap: GoodsLinkMap = {};

    goodsList.forEach((item: any, index: number) => {
      try {
        const goodsId = item?.data?.goodsId;
        const seoLinkUrl = item?.data?.seoLinkUrl;

        if (goodsId && seoLinkUrl) {
          // 如果链接以 / 开头，添加域名
          const fullUrl = seoLinkUrl.startsWith('/')
            ? `https://www.temu.com${seoLinkUrl}`
            : seoLinkUrl;

          goodsLinkMap[goodsId.toString()] = fullUrl;
          console.log(`商品 ${index + 1}: ID=${goodsId}, URL=${fullUrl}`);
        }
      } catch (itemError) {
        console.warn(`处理商品项 ${index} 时出错:`, itemError);
      }
    });

    console.log('成功提取商品数据:', Object.keys(goodsLinkMap).length, '个');

    return {
      sessionId,
      goodsLinkMap
    };
  } catch (error) {
    console.error('Error extracting goods data from rawData:', error);
    console.error('rawData structure:', typeof rawData, rawData);
    return null;
  }
}

/**
 * 观察搜索页面并提取初始数据
 */
export function observeSearchPageData(callback: (data: SearchPageData) => void): () => void {
  const { cleanup } = createDomObserver({
    textPattern: 'window.rawData=',
    timeout: 30000,
    maxRetries: 5,
    initialDelay: 1000,
    onSuccess: () => {
      try {
        const htmlContent = document.documentElement.outerHTML;
        const rawData = extractRawDataFromSearchPage(htmlContent);

        if (rawData) {
          const searchData = extractGoodsDataFromRawData(rawData);
          if (searchData) {
            console.log('成功提取搜索页面数据:', searchData);
            callback(searchData);
          }
        }
      } catch (error) {
        console.error('处理搜索页面数据时出错:', error);
      }
    },
    onTimeout: () => {
      console.log('提取搜索页面数据超时');
    }
  });

  return cleanup;
}

// 删除了 createSearchNetworkListener 函数，现在通过 background script 处理

/**
 * 启动持续的网络监听（通过 background script）
 */
export async function startContinuousNetworkListening(
  onNewData: (goodsData: GoodsLinkMap) => void
): Promise<{ stop: () => void } | null> {
  try {
    console.log('开始启动持续的网络监听...');

    // 生成唯一的监听器ID
    const listenerId = `temu_search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 监听来自 background script 的消息
    const messageListener = (message: any) => {
      if (message.type === 'temu_search_data' && message.listenerId === listenerId) {
        console.log('收到来自 background script 的 Temu 搜索数据:', message.data);

        try {
          const goodsData = extractGoodsFromNetworkResponse(message.data);
          if (goodsData && Object.keys(goodsData).length > 0) {
            onNewData(goodsData);
          }
        } catch (error) {
          console.error('处理网络响应数据时出错:', error);
        }
      }
    };

    // 添加消息监听器
    chrome.runtime.onMessage.addListener(messageListener);

    // 向 background script 发送启动网络监听的请求
    const response = await new Promise<any>((resolve) => {
      chrome.runtime.sendMessage({
        funType: 'temuSearchNetworkListen',
        action: 'start',
        listenerId: listenerId,
        config: {
          timeout: 30000
        }
      }, resolve);
    });

    if (!response || !response.success) {
      const errorMessage = response?.message || '未知错误';
      console.error('启动网络监听失败:', errorMessage);

      // 特殊处理调试器冲突错误，给用户更好的提示
      if (errorMessage.includes('Another debugger is already attached') ||
          errorMessage.includes('调试器冲突')) {
        console.warn('🔧 调试器冲突解决方案:');
        console.warn('1. 关闭浏览器开发者工具 (F12)');
        console.warn('2. 刷新当前页面');
        console.warn('3. 重新尝试操作');
        console.warn('4. 如果问题持续，请重启浏览器');
      }

      chrome.runtime.onMessage.removeListener(messageListener);
      return null;
    }

    console.log('网络监听启动成功');

    // 返回停止函数
    return {
      stop: () => {
        console.log('停止网络监听...');

        // 移除消息监听器
        chrome.runtime.onMessage.removeListener(messageListener);

        // 向 background script 发送停止网络监听的请求
        chrome.runtime.sendMessage({
          funType: 'temuSearchNetworkListen',
          action: 'stop',
          listenerId: listenerId
        }, (response: any) => {
          if (response && response.success) {
            console.log('网络监听已停止');
          } else {
            console.warn('停止网络监听时出现警告:', response?.message || '未知错误');
          }
        });
      }
    };

  } catch (error) {
    console.error('启动持续网络监听时出错:', error);
    return null;
  }
}

/**
 * 从网络响应中提取商品数据
 */
function extractGoodsFromNetworkResponse(responseData: any): GoodsLinkMap {
  const goodsLinkMap: GoodsLinkMap = {};

  try {
    console.log('开始解析网络响应数据，数据结构:', {
      hasResponseBody: !!responseData?.responseBody,
      hasResult: !!responseData?.responseBody?.result,
      hasData: !!responseData?.responseBody?.result?.data,
      hasGoodsList: !!responseData?.responseBody?.result?.data?.goods_list
    });

    // 检查响应数据结构 - 根据实际数据结构调整
    if (!responseData || !responseData.responseBody) {
      console.warn('网络响应数据格式不正确 - 缺少 responseBody');
      return goodsLinkMap;
    }

    const body = responseData.responseBody;

    // 尝试多种可能的数据路径
    let goodsList: any[] = [];

    if (body.result && body.result.data && body.result.data.goods_list) {
      // 路径: responseBody.result.data.goods_list
      goodsList = body.result.data.goods_list;
      console.log('使用路径: responseBody.result.data.goods_list');
    } else if (body.goods_list) {
      // 路径: responseBody.goods_list
      goodsList = body.goods_list;
      console.log('使用路径: responseBody.goods_list');
    } else if (body.data && body.data.goods_list) {
      // 路径: responseBody.data.goods_list
      goodsList = body.data.goods_list;
      console.log('使用路径: responseBody.data.goods_list');
    } else {
      console.warn('未找到 goods_list 数据，可用的数据结构:', Object.keys(body));
      return goodsLinkMap;
    }

    console.log('从网络响应中提取商品数据，商品数量:', goodsList.length);

    goodsList.forEach((item: any, index: number) => {
      try {
        const goodsId = item.goods_id;
        const seoLinkUrl = item.seo_link_url;

        if (goodsId && seoLinkUrl) {
          // 如果链接以 / 开头，添加域名
          const fullUrl = seoLinkUrl.startsWith('/')
            ? `https://www.temu.com${seoLinkUrl}`
            : seoLinkUrl;

          goodsLinkMap[goodsId.toString()] = fullUrl;

          if (index < 300000000) { // 只打印前3个商品的详细信息
            console.log(`网络商品 ${index + 1}: ID=${goodsId}, URL=${fullUrl.substring(0, 100)}...`);
          }
        } else {
          if (index < 300000000) { // 只打印前3个问题商品的信息
            console.warn(`网络商品 ${index + 1} 缺少必要数据:`, {
              hasGoodsId: !!goodsId,
              hasSeoLinkUrl: !!seoLinkUrl,
              availableKeys: Object.keys(item)
            });
          }
        }
      } catch (itemError) {
        console.warn(`处理网络商品项 ${index} 时出错:`, itemError);
      }
    });

    console.log('成功从网络响应中提取商品数据:', Object.keys(goodsLinkMap).length, '个');

  } catch (error) {
    console.error('解析网络响应数据时出错:', error);
    console.error('响应数据结构:', responseData);
  }

  return goodsLinkMap;
}

/**
 * 从当前页面 URL 中提取商品 ID
 */
export function extractGoodsIdFromUrl(url: string): string | null {
  const regex = /g-(\d+)\.html/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

/**
 * 从 URL 中提取 search_key 参数
 */
export function extractSearchKeyFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    return urlObj.searchParams.get('search_key');
  } catch (error) {
    console.error('解析 URL 失败:', error);
    return null;
  }
}

/**
 * 构建完整的商品链接
 */
export function buildCompleteGoodsUrl(
  goodsId: string,
  seoLinkUrl: string,
  sessionId: string,
  searchKey?: string
): string {
  try {
    const url = new URL(seoLinkUrl);
    // 添加 search_key 参数（如果不存在且提供了值）
    if (searchKey && !url.searchParams.has('search_key')) {
      url.searchParams.set('search_key', encodeURIComponent(searchKey));
    }
    // 添加固定参数
    url.searchParams.set('refer_page_el_sn', '200049');
    url.searchParams.set('refer_page_name', 'search_result');

    // 生成并添加 refer_page_id
    const referPageId = generateUniqueId('10009');
    url.searchParams.set('refer_page_id', referPageId);

    // 添加 session ID
    if (sessionId) {
      url.searchParams.set('_x_sessn_id', sessionId);
    }

    let urlString = url.toString();

    // 修复URL中的编码问题：
    // 1. 先处理双重编码问题：%2520 -> %20
    urlString = urlString.replace(/%2520/g, '%20');
    // 2. 然后将 + 替换为 %20，确保空格的正确编码
    urlString = urlString.replace(/\+/g, '%20');

    return urlString;
  } catch (error) {
    console.error('构建完整商品链接失败:', error);
    return seoLinkUrl;
  }
}

/**
 * 处理导出链接的逻辑
 */
export function processExportLinks(
  originalLinks: string[],
  goodsLinkMap: GoodsLinkMap,
  sessionId: string,
  currentPageUrl: string
): string[] {
  const searchKey = extractSearchKeyFromUrl(currentPageUrl);

  return originalLinks.map(link => {
    const goodsId = extractGoodsIdFromUrl(link);

    if (goodsId && goodsLinkMap[goodsId]) {
      // 使用存储的 seoLinkUrl 构建完整链接
      return buildCompleteGoodsUrl(
        goodsId,
        goodsLinkMap[goodsId],
        sessionId,
        searchKey || undefined
      );
    }

    // 如果没有找到对应的数据，返回原链接
    return link;
  });
}
