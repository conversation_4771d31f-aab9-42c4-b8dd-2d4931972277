<?php

namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CardOperationLogModel extends BaseModel
{
    protected $table = 'card_operation_logs';
    
    protected $fillable = [
        'admin_id',
        'admin_phone',
        'operation_type',
        'card_code_id',
        'card_code',
        'batch_no',
        'operation_count',
        'operation_data',
        'ip_address',
        'user_agent',
        'result_status',
        'error_message',
        'memo'
    ];

    protected $casts = [
        'admin_id' => 'integer',
        'card_code_id' => 'integer',
        'operation_count' => 'integer',
        'result_status' => 'integer',
        'operation_data' => 'array'
    ];

    // 操作类型常量
    const OPERATION_CREATE = 'create'; // 生成单个卡密
    const OPERATION_UPDATE = 'update'; // 修改卡密
    const OPERATION_DELETE = 'delete'; // 删除卡密
    const OPERATION_BATCH_CREATE = 'batch_create'; // 批量生成卡密
    const OPERATION_COPY = 'copy'; // 复制卡密

    // 操作结果常量
    const RESULT_FAILED = 0; // 失败
    const RESULT_SUCCESS = 1; // 成功

    /**
     * 关联管理员
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id', 'id');
    }

    /**
     * 关联卡密（可能为空，批量操作时）
     */
    public function cardCode(): BelongsTo
    {
        return $this->belongsTo(CardCodeModel::class, 'card_code_id', 'id');
    }

    /**
     * 按管理员ID查询
     */
    public function scopeByAdminId(Builder $query, int $adminId): Builder
    {
        return $query->where('admin_id', $adminId);
    }

    /**
     * 按操作类型查询
     */
    public function scopeByOperationType(Builder $query, string $operationType): Builder
    {
        return $query->where('operation_type', $operationType);
    }

    /**
     * 按卡密ID查询
     */
    public function scopeByCardCodeId(Builder $query, int $cardCodeId): Builder
    {
        return $query->where('card_code_id', $cardCodeId);
    }

    /**
     * 按卡密查询（区分大小写）
     */
    public function scopeByCardCode(Builder $query, string $cardCode): Builder
    {
        return $query->whereRaw('BINARY card_code = ?', [$cardCode]);
    }

    /**
     * 按批次号查询
     */
    public function scopeByBatchNo(Builder $query, string $batchNo): Builder
    {
        return $query->where('batch_no', $batchNo);
    }

    /**
     * 按操作结果查询
     */
    public function scopeByResultStatus(Builder $query, int $resultStatus): Builder
    {
        return $query->where('result_status', $resultStatus);
    }

    /**
     * 按创建时间范围查询
     */
    public function scopeByDateRange(Builder $query, ?string $startDate = null, ?string $endDate = null): Builder
    {
        if ($startDate) {
            $query->where('created_at', '>=', $startDate . ' 00:00:00');
        }
        if ($endDate) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }
        return $query;
    }

    /**
     * 按IP地址查询
     */
    public function scopeByIpAddress(Builder $query, string $ipAddress): Builder
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * 查询成功的操作
     */
    public function scopeSuccessful(Builder $query): Builder
    {
        return $query->where('result_status', self::RESULT_SUCCESS);
    }

    /**
     * 查询失败的操作
     */
    public function scopeFailed(Builder $query): Builder
    {
        return $query->where('result_status', self::RESULT_FAILED);
    }

    /**
     * 查询创建操作
     */
    public function scopeCreateOperations(Builder $query): Builder
    {
        return $query->whereIn('operation_type', [self::OPERATION_CREATE, self::OPERATION_BATCH_CREATE]);
    }

    /**
     * 查询修改操作
     */
    public function scopeUpdateOperations(Builder $query): Builder
    {
        return $query->where('operation_type', self::OPERATION_UPDATE);
    }

    /**
     * 查询删除操作
     */
    public function scopeDeleteOperations(Builder $query): Builder
    {
        return $query->where('operation_type', self::OPERATION_DELETE);
    }

    /**
     * 查询批量操作
     */
    public function scopeBatchOperations(Builder $query): Builder
    {
        return $query->where('operation_type', self::OPERATION_BATCH_CREATE);
    }

    /**
     * 获取操作类型文本
     */
    public function getOperationTypeTextAttribute(): string
    {
        return match($this->operation_type) {
            self::OPERATION_CREATE => '生成卡密',
            self::OPERATION_UPDATE => '修改卡密',
            self::OPERATION_DELETE => '删除卡密',
            self::OPERATION_BATCH_CREATE => '批量生成',
            default => '未知操作'
        };
    }

    /**
     * 获取操作结果文本
     */
    public function getResultStatusTextAttribute(): string
    {
        return match($this->result_status) {
            self::RESULT_SUCCESS => '成功',
            self::RESULT_FAILED => '失败',
            default => '未知'
        };
    }

    /**
     * 获取操作摘要
     */
    public function getOperationSummaryAttribute(): string
    {
        $summary = $this->operation_type_text;
        
        if ($this->operation_count > 1) {
            $summary .= "（{$this->operation_count}个）";
        }
        
        if ($this->batch_no) {
            $summary .= " 批次：{$this->batch_no}";
        }
        
        return $summary;
    }

    /**
     * 获取操作类型选项
     */
    public static function getOperationTypeOptions(): array
    {
        return [
            self::OPERATION_CREATE => '生成卡密',
            self::OPERATION_UPDATE => '修改卡密',
            self::OPERATION_DELETE => '删除卡密',
            self::OPERATION_BATCH_CREATE => '批量生成'
        ];
    }

    /**
     * 获取操作结果选项
     */
    public static function getResultStatusOptions(): array
    {
        return [
            self::RESULT_SUCCESS => '成功',
            self::RESULT_FAILED => '失败'
        ];
    }

    /**
     * 记录操作日志
     */
    public static function logOperation(array $data): self
    {
        return self::create([
            'admin_id' => $data['admin_id'],
            'admin_phone' => $data['admin_phone'] ?? '',
            'operation_type' => $data['operation_type'],
            'card_code_id' => $data['card_code_id'] ?? null,
            'card_code' => $data['card_code'] ?? '',
            'batch_no' => $data['batch_no'] ?? '',
            'operation_count' => $data['operation_count'] ?? 1,
            'operation_data' => $data['operation_data'] ?? [],
            'ip_address' => $data['ip_address'] ?? '',
            'user_agent' => $data['user_agent'] ?? '',
            'result_status' => $data['result_status'] ?? self::RESULT_SUCCESS,
            'error_message' => $data['error_message'] ?? '',
            'memo' => $data['memo'] ?? ''
        ]);
    }

    /**
     * 统计某管理员的操作情况
     */
    public static function getAdminOperationStats(int $adminId): array
    {
        $query = self::byAdminId($adminId);
        
        return [
            'total_operations' => $query->count(),
            'successful_operations' => $query->successful()->count(),
            'failed_operations' => $query->failed()->count(),
            'create_operations' => $query->where('operation_type', self::OPERATION_CREATE)->count(),
            'batch_create_operations' => $query->where('operation_type', self::OPERATION_BATCH_CREATE)->count(),
            'update_operations' => $query->where('operation_type', self::OPERATION_UPDATE)->count(),
            'delete_operations' => $query->where('operation_type', self::OPERATION_DELETE)->count(),
            'total_cards_created' => $query->createOperations()->sum('operation_count'),
            'last_operation_at' => $query->max('created_at')
        ];
    }

    /**
     * 统计操作概况
     */
    public static function getOperationOverview(): array
    {
        return [
            'total_operations' => self::count(),
            'successful_operations' => self::successful()->count(),
            'failed_operations' => self::failed()->count(),
            'total_cards_created' => self::createOperations()->sum('operation_count'),
            'unique_admins' => self::distinct('admin_id')->count(),
            'recent_operations' => self::orderBy('id', 'desc')->limit(10)->get()
        ];
    }
}
