/**
 * 商品目录管理API接口
 * 参考goodsApi.ts的结构，通过background页面发送请求
 */
import { sendRequestViaBackground } from './api'
import { getApiUrl } from './apiConfig'

// 目录接口定义
export interface Directory {
  id: number
  user_id: number
  user_sub_id: number
  name: string
  description: string
  sort_order: number
  status: number
  status_name: string
  status_sub: number
  status_sub_name: string
  goods_count: number
  sub_account_goods_count?: number // 子账号在系统目录下的商品数量
  created_at: string
  updated_at: string
  user_info?: {
    id: number
    name: string
    phone: string
    is_vip: number
    vip_level: number
    vip_end_time: string
    status: number
  }
}

// 目录列表参数
export interface DirectoryListParams {
  page?: number
  pageSize?: number
  status?: number
  name?: string
  start_date?: string
  end_date?: string
  directory_type?: number // 目录类型：0=系统目录，1=子账号目录
  creator_name?: string // 创建人姓名
  creator_phone?: string // 创建人手机号
  status_sub?: number // 子账号可见状态：0=不可见，1=可见
}

// 目录创建/更新参数
export interface DirectoryFormData {
  id?: number
  name: string
  description?: string
  sort_order?: number
  status?: number
  status_sub?: number
}

// 批量更新参数
export interface DirectoryBatchUpdateData {
  ids: number[]
  status?: number
}

// API响应类型
export interface DirectoryListResponse {
  list: Directory[]
  pagination: {
    currentPage: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
  }
}

export interface DirectoryResponse {
  id?: number
  message: string
}



/**
 * 获取目录列表
 */
export const getDirectoryList = async (params: DirectoryListParams): Promise<DirectoryListResponse> => {
  const url = await getApiUrl('apiGoodsDirectoryListUrl');
  console.log('获取目录列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getDirectoryList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 创建目录
 */
export const createDirectory = async (data: DirectoryFormData): Promise<DirectoryResponse> => {
  const url = await getApiUrl('apiGoodsDirectoryCreateUrl');
  
  return sendRequestViaBackground({
    funName: 'createDirectory',
    url,
    method: 'post',
    data,
    auth: true
  });
};

/**
 * 更新目录
 */
export const updateDirectory = async (data: DirectoryFormData): Promise<DirectoryResponse> => {
  const url = await getApiUrl('apiGoodsDirectoryUpdateUrl');
  
  return sendRequestViaBackground({
    funName: 'updateDirectory',
    url,
    method: 'post',
    data,
    auth: true
  });
};

/**
 * 删除目录
 */
export const deleteDirectory = async (id: number): Promise<DirectoryResponse> => {
  const url = await getApiUrl('apiGoodsDirectoryDeleteUrl');
  
  return sendRequestViaBackground({
    funName: 'deleteDirectory',
    url,
    method: 'post',
    data: { id },
    auth: true
  });
};

/**
 * 批量更新目录
 */
export const batchUpdateDirectory = async (data: DirectoryBatchUpdateData): Promise<DirectoryResponse> => {
  const url = await getApiUrl('apiGoodsDirectoryBatchUpdateUrl');
  
  return sendRequestViaBackground({
    funName: 'batchUpdateDirectory',
    url,
    method: 'post',
    data,
    auth: true
  });
};

/**
 * 获取目录详情
 */
export const getDirectoryDetail = async (id: number): Promise<Directory> => {
  const url = await getApiUrl('apiGoodsDirectoryDetailUrl');
  
  return sendRequestViaBackground({
    funName: 'getDirectoryDetail',
    url,
    method: 'get',
    params: { id },
    auth: true
  });
};

/**
 * 更新目录商品数量
 */
export const updateDirectoryGoodsCount = async (directoryId: number): Promise<DirectoryResponse> => {
  const url = await getApiUrl('apiGoodsDirectoryUpdateGoodsCountUrl');
  
  return sendRequestViaBackground({
    funName: 'updateDirectoryGoodsCount',
    url,
    method: 'post',
    data: { directory_id: directoryId },
    auth: true
  });
};

/**
 * 批量更新所有目录的商品数量
 */
export const updateAllDirectoryGoodsCount = async (): Promise<DirectoryResponse> => {
  const url = await getApiUrl('apiGoodsDirectoryUpdateAllGoodsCountUrl');
  
  return sendRequestViaBackground({
    funName: 'updateAllDirectoryGoodsCount',
    url,
    method: 'post',
    auth: true
  });
};