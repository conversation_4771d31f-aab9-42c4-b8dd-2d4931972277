<?php

namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CardUsageRecordModel extends BaseModel
{
    protected $table = 'card_usage_records';
    
    protected $fillable = [
        'card_code_id',
        'card_code',
        'user_id',
        'card_name',
        'card_type',
        'points_added',
        'vip_days_added',
        'vip_days_unit',
        'vip_level_set',
        'user_points_before',
        'user_points_after',
        'user_vip_status_before',
        'user_vip_status_after',
        'user_vip_end_time_before',
        'user_vip_end_time_after',
        'ip_address',
        'user_agent',
        'memo',
        'used_at'
    ];

    protected $casts = [
        'card_code_id' => 'integer',
        'user_id' => 'integer',
        'card_type' => 'integer',
        'points_added' => 'integer',
        'vip_days_added' => 'integer',
        'vip_level_set' => 'integer',
        'user_points_before' => 'integer',
        'user_points_after' => 'integer',
        'user_vip_status_before' => 'integer',
        'user_vip_status_after' => 'integer',
        'user_vip_end_time_before' => 'datetime',
        'user_vip_end_time_after' => 'datetime',
        'used_at' => 'datetime'
    ];

    /**
     * 关联卡密
     */
    public function cardCode(): BelongsTo
    {
        return $this->belongsTo(CardCodeModel::class, 'card_code_id', 'id');
    }

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 按卡密ID查询
     */
    public function scopeByCardCodeId(Builder $query, int $cardCodeId): Builder
    {
        return $query->where('card_code_id', $cardCodeId);
    }

    /**
     * 按用户ID查询
     */
    public function scopeByUserId(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 按卡密查询（区分大小写）
     */
    public function scopeByCardCode(Builder $query, string $cardCode): Builder
    {
        return $query->whereRaw('BINARY card_code = ?', [$cardCode]);
    }

    /**
     * 按卡密类型查询
     */
    public function scopeByCardType(Builder $query, int $cardType): Builder
    {
        return $query->where('card_type', $cardType);
    }

    /**
     * 按使用时间范围查询
     */
    public function scopeByUsedDateRange(Builder $query, ?string $startDate = null, ?string $endDate = null): Builder
    {
        if ($startDate) {
            $query->where('used_at', '>=', $startDate . ' 00:00:00');
        }
        if ($endDate) {
            $query->where('used_at', '<=', $endDate . ' 23:59:59');
        }
        return $query;
    }

    /**
     * 按创建时间范围查询
     */
    public function scopeByDateRange(Builder $query, ?string $startDate = null, ?string $endDate = null): Builder
    {
        if ($startDate) {
            $query->where('created_at', '>=', $startDate . ' 00:00:00');
        }
        if ($endDate) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }
        return $query;
    }

    /**
     * 按IP地址查询
     */
    public function scopeByIpAddress(Builder $query, string $ipAddress): Builder
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * 查询有效期卡使用记录
     */
    public function scopeVipCardUsage(Builder $query): Builder
    {
        return $query->where('card_type', CardCodeModel::TYPE_VIP_CARD);
    }

    /**
     * 查询积分卡使用记录
     */
    public function scopePointsCardUsage(Builder $query): Builder
    {
        return $query->where('card_type', CardCodeModel::TYPE_POINTS_CARD);
    }

    /**
     * 按积分增加范围查询
     */
    public function scopeByPointsAddedRange(Builder $query, ?int $minPoints = null, ?int $maxPoints = null): Builder
    {
        if ($minPoints !== null) {
            $query->where('points_added', '>=', $minPoints);
        }
        if ($maxPoints !== null) {
            $query->where('points_added', '<=', $maxPoints);
        }
        return $query;
    }

    /**
     * 按VIP时长增加范围查询
     */
    public function scopeByVipDaysAddedRange(Builder $query, ?int $minDays = null, ?int $maxDays = null): Builder
    {
        if ($minDays !== null) {
            $query->where('vip_days_added', '>=', $minDays);
        }
        if ($maxDays !== null) {
            $query->where('vip_days_added', '<=', $maxDays);
        }
        return $query;
    }

    /**
     * 获取卡密类型文本
     */
    public function getCardTypeTextAttribute(): string
    {
        return match($this->card_type) {
            CardCodeModel::TYPE_VIP_CARD => '有效期卡',
            CardCodeModel::TYPE_POINTS_CARD => '积分卡',
            default => '未知类型'
        };
    }

    /**
     * 获取积分变化文本
     */
    public function getPointsChangeTextAttribute(): string
    {
        return "{$this->user_points_before} → {$this->user_points_after} (+{$this->points_added})";
    }

    /**
     * 获取VIP状态变化文本
     */
    public function getVipStatusChangeTextAttribute(): string
    {
        $beforeText = $this->user_vip_status_before ? 'VIP' : '普通';
        $afterText = $this->user_vip_status_after ? 'VIP' : '普通';
        return "{$beforeText} → {$afterText}";
    }

    /**
     * 获取VIP到期时间变化文本
     */
    public function getVipEndTimeChangeTextAttribute(): string
    {
        $beforeText = $this->user_vip_end_time_before ? $this->user_vip_end_time_before->format('Y-m-d H:i:s') : '无';
        $afterText = $this->user_vip_end_time_after ? $this->user_vip_end_time_after->format('Y-m-d H:i:s') : '无';
        return "{$beforeText} → {$afterText}";
    }

    /**
     * 获取使用效果摘要
     */
    public function getUsageEffectSummaryAttribute(): string
    {
        $effects = [];

        if ($this->points_added > 0) {
            $effects[] = "积分+{$this->points_added}";
        }

        if ($this->vip_days_added > 0) {
            // 根据VIP时长单位显示正确的单位
            $unitText = match($this->vip_days_unit) {
                CardCodeModel::VIP_UNIT_YEAR => '年',
                CardCodeModel::VIP_UNIT_MONTH => '月',
                CardCodeModel::VIP_UNIT_DAY => '天',
                default => '天'
            };
            $effects[] = "VIP+{$this->vip_days_added}{$unitText}";
        }

        if ($this->vip_level_set > 0) {
            $effects[] = "VIP等级{$this->vip_level_set}";
        }

        return implode('，', $effects) ?: '无效果';
    }

    /**
     * 统计某用户的卡密激活情况
     */
    public static function getUserUsageStats(int $userId): array
    {
        $query = self::byUserId($userId);
        
        return [
            'total_count' => $query->count(),
            'total_points_added' => $query->sum('points_added'),
            'total_vip_days_added' => $query->sum('vip_days_added'),
            'vip_card_count' => $query->where('card_type', CardCodeModel::TYPE_VIP_CARD)->count(),
            'points_card_count' => $query->where('card_type', CardCodeModel::TYPE_POINTS_CARD)->count(),
            'last_used_at' => $query->max('used_at')
        ];
    }

    /**
     * 统计某卡密的使用情况
     */
    public static function getCardUsageStats(int $cardCodeId): array
    {
        $query = self::byCardCodeId($cardCodeId);
        
        return [
            'usage_count' => $query->count(),
            'unique_users' => $query->distinct('user_id')->count(),
            'total_points_distributed' => $query->sum('points_added'),
            'total_vip_days_distributed' => $query->sum('vip_days_added'),
            'first_used_at' => $query->min('used_at'),
            'last_used_at' => $query->max('used_at')
        ];
    }
}
