<?php
declare(strict_types=1);

namespace App\Helpers\Traits;
use Symfony\Component\HttpFoundation\Response as FoundationResponse;

trait ApiResponse
{

    protected $statusCode = FoundationResponse::HTTP_OK;
    protected $token = '';

    /**
     * @return int
     */
    public function getStatusCode()
    {
        return $this->statusCode;
    }

    /**
     * @param $statusCode
     * @return $this
     */
    public function setStatusCode($statusCode)
    {
        $this->statusCode = $statusCode;
        return $this;
    }

    /**
     * @param $token
     * @return $this
     */
    public function setToken($token)
    {
        $this->token = $token;
        return $this;
    }

    /**
     * @param $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function respond($data)
    {
        $response = response()->json($data, $this->getStatusCode())->setEncodingOptions(JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        if ($this->token) {
            $response->headers->set('Authorization', 'Bearer ' . $this->token);
        }
        return $response;
    }

    /**
     * @param $status
     * @param array $data
     * @param null $code
     * @return \Illuminate\Http\JsonResponse
     */
    public function status($status, array $data, $code = null)
    {
        if ($code) {
            $this->setStatusCode($code);
        }
        $status = [
            'status' => (int)$status,
            'code' => $this->statusCode
        ];
        //增加系统默认返回信息
        $system = [
            'system' => request()->system ?? new \stdClass(),
            'user'   => request()->user   ?? new \stdClass(),
            'token'  => request()->token  ?? ''
        ];
        $data = array_merge($system,$status, $data);
        return $this->respond($data);
    }

    /**
     * @param $message
     * @param int $code
     * @param string $status
     * @return mixed
     */
    public function failed($message="暂无数据", $code = FoundationResponse::HTTP_OK, $status =500)
    {
        return $this->setStatusCode($code)->message($message, $status);
    }

    /**
     * @param $message
     * @param string $status
     * @return \Illuminate\Http\JsonResponse
     */
    public function message($message, $status = 200)
    {
        $status = [
            'status' => (int)$status,
            'code' => $this->statusCode,
            'message' => $message,
        ];
        return $this->respond($status);
    }

    /**
     * @param string $message
     * @return mixed
     */
    public function internalError($message = "Internal Error!")
    {
        return $this->failed($message, FoundationResponse::HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function created($message = "created")
    {
        return $this->setStatusCode(FoundationResponse::HTTP_CREATED)
            ->message($message);
    }

    /**
     * @param $data
     * @param string $status
     * @return \Illuminate\Http\JsonResponse
     */
    public function success($data=[], $status = 200, $message = "操作成功")
    {
        $data = array_merge(compact('data'), ['message' => $message]);
        return $this->status($status, $data);
    }

    /**
     * @param string $message
     * @return mixed
     */
    public function notFond($message = 'Not Fond!')
    {
        return $this->failed($message, Foundationresponse::HTTP_NOT_FOUND);
    }

    public function apiSuccess($data=[], $status = 200, $message = "success"){
        // 获取当前请求
        $request = request();

        // 获取当前请求方法和应用URL的主机名
        $isPostRequest = $request->isMethod('post');
        $appUrl = config('app.url');
        //是否强制返回加密的数据
        $responseEncrypto = config('ad.response_encrypto');
        $appHost = parse_url($appUrl, PHP_URL_HOST);
        $currentHost = $request->getHost();
        // 检查是否需要加密（只有在POST请求且当前域名不是配置的app.url主机名时才加密）
        if ( !empty($data) && ($responseEncrypto == 1 || ($isPostRequest && $currentHost !== $appHost) )) {
            $appKey = config('ad.appkey');
            
            // 检查数据类型，确保可以安全地进行加密
            if (is_scalar($data) && !is_string($data) && !is_numeric($data)) {
                // 对于boolean或其他非字符串/数字的标量类型，转换为字符串
                $data = var_export($data, true);
            }
            
            try{
                $encryptedData = \App\Utils\AdSecurity::encrypt(['data' => $data], $appKey);
                $data = $encryptedData;
            }catch(\Exception $e){
                // 记录加密失败的详细信息，便于调试
                \Illuminate\Support\Facades\Log::warning('API响应加密失败', [
                    'error' => $e->getMessage(),
                    'data_type' => gettype($data),
                    'data_value' => $data,
                    'trace' => $e->getTraceAsString()
                ]);
                //捕获异常 不进行任何操作 执行下面的原值返回逻辑 兼容加密失败的情况
            }
        }
        // 不需要加密，保持原有逻辑
        $data = array_merge(compact('data'), ['msg' => $message]);
        return $this->apiStatus($status, $data);
    }

    public function apiError($message = "error", $status = 200){
        return $this->apiStatus($status, ['msg'=>$message], 0);
    }

    public function apiStatus($status, array $data, $code = 1,$status_code = 200)
    {
        if ($status_code) {
            $this->setStatusCode($status_code);
        }
        $status = [
            'status' => (int)$status,
            'code' => $code
        ];
        $data = array_merge($status, $data);
        if(isset($data['data']) && is_array($data['data']) && count($data['data'])==1 && array_key_exists(0, $data['data'])){
            $data = array_merge($data, ['data'=>$data['data'][0]]);
        }
        return $this->respond($data);
    }

    /**
     * @param $message
     * @param int $code
     * @param string $status
     * @return mixed
     */
    public function apiFailed($message="暂无数据", $code = 0, $status_code =200)
    {
        return $this->setStatusCode($status_code)->apiMessage($message, $code,$status_code);
    }

    /**
     * @param $message
     * @param string $status
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiMessage($message, $code = 0,$status_code = 200)
    {
        $status = [
            'status' => (int)$status_code,
            'code' => $code,
            'msg' => $message,
            'data' => [],
        ];
        return $this->respond($status);
    }

    /**
     * 返回标准化的成功响应（EXE格式）
     * 为EXE程序提供专用的响应格式
     *
     * @param mixed $data
     * @param string $message
     * @param array $meta 元数据信息
     * @return \Illuminate\Http\JsonResponse
     */
    public function exeSuccessResponse($data = null, string $message = 'success', array $meta = []): \Illuminate\Http\JsonResponse
    {
        $response = [
            'code' => 200,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        return response()->json($response);
    }

    /**
     * 返回标准化的错误响应（EXE格式）
     * 为EXE程序提供专用的错误响应格式
     *
     * @param string $message
     * @param int $code
     * @param mixed $errors
     * @param array $meta 元数据信息
     * @return \Illuminate\Http\JsonResponse
     */
    public function exeErrorResponse(string $message, int $code = 400, $errors = null, array $meta = []): \Illuminate\Http\JsonResponse
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'data' => null,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        return response()->json($response, $code);
    }

    /**
     * 返回分页响应（EXE格式）
     *
     * @param mixed $data
     * @param array $pagination
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function exePaginatedResponse($data, array $pagination, string $message = 'success'): \Illuminate\Http\JsonResponse
    {
        return $this->exeSuccessResponse($data, $message, [
            'pagination' => $pagination
        ]);
    }

    /**
     * 清理响应数据，移除敏感信息
     *
     * @param mixed $data
     * @return mixed
     */
    public function sanitizeResponseData($data)
    {
        if (is_array($data)) {
            // 移除敏感字段
            $sensitiveFields = ['appsecret', 'password', 'token_pc'];
            foreach ($sensitiveFields as $field) {
                if (isset($data[$field])) {
                    unset($data[$field]);
                }
            }

            // 递归处理数组
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeResponseData($value);
            }
        } elseif (is_object($data)) {
            // 处理对象
            $data = json_decode(json_encode($data), true);
            $data = $this->sanitizeResponseData($data);
        }

        return $data;
    }

    /**
     * 记录操作日志
     *
     * @param string $action
     * @param array $context
     */
    public function logOperation(string $action, array $context = []): void
    {
        \Illuminate\Support\Facades\Log::channel('file_process')->info($action, array_merge($context, [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]));
    }

}
