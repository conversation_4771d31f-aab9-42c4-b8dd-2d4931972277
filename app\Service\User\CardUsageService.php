<?php

namespace App\Service\User;

use App\Service\BaseService;
use App\Models\User\CardCodeModel;
use App\Models\User\CardUsageRecordModel;
use App\Models\User\User;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class CardUsageService extends BaseService
{
    /**
     * 卡密激活
     */
    public function useCardCode(int $userId, string $cardCode, array $requestInfo = [], int $isVip = 0): array
    {
        $maxRetries = 3;
        $retryCount = 0;
        $cardCode = trim($cardCode);
        while ($retryCount < $maxRetries) {
            try {
                // 先在事务外检查卡密是否过期，如果过期则禁用
                $this->checkAndDisableExpiredCard($cardCode);

                return DB::transaction(function () use ($userId, $cardCode, $requestInfo, $isVip) {
                    // 查找用户
                    $user = User::find($userId);
                    if (!$user) {
                        throw new MyException('用户不存在');
                    }

                    // 使用行锁查找卡密，确保并发安全（区分大小写）
                    $cardCodeModel = CardCodeModel::byCardCode($cardCode)
                        ->where('status', CardCodeModel::STATUS_UNUSED)
                        ->lockForUpdate()
                        ->first();

                    if (!$cardCodeModel) {
                        throw new MyException('您输入的卡密无效');
                    }

                    // 再次检查卡密有效期（防止并发情况下的竞态条件）
                    if ($cardCodeModel->valid_until && $cardCodeModel->valid_until->isPast()) {
                        throw new MyException('卡密已过期');
                    }

                    // 检查用户是否已经使用过这张卡密
                    $existingUsage = CardUsageRecordModel::byCardCodeId($cardCodeModel->id)
                                                    ->byUserId($userId)
                                                    ->first();
                    if ($existingUsage) {
                        throw new MyException('您已经使用过该卡密，请勿重复使用');
                    }

                    // 记录使用前的用户状态
                    $userPointsBefore = $user->points ?? 0;
                    $userVipStatusBefore = $user->is_vip ?? 0;
                    $userVipEndTimeBefore = $user->vip_end_time;

                    // 根据卡密类型执行不同的操作
                    if ($cardCodeModel->card_type === CardCodeModel::TYPE_VIP_CARD) {
                        // 有效期卡：增加积分 + 设置VIP
                        if ($cardCodeModel->points > 0) {
                            $user->addPoints($cardCodeModel->points);
                        }

                        if ($cardCodeModel->vip_days > 0) {
                            // 根据VIP时长单位计算VIP到期时间
                            $vipEndTime = $this->calculateVipEndTime($user->vip_end_time, $cardCodeModel->vip_days, $cardCodeModel->vip_days_unit);
                            $user->setVipWithEndTime($vipEndTime, $cardCodeModel->vip_level);
                        }
                    } else {
                        if($isVip == 0){
                            throw new MyException('您不是VIP用户，不能使用积分卡');
                        }
                        // 积分卡：仅增加积分
                        if ($cardCodeModel->points > 0) {
                            $user->addPoints($cardCodeModel->points);
                        }
                    }

                    // 刷新用户数据
                    $user->refresh();

                    // 记录使用后的用户状态
                    $userPointsAfter = $user->points ?? 0;
                    $userVipStatusAfter = $user->is_vip ?? 0;
                    $userVipEndTimeAfter = $user->vip_end_time;

                    // 创建使用记录
                    $usageRecord = CardUsageRecordModel::create([
                        'card_code_id' => $cardCodeModel->id,
                        'card_code' => $cardCode,
                        'user_id' => $userId,
                        'card_name' => $cardCodeModel->card_name,
                        'card_type' => $cardCodeModel->card_type,
                        'points_added' => $cardCodeModel->points,
                        'vip_days_added' => $cardCodeModel->vip_days,
                        'vip_days_unit' => $cardCodeModel->vip_days_unit,
                        'vip_level_set' => $cardCodeModel->vip_level,
                        'user_points_before' => $userPointsBefore,
                        'user_points_after' => $userPointsAfter,
                        'user_vip_status_before' => $userVipStatusBefore,
                        'user_vip_status_after' => $userVipStatusAfter,
                        'user_vip_end_time_before' => $userVipEndTimeBefore,
                        'user_vip_end_time_after' => $userVipEndTimeAfter,
                        'ip_address' => $requestInfo['ip'] ?? '',
                        'user_agent' => $requestInfo['user_agent'] ?? '',
                        'memo' => '用户卡密激活',
                        'used_at' => now()
                    ]);

                    // 更新卡密状态为已使用，同时记录使用信息
                    $cardCodeModel->update([
                        'status' => CardCodeModel::STATUS_USED,
                        'used_at' => now(),
                        'used_by' => $userId
                    ]);

                    return [
                        'card_code' => $cardCode,
                        'card_name' => $cardCodeModel->card_name,
                        'card_type' => $cardCodeModel->card_type,
                        'card_type_text' => $cardCodeModel->card_type_text,
                        'points_added' => $cardCodeModel->points,
                        'vip_days_added' => $cardCodeModel->vip_days,
                        'vip_days_unit' => $cardCodeModel->vip_days_unit,
                        'vip_level_set' => $cardCodeModel->vip_level,
                        'user_points_before' => $userPointsBefore,
                        'user_points_after' => $userPointsAfter,
                        'user_vip_status_before' => $userVipStatusBefore,
                        'user_vip_status_after' => $userVipStatusAfter,
                        'user_vip_end_time_before' => $userVipEndTimeBefore?->format('Y-m-d H:i:s'),
                        'user_vip_end_time_after' => $userVipEndTimeAfter?->format('Y-m-d H:i:s'),
                        'usage_effect_summary' => $usageRecord->usage_effect_summary,
                        'used_at' => $usageRecord->used_at->format('Y-m-d H:i:s')
                    ];
                });
            } catch (\Illuminate\Database\QueryException $e) {
                // 检查是否是死锁错误
                if ($e->getCode() == '40001' || strpos($e->getMessage(), 'Deadlock') !== false) {
                    $retryCount++;
                    if ($retryCount >= $maxRetries) {
                        throw new MyException('系统繁忙，请稍后重试');
                    }
                    // 等待随机时间后重试
                    usleep(rand(100000, 500000)); // 100-500ms
                    continue;
                } else {
                    throw new MyException('卡密激活失败：' . $e->getMessage());
                }
            } catch (\Exception $e) {
                throw new MyException('卡密激活失败：' . $e->getMessage());
            }
        }

        throw new MyException('卡密激活失败，请稍后重试');
    }

    /**
     * 检查并禁用过期的卡密
     */
    private function checkAndDisableExpiredCard(string $cardCode): void
    {
        // 在独立事务中处理过期卡密的禁用，避免影响主事务
        DB::transaction(function () use ($cardCode) {
            $cardCodeModel = CardCodeModel::byCardCode($cardCode)
                ->where('status', CardCodeModel::STATUS_UNUSED)
                ->lockForUpdate()
                ->first();

            if ($cardCodeModel && $cardCodeModel->valid_until && $cardCodeModel->valid_until->isPast()) {
                $cardCodeModel->update(['status' => CardCodeModel::STATUS_DISABLED]);
            }
        });
    }

    /**
     * 根据VIP时长单位计算VIP到期时间
     */
    private function calculateVipEndTime($currentEndTime, int $vipDays, string $vipDaysUnit): \Carbon\Carbon
    {
        $baseTime = $currentEndTime && $currentEndTime->isFuture() ? $currentEndTime : now();

        switch ($vipDaysUnit) {
            case CardCodeModel::VIP_UNIT_YEAR:
                return $baseTime->addYears($vipDays);
            case CardCodeModel::VIP_UNIT_MONTH:
                return $baseTime->addMonths($vipDays);
            case CardCodeModel::VIP_UNIT_DAY:
            default:
                return $baseTime->addDays($vipDays);
        }
    }

    /**
     * 获取用户卡密激活记录
     */
    public function getUserUsageRecords(int $userId, array $params): array
    {
        // 查找用户
        $user = User::find($userId);
        if (!$user) {
            throw new MyException('用户不存在');
        }

        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));
        
        // 获取筛选参数
        $cardType = $params['card_type'] ?? null;
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;

        // 构建查询
        $query = CardUsageRecordModel::byUserId($userId);

        // 按卡密类型筛选
        if (is_numeric($cardType) && in_array($cardType, [1, 2])) {
            $query->byCardType($cardType);
        }

        // 按使用时间范围筛选
        if ($startDate || $endDate) {
            $query->byUsedDateRange($startDate, $endDate);
        }

        // 获取总数
        $total = $query->count();

        // 分页查询
        $list = $query->orderBy('id', 'desc')
                     ->offset(($page - 1) * $pageSize)
                     ->limit($pageSize)
                     ->get();

        // 格式化数据
        $formattedList = $list->map(function ($record) {
            return [
                'id' => $record->id,
                'card_code' => $record->card_code,
                'card_name' => $record->card_name,
                'card_type' => $record->card_type,
                'card_type_text' => $record->card_type_text,
                'points_added' => $record->points_added,
                'vip_days_added' => $record->vip_days_added,
                'vip_days_unit' => $record->vip_days_unit,
                'vip_level_set' => $record->vip_level_set,
                'points_change_text' => $record->points_change_text,
                'vip_status_change_text' => $record->vip_status_change_text,
                'vip_end_time_change_text' => $record->vip_end_time_change_text,
                'usage_effect_summary' => $record->usage_effect_summary,
                'used_at' => $record->used_at->format('Y-m-d H:i:s'),
                'created_at' => $record->created_at
            ];
        });

        // 获取用户使用统计
        $userStats = CardUsageRecordModel::getUserUsageStats($userId);

        return [
            'list' => $formattedList,
            'pagination' => [
                'page' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => ceil($total / $pageSize)
            ],
            'user_stats' => [
                'total_count' => $userStats['total_count'],
                'total_points_added' => $userStats['total_points_added'],
                'total_vip_days_added' => $userStats['total_vip_days_added'],
                'vip_card_count' => $userStats['vip_card_count'],
                'points_card_count' => $userStats['points_card_count'],
                'last_used_at' => $userStats['last_used_at']
            ]
        ];
    }

    /**
     * 获取卡密激活记录列表（管理员查看）
     */
    public function getUsageRecordsList(int $adminId, array $params): array
    {
        // 验证管理员权限
        $admin = User::find($adminId);
        if (!$admin || !$admin->isCardAdmin()) {
            throw new MyException('无卡密管理权限');
        }

        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));
        
        // 获取筛选参数
        $cardType = $params['card_type'] ?? null;
        $cardCode = $params['card_code'] ?? null;
        $userPhone = $params['user_phone'] ?? null;
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;

        // 构建查询
        $query = CardUsageRecordModel::query();

        // 按卡密类型筛选
        if (is_numeric($cardType) && in_array($cardType, [1, 2])) {
            $query->byCardType($cardType);
        }

        // 按卡密筛选
        if ($cardCode) {
            $query->byCardCode($cardCode);
        }

        // 按用户手机号筛选
        if ($userPhone) {
            $userIds = User::where('phone', 'like', "%{$userPhone}%")->pluck('id');
            $query->whereIn('user_id', $userIds);
        }

        // 按使用时间范围筛选
        if ($startDate || $endDate) {
            $query->byUsedDateRange($startDate, $endDate);
        }

        // 获取总数
        $total = $query->count();

        // 分页查询
        $list = $query->with(['user:id,phone', 'cardCode:id,card_code,card_name,card_type,price'])
                     ->orderBy('id', 'desc')
                     ->offset(($page - 1) * $pageSize)
                     ->limit($pageSize)
                     ->get();

        // 格式化数据
        $formattedList = $list->map(function ($record) {
            return [
                'id' => $record->id,
                'card_code' => $record->card_code,
                'card_name' => $record->card_name,
                'card_type' => $record->card_type,
                'card_type_text' => $record->card_type_text,
                'user_phone' => $record->user?->phone ?? '',
                'points_added' => $record->points_added,
                'vip_days_added' => $record->vip_days_added,
                'vip_level_set' => $record->vip_level_set,
                'points_change_text' => $record->points_change_text,
                'vip_status_change_text' => $record->vip_status_change_text,
                'vip_end_time_change_text' => $record->vip_end_time_change_text,
                'usage_effect_summary' => $record->usage_effect_summary,
                'ip_address' => $record->ip_address,
                'used_at' => $record->used_at->format('Y-m-d H:i:s'),
                'created_at' => $record->created_at->format('Y-m-d H:i:s')
            ];
        });

        return [
            'list' => $formattedList,
            'pagination' => [
                'page' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => ceil($total / $pageSize)
            ]
        ];
    }
}
