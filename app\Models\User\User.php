<?php
namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends BaseModel
{
    protected $table = 'user';
    protected $appends = [];

    protected $hidden = [
        'appsecret'
    ];

    protected $fillable = [
        'pid',
        'name',
        'phone',
        'password',
        'login_time',
        'token_pc',
        'is_vip',
        'vip_level',
        'vip_start_time',
        'vip_end_time',
        'status',
        'invite_code',
        'invite_1',
        'is_admin',
        'is_card_admin',
        'points',
        'points_all',
        'appid',
        'appsecret',
        'appstatus'
    ];

    protected $casts = [
        'is_vip' => 'integer',
        'vip_level' => 'integer',
        'status' => 'integer',
        'invite_1' => 'integer',
        'is_admin' => 'integer',
        'is_card_admin' => 'integer',
        'points' => 'integer',
        'points_all' => 'integer',
        'appstatus' => 'integer',
        'login_time' => 'datetime',
        'vip_start_time' => 'datetime',
        'vip_end_time' => 'datetime'
    ];

    /**
     * 关联创建的卡密
     */
    public function createdCardCodes(): HasMany
    {
        return $this->hasMany(CardCodeModel::class, 'admin_id', 'id');
    }

    /**
     * 关联使用的卡密记录
     */
    public function cardUsageRecords(): HasMany
    {
        return $this->hasMany(CardUsageRecordModel::class, 'user_id', 'id');
    }

    /**
     * 关联操作日志
     */
    public function cardOperationLogs(): HasMany
    {
        return $this->hasMany(CardOperationLogModel::class, 'admin_id', 'id');
    }

    /**
     * 检查是否为卡密管理员
     */
    public function isCardAdmin(): bool
    {
        return $this->is_card_admin === 1;
    }

    /**
     * 检查是否为管理员
     */
    public function isAdmin(): bool
    {
        return $this->is_admin === 1;
    }

    /**
     * 检查是否为VIP
     */
    public function isVip(): bool
    {
        // 如果用户是管理员，默认就是VIP
        if ($this->is_admin === 1) {
            return true;
        }

        return $this->is_vip === 1 && $this->vip_end_time && $this->vip_end_time->isFuture();
    }

    /**
     * 按卡密管理员权限查询
     */
    public function scopeCardAdmins(Builder $query): Builder
    {
        return $query->where('is_card_admin', 1);
    }

    /**
     * 按管理员权限查询
     */
    public function scopeAdmins(Builder $query): Builder
    {
        return $query->where('is_admin', 1);
    }

    /**
     * 按VIP状态查询
     */
    public function scopeVips(Builder $query): Builder
    {
        return $query->where('is_vip', 1)
            ->where('vip_end_time', '>', now());
    }

    /**
     * 按手机号查询
     */
    public function scopeByPhone(Builder $query, string $phone): Builder
    {
        return $query->where('phone', $phone);
    }

    /**
     * 按状态查询
     */
    public function scopeByStatus(Builder $query, int $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * 增加用户积分
     */
    public function addPoints(int $points): bool
    {
        $this->points = ($this->points ?? 0) + $points;
        $this->points_all = ($this->points_all ?? 0) + $points;
        return $this->save();
    }

    /**
     * 扣除用户积分
     */
    public function deductPoints(int $points): bool
    {
        if (($this->points ?? 0) < $points) {
            return false; // 积分不足
        }

        $this->points = ($this->points ?? 0) - $points;
        return $this->save();
    }

    /**
     * 设置VIP状态和到期时间
     */
    public function setVip(int $days, int $level = 1): bool
    {
        $this->is_vip = 1;
        $this->vip_level = $level;

        // 如果当前是VIP且未过期，则在现有基础上延长
        if ($this->vip_end_time && $this->vip_end_time->isFuture()) {
            $this->vip_end_time = $this->vip_end_time->addDays($days);
        } else {
            // 否则从现在开始计算
            $this->vip_start_time = now();
            $this->vip_end_time = now()->addDays($days);
        }

        return $this->save();
    }

    /**
     * 设置VIP状态和指定的到期时间
     */
    public function setVipWithEndTime(\Carbon\Carbon $endTime, int $level = 1): bool
    {
        $this->is_vip = 1;
        $this->vip_level = $level;
        $this->vip_start_time = now();
        $this->vip_end_time = $endTime;

        return $this->save();
    }

    /**
     * 获取VIP剩余天数
     */
    public function getVipRemainingDaysAttribute(): int
    {
        if (!$this->isVip()) {
            return 0;
        }

        return max(0, now()->diffInDays($this->vip_end_time, false));
    }

    /**
     * 获取用户权限文本
     */
    public function getPermissionTextAttribute(): string
    {
        $permissions = [];

        if ($this->isAdmin()) {
            $permissions[] = '系统管理员';
        }

        if ($this->isCardAdmin()) {
            $permissions[] = '卡密管理员';
        }

        if ($this->isVip()) {
            $permissions[] = 'VIP会员';
        }

        return implode('、', $permissions) ?: '普通用户';
    }
}
