<?php

namespace App\Models\User;

use App\Models\BaseModel;
use App\Models\User\User as UserModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserGoodsCollectionSettingsModel extends BaseModel
{
    protected $table = 'user_goods_collection_settings';

    protected $fillable = [
        'user_id',
        'default_directory_id',
        'collection_mode',
        'no_remind_until'
    ];

    protected $casts = [
        'user_id' => 'integer',
        'default_directory_id' => 'integer',
        'collection_mode' => 'integer',
        // 'no_remind_until' => 'datetime', // 注释掉，遵循项目不使用模型$casts属性的规范
    ];

    // 采集模式常量
    const COLLECTION_MODE_AUTO = 1;     // 自动采集
    const COLLECTION_MODE_MANUAL = 2;   // 手动确认
    const COLLECTION_MODE_NONE = 3;     // 不采集

    /**
     * 获取采集模式映射
     */
    public static function getCollectionModes(): array
    {
        return [
            self::COLLECTION_MODE_AUTO => '自动采集',
            self::COLLECTION_MODE_MANUAL => '手动确认',
            self::COLLECTION_MODE_NONE => '不采集',
        ];
    }

    /**
     * 获取采集模式名称
     */
    public function getCollectionModeName(): string
    {
        $modes = self::getCollectionModes();
        return $modes[$this->collection_mode] ?? '未知';
    }

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联默认目录
     */
    public function defaultDirectory(): BelongsTo
    {
        return $this->belongsTo(UserGoodsDirectoryModel::class, 'default_directory_id');
    }

    /**
     * 检查是否在免提醒期内
     */
    public function isInNoRemindPeriod(): bool
    {
        if (!$this->no_remind_until) {
            return false;
        }
        
        return now()->lt($this->no_remind_until);
    }

    /**
     * 设置24小时免提醒
     */
    public function setNoRemindFor24Hours(): void
    {
        $this->update([
            'no_remind_until' => now()->addHours(24)
        ]);
    }

    /**
     * 清除免提醒设置
     */
    public function clearNoRemind(): void
    {
        $this->update([
            'no_remind_until' => null
        ]);
    }

    /**
     * 按用户ID查找设置
     */
    public static function findByUserId(int $userId): ?self
    {
        return self::where('user_id', $userId)->first();
    }

    /**
     * 检查默认目录是否有效
     */
    public function isDefaultDirectoryValid(): bool
    {
        if ($this->default_directory_id == 0) {
            return false;
        }
        return true;
        //不再进行所属关系验证
        /* return UserGoodsDirectoryModel::where('id', $this->default_directory_id)
            ->where('user_id', $this->user_id)
            ->where('status', 1)
            ->exists(); */
    }
}