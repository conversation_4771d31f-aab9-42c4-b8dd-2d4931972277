export const formatDate = (date:any)=>{
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
}

export const getMonthNum = (start:any,end:any)=>{
    let startArr = start.split('-');
    let endArr = end.split('-');
    let startYear = parseInt(startArr[0]);
    let startMonth = parseInt(startArr[1]);
    let endYear = parseInt(endArr[0]);
    let endMonth = parseInt(endArr[1]);
    let num = (endYear-startYear)*12+(endMonth-startMonth)+1;
    return num;
} 

export const compareDate = (date1: any, date2?: any) => {
    const startTime = new Date(date1);
    const endTime = date2 ? new Date(date2) : new Date();
    startTime.setHours(0, 0, 0, 0);
    endTime.setHours(0, 0, 0, 0);
    return startTime.getTime() <= endTime.getTime();
}

export const formatDateTime = (date: Date | string): string => {
    try {
        const dateObj = date instanceof Date ? date : new Date(date);
        const year = dateObj.getFullYear();
        const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
        const day = dateObj.getDate().toString().padStart(2, '0');
        const hours = dateObj.getHours().toString().padStart(2, '0');
        const minutes = dateObj.getMinutes().toString().padStart(2, '0');
        const seconds = dateObj.getSeconds().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
        return String(date);
    }
};