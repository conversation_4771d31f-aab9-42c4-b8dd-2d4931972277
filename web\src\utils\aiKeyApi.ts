/**
 * AI Key管理API接口
 * 参考directoryApi.ts的结构，通过background页面发送请求
 */
import { sendRequestViaBackground } from './api'
import { getApiUrl } from './apiConfig'

// AI Key接口定义
export interface AiKey {
  id: number
  user_id: number
  ai_key: string
  description: string
  sort_order: number
  status: number
  status_name: string
  ai_type: number
  ai_type_name: string
  created_at: string
  updated_at: string
}

// AI Key列表参数
export interface AiKeyListParams {
  page?: number
  pageSize?: number
  status?: number
  ai_type?: number
  ai_key?: string
  start_date?: string
  end_date?: string
}

// AI Key创建/更新参数
export interface AiKeyFormData {
  id?: number
  ai_key: string
  description?: string
  sort_order?: number
  status?: number
  ai_type?: number
}

// 批量更新参数
export interface AiKeyBatchUpdateData {
  ids: number[]
  status?: number
}

// API响应类型
export interface AiKeyListResponse {
  list: AiKey[]
  pagination: {
    currentPage: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
  }
}

export interface AiKeyResponse {
  id?: number
  message: string
}

// AI类型常量
export const AI_TYPES = {
  DEEPSEEK: 1
} as const

export const AI_TYPE_NAMES = {
  [AI_TYPES.DEEPSEEK]: 'DeepSeek'
} as const

/**
 * 获取AI Key列表
 */
export const getAiKeyList = async (params: AiKeyListParams): Promise<AiKeyListResponse> => {
  const url = await getApiUrl('apiAiKeyListUrl');
  console.log('获取AI Key列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getAiKeyList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 创建AI Key
 */
export const createAiKey = async (data: AiKeyFormData): Promise<AiKeyResponse> => {
  const url = await getApiUrl('apiAiKeyCreateUrl');

  return sendRequestViaBackground({
    funName: 'createAiKey',
    url,
    method: 'post',
    data,
    auth: true
  });
};

/**
 * 更新AI Key
 */
export const updateAiKey = async (data: AiKeyFormData): Promise<AiKeyResponse> => {
  const url = await getApiUrl('apiAiKeyUpdateUrl');

  return sendRequestViaBackground({
    funName: 'updateAiKey',
    url,
    method: 'post',
    data,
    auth: true
  });
};

/**
 * 删除AI Key
 */
export const deleteAiKey = async (id: number): Promise<AiKeyResponse> => {
  const url = await getApiUrl('apiAiKeyDeleteUrl');

  return sendRequestViaBackground({
    funName: 'deleteAiKey',
    url,
    method: 'post',
    data: { id },
    auth: true
  });
};

/**
 * 批量更新AI Key
 */
export const batchUpdateAiKey = async (data: AiKeyBatchUpdateData): Promise<AiKeyResponse> => {
  const url = await getApiUrl('apiAiKeyBatchUpdateUrl');

  return sendRequestViaBackground({
    funName: 'batchUpdateAiKey',
    url,
    method: 'post',
    data,
    auth: true
  });
};

/**
 * 获取AI Key详情
 */
export const getAiKeyDetail = async (id: number): Promise<AiKey> => {
  const url = await getApiUrl('apiAiKeyDetailUrl');

  return sendRequestViaBackground({
    funName: 'getAiKeyDetail',
    url,
    method: 'get',
    params: { id },
    auth: true
  });
};
