/**
 * N11 商品列表处理器 - 专门处理商品列表页面的DOM操作
 */

/**
 * 商品处理结果接口
 */
export interface ProductProcessResult {
  success: boolean;
  message: string;
  data?: any;
}

/**
 * 商品统计信息接口
 */
export interface ProductStats {
  totalProducts: number;
  currentPageSize: number;
  needsPageSizeChange: boolean;
}

/**
 * 获取第6个按钮（"Katalogdan Reddedilenler"）中的待处理商品数量
 */
export function getPendingProductCount(): ProductProcessResult {
  try {
    console.log('开始获取待处理商品数量...')
    
    // 查找tabManager容器
    const tabManager = document.querySelector('.tabManager')
    if (!tabManager) {
      return {
        success: false,
        message: '未找到 .tabManager 容器'
      }
    }

    // 获取所有按钮
    const buttons = tabManager.querySelectorAll('button.tab-btn')
    if (buttons.length < 6) {
      return {
        success: false,
        message: `按钮数量不足，当前数量: ${buttons.length}，需要至少6个`
      }
    }

    // 获取第6个按钮（索引为5）
    const sixthButton = buttons[5] as HTMLElement
    
    // 查找count元素
    const countElement = sixthButton.querySelector('.count')
    if (!countElement) {
      return {
        success: false,
        message: '在第6个按钮中未找到 .count 元素'
      }
    }

    // 提取数字
    const countText = countElement.textContent?.trim() || ''
    console.log('第6个按钮count文本:', countText)
    
    // 使用正则表达式提取数字，支持格式如 "(258)" 或 "258"
    const numberMatch = countText.match(/\((\d+)\)|\d+/)
    if (!numberMatch) {
      return {
        success: false,
        message: `无法从count文本中提取数字: ${countText}`
      }
    }

    const productCount = parseInt(numberMatch[1] || numberMatch[0])
    console.log('提取到的待处理商品数量:', productCount)

    return {
      success: true,
      message: `成功获取待处理商品数量: ${productCount}`,
      data: {
        count: productCount,
        buttonText: sixthButton.textContent?.trim(),
        countText: countText
      }
    }

  } catch (error: any) {
    console.error('获取待处理商品数量失败:', error)
    return {
      success: false,
      message: `获取待处理商品数量失败: ${error.message}`
    }
  }
}

/**
 * 获取当前页面大小设置
 */
export function getCurrentPageSize(): ProductProcessResult {
  try {
    console.log('开始获取当前页面大小设置...')
    
    // 查找页面大小选择器
    const selectContainer = document.querySelector('.simpleSelect')
    if (!selectContainer) {
      return {
        success: false,
        message: '未找到页面大小选择器 .simpleSelect'
      }
    }

    // 查找当前选中的按钮
    const selectedButton = selectContainer.querySelector('.simpleSelect-btn')
    if (!selectedButton) {
      return {
        success: false,
        message: '未找到当前选中的页面大小按钮 .simpleSelect-btn'
      }
    }

    const selectedText = selectedButton.textContent?.trim() || ''
    console.log('当前选中的页面大小文本:', selectedText)

    // 提取数字，支持格式如 "10 Ürün" 或 "100 Ürün"
    const numberMatch = selectedText.match(/(\d+)/)
    if (!numberMatch) {
      return {
        success: false,
        message: `无法从页面大小文本中提取数字: ${selectedText}`
      }
    }

    const currentPageSize = parseInt(numberMatch[1])
    console.log('当前页面大小:', currentPageSize)

    return {
      success: true,
      message: `当前页面大小: ${currentPageSize}条/页`,
      data: {
        pageSize: currentPageSize,
        text: selectedText,
        is100: currentPageSize === 100
      }
    }

  } catch (error: any) {
    console.error('获取当前页面大小失败:', error)
    return {
      success: false,
      message: `获取当前页面大小失败: ${error.message}`
    }
  }
}

/**
 * 设置页面大小为100条/页（基于debugOperations.ts的setPageSize100ViaDirect方法）
 */
export async function setPageSizeTo100(): Promise<ProductProcessResult> {
  return new Promise((resolve) => {
    try {
      console.log('开始设置页面大小为100条/页...')
      
      // 查找页面大小选择器
      const selectContainer = document.querySelector('.simpleSelect')
      if (!selectContainer) {
        resolve({
          success: false,
          message: '未找到页面大小选择器 .simpleSelect'
        })
        return
      }

      console.log('找到选择器容器:', selectContainer)

      // 查找下拉按钮
      const dropdownButton = selectContainer.querySelector('.dropdown-btn') ||
                            selectContainer.querySelector('button')
      
      if (!dropdownButton) {
        resolve({
          success: false,
          message: '未找到下拉按钮'
        })
        return
      }

      console.log('找到下拉按钮:', dropdownButton)

      // 点击下拉按钮
      const buttonElement = dropdownButton as HTMLElement
      buttonElement.click()
      console.log('已点击下拉按钮')
      
      // 等待下拉菜单出现
      setTimeout(() => {
        console.log('开始查找100选项')
        
        // 查找下拉内容容器
        const dropdownContent = selectContainer.querySelector('.dropdown-content') ||
                               selectContainer.querySelector('.simpleSelect-content')
        
        if (!dropdownContent) {
          resolve({
            success: false,
            message: '未找到下拉内容容器'
          })
          return
        }
        
        console.log('找到下拉内容容器:', dropdownContent)
        
        // 查找所有选项按钮
        const optionButtons = dropdownContent.querySelectorAll('li button[type="button"]') ||
                             dropdownContent.querySelectorAll('button')
        let option100Button: Element | null = null
        
        console.log('找到选项按钮数量:', optionButtons.length)
        
        for (const button of Array.from(optionButtons)) {
          const text = button.textContent?.trim()
          console.log('检查按钮文本:', text)
          // 匹配 "100 Ürün"
          if (text === '100 Ürün' || text === '100' || (text && text.includes('100'))) {
            option100Button = button
            console.log('找到100选项按钮:', button)
            break
          }
        }
        
        if (!option100Button) {
          // 备用查找方式
          const foundButton = Array.from(document.querySelectorAll('button')).find(btn => 
            btn.textContent?.includes('100 Ürün')
          )
          option100Button = foundButton || null
          console.log('备用查找结果:', option100Button)
        }
        
        if (option100Button) {
          console.log('准备点击100选项按钮:', option100Button)
          const buttonElement = option100Button as HTMLElement
          buttonElement.click()
        
          // 等待页面更新
          setTimeout(() => {
            console.log('页面大小设置完成')
            resolve({
              success: true,
              message: '成功设置页面大小为100条/页'
            })
          }, 1000)
        } else {
          console.log('未找到100条选项按钮')
          resolve({
            success: false,
            message: '未找到100条选项按钮'
          })
        }
      }, 500)
      
    } catch (error: any) {
      console.error('设置页面大小失败:', error)
      resolve({
        success: false,
        message: `设置页面大小失败: ${error.message}`
      })
    }
  })
}

/**
 * 获取表格中所有商品的 stock_code 值
 */
export function getAllStockCodes(): ProductProcessResult {
  try {
    console.log('开始获取表格中所有商品的 stock_code 值...')
    
    // 查找表格
    const table = document.querySelector('table.advanced-table')
    if (!table) {
      return {
        success: false,
        message: '未找到商品表格 table.advanced-table'
      }
    }

    // 查找 tbody
    const tbody = table.querySelector('tbody')
    if (!tbody) {
      return {
        success: false,
        message: '未找到表格体 tbody'
      }
    }

    // 获取所有行
    const rows = tbody.querySelectorAll('tr')
    if (rows.length === 0) {
      return {
        success: true,
        message: '表格中没有商品数据',
        data: {
          stockCodes: [],
          totalCount: 0
        }
      }
    }

    console.log(`找到 ${rows.length} 行商品数据`)

    const stockCodes: string[] = []
    
    // 遍历每一行
    rows.forEach((row, index) => {
      try {
        // 获取所有 td
        const cells = row.querySelectorAll('td')
        
        // 第4个 td（索引为3）是 stock_code 列
        if (cells.length >= 4) {
          const stockCodeCell = cells[3]
          const label = stockCodeCell.querySelector('label')
          
          if (label) {
            const stockCode = label.textContent?.trim()
            if (stockCode) {
              stockCodes.push(stockCode)
              console.log(`第${index + 1}行 stock_code:`, stockCode)
            } else {
              console.warn(`第${index + 1}行 stock_code 为空`)
            }
          } else {
            console.warn(`第${index + 1}行未找到 stock_code label`)
          }
        } else {
          console.warn(`第${index + 1}行列数不足，当前列数: ${cells.length}`)
        }
      } catch (error) {
        console.error(`处理第${index + 1}行时发生错误:`, error)
      }
    })

    console.log(`成功获取 ${stockCodes.length} 个 stock_code:`, stockCodes)

    return {
      success: true,
      message: `成功获取 ${stockCodes.length} 个商品的 stock_code`,
      data: {
        stockCodes,
        totalCount: stockCodes.length
      }
    }

  } catch (error: any) {
    console.error('获取 stock_code 列表失败:', error)
    return {
      success: false,
      message: `获取 stock_code 列表失败: ${error.message}`
    }
  }
}

/**
 * 点击全选 checkbox
 */
export function clickSelectAllCheckbox(): ProductProcessResult {
  try {
    console.log('开始点击全选 checkbox...')
    
    // 查找全选 checkbox
    const selectAllCheckbox = document.querySelector('#products-table-selectAll') as HTMLInputElement
    if (!selectAllCheckbox) {
      return {
        success: false,
        message: '未找到全选 checkbox (#products-table-selectAll)'
      }
    }

    console.log('找到全选 checkbox，准备点击')
    selectAllCheckbox.click()
    
    return {
      success: true,
      message: '成功点击全选 checkbox'
    }

  } catch (error: any) {
    console.error('点击全选 checkbox 失败:', error)
    return {
      success: false,
      message: `点击全选 checkbox 失败: ${error.message}`
    }
  }
}

/**
 * 点击批量删除按钮
 */
export async function clickBatchDeleteButton(): Promise<ProductProcessResult> {
  return new Promise((resolve) => {
    try {
      console.log('开始点击批量删除按钮...')
      
      // 查找批量操作下拉菜单
      const bulkDropdown = document.querySelector('.bulk-dropdown .dropdown-btn')
      if (!bulkDropdown) {
        resolve({
          success: false,
          message: '未找到批量操作下拉菜单按钮'
        })
        return
      }

      console.log('找到批量操作下拉菜单，准备点击')
      const dropdownButton = bulkDropdown as HTMLElement
      dropdownButton.click()

      // 等待下拉菜单出现
      setTimeout(() => {
        // 查找删除按钮
        const deleteButton = document.querySelector('.dropdownList-menu-item') as HTMLElement
        let foundDeleteButton: HTMLElement | null = null

        // 遍历所有菜单项，找到"Sil"按钮
        const menuItems = document.querySelectorAll('.dropdownList-menu-item')
        for (const item of Array.from(menuItems)) {
          const text = item.textContent?.trim()
          if (text === 'Sil') {
            foundDeleteButton = item as HTMLElement
            break
          }
        }

        if (!foundDeleteButton) {
          resolve({
            success: false,
            message: '未找到删除按钮 (Sil)'
          })
          return
        }

        console.log('找到删除按钮，准备点击')
        foundDeleteButton.click()

        resolve({
          success: true,
          message: '成功点击批量删除按钮'
        })

      }, 500)

    } catch (error: any) {
      console.error('点击批量删除按钮失败:', error)
      resolve({
        success: false,
        message: `点击批量删除按钮失败: ${error.message}`
      })
    }
  })
}

/**
 * 确认删除操作
 */
export async function confirmDelete(): Promise<ProductProcessResult> {

  return new Promise((resolve) => {
    try {
      console.log('开始确认删除操作...')
      // 等待确认对话框出现
      setTimeout(() => {
        // 查找确认删除按钮
        // 测试环境使用取消按钮 并不真正确认删除
        //const confirmButton = document.querySelector('#negative-button') as HTMLElement
        const confirmButton = document.querySelector('#positive-button') as HTMLElement
        if (!confirmButton) {
          resolve({
            success: false,
            message: '未找到确认删除按钮 (#positive-button)'
          })
          return
        }

        console.log('找到确认删除按钮，准备点击')
        confirmButton.click()

        resolve({
          success: true,
          message: '成功确认删除操作'
        })

      }, 1000) // 等待1秒确保对话框完全加载

    } catch (error: any) {
      console.error('确认删除操作失败:', error)
      resolve({
        success: false,
        message: `确认删除操作失败: ${error.message}`
      })
    }
  })
}

/**
 * 检查表格是否还有数据
 */
export function hasTableData(): boolean {
  try {
    const table = document.querySelector('table.advanced-table')
    if (!table) return false

    const tbody = table.querySelector('tbody')
    if (!tbody) return false

    const rows = tbody.querySelectorAll('tr')
    return rows.length > 0
  } catch (error) {
    console.error('检查表格数据时发生错误:', error)
    return false
  }
}

/**
 * 执行完整的删除流程（全选 → 删除 → 确认）
 */
export async function executeDeleteFlow(): Promise<ProductProcessResult> {
  try {
    console.log('开始执行删除流程...')

    // 1. 点击全选
    const selectAllResult = clickSelectAllCheckbox()
    if (!selectAllResult.success) {
      return selectAllResult
    }

    // 等待选择状态更新
    await new Promise(resolve => setTimeout(resolve, 500))

    // 2. 点击批量删除
    const deleteResult = await clickBatchDeleteButton()
    if (!deleteResult.success) {
      return deleteResult
    }

    // 等待确认对话框出现
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 3. 确认删除
    const confirmResult = await confirmDelete()
    if (!confirmResult.success) {
      return confirmResult
    }

    // 等待删除操作完成和页面重新加载
    await new Promise(resolve => setTimeout(resolve, 10000))

    return {
      success: true,
      message: '删除流程执行完成'
    }

  } catch (error: any) {
    console.error('执行删除流程失败:', error)
    return {
      success: false,
      message: `执行删除流程失败: ${error.message}`
    }
  }
}

/**
 * 获取商品统计信息并检查是否需要调整页面大小
 */
export async function getProductStatsAndCheckPageSize(): Promise<ProductProcessResult> {
  try {
    console.log('开始获取商品统计信息并检查页面大小...')

    // 1. 获取待处理商品数量
    const countResult = getPendingProductCount()
    if (!countResult.success) {
      return countResult
    }

    // 2. 获取当前页面大小
    const pageSizeResult = getCurrentPageSize()
    if (!pageSizeResult.success) {
      return pageSizeResult
    }

    const totalProducts = countResult.data.count
    const currentPageSize = pageSizeResult.data.pageSize
    const needsPageSizeChange = currentPageSize !== 100

    console.log('商品统计信息:', {
      totalProducts,
      currentPageSize,
      needsPageSizeChange
    })

    // 3. 如果需要，自动设置页面大小为100
    if (needsPageSizeChange) {
      console.log('当前页面大小不是100条，开始自动调整...')
      const setPageSizeResult = await setPageSizeTo100()
      
      if (!setPageSizeResult.success) {
        return {
          success: false,
          message: `页面大小调整失败: ${setPageSizeResult.message}`,
          data: {
            totalProducts,
            currentPageSize,
            needsPageSizeChange: true,
            pageSizeChangeAttempted: true,
            pageSizeChangeSuccess: false
          }
        }
      }

      // 重新获取页面大小确认
      await new Promise(resolve => setTimeout(resolve, 1500))
      const newPageSizeResult = getCurrentPageSize()
      const finalPageSize = newPageSizeResult.success ? newPageSizeResult.data.pageSize : currentPageSize

      return {
        success: true,
        message: `商品统计完成。待处理商品: ${totalProducts}，页面大小已调整: ${currentPageSize} → ${finalPageSize}`,
        data: {
          totalProducts,
          currentPageSize,
          finalPageSize,
          needsPageSizeChange: false,
          pageSizeChangeAttempted: true,
          pageSizeChangeSuccess: finalPageSize === 100
        }
      }
    } else {
      return {
        success: true,
        message: `商品统计完成。待处理商品: ${totalProducts}，当前页面大小: ${currentPageSize}条/页（无需调整）`,
        data: {
          totalProducts,
          currentPageSize,
          needsPageSizeChange: false,
          pageSizeChangeAttempted: false,
          pageSizeChangeSuccess: true
        }
      }
    }

  } catch (error: any) {
    console.error('获取商品统计信息失败:', error)
    return {
      success: false,
      message: `获取商品统计信息失败: ${error.message}`
    }
  }
} 