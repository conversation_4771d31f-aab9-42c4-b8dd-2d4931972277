/**
 * 任务管理API接口
 */
import { sendRequestViaBackground } from './api'
import { getApiUrl as getConfigApiUrl } from './apiConfig'

// 任务接口类型定义
export interface TaskParams {
  timeRange?: string
  customDateRange?: string[]
  sortOrder?: string
  selectedStores: number[]
  executeTime: string
  selected_ids?: number[]
  directory_id?: number
  account_setting_type?: number
  price_rate?: number
  price_add?: number
  price_subtract?: number
  quantity?: number
  vat_rate?: number
  preparing_day?: number
}

// 任务列表查询参数
export interface TaskListParams {
  page?: number
  pageSize?: number
  task_over?: number | null | undefined
  day_start?: string
  day_end?: string
  has_rejected?: boolean
}

// 任务类型定义
export interface Task {
  id: number
  user_id: number
  is_selected: number
  selected_ids: number[]
  time_range: string
  day_start: string
  day_end: string
  sort_order: string
  execute_type: string
  user_account_id: number
  store_name: string
  store_logo: string
  task_count: number
  task_num: number
  goods_count: number
  sku_count: number
  rejected_count: number
  latest_goods_id: number
  latest_time: string | null
  task_over: number
  goods_ai_name_status: number // AI标题完成状态 (0: 未完成, 1: 已完成)
  latest_ai_goods_id: number // 最新的AI商品ID
  memo: string | null
  created_at: string
  updated_at: string
  directory_name?: string | null
  // 新增字段
  price_min?: string // 最低价格
  price_max?: string // 最高价格
  currentcy?: string // 发布货币单位（如 "$"）
  exchange_rate_currency?: string // 汇率货币单位（如 "TL"）
  price_rate?: string // 价格倍率
  price_add?: string // 价格增加值
  price_subtract?: string // 价格减少值
}

// 开始任务响应类型定义
export interface TaskStartResponse {
  task_over: number // 0: 未完成, 1: 已完成
  task_exist: number // 0: 不存在, 1: 已存在
  goods_no_cat_relation?: number

  // 当task_over != 1时，包含以下字段
  id?: number // task_detail_id
  task_id?: number // 主任务ID
  user_id?: number
  user_account_id?: number
  user_goods_id?: number
  user_goods_sku_id?: number
  goods_id?: number
  thumb_url?: string
  images?: Array<{
    url: string
    order: number
  }>
  currentcy?: string
  currentcy_goods?: string
  price?: string
  price_third?: string
  price_list_third?: string
  spec_key_values?: string
  is_skc_gallery?: number
  category_id?: number
  product_main_id?: string
  stock_code?: string
  status?: number
  goods_info?: {
    id: number
    goods_name: string
    goods_property: string
    front_cat_id_2: number
  }
  sku_info?: {
    id: number
    sku_id: number
    url: string
    skc_gallery: string
  }
  store_info?: {
    id: number
    account_name: string
    account_type: number
    brand: string
    price_rate: number
    shipment_template: string
    quantity: number
    vat_rate: number
    preparing_day: number
    integrator_name: string
    app_key: string
    app_secret: string
  }
  created_at?: string
  updated_at?: string
  task_count?: number
  task_num?: number

  // 原有字段（用于兼容）
  goods_name?: string
  store_name?: string
  goods_sku_name?: string
  message?: string
}

// 任务更新参数接口
export interface TaskUpdateParams {
  task_detail_id?: number
  task_id: number
  third_task_id?: number
  third_type?: string
  third_status?: string
  third_result?: string
  task_over?: number
  task_num?: number
}

// 任务详情查询参数
export interface TaskDetailListParams {
  task_id: number
  page?: number
  pageSize?: number
  status?: number | null
  goods_name?: string
  created_at_start?: string
  created_at_end?: string
  task_detail_id?: number
}

// 任务详情项接口
export interface TaskDetail {
  id: number
  goods_name: string
  thumb_url: string
  spec_key_values: string
  spec_values: string
  price: number
  price_third: number
  price_list_third: number
  currentcy: string
  quantity: number
  vat_rate: number
  preparing_day: number
  category_id: number
  n11_category?: {
    id: number
    name: string
    name_tl: string
    path_name: string
    path_name_tl: string
    level: number
    is_leaf: boolean
  }
  product_main_id: string
  stock_code: string
  status: number
  status_text: string
  status_type: string
  third_task_id: number
  third_type: string
  third_status: string
  third_result: string
  memo: string
  store_name: string
  goods_info?: {
    id: number
    goods_name: string
    source_url: string
  }
  upload_params?: {
    id: number
    third_type: string
    params: string
  }
  created_at: string
  updated_at: string
}

// 任务详情响应接口
export interface TaskDetailResponse {
  task_info: Task
  statistics: {
    total_count: number
    success_count: number
    failed_count: number
    pending_count: number
    processing_count: number
    success_rate: number
  }
}

/**
 * 获取配置中的API地址
 * @param configKey 配置键名
 * @returns Promise<string> API地址
 */
const getApiUrl = (configKey: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey
    }, (response) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      if (response?.url) {
        resolve(response.url);
      } else {
        resolve('');
      }
    });
  });
};

/**
 * 添加任务
 * @param params 任务参数
 * @returns 任务创建结果
 */
export const addTask = async (params: TaskParams): Promise<any> => {
  const url = await getApiUrl('apiTaskAddUrl');
  console.log('添加任务URL:', url);

  return sendRequestViaBackground({
    funName: 'addTask',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};

/**
 * 获取任务列表
 * @param params 查询参数
 * @returns 任务列表数据
 */
export const getTaskList = async (params: TaskListParams): Promise<any> => {
  const url = await getApiUrl('apiTaskListUrl');
  console.log('获取任务列表URL:', url);

  return sendRequestViaBackground({
    funName: 'getTaskList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 开始任务
 * @param taskId 任务ID
 * @returns 任务开始结果
 */
export const startTask = async (taskId: number): Promise<TaskStartResponse> => {
  const url = await getApiUrl('apiTaskStartUrl');
  console.log('开始任务URL:', url);

  return sendRequestViaBackground({
    funName: 'startTask',
    url,
    method: 'get',
    params: { task_id: taskId },
    auth: true
  });
};

/**
 * 更新任务详情状态
 * @param params 更新参数
 * @returns 更新结果
 */
export const updateTask = async (params: TaskUpdateParams): Promise<any> => {
  const url = await getApiUrl('apiTaskUpdateUrl');
  console.log('更新任务URL:', url);
  console.log('更新任务参数:', params);

  return sendRequestViaBackground({
    funName: 'updateTask',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};

/**
 * 获取任务详情
 * @param taskId 任务ID
 * @returns 任务详情数据
 */
export const getTaskDetail = async (taskId: number): Promise<TaskDetailResponse> => {
  const url = await getConfigApiUrl('apiTaskDetailUrl');
  console.log('获取任务详情URL:', url);

  return sendRequestViaBackground({
    funName: 'getTaskDetail',
    url,
    method: 'get',
    params: { task_id: taskId },
    auth: true
  });
};

/**
 * 获取任务详情列表
 * @param params 查询参数
 * @returns 任务详情列表数据
 */
export const getTaskDetailList = async (params: TaskDetailListParams): Promise<any> => {
  const url = await getConfigApiUrl('apiTaskDetailListUrl');
  console.log('获取任务详情列表URL:', url);

  return sendRequestViaBackground({
    funName: 'getTaskDetailList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 查询任务结果
 * @param taskId 任务ID
 * @returns 查询结果
 */
export const queryTaskResults = async (taskId: number): Promise<any> => {
  const url = await getConfigApiUrl('apiTaskQueryResultsUrl');
  console.log('查询任务结果URL:', url);

  return sendRequestViaBackground({
    funName: 'queryTaskResults',
    url,
    method: 'post',
    data: { task_id: taskId },
    auth: true
  });
};

/**
 * 获取待查询结果的任务列表
 * @param taskId 任务ID
 * @param limit 每次获取的数量限制
 * @param offset 偏移量
 * @returns 待查询任务列表
 */
export const getPendingQueryTasks = async (taskId: number, limit: number = 500, offset: number = 0): Promise<any> => {
  const url = await getConfigApiUrl('apiTaskPendingQueryUrl');
  console.log('获取待查询任务列表URL:', url);

  return sendRequestViaBackground({
    funName: 'getPendingQueryTasks',
    url,
    method: 'get',
    params: { task_id: taskId, limit, offset },
    auth: true
  });
};

/**
 * 批量更新任务详情状态
 * @param updates 更新数据数组
 * @returns 更新结果
 */
export const batchUpdateTaskDetails = async (updates: Array<{
  detail_id: number
  status: number
  third_status: string
  third_result: string
  memo: string
}>): Promise<any> => {
  const url = await getConfigApiUrl('apiTaskBatchUpdateUrl');
  console.log('批量更新任务状态URL:', url);

  return sendRequestViaBackground({
    funName: 'batchUpdateTaskDetails',
    url,
    method: 'post',
    data: { updates },
    auth: true
  });
};

/**
 * 保存上传参数
 * @param taskDetailId 任务详情ID
 * @param thirdType 第三方类型
 * @param params 参数JSON字符串
 * @returns 保存结果
 */
export const saveUploadParams = async (taskDetailId: number, thirdType: string, params: string): Promise<any> => {
  const url = await getConfigApiUrl('apiTaskSaveUploadParamsUrl');
  console.log('保存上传参数URL:', url);

  return sendRequestViaBackground({
    funName: 'saveUploadParams',
    url,
    method: 'post',
    data: {
      task_detail_id: taskDetailId,
      third_type: thirdType,
      params: params
    },
    auth: true
  });
};

/**
 * 获取重新上传参数
 * @param taskDetailId 任务详情ID
 * @returns 重新上传参数
 */
export const getRetryUploadParams = async (taskDetailId: number): Promise<TaskStartResponse> => {
  const url = await getConfigApiUrl('apiTaskRetryUploadParamsUrl');
  console.log('获取重新上传参数URL:', url);

  return sendRequestViaBackground({
    funName: 'getRetryUploadParams',
    url,
    method: 'get',
    params: { task_detail_id: taskDetailId },
    auth: true
  });
};

/**
 * 批量重新上传失败任务
 * @param taskId 任务ID
 * @param detailIds 任务详情ID数组，为空则处理所有失败任务
 * @returns 重新上传结果
 */
export const batchRetryUpload = async (taskId: number, detailIds: number[] = []): Promise<any> => {
  const url = await getConfigApiUrl('apiTaskBatchRetryUploadUrl');
  console.log('批量重新上传URL:', url);

  return sendRequestViaBackground({
    funName: 'batchRetryUpload',
    url,
    method: 'post',
    data: {
      task_id: taskId,
      detail_ids: detailIds
    },
    auth: true
  });
};

/**
 * 按状态批量重新上传任务
 * @param taskId 任务ID
 * @param statusList 状态数组，如 [3] 表示失败，[6] 表示审核未通过，[3,6] 表示失败和审核未通过
 * @param detailIds 任务详情ID数组，为空则处理所有指定状态的任务
 * @returns 重新上传结果
 */
export const batchRetryUploadByStatus = async (taskId: number, statusList: number[], detailIds: number[] = []): Promise<any> => {
  const url = await getConfigApiUrl('apiTaskBatchRetryUploadByStatusUrl');
  console.log('按状态批量重新上传URL:', url);

  return sendRequestViaBackground({
    funName: 'batchRetryUploadByStatus',
    url,
    method: 'post',
    data: {
      task_id: taskId,
      status_list: statusList,
      detail_ids: detailIds
    },
    auth: true
  });
};
