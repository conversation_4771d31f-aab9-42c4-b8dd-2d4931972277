<?php
namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\UserGoodsDirectoryService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserGoodsDirectoryController extends Controller
{
    protected UserGoodsDirectoryService $directoryService;

    public function __construct(UserGoodsDirectoryService $directoryService)
    {
        $this->directoryService = $directoryService;
        parent::__construct();
    }

    /**
     * 获取目录列表（分页）
     */
    public function list(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $userPid = $user['pid'];
        
        $params = $request->only(['page', 'pageSize', 'status', 'name', 'start_date', 'end_date', 'directory_type', 'creator_name', 'creator_phone', 'status_sub']);
        $result = $this->directoryService->getDirectoryList($userId, $userPid, $params);
        
        return $this->apiSuccess($result);
    }

    /**
     * 创建目录
     */
    public function create(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $userPid = $user['pid'];
        $data = $request->only([
            'name', 'description', 'sort_order', 'status', 'status_sub'
        ]);
        
        // 验证目录名称不能包含连字符
        if (isset($data['name']) && strpos($data['name'], '-') !== false) {
            return $this->apiError('目录名称不能包含连字符"-"');
        }
        
        $result = $this->directoryService->createDirectory($userId, $userPid, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 更新目录
     */
    public function update(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $userPid = $user['pid'];
        $data = $request->only([
            'id', 'name', 'description', 'sort_order', 'status', 'status_sub'
        ]);
        
        // 验证目录名称不能包含连字符
        if (isset($data['name']) && strpos($data['name'], '-') !== false) {
            return $this->apiError('目录名称不能包含连字符"-"');
        }
        
        $result = $this->directoryService->updateDirectory($userId, $userPid, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 批量更新目录
     */
    public function batchUpdate(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $userPid = $user['pid'];
        
        $data = $request->only(['ids', 'status']);
        $result = $this->directoryService->batchUpdateDirectory($userId, $userPid, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 删除目录
     */
    public function delete(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $userPid = $user['pid'];
        $directoryId = (int)$request->input('id');
        $result = $this->directoryService->deleteDirectory($userId, $userPid, $directoryId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取目录详情
     */
    public function detail(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $userPid = $user['pid'];
        
        $directoryId = (int)$request->input('id');
        $result = $this->directoryService->getDirectoryDetail($userId, $userPid, $directoryId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 更新目录商品数量
     */
    public function updateGoodsCount(Request $request): JsonResponse
    {
        $directoryId = (int)$request->input('directory_id');
        $this->directoryService->updateDirectoryGoodsCount($directoryId);
        
        return $this->apiSuccess(['message' => '商品数量更新成功']);
    }

    /**
     * 批量更新所有目录的商品数量
     */
    public function updateAllGoodsCount(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $this->directoryService->updateAllDirectoryGoodsCount($userId);
        
        return $this->apiSuccess(['message' => '所有目录商品数量更新成功']);
    }
}