/**
 * Tab管理器 - 处理Tab页面的创建、关闭和消息通信
 */

// 声明 Chrome API
declare const chrome: any;

export interface TabInfo {
  id: number;
  url: string;
  status: 'loading' | 'complete' | 'error';
}

export interface TabMessage {
  type: string;
  data?: any;
  error?: string;
}

/**
 * 检查Chrome API是否可用
 * @returns boolean
 */
const isChromeAPIAvailable = (): boolean => {
  return typeof chrome !== 'undefined' &&
         chrome.runtime &&
         chrome.tabs &&
         typeof chrome.tabs.create === 'function';
};

/**
 * 通过消息传递调用background script的API
 * @param action 操作类型
 * @param params 参数
 * @returns Promise<any>
 */
const callBackgroundAPI = async (action: string, params: any = {}): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      reject(new Error('Chrome runtime API不可用'));
      return;
    }

    chrome.runtime.sendMessage({
      type: action,
      ...params
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
        return;
      }

      if (response && response.success) {
        resolve(response.data || response);
      } else {
        reject(new Error(response?.error || response?.message || 'API调用失败'));
      }
    });
  });
};

/**
 * 创建新Tab页面
 * @param url 要打开的URL
 * @returns Promise<TabInfo> Tab信息
 */
export const createTab = async (url: string): Promise<TabInfo> => {
  // 检查Chrome API是否可用
  if (!isChromeAPIAvailable()) {
    // 如果Chrome API不可用，尝试通过消息传递调用background script
    try {
      const result = await callBackgroundAPI('createTab', { url });
      return result;
    } catch (error) {
      throw new Error(`创建Tab失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  return new Promise((resolve, reject) => {
    try {
      chrome.tabs.create({ url }, (tab: any) => {
        if (chrome.runtime.lastError) {
          reject(new Error(`创建Tab失败: ${chrome.runtime.lastError.message}`));
          return;
        }

        if (!tab || !tab.id) {
          reject(new Error('创建Tab失败: 未获取到有效的Tab信息'));
          return;
        }

        resolve({
          id: tab.id,
          url: tab.url || url,
          status: 'loading'
        });
      });
    } catch (error) {
      reject(new Error(`创建Tab时发生异常: ${error instanceof Error ? error.message : String(error)}`));
    }
  });
};

/**
 * 关闭Tab页面
 * @param tabId Tab ID
 * @returns Promise<void>
 */
export const closeTab = async (tabId: number): Promise<void> => {
  // 检查Chrome API是否可用
  if (!isChromeAPIAvailable()) {
    // 如果Chrome API不可用，尝试通过消息传递调用background script
    try {
      await callBackgroundAPI('closeTab', { tabId });
      return;
    } catch (error) {
      throw new Error(`关闭Tab失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  return new Promise((resolve, reject) => {
    try {
      chrome.tabs.remove(tabId, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(`关闭Tab失败: ${chrome.runtime.lastError.message}`));
          return;
        }
        resolve();
      });
    } catch (error) {
      reject(new Error(`关闭Tab时发生异常: ${error instanceof Error ? error.message : String(error)}`));
    }
  });
};

/**
 * 向Tab页面发送消息
 * @param tabId Tab ID
 * @param message 消息内容
 * @returns Promise<any> 响应数据
 */
export const sendMessageToTab = async (tabId: number, message: TabMessage): Promise<any> => {
  // 检查Chrome API是否可用
  if (!isChromeAPIAvailable()) {
    throw new Error('Chrome API不可用，请确保在Chrome扩展环境中运行');
  }

  return new Promise((resolve, reject) => {
    try {
      chrome.tabs.sendMessage(tabId, message, (response: any) => {
        if (chrome.runtime.lastError) {
          reject(new Error(`发送消息失败: ${chrome.runtime.lastError.message}`));
          return;
        }
        resolve(response);
      });
    } catch (error) {
      reject(new Error(`发送消息时发生异常: ${error instanceof Error ? error.message : String(error)}`));
    }
  });
};

/**
 * 等待Tab页面加载完成
 * @param tabId Tab ID
 * @param timeout 超时时间（毫秒）
 * @returns Promise<void>
 */
export const waitForTabLoad = async (tabId: number, timeout: number = 30000): Promise<void> => {
  // 检查Chrome API是否可用
  if (!isChromeAPIAvailable()) {
    // 如果Chrome API不可用，尝试通过消息传递调用background script
    try {
      await callBackgroundAPI('waitForTabLoad', { tabId, timeout });
      return;
    } catch (error) {
      throw new Error(`等待Tab加载失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      reject(new Error('Tab加载超时'));
    }, timeout);

    const checkTabStatus = () => {
      try {
        chrome.tabs.get(tabId, (tab: any) => {
          if (chrome.runtime.lastError) {
            clearTimeout(timer);
            reject(new Error(`检查Tab状态失败: ${chrome.runtime.lastError.message}`));
            return;
          }

          if (tab.status === 'complete') {
            clearTimeout(timer);
            resolve();
          } else {
            // 继续等待
            setTimeout(checkTabStatus, 100);
          }
        });
      } catch (error) {
        clearTimeout(timer);
        reject(new Error(`检查Tab状态时发生异常: ${error instanceof Error ? error.message : String(error)}`));
      }
    };

    checkTabStatus();
  });
};

/**
 * 获取Tab页面信息
 * @param tabId Tab ID
 * @returns Promise<TabInfo> Tab信息
 */
export const getTabInfo = async (tabId: number): Promise<TabInfo> => {
  // 检查Chrome API是否可用
  if (!isChromeAPIAvailable()) {
    throw new Error('Chrome API不可用，请确保在Chrome扩展环境中运行');
  }

  return new Promise((resolve, reject) => {
    try {
      chrome.tabs.get(tabId, (tab: any) => {
        if (chrome.runtime.lastError) {
          reject(new Error(`获取Tab信息失败: ${chrome.runtime.lastError.message}`));
          return;
        }

        if (!tab) {
          reject(new Error('Tab不存在'));
          return;
        }

        resolve({
          id: tab.id,
          url: tab.url,
          status: tab.status || 'loading'
        });
      });
    } catch (error) {
      reject(new Error(`获取Tab信息时发生异常: ${error instanceof Error ? error.message : String(error)}`));
    }
  });
};
