<?php
namespace App\Service\TeMu;

use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\User\UserGoodsDirectoryModel;
use App\Models\User\GoodsInstructionImagesModel;
use App\Models\User\GoodsPriceAdjustmentLogModel;


class GoodsService extends BaseService{

    public function getGoodsType($host){
        $host = strtolower($host);
        $host_parts = explode('.', $host);
        if (count($host_parts) > 1) {
            $main_domain = implode('.', array_slice($host_parts, -2));
        } else {
            $main_domain = $host;
        }
        $goodsType = [
            'temu.com' => 1,
        ];
        if(isset($goodsType[$main_domain])){
            return $goodsType[$main_domain];
        }

        return 0;
    }    
    
    public function getGoodsList(){

    }

    /**
     * 检查商品ID是否存在
     * @param string|int $goodsId 商品ID
     * @param int $userId 用户ID（主账号ID）
     * @return bool 商品是否存在
     */
    public function checkGoodsExists($goodsId, int $userId, int $currentUserId): bool
    {
        if (empty($goodsId)) {
            return false;
        }

        $goods = GoodsModel::where([
            'user_id' => $userId,
            'goods_id' => $goodsId,
            'type' => GoodsModel::TYPE_TEMU,
            'status' => 1
        ])->select(['user_id', 'user_sub_id'])->first();

        if (!$goods) {
            return false;
        }
        return true;
    }

    /**
     * 批量检查商品ID是否存在
     * @param array $goodsIds 商品ID数组
     * @param int $userId 用户ID（主账号ID）
     * @param int $currentUserId 当前用户ID
     * @return array 商品ID存在性映射数组
     */
    public function batchCheckGoodsExists(array $goodsIds, int $userId, int $currentUserId): array
    {
        if (empty($goodsIds)) {
            return [];
        }

        // 限制最大检查数量
        $goodsIds = array_slice($goodsIds, 0, 100);
        
        // 查询存在的商品ID
        $existingGoods = GoodsModel::where([
            'user_id' => $userId,
            'type' => GoodsModel::TYPE_TEMU,
            'status' => 1
        ])
        ->whereIn('goods_id', $goodsIds)
        ->pluck('goods_id')
        ->toArray();

        // 构建返回映射
        $result = [];
        foreach ($goodsIds as $goodsId) {
            $result[$goodsId] = in_array($goodsId, $existingGoods);
        }

        return $result;
    }

    public function saveGoodsData(array $data)
    {
        if(empty($data['goodsName'])){
            throw new MyException("商品名称不能为空");
        }

        if(empty($data['source_url'])){
            throw new MyException("商品来源链接不能为空");
        }

        $url = parse_url($data['source_url']);
        if(empty($url['host'])){
            throw new MyException("商品来源链接格式不正确");
        }

        $type = $this->getGoodsType($url['host']);

        if(empty($data['optIdNameMap']) || !is_array($data['optIdNameMap']) || count($data['optIdNameMap']) < 2){
            throw new MyException("未获取到商品详情页分类数据");
        }

        $front_cat_id_1 = array_keys($data['optIdNameMap'])[0];
        $front_cat_id_2 = array_keys($data['optIdNameMap'])[1];
        $front_cat_desc = $data['optNameString'] ?? '';

        $mall_id = $data['mallId'] ?? 0;
        $goods_id = $data['goodsId'] ?? 0;
        $goods_name = $data['goodsName'] ?? '';
        $goods_detail = '';

        if(isset($data['goodsDetail']) && !empty($data['goodsDetail'])){
            $goods_detail =array_column($data['goodsDetail'], 'url');
            $goods_detail = json_encode($goods_detail,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
        }

        $goods_video = '';

        if($data['video']){
            $goods_video = $data['video']['videoUrl'] ?? '';
        }

        $goods_pic = '';
        $goods_pic_urls = [];
        if (isset($data['pics']) && is_array($data['pics']) && count($data['pics']) > 0) {
            $goods_pic_urls = array_column($data['pics'], 'url');
            $goods_pic = json_encode($goods_pic_urls,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES); 
        }

        $goods_instruction_images = '';
        if(isset($data['instructionImages']) && is_array($data['instructionImages']) && count($data['instructionImages']) > 0){
            $goods_instruction_images = json_encode($data['instructionImages'],JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
        }

        $goods_property = '';
        if(isset($data['goodsProperty']) && is_array($data['goodsProperty'])){
            $properties = $data['goodsProperty'];
            $property_text = [];
            $filtered_properties = [];
            
            // 遍历数组找到 floorListProperties 并过滤掉它
            foreach ($properties as $property) {
                if (isset($property['key']) && $property['key'] === 'floorListProperties') {
                    $property_text = $property['values'] ?? [];
                } else {
                    $filtered_properties[] = $property;
                }
            }
            
            $count = count($filtered_properties);
            for ($i = 0; $i < $count; $i++) {
                if ($count > 1 && $i === $count - 2) {
                    continue;
                }
                
                $property = $filtered_properties[$i];
                
                $values_string = '';
                if (isset($property['values'])) {
                    if (is_array($property['values']) && count($property['values']) > 1) {
                        $values_string = implode(',', $property['values']);
                    } elseif (isset($property['values'][0])) {
                        $values_string = (string) $property['values'][0];
                    }
                }
                
                if (!empty($values_string)) {
                     $goods_property .= $property['key'] . ':' . $values_string . ',';
                }
            }
            
            $goods_property = rtrim($goods_property, ',');
            
            // 处理 floorListProperties 属性，将其以换行符拼接后追加到 goods_property
            if (!empty($property_text) && is_array($property_text)) {
                $str1 = implode("\n", $property_text);
                if (!empty($str1)) {
                    $goods_property .= "\n" . $str1;
                }
            }
        }

        $goods_sku_num = 0;
        if(isset($data['sku']) && is_array($data['sku'])){
            $goods_sku_num = count($data['sku']);
        }

        $goods_sold_quantity = $data['goodsSoldQuantity'] ?? 0;
        $goods_score = $data['goodsScore'] ?? 0;
        $goods_pdf   = $data['goodsPdf'] ?? '';
        
        // 验证PDF链接格式
        if (!empty($goods_pdf)) {
            // 验证是否为有效的PDF链接（包含.pdf，不区分大小写）
            if (!preg_match('/\.pdf(?:\?.*)?$/i', $goods_pdf) || !filter_var($goods_pdf, FILTER_VALIDATE_URL)) {
                $goods_pdf = ''; // 如果不是有效的PDF链接，则设置为空
            }
        }

        if($data['user_pid'] > 0){
            $user_id      = $data['user_pid'];
            $user_sub_id  = $data['user_id'];
        }else{
            $user_id      = $data['user_id'];
            $user_sub_id  = 0;
        }

        $goods_data = [
            'type' => $type,
            'user_id' => $user_id,
            'user_sub_id' => $user_sub_id,
            'directory_id' => $data['directory_id'] ?? 0, // 新增目录ID支持
            'source_url' => $data['source_url'],
            'cat_id' => $data['catId'],
            'front_cat_id_1' => $front_cat_id_1,
            'front_cat_id_2' => $front_cat_id_2,
            'front_cat_desc' => $front_cat_desc,
            'mall_id' => $mall_id,
            'goods_id' => $goods_id,
            'goods_name' => $goods_name,
            'goods_detail' => $goods_detail,
            'goods_video' => $goods_video,
            'goods_pic' => $goods_pic,
            'goods_sku' => '',
            'goods_sku_num' => $goods_sku_num,
            'goods_property' => $goods_property,
            'goods_sold_quantity' => $goods_sold_quantity,
            'goods_score' => $goods_score,
            'img_local_status' => 0,
            'img_local_error'  => 0,
            'goods_pdf' => $goods_pdf,
            'status' => 1,
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        DB::beginTransaction();
        try{
            // 修改商品存在性检查，
            // 主账号下 只能有一个goods_id商品  有一种情况 需要注意 就是原商品已经删除的情形
            $goodsModel = GoodsModel::where([
                'user_id' => $user_id, 
                'goods_id' => $goods_id, 
                'type' => $type,
            ])->first();

            $oldDirectoryId = null; // 记录旧的目录ID，用于更新商品数量
            $isNewGoods = false; // 是否为新商品

            if ($goodsModel) {
                if($goodsModel->status == 1){
                    //商品存在的情况 不更新商品原所属的主账号和子账号信息及目录信息。
                    unset($goods_data['user_id']);
                    unset($goods_data['user_sub_id']);
                    unset($goods_data['directory_id']);
                }else{
                    //如果原先商品已经删除了  则需要更新商品的主账号和子账号信息及目录信息。
                    //同时创建时间需要更新 方便统计
                    $goods_data['created_at'] = date('Y-m-d H:i:s');
                    $oldDirectoryId = $goodsModel->directory_id;
                }
                
                // 使用原生查询更新，避免模型的类型转换影响JSON格式
                DB::table('user_goods')
                    ->where('id', $goodsModel->id)
                    ->update($goods_data);
                $user_goods_id = $goodsModel->id;
            } else {
                $goods_data['created_at'] = date('Y-m-d H:i:s');
                $user_goods_id = GoodsModel::insertGetId($goods_data);
                $isNewGoods = true;
            }

            if($user_goods_id <= 0){
                if (!$user_goods_id) {
                    throw new MyException("商品保存或更新失败");
                }
            }

            if (isset($data['sku']) && is_array($data['sku'])) {
                foreach($data['sku'] as $sku){
                    $sku['currency'] = $sku['currency'] ?? '';
                    if(!empty($sku['currency'])){
                        $sku['currency'] = strtoupper(trim($sku['currency']));
                    }
                    if($sku['currency'] == 'TL'){
                        $price_str = str_replace('.', '', $sku['price']);
                        $price_float = (float)str_replace(',', '.', $price_str);
                    }else{
                        $price_float = $sku['price'] ?? 0;
                    }

                    if(floatval($price_float) <= 0){
                        continue;
                    }

                    $sku_data = [
                        'user_goods_id' => $user_goods_id,
                        'sku_id' => $sku['skuId'] ?? 0,
                        'goods_id' => $sku['goodsId'] ?? 0,
                        'thumb_url' => !empty($sku['thumbUrl']) ? trim($sku['thumbUrl']) : '',
                        'currentcy' => !empty($sku['currency']) ? trim($sku['currency']) : '',
                        'price' => $price_float,
                        'spec_key_values' => !empty($sku['specKeyValues']) ? trim($sku['specKeyValues']) : '',
                        'spec_values' => !empty($sku['specValues']) ? trim($sku['specValues']) : '',
                        'skc_gallery' => !empty($sku['skcGallery']) ? json_encode($sku['skcGallery'], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES) : '',
                        'is_skc_gallery' => $sku['isSkcGallery'] ?? 0,
                        'url' => !empty($sku['url']) ? trim($sku['url']) : '',
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];

                    $goodsSkuModel = GoodsSkuModel::where(['goods_id' => $sku_data['goods_id'], 'sku_id' => $sku_data['sku_id']])->first();

                    if ($goodsSkuModel) {
                        // 使用原生查询更新，避免模型的类型转换影响JSON格式 已经存在的SKU商品 不再更新价格和币种
                        
                        // 检查SKU是否已经调整过价格
                        if ($this->hasSkuPriceBeenAdjusted($data['user_id'],$goodsSkuModel->id)) {
                            
                            unset($sku_data['currentcy']);
                            unset($sku_data['price']);
                        }

                        DB::table('user_goods_sku')
                            ->where('id', $goodsSkuModel->id)
                            ->update($sku_data);
                    } else {
                        $sku_data['created_at'] = date('Y-m-d H:i:s');
                        GoodsSkuModel::insert($sku_data);
                    }
                }
            }

            if($goods_instruction_images){
                $goodsInstructionImagesModel = GoodsInstructionImagesModel::where(['user_goods_id' => $user_goods_id])->first();
                if($goodsInstructionImagesModel){
                    $goodsInstructionImagesModel->urls = $goods_instruction_images;
                    $goodsInstructionImagesModel->save();
                }else{
                    $goodsInstructionImagesModel = new GoodsInstructionImagesModel();
                    $goodsInstructionImagesModel->user_goods_id = $user_goods_id;
                    $goodsInstructionImagesModel->goods_id = $goods_id;
                    $goodsInstructionImagesModel->urls = $goods_instruction_images;
                    $goodsInstructionImagesModel->save();
                }
            }

            // 更新目录商品数量统计 注意第一个参数 这里的含义是 主账户ID 如果当前登录的是子账号 会自动获取并传递主账户ID
            $this->updateDirectoryGoodsCount($user_id, $data['directory_id'] ?? 0, $oldDirectoryId, $isNewGoods);

            DB::commit();
        }catch(\Exception $e){
            DB::rollBack();
            throw new MyException("商品保存失败".$e->getMessage());
        }

        return ['info_id' => $user_goods_id, 'goods_id' => $goods_id, 'goods_name' => $goods_name,'goods_sku_num' => $goods_sku_num];
    }

    /**
     * 检查SKU是否已经调整过价格
     * @param int $skuId SKU ID
     * @return bool 如果已经调整过价格返回true，否则返回false
     */
    private function hasSkuPriceBeenAdjusted(int $userId,int $skuId): bool
    {
        return GoodsPriceAdjustmentLogModel::query()
            ->where('modifier_id', $userId)
            ->where('sku_id', $skuId)
            ->exists();
    }

    /**
     * 更新目录商品数量统计
     * 使用统计计算的方法更新值，不使用increment/decrement
     * 
     * @param int $userId 用户ID
     * @param int $newDirectoryId 新目录ID
     * @param int|null $oldDirectoryId 旧目录ID（更新商品时）
     * @param bool $isNewGoods 是否为新商品
     */
    private function updateDirectoryGoodsCount(int $userId, int $newDirectoryId, ?int $oldDirectoryId, bool $isNewGoods): void
    {
        // 需要更新的目录ID集合
        $directoryIdsToUpdate = [];
        
        if ($isNewGoods) {
            // 新商品，只需要更新新目录
            if ($newDirectoryId > 0) {
                $directoryIdsToUpdate[] = $newDirectoryId;
            }
        } else {
            // 更新商品，可能需要更新新旧两个目录
            if ($oldDirectoryId !== $newDirectoryId) {
                // 目录发生变化
                if ($oldDirectoryId > 0) {
                    $directoryIdsToUpdate[] = $oldDirectoryId;
                }
                if ($newDirectoryId > 0) {
                    $directoryIdsToUpdate[] = $newDirectoryId;
                }
            }
            // 如果目录没有变化，不需要更新统计
        }

        // 批量更新目录商品数量
        if(count($directoryIdsToUpdate) > 0){
            foreach (array_unique($directoryIdsToUpdate) as $directoryId) {
                $this->recalculateDirectoryGoodsCount($userId, $directoryId);
            }
        }
    }

    /**
     * 重新计算指定目录的商品数量
     * 
     * @param int $userId 用户ID
     * @param int $directoryId 目录ID
     */
    private function recalculateDirectoryGoodsCount(int $userId, int $directoryId): void
    {
        // 统计该目录下的商品数量
        $goodsCount = GoodsModel::where('user_id', $userId)
            ->where('directory_id', $directoryId)
            ->where('status', 1) // 只统计有效商品
            ->count();

        // 更新目录的商品数量
        UserGoodsDirectoryModel::where('user_id', $userId)
            ->where('id', $directoryId)
            ->update(['goods_count' => $goodsCount]);
    }
}

