<?php

namespace App\Service\N11;

use App\Models\N11\N11RejectedProductModel;
use App\Service\BaseService;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class N11RejectedProductService extends BaseService
{
    protected $n11RejectedProductModel;

    public function __construct(N11RejectedProductModel $n11RejectedProductModel)
    {
        $this->n11RejectedProductModel = $n11RejectedProductModel;
    }

    /**
     * 批量保存重新上传商品数据
     * @param int $userId 用户ID
     * @param array $products 商品数据数组
     * @return array 返回保存结果统计
     * @throws MyException
     */
    public function batchSaveRejectedProducts(int $userId, array $products): array
    {
        if (empty($userId)) {
            throw new MyException('用户ID不能为空');
        }

        if (empty($products)) {
            throw new MyException('商品数据不能为空');
        }

        try {
            $insertData = [];
            $now = now();

            foreach ($products as $product) {
                $insertData[] = [
                    'product_id' => $product['product_id'] ?? '',
                    'user_id' => $userId,
                    'sku_id' => $product['sku_id'] ?? null,
                    'title' => $product['title'] ?? null,
                    'commission' => $product['commission'] ?? null,
                    'image_url' => $product['image_url'] ?? null,
                    'brand' => $product['brand'] ?? null,
                    'commission_rate_value' => $product['commission_rate_value'] ?? null,
                    'stock_code' => $product['stock_code'] ?? null,
                    'barcode' => $product['barcode'] ?? null,
                    'product_main_id' => $product['product_main_id'] ?? null,
                    'sales_price' => $product['sales_price'] ?? null,
                    'list_price' => $product['list_price'] ?? null,
                    'quantity' => $product['quantity'] ?? null,
                    'status_original' => $product['status_original'] ?? null,
                    'group_id' => $product['group_id'] ?? null,
                    'category_name' => $product['category_name'] ?? null,
                    'preparing_time' => $product['preparing_time'] ?? null,
                    'catalog_id' => $product['catalog_id'] ?? null,
                    'shipment_template' => $product['shipment_template'] ?? null,
                    'in_approval_reason' => is_array($product['in_approval_reason'] ?? null) 
                        ? (empty($product['in_approval_reason']) ? '' : json_encode($product['in_approval_reason']))
                        : ($product['in_approval_reason'] ?? ''),
                    'reject_info' => $product['reject_info'] ?? null,
                    'vat_rate' => $product['vat_rate'] ?? null,
                    'status' => 0, // 默认待处理状态
                    'created_at' => $now,
                    'updated_at' => $now
                ];
            }

            // 使用批量插入，忽略重复记录
            $insertedCount = 0;
            $duplicateCount = 0;

            // 分批处理，每次500条
            $chunks = array_chunk($insertData, 500);
            foreach ($chunks as $chunk) {
                try {
                    DB::table('n11_rejected_products')->insertOrIgnore($chunk);
                    $insertedCount += count($chunk);
                } catch (\Exception $e) {
                    // 如果批量插入失败，尝试逐个插入
                    foreach ($chunk as $item) {
                        try {
                            DB::table('n11_rejected_products')->insertOrIgnore([$item]);
                            $insertedCount++;
                        } catch (\Exception $ex) {
                            $duplicateCount++;
                            Log::warning('插入重复记录: ' . $ex->getMessage(), $item);
                        }
                    }
                }
            }

            // 获取当前待处理总数
            $pendingCount = $this->getPendingCount($userId);

            return [
                'inserted_count' => $insertedCount,
                'duplicate_count' => $duplicateCount,
                'pending_total' => $pendingCount,
                'message' => "成功保存 {$insertedCount} 条记录，跳过重复记录 {$duplicateCount} 条"
            ];

        } catch (\Exception $e) {
            Log::error('批量保存重新上传商品失败: ' . $e->getMessage(), [
                'user_id' => $userId,
                'products_count' => count($products)
            ]);
            throw new MyException('保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户待处理记录总数
     * @param int $userId 用户ID
     * @return int
     */
    public function getPendingCount(int $userId): int
    {
        return $this->n11RejectedProductModel
            ->byUserId($userId)
            ->pending()
            ->count();
    }

    /**
     * 获取下一个待处理的商品记录
     * @param int $userId 用户ID
     * @return array|null
     */
    public function getNextPendingProduct(int $userId): ?array
    {
        $product = $this->n11RejectedProductModel
            ->byUserId($userId)
            ->pending()
            ->orderBy('id', 'asc')
            ->first();

        if (!$product) {
            return null;
        }

        return [
            'id' => $product->id,
            'product_id' => $product->product_id,
            'stock_code' => $product->stock_code,
            'title' => $product->title,
            'sales_price' => $product->sales_price,
            'quantity' => $product->quantity
        ];
    }

    /**
     * 更新商品处理状态
     * @param int $userId 用户ID
     * @param string $productId 商品ID
     * @param int $status 状态（0=待处理，1=已完成）
     * @return bool
     * @throws MyException
     */
    public function updateProductStatus(int $userId, string $productId, int $status): bool
    {
        if (empty($userId) || empty($productId)) {
            throw new MyException('用户ID和商品ID不能为空');
        }

        $updated = $this->n11RejectedProductModel
            ->byUserId($userId)
            ->where('product_id', $productId)
            ->update(['status' => $status, 'updated_at' => now()]);

        return $updated > 0;
    }

    /**
     * 获取处理统计信息
     * @param int $userId 用户ID
     * @return array
     */
    public function getProcessingStatistics(int $userId): array
    {
        $pendingCount = $this->n11RejectedProductModel
            ->byUserId($userId)
            ->pending()
            ->count();

        $completedCount = $this->n11RejectedProductModel
            ->byUserId($userId)
            ->completed()
            ->count();

        return [
            'pending_count' => $pendingCount,
            'completed_count' => $completedCount,
            'total_count' => $pendingCount + $completedCount
        ];
    }

    /**
     * 批量更新商品状态为拒绝状态
     * @param int $userId 用户ID
     * @param array $stockCodes stock_code 数组
     * @return array 返回更新结果统计
     * @throws MyException
     */
    public function batchUpdateRejectedStatus(int $userId, array $stockCodes): array
    {
        if (empty($userId)) {
            throw new MyException('没有获取到用户信息');
        }

        if (empty($stockCodes)) {
            throw new MyException('没有获取到商品数据');
        }

        try {
            DB::beginTransaction();

            // 更新 user_task_detail 表中的记录
            $updated = DB::table('user_task_detail')
                ->where('user_id', $userId)
                ->whereIn('stock_code', $stockCodes)
                ->update([
                    'status' => 6, // 审核拒绝
                    'third_status' => 'REJECT',
                    'third_result' => '审核未通过',
                    'memo' => '审核未通过',
                    'updated_at' => now()
                ]);

            // 获取受影响的task_id并统计每个task的rejected数量
            $taskRejectedCounts = DB::table('user_task_detail')
                ->select('task_id', DB::raw('COUNT(*) as rejected_count'))
                ->where('user_id', $userId)
                ->where('status', 6)
                ->whereIn('task_id', function($query) use ($userId, $stockCodes) {
                    $query->select('task_id')
                        ->from('user_task_detail')
                        ->where('user_id', $userId)
                        ->whereIn('stock_code', $stockCodes)
                        ->distinct();
                })
                ->groupBy('task_id')
                ->get();

            // 批量更新user_task表的rejected_count字段
            $taskUpdatedCount = 0;
            foreach ($taskRejectedCounts as $taskCount) {
                $taskUpdated = DB::table('user_task')
                    ->where('id', $taskCount->task_id)
                    ->update(['rejected_count' => $taskCount->rejected_count]);
                
                if ($taskUpdated) {
                    $taskUpdatedCount++;
                }
            }

            DB::commit();

            return [
                'updated_count' => $updated,
                'task_updated_count' => $taskUpdatedCount,
                'total_stock_codes' => count($stockCodes),
                'message' => "成功更新 {$updated} 条记录为审核未通过状态，同时更新了 {$taskUpdatedCount} 个任务的拒绝统计"
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批量更新商品状态为拒绝状态失败: ' . $e->getMessage(), [
                'user_id' => $userId,
                'stock_codes' => $stockCodes
            ]);
            throw new MyException('批量更新状态失败: ' . $e->getMessage());
        }
    }
} 