#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理器
负责文件下载、路径生成、文件验证等操作
"""

import os
import requests
import time
import hashlib
from urllib.parse import urlparse, unquote
from typing import Optional, Tuple, Dict, Any, List, Callable
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading


class FileManager:
    """文件管理器类"""
    
    # 用户ID阈值，参考Laravel项目的MIN_USER_ID_THRESHOLD
    MIN_USER_ID_THRESHOLD = 0
    
    def __init__(self, base_path: str = None, timeout: int = 30):
        """
        初始化文件管理器

        Args:
            base_path: 基础存储路径，默认为当前目录下的attachment
            timeout: 下载超时时间（秒）
        """
        if base_path is None:
            # 使用统一的路径管理工具获取attachment目录
            from utils.helpers import get_attachment_directory
            self.base_path = get_attachment_directory()
        else:
            self.base_path = base_path

        self.timeout = timeout

        # 确保基础目录存在
        os.makedirs(self.base_path, exist_ok=True)

        # 创建会话对象用于连接池管理
        self.session = requests.Session()
        # 设置连接池参数
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=20,  # 连接池大小
            pool_maxsize=20,      # 最大连接数
            max_retries=3         # 重试次数
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
    
    def is_remote_url(self, url: str) -> bool:
        """
        判断是否为远程URL

        Args:
            url: 待检查的URL

        Returns:
            是否为远程URL
        """
        return url.startswith(('http://', 'https://'))

    def is_image_url(self, url: str) -> bool:
        """
        判断URL是否为图片

        Args:
            url: 待检查的URL

        Returns:
            是否为图片URL
        """
        try:
            # 解析URL获取路径
            parsed_url = urlparse(url)
            path = unquote(parsed_url.path.lower())

            # 定义图片扩展名
            image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']

            # 检查文件扩展名
            for ext in image_extensions:
                if path.endswith(ext):
                    return True

            return False
        except Exception:
            return False

    def add_image_params(self, url: str) -> str:
        """
        为图片URL添加处理参数

        Args:
            url: 原始图片URL

        Returns:
            添加参数后的URL
        """
        if not self.is_image_url(url):
            return url

        # 添加图片处理参数
        separator = '&' if '?' in url else '?'
        return f"{url}{separator}imageView2/2/w/602/q/90/format/webp"
    
    def get_file_sub_directory(self, file_type: str) -> str:
        """
        根据文件类型获取子目录名称
        参考Laravel项目的getFileSubDirectory方法
        
        Args:
            file_type: 文件类型
            
        Returns:
            子目录名称
        """
        sub_dirs = {
            'goods_pic': 'goods_pic',
            'goods_detail': 'goods_pic',  # goods_detail图片存储在与goods_pic相同的目录中
            'goods_video': 'goods_video',
            'goods_pdf': 'goods_pdf',
            'instruction_images': 'goods_instruction_images',
            'sku_thumb': 'sku',
            'sku_skc_gallery': 'sku'
        }
        
        return sub_dirs.get(file_type, 'other')
    
    def generate_file_name(self, url: str, extension: str = None) -> str:
        """
        从URL生成文件名
        参考Laravel项目的generateFileName方法
        
        Args:
            url: 原始URL
            extension: 文件扩展名（可选）
            
        Returns:
            生成的文件名
        """
        try:
            # 解析URL获取文件名
            parsed_url = urlparse(url)
            if parsed_url.path:
                # URL解码
                path = unquote(parsed_url.path)
                file_name = os.path.basename(path)
                
                if file_name and '.' in file_name:
                    name, ext = os.path.splitext(file_name)
                    if name:
                        # 如果提供了扩展名，使用提供的扩展名
                        if extension:
                            return f"{name}.{extension.lower()}"
                        else:
                            return file_name
            
            # 如果无法从URL提取文件名，使用时间戳和随机数生成
            import random
            timestamp = int(time.time())
            random_num = random.randint(1000, 9999)
            
            if extension:
                return f"{timestamp}_{random_num}.{extension.lower()}"
            else:
                return f"{timestamp}_{random_num}.jpg"  # 默认为jpg
                
        except Exception:
            # 如果出现任何错误，使用默认生成方式
            import random
            timestamp = int(time.time())
            random_num = random.randint(1000, 9999)
            ext = extension.lower() if extension else 'jpg'
            return f"{timestamp}_{random_num}.{ext}"
    
    def generate_file_path(self, user_id: int, goods_platform_id: int, file_type: str, url: str) -> str:
        """
        生成文件存储路径
        参考Laravel项目的generateFilePath方法

        Args:
            user_id: 用户ID
            goods_platform_id: 平台商品ID
            file_type: 文件类型
            url: 原始URL

        Returns:
            相对存储路径，格式为: attachment/goods_platform_id/类型文件夹/文件名.jpg
        """
        # 获取子目录
        sub_dir = self.get_file_sub_directory(file_type)

        # 从URL中提取文件扩展名
        parsed_url = urlparse(url)
        path = unquote(parsed_url.path)
        _, ext = os.path.splitext(path)
        if ext:
            ext = ext[1:]  # 移除点号

        # 生成文件名
        file_name = self.generate_file_name(url, ext)

        # 使用新的目录结构：attachment/goods_platform_id/子目录/文件
        relative_path = f"attachment/{goods_platform_id}/{sub_dir}/{file_name}"

        return relative_path
    
    def get_full_path(self, relative_path: str) -> str:
        """
        获取完整的本地文件路径

        Args:
            relative_path: 相对路径（可能包含attachment/前缀）

        Returns:
            完整的本地文件路径
        """
        # 如果相对路径以 "attachment/" 开头，需要去除这个前缀
        # 因为 base_path 已经指向了 attachment 目录
        if relative_path.startswith("attachment/"):
            relative_path = relative_path[11:]  # 去除 "attachment/" 前缀
        elif relative_path.startswith("attachment\\"):
            relative_path = relative_path[12:]  # 去除 "attachment\" 前缀（Windows路径）

        return os.path.join(self.base_path, relative_path)
    
    def download_file(self, url: str, local_path: str) -> Tuple[bool, str]:
        """
        下载文件到本地

        Args:
            url: 远程文件URL
            local_path: 本地存储路径（完整路径）

        Returns:
            (是否成功, 错误消息)
        """
        try:
            # 检查是否为远程URL
            if not self.is_remote_url(url):
                return False, "不是远程URL，跳过下载"

            # 创建目录
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # 检查文件是否已存在
            if os.path.exists(local_path):
                return True, "文件已存在"

            # 如果是图片URL，添加处理参数
            download_url = self.add_image_params(url)

            # 下载文件
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = self.session.get(download_url, headers=headers, timeout=self.timeout, stream=True)
            response.raise_for_status()

            # 写入文件
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # 验证文件是否下载成功
            if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
                return True, "下载成功"
            else:
                return False, "下载的文件为空"

        except requests.exceptions.Timeout:
            return False, "下载超时"
        except requests.exceptions.ConnectionError:
            return False, "连接错误"
        except requests.exceptions.HTTPError as e:
            return False, f"HTTP错误: {e}"
        except Exception as e:
            return False, f"下载失败: {str(e)}"
    
    def validate_file(self, file_path: str) -> Tuple[bool, str]:
        """
        验证文件是否有效
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否有效, 错误消息)
        """
        try:
            if not os.path.exists(file_path):
                return False, "文件不存在"
            
            if os.path.getsize(file_path) == 0:
                return False, "文件为空"
            
            # 可以添加更多验证逻辑，如文件格式验证等
            
            return True, "文件有效"
            
        except Exception as e:
            return False, f"验证失败: {str(e)}"
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            if not os.path.exists(file_path):
                return {}
            
            stat = os.stat(file_path)
            
            return {
                'size': stat.st_size,
                'created_time': stat.st_ctime,
                'modified_time': stat.st_mtime,
                'exists': True
            }
            
        except Exception:
            return {'exists': False}
    
    def cleanup_empty_dirs(self, path: str):
        """
        清理空目录

        Args:
            path: 要清理的路径
        """
        try:
            if os.path.isdir(path):
                # 递归清理子目录
                for item in os.listdir(path):
                    item_path = os.path.join(path, item)
                    if os.path.isdir(item_path):
                        self.cleanup_empty_dirs(item_path)

                # 如果目录为空，删除它
                if not os.listdir(path):
                    os.rmdir(path)

        except Exception:
            pass  # 忽略清理错误

    def download_files_concurrent(self, download_tasks: List[Dict], max_workers: int = 5,
                                 progress_callback: Callable = None,
                                 stop_flag: Callable = None) -> Tuple[int, int]:
        """
        并发下载多个文件

        Args:
            download_tasks: 下载任务列表，每个任务包含 {'url': str, 'local_path': str, 'record_id': int}
            max_workers: 最大并发数
            progress_callback: 进度回调函数，接收参数 (completed_count, total_count, current_file, success)
            stop_flag: 停止标志检查函数，返回True时停止下载

        Returns:
            (成功下载数量, 总任务数量)
        """
        if not download_tasks:
            return 0, 0

        total_tasks = len(download_tasks)
        completed_count = 0
        success_count = 0

        # 限制最大并发数，避免系统过载
        max_workers = min(max_workers, 20)  # 最大不超过20
        max_workers = max(max_workers, 1)   # 最小为1

        # 使用线程锁保护计数器
        count_lock = threading.Lock()

        def download_single_task(task):
            """下载单个任务"""
            nonlocal completed_count, success_count

            # 检查停止标志
            if stop_flag and stop_flag():
                return False, "用户停止", task

            url = task['url']
            local_path = task['local_path']
            record_id = task.get('record_id')

            try:
                # 执行下载（带重试机制）
                success, error_msg = self.download_file_with_retry(url, local_path, max_retries=2)

                # 线程安全地更新计数器
                with count_lock:
                    completed_count += 1
                    if success:
                        success_count += 1

                    # 调用进度回调
                    if progress_callback:
                        progress_callback(completed_count, total_tasks, os.path.basename(url), success)

                return success, error_msg, task

            except Exception as e:
                with count_lock:
                    completed_count += 1
                    if progress_callback:
                        progress_callback(completed_count, total_tasks, os.path.basename(url), False)

                return False, f"下载异常: {str(e)}", task

        # 使用线程池执行并发下载
        try:
            with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="FileDownload") as executor:
                # 提交所有任务
                future_to_task = {executor.submit(download_single_task, task): task
                                for task in download_tasks}

                # 处理完成的任务
                completed_futures = 0
                for future in as_completed(future_to_task, timeout=300):  # 5分钟总超时
                    if stop_flag and stop_flag():
                        # 用户要求停止，取消剩余任务
                        for f in future_to_task:
                            if not f.done():
                                f.cancel()
                        break

                    try:
                        success, error_msg, task = future.result(timeout=30)  # 单个任务30秒超时
                        completed_futures += 1
                        # 这里可以添加额外的结果处理逻辑
                    except Exception as e:
                        # 处理future执行异常
                        task = future_to_task[future]
                        with count_lock:
                            if completed_count < total_tasks:
                                completed_count += 1
                                if progress_callback:
                                    progress_callback(completed_count, total_tasks,
                                                    os.path.basename(task['url']), False)
                        completed_futures += 1

                # 确保所有任务都被处理
                if completed_futures < len(download_tasks) and not (stop_flag and stop_flag()):
                    # 有任务可能因为超时等原因未完成，更新计数器
                    remaining = len(download_tasks) - completed_futures
                    with count_lock:
                        completed_count += remaining
                        if progress_callback:
                            for i in range(remaining):
                                progress_callback(completed_count - remaining + i + 1, total_tasks, "超时任务", False)

        except Exception as e:
            # 处理线程池异常
            print(f"并发下载异常: {str(e)}")
            # 确保计数器正确
            with count_lock:
                if completed_count < total_tasks:
                    remaining = total_tasks - completed_count
                    completed_count = total_tasks
                    if progress_callback:
                        for i in range(remaining):
                            progress_callback(completed_count - remaining + i + 1, total_tasks, "异常任务", False)

        return success_count, total_tasks

    def download_file_with_retry(self, url: str, local_path: str, max_retries: int = 3) -> Tuple[bool, str]:
        """
        带重试机制的文件下载

        Args:
            url: 远程文件URL
            local_path: 本地存储路径（完整路径）
            max_retries: 最大重试次数

        Returns:
            (是否成功, 错误消息)
        """
        last_error = ""

        for attempt in range(max_retries + 1):
            success, error_msg = self.download_file(url, local_path)

            if success:
                return True, "下载成功"

            last_error = error_msg

            # 如果不是最后一次尝试，等待一段时间后重试
            if attempt < max_retries:
                wait_time = (attempt + 1) * 2  # 递增等待时间：2s, 4s, 6s
                time.sleep(wait_time)

        return False, f"重试{max_retries}次后仍然失败: {last_error}"

    def close(self):
        """关闭文件管理器，清理资源"""
        try:
            if hasattr(self, 'session'):
                self.session.close()
        except Exception:
            pass  # 忽略清理错误

    def __del__(self):
        """析构函数，确保资源被清理"""
        self.close()
