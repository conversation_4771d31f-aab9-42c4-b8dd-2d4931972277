<?php

namespace App\Service\User;

use App\Service\BaseService;
use App\Models\User\UserAiKeyModel;
use App\Exceptions\MyException;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class UserAiKeyService extends BaseService
{
    /**
     * 获取AI Key列表（分页）
     */
    public function getAiKeyList(int $userId, array $params): array
    {
        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));
        
        // 获取筛选参数
        $status = $params['status'] ?? null;
        $aiType = $params['ai_type'] ?? null;
        $aiKey = $params['ai_key'] ?? null;
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;

        // 构建查询
        $query = UserAiKeyModel::byUserId($userId);

        // 按状态筛选
        if (is_numeric($status) && in_array($status, [0, 1])) {
            $query->byStatus($status);
        }

        // 按AI类型筛选
        if (is_numeric($aiType)) {
            $query->byAiType($aiType);
        }

        // 按AI Key筛选
        if ($aiKey) {
            $query->where('ai_key', 'like', "%{$aiKey}%");
        }

        // 按创建时间范围筛选
        if ($startDate || $endDate) {
            $query->byDateRange($startDate, $endDate);
        }

        // 排序
        $query->ordered();

        // 分页查询
        $total = $query->count();
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;
        
        $aiKeys = $query->offset($offset)
                        ->limit($pageSize)
                        ->get()
                        ->map(function($item) {
                            return $this->formatAiKeyData($item);
                        });

        return [
            'list' => $aiKeys,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasNext' => $page < $totalPages,
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 创建AI Key
     */
    public function createAiKey(int $userId, array $data): array
    {
        // 验证数据
        $this->validateAiKeyData($data, true);

        // 检查同一用户下是否已存在相同的AI Key
        $exists = UserAiKeyModel::byUserId($userId)
            ->where('ai_key', $data['ai_key'])
            ->exists();

        if ($exists) {
            throw new MyException('该AI Key已存在');
        }

        // 创建AI Key
        $aiKeyData = array_merge($data, [
            'user_id' => $userId,
            'status' => $data['status'] ?? 1,
            'sort_order' => $data['sort_order'] ?? 0,
            'ai_type' => $data['ai_type'] ?? 1,
        ]);

        $aiKey = UserAiKeyModel::create($aiKeyData);

        return [
            'id' => $aiKey->id,
            'message' => 'AI Key创建成功'
        ];
    }

    /**
     * 更新AI Key
     */
    public function updateAiKey(int $userId, array $data): array
    {
        // 验证数据
        $this->validateAiKeyData($data, false);

        if (!isset($data['id'])) {
            throw new MyException('AI Key ID不能为空');
        }

        // 查找AI Key并验证所有权
        $aiKey = UserAiKeyModel::find($data['id']);
        
        if (!$aiKey) {
            throw new MyException('AI Key不存在');
        }

        if (!$aiKey->belongsToUser($userId)) {
            throw new MyException('无权限操作此AI Key');
        }

        // 如果更新了AI Key，检查是否重复
        if (isset($data['ai_key']) && $data['ai_key'] !== $aiKey->ai_key) {
            $exists = UserAiKeyModel::byUserId($userId)
                ->where('ai_key', $data['ai_key'])
                ->where('id', '!=', $aiKey->id)
                ->exists();

            if ($exists) {
                throw new MyException('该AI Key已存在');
            }
        }

        // 更新AI Key
        $updateData = $data;
        unset($updateData['id']);

        $aiKey->update($updateData);

        return [
            'message' => 'AI Key更新成功'
        ];
    }

    /**
     * 批量更新AI Key
     */
    public function batchUpdateAiKey(int $userId, array $data): array
    {
        $count = UserAiKeyModel::where('user_id', $userId)->whereIn('id', $data['ids'])->count();
        if ($count != count($data['ids'])) {
            throw new MyException('参数错误');
        }
        
        $ids = $data['ids'];
        unset($data['ids']);
        
        if (count($data) == 0) {
            throw new MyException('请至少选择一项');
        }
        
        UserAiKeyModel::where('user_id', $userId)->whereIn('id', $ids)->update($data);
        
        return [
            'message' => 'AI Key批量更新成功'
        ];
    }

    /**
     * 删除AI Key
     */
    public function deleteAiKey(int $userId, int $aiKeyId): array
    {
        // 验证AI Key ID
        if (!$aiKeyId) {
            throw new MyException('AI Key ID不能为空');
        }

        // 查找AI Key并验证所有权
        $aiKey = UserAiKeyModel::find($aiKeyId);
        
        if (!$aiKey) {
            throw new MyException('AI Key不存在');
        }

        if (!$aiKey->belongsToUser($userId)) {
            throw new MyException('无权限操作此AI Key');
        }

        // 删除AI Key
        $aiKey->delete();

        return [
            'message' => 'AI Key删除成功'
        ];
    }

    /**
     * 获取AI Key详情
     */
    public function getAiKeyDetail(int $userId, int $aiKeyId): array
    {
        // 验证AI Key ID
        if (!$aiKeyId) {
            throw new MyException('AI Key ID不能为空');
        }

        // 查找AI Key并验证所有权
        $aiKey = UserAiKeyModel::find($aiKeyId);
        
        if (!$aiKey) {
            throw new MyException('AI Key不存在');
        }

        if (!$aiKey->belongsToUser($userId)) {
            throw new MyException('无权限查看此AI Key');
        }

        return $this->formatAiKeyDetailData($aiKey);
    }

    /**
     * 验证AI Key数据
     */
    private function validateAiKeyData(array $data, bool $isCreate = false): void
    {
        $rules = [
            'ai_key' => 'required|string|max:500',
            'description' => 'nullable|string|max:500',
            'sort_order' => 'nullable|integer|min:0|max:999999',
            'status' => 'nullable|integer|in:0,1',
            'ai_type' => 'nullable|integer|in:1',
        ];

        if (!$isCreate) {
            $rules['id'] = 'required|integer|min:1';
            // 更新时ai_key不是必需的
            $rules['ai_key'] = 'nullable|string|max:500';
        }

        $messages = [
            'ai_key.required' => 'AI Key不能为空',
            'ai_key.max' => 'AI Key长度不能超过500个字符',
            'description.max' => '描述长度不能超过500个字符',
            'sort_order.integer' => '排序必须是整数',
            'sort_order.min' => '排序不能小于0',
            'sort_order.max' => '排序不能大于999999',
            'status.in' => '状态值无效',
            'ai_type.in' => 'AI类型值无效',
            'id.required' => 'AI Key ID不能为空',
            'id.integer' => 'AI Key ID必须是整数',
            'id.min' => 'AI Key ID无效',
        ];

        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            throw new MyException($validator->errors()->first());
        }
    }

    /**
     * 格式化AI Key数据
     */
    private function formatAiKeyData($aiKey): array
    {
        return [
            'id' => $aiKey->id,
            'user_id' => $aiKey->user_id,
            'ai_key' => $aiKey->ai_key,
            'description' => $aiKey->description,
            'sort_order' => $aiKey->sort_order,
            'status' => $aiKey->status,
            'status_name' => $aiKey->status_name,
            'ai_type' => $aiKey->ai_type,
            'ai_type_name' => $aiKey->ai_type_name,
            'created_at' => $aiKey->created_at,
            'updated_at' => $aiKey->updated_at,
        ];
    }

    /**
     * 格式化AI Key详情数据
     */
    private function formatAiKeyDetailData($aiKey): array
    {
        return $this->formatAiKeyData($aiKey);
    }
} 