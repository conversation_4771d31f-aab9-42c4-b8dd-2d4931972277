/**
 * 用户商品采集设置API接口
 */

// 接口类型定义
export interface CollectionSettings {
  has_settings: boolean
  default_directory_id: number
  default_directory_name: string
  collection_mode: number
  collection_mode_name: string
  no_remind_until: string | null
  is_in_no_remind_period: boolean
  is_default_directory_valid: boolean
  need_setup: boolean
}

export interface Directory {
  id: number
  user_id: number
  user_sub_id: number
  name: string
  description: string
  goods_count: number
}

export interface SaveSettingsData {
  default_directory_id: number
  collection_mode: number
  no_remind_24h?: boolean
}

export interface CheckNeedSetupResponse {
  need_setup: boolean
  reason: string
  settings: CollectionSettings
}

/**
 * 获取配置中的API地址
 */
const getApiUrl = (configKey: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      if (response?.url) {
        resolve(response.url);
      } else {
        resolve('');
      }
    });
  });
};

/**
 * 发送请求到background
 */
const sendRequest = (config: any): Promise<any> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      ...config,
      auth: true,
      encrypto: true
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      
      if (response && response[0] && response[0].data) {
        const result = response[0].data;
        if (result.code === 1) {
          resolve(result.data);
        } else {
          reject(new Error(result.errMsg || '请求失败'));
        }
      } else {
        reject(new Error('请求未返回有效数据'));
      }
    });
  });
};

/**
 * 获取用户采集设置
 */
export const getUserCollectionSettings = async (): Promise<CollectionSettings> => {
  const url = await getApiUrl('apiUserCollectionSettingsUrl');
  
  return sendRequest({
    url,
    method: 'get',
    funName: 'getUserCollectionSettings'
  });
};

/**
 * 保存用户采集设置
 */
export const saveUserCollectionSettings = async (data: SaveSettingsData): Promise<any> => {
  const url = await getApiUrl('apiUserCollectionSettingsUrl');
  
  return sendRequest({
    url,
    method: 'post',
    pramas: data,
    funName: 'saveUserCollectionSettings'
  });
};

/**
 * 获取用户可用目录列表
 */
export const getUserAvailableDirectories = async (): Promise<{ list: Directory[] }> => {
  const url = await getApiUrl('apiUserAvailableDirectoriesUrl');
  
  return sendRequest({
    url,
    method: 'get',
    funName: 'getUserAvailableDirectories'
  });
};

/**
 * 检查用户是否需要设置采集参数
 */
export const checkUserNeedSetup = async (): Promise<CheckNeedSetupResponse> => {
  const url = await getApiUrl('apiUserCheckNeedSetupUrl');
  
  return sendRequest({
    url,
    method: 'get',
    funName: 'checkUserNeedSetup'
  });
};

// 注意：checkUserLoginStatus 函数已移动到 @/utils/userStore.ts
// 请使用统一的用户信息管理