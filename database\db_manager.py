#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理类
负责SQLite数据库的连接、表创建和基础操作
"""

import sqlite3
import os
import sys
from typing import Optional, List, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import get_database_path


class DatabaseManager:
    """数据库管理类"""

    def __init__(self, db_path: str = None):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径，如果为None则使用默认路径
        """
        if db_path is None:
            # 使用统一的路径管理工具
            self.db_path = get_database_path()
        else:
            self.db_path = db_path

        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
        return conn
    
    def init_database(self):
        """初始化数据库，创建必要的表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 创建config表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    appid TEXT NOT NULL UNIQUE,
                    appsecret TEXT NOT NULL,
                    memo TEXT,
                    user_id INTEGER,
                    phone TEXT,
                    is_default INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建商品主表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS goods (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    phone TEXT,
                    goods_id INTEGER NOT NULL UNIQUE,
                    goods_name TEXT NOT NULL,
                    goods_platform_id INTEGER,
                    directory_name TEXT,
                    media_files_json TEXT,
                    pending_total_count INTEGER DEFAULT 0,
                    is_all_files_downloaded INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建文件下载记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS file_download_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    goods_id INTEGER NOT NULL,
                    type TEXT NOT NULL,
                    field TEXT NOT NULL,
                    url TEXT NOT NULL,
                    url_local TEXT,
                    model_id INTEGER,
                    sku_id INTEGER,
                    is_downloaded INTEGER DEFAULT 0,
                    is_uploaded INTEGER DEFAULT 0,
                    download_attempts INTEGER DEFAULT 0,
                    upload_attempts INTEGER DEFAULT 0,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (goods_id) REFERENCES goods (goods_id)
                )
            ''')

            # 检查并添加新字段（为了兼容已存在的数据库）
            self._add_missing_columns(cursor)

            conn.commit()

    def _add_missing_columns(self, cursor):
        """添加缺少的列（兼容已存在的数据库）"""
        try:
            # 检查config表字段
            cursor.execute("PRAGMA table_info(config)")
            config_columns = [column[1] for column in cursor.fetchall()]

            # 检查并添加user_id字段
            if 'user_id' not in config_columns:
                cursor.execute("ALTER TABLE config ADD COLUMN user_id INTEGER")

            # 检查并添加phone字段
            if 'phone' not in config_columns:
                cursor.execute("ALTER TABLE config ADD COLUMN phone TEXT")

            # 检查并添加is_default字段
            if 'is_default' not in config_columns:
                cursor.execute("ALTER TABLE config ADD COLUMN is_default INTEGER DEFAULT 0")

            # 检查并添加concurrent_download_count字段
            if 'concurrent_download_count' not in config_columns:
                cursor.execute("ALTER TABLE config ADD COLUMN concurrent_download_count INTEGER DEFAULT 5")

            # 检查并添加concurrent_upload_count字段
            if 'concurrent_upload_count' not in config_columns:
                cursor.execute("ALTER TABLE config ADD COLUMN concurrent_upload_count INTEGER DEFAULT 2")

            # 检查goods表字段
            cursor.execute("PRAGMA table_info(goods)")
            goods_columns = [column[1] for column in cursor.fetchall()]

            # 检查并添加goods表的新字段
            if 'media_files_json' not in goods_columns:
                cursor.execute("ALTER TABLE goods ADD COLUMN media_files_json TEXT")

            if 'pending_total_count' not in goods_columns:
                cursor.execute("ALTER TABLE goods ADD COLUMN pending_total_count INTEGER DEFAULT 0")

            if 'is_all_files_downloaded' not in goods_columns:
                cursor.execute("ALTER TABLE goods ADD COLUMN is_all_files_downloaded INTEGER DEFAULT 0")

            # 检查file_download_records表字段
            cursor.execute("PRAGMA table_info(file_download_records)")
            file_columns = [column[1] for column in cursor.fetchall()]

            # 检查并添加file_download_records表的新字段
            if 'model_id' not in file_columns:
                cursor.execute("ALTER TABLE file_download_records ADD COLUMN model_id INTEGER")

            if 'sku_id' not in file_columns:
                cursor.execute("ALTER TABLE file_download_records ADD COLUMN sku_id INTEGER")

            if 'download_attempts' not in file_columns:
                cursor.execute("ALTER TABLE file_download_records ADD COLUMN download_attempts INTEGER DEFAULT 0")

            if 'upload_attempts' not in file_columns:
                cursor.execute("ALTER TABLE file_download_records ADD COLUMN upload_attempts INTEGER DEFAULT 0")

            if 'error_message' not in file_columns:
                cursor.execute("ALTER TABLE file_download_records ADD COLUMN error_message TEXT")

        except Exception as e:
            print(f"添加字段时出错: {e}")
            # 如果出错，继续执行，不影响程序运行
    
    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """
        执行查询语句
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        执行更新语句（INSERT, UPDATE, DELETE）

        Args:
            query: SQL更新语句
            params: 更新参数

        Returns:
            受影响的行数
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount
    
    def execute_insert(self, query: str, params: tuple = ()) -> int:
        """
        执行插入语句

        Args:
            query: SQL插入语句
            params: 插入参数

        Returns:
            新插入记录的ID
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.lastrowid
