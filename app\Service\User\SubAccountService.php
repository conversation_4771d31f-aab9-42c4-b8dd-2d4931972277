<?php
namespace App\Service\User;

use Exception;
use App\Utils\Tools;
use App\Service\BaseService;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User\User as UserModel;
use App\Models\User\GoodsModel;

class SubAccountService extends BaseService
{
    /**
     * 获取子账号列表
     * @param int $parentId 主账号ID
     * @param array $params 分页和筛选参数
     * @return array
     */
    public function getSubAccountList(int $parentId, array $params = []): array
    {
        try {
            $page = max(1, intval($params['page'] ?? 1));
            $pageSize = max(1, intval($params['pageSize'] ?? 10));

            $query = UserModel::where('pid', $parentId)
                ->select(['id', 'name', 'phone', 'status', 'created_at', 'updated_at'])
                ->orderBy('created_at', 'desc');

            // 添加筛选条件
            if (!empty($params['name'])) {
                $query->where('name', 'like', '%' . $params['name'] . '%');
            }

            if (!empty($params['phone'])) {
                $query->where('phone', 'like', '%' . $params['phone'] . '%');
            }

            if (isset($params['status']) && $params['status'] !== '') {
                $status = intval($params['status']);
                $query->where('status', $status);
            }

            $total = $query->count();

            $subAccounts = $query->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get()
                ->toArray();

            // 为每个子账号添加商品统计信息
            foreach ($subAccounts as &$subAccount) {
                // 统计商品总数：user_sub_id等于子账号ID且status=1的商品总数
                $totalGoodsCount = GoodsModel::where('user_sub_id', $subAccount['id'])
                    ->where('status', 1)
                    ->count();

                // 统计今日商品：user_sub_id等于子账号ID且status=1且创建时间为今日的商品数量
                $todayGoodsCount = GoodsModel::where('user_sub_id', $subAccount['id'])
                    ->where('status', 1)
                    ->whereDate('created_at', date('Y-m-d'))
                    ->count();

                $subAccount['total_goods_count'] = $totalGoodsCount;
                $subAccount['today_goods_count'] = $todayGoodsCount;
            }

            // 获取主账号信息，检查子账号限制
            $parentUser = UserModel::find($parentId);
            $subNum = $parentUser ? $parentUser->sub_num : 2;

            return [
                'list' => $subAccounts,
                'pagination' => [
                    'current' => $page,
                    'pageSize' => $pageSize,
                    'total' => $total,
                    'totalPages' => ceil($total / $pageSize),
                    'hasNext' => $page * $pageSize < $total,
                    'hasPrevious' => $page > 1,
                ],
                'sub_num_limit' => $subNum,
                'can_add_more' => $total < $subNum
            ];
        } catch (Exception $e) {
            throw new MyException('获取子账号列表失败');
        }
    }

    /**
     * 生成唯一的6位注册码
     * @return string 生成的注册码
     * @throws MyException 当无法生成唯一注册码时抛出异常
     */

    public function generateInviteCode()
    {
        $maxAttempts = 30; // 最大尝试次数
        $attempts = 0;
        do {
            // 生成6位随机字符串(只包含数字和大写字母,排除易混淆的字符)
            $code = Tools::generateRandomStr(6);
            // 检查是否已存在
            $exists = UserModel::where('invite_code', $code)->exists();
            $attempts++;
            // 如果不存在则返回该码
            if(!$exists) {
                return $code;
            }
        } while($attempts < $maxAttempts);
        
        // 如果尝试次数过多仍未成功,则抛出异常
        throw new MyException('无法生成唯一邀请码,请重试');
    }

    /**
     * 创建子账号
     * @param int $parentId 主账号ID
     * @param array $data 子账号数据
     * @return array
     */
    public function createSubAccount(int $parentId, array $data): array
    {
        try {
            // 检查主账号是否存在
            $parentUser = UserModel::find($parentId);
            if (!$parentUser) {
                throw new MyException('主账号不存在');
            }

            // 检查子账号数量限制
            $currentSubCount = UserModel::where('pid', $parentId)->count();
            if ($currentSubCount >= $parentUser->sub_num) {
                throw new MyException('子账号数量已达上限（' . $parentUser->sub_num . '个）');
            }

            // 检查手机号是否已存在
            $existingUser = UserModel::where('phone', $data['phone'])->first();
            if ($existingUser) {
                throw new MyException('手机号已存在');
            }

            $invite_code = $this->generateInviteCode();

            DB::beginTransaction();

            // 创建子账号
            $subAccount = UserModel::create([
                'pid' => $parentId,
                'name' => $data['name'],
                'phone' => $data['phone'],
                'password' => getMd5($data['password']),
                'invite_code' => $invite_code,
                'status' => 1,
                'is_vip' => $parentUser->is_vip, // 继承主账号VIP状态
                'vip_level' => $parentUser->vip_level,
                'vip_start_time' => $parentUser->vip_start_time,
                'vip_end_time' => $parentUser->vip_end_time,
                'sub_num' => 0, // 子账号不能创建子账号
                'points' => 0,
                'points_all' => 0
            ]);

            DB::commit();

            return [
                'id' => $subAccount->id,
                'name' => $subAccount->name,
                'phone' => $subAccount->phone,
                'status' => $subAccount->status,
                'created_at' => $subAccount->created_at
            ];
        } catch (Exception $e) {
            DB::rollBack();
            throw new MyException($e->getMessage());
        }
    }

    /**
     * 更新子账号
     * @param int $parentId 主账号ID
     * @param array $data 更新数据
     * @return array
     */
    public function updateSubAccount(int $parentId, array $data): array
    {
        try {
            // 检查子账号是否属于当前主账号
            $subAccount = UserModel::where('id', $data['id'])
                ->where('pid', $parentId)
                ->first();

            if (!$subAccount) {
                throw new MyException('子账号不存在或无权限操作');
            }

            // 如果要更新手机号，检查是否已被其他用户使用
            if (isset($data['phone']) && $data['phone'] !== $subAccount->phone) {
                $existingUser = UserModel::where('phone', $data['phone'])
                    ->where('id', '!=', $data['id'])
                    ->first();
                if ($existingUser) {
                    throw new MyException('手机号已存在');
                }
            }

            DB::beginTransaction();

            $updateData = [];
            if (isset($data['name'])) {
                $updateData['name'] = $data['name'];
            }
            if (isset($data['phone'])) {
                $updateData['phone'] = $data['phone'];
            }
            if (isset($data['password'])) {
                $updateData['password'] = getMd5($data['password']);
            }
            if (isset($data['status'])) {
                $updateData['status'] = $data['status'];
            }

            $subAccount->update($updateData);

            DB::commit();

            return [
                'id' => $subAccount->id,
                'name' => $subAccount->name,
                'phone' => $subAccount->phone,
                'status' => $subAccount->status,
                'updated_at' => $subAccount->updated_at
            ];
        } catch (Exception $e) {
            DB::rollBack();
            throw new MyException('更新子账号失败');
        }
    }

    /**
     * 删除子账号
     * @param int $parentId 主账号ID
     * @param int $subAccountId 子账号ID
     * @return bool
     */
    public function deleteSubAccount(int $parentId, int $subAccountId): bool
    {
        try {
            // 检查子账号是否属于当前主账号
            $subAccount = UserModel::where('id', $subAccountId)
                ->where('pid', $parentId)
                ->first();

            if (!$subAccount) {
                throw new MyException('子账号不存在或无权限操作');
            }

            DB::beginTransaction();

            // 删除子账号
            $subAccount->delete();

            DB::commit();

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            throw new MyException('删除子账号失败');
        }
    }

    /**
     * 启用/禁用子账号
     * @param int $parentId 主账号ID
     * @param int $subAccountId 子账号ID
     * @param int $status 状态（0禁用，1启用）
     * @return array
     */
    public function toggleSubAccountStatus(int $parentId, int $subAccountId, int $status): array
    {
        try {
            // 检查子账号是否属于当前主账号
            $subAccount = UserModel::where('id', $subAccountId)
                ->where('pid', $parentId)
                ->first();

            if (!$subAccount) {
                throw new MyException('子账号不存在或无权限操作');
            }

            DB::beginTransaction();

            $subAccount->update(['status' => $status]);

            DB::commit();

            return [
                'id' => $subAccount->id,
                'status' => $subAccount->status,
                'updated_at' => $subAccount->updated_at
            ];
        } catch (Exception $e) {
            DB::rollBack();
            throw new MyException('更新子账号状态失败');
        }
    }

    /**
     * 获取子账号详情
     * @param int $parentId 主账号ID
     * @param int $subAccountId 子账号ID
     * @return array
     */
    public function getSubAccountDetail(int $parentId, int $subAccountId): array
    {
        try {
            $subAccount = UserModel::where('id', $subAccountId)
                ->where('pid', $parentId)
                ->select(['id', 'name', 'phone', 'status', 'is_vip', 'vip_end_time', 'created_at', 'updated_at'])
                ->first();

            if (!$subAccount) {
                throw new MyException('子账号不存在或无权限查看');
            }

            return $subAccount->toArray();
        } catch (Exception $e) {
            throw new MyException('获取子账号详情失败');
        }
    }
}
