#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品相关API服务
"""

import os
import json
from typing import Dict, Any, Tuple
from .api_client import APIClient


class GoodsService:
    """商品服务类"""
    
    def __init__(self, api_client: APIClient = None):
        """
        初始化商品服务
        
        Args:
            api_client: API客户端实例，如果为None则创建新实例
        """
        self.api_client = api_client or APIClient()
    
    def get_pending_goods(self, appid: str, appsecret: str) -> Tuple[bool, Dict[str, Any], str]:
        """
        获取待处理商品信息
        
        Args:
            appid: 应用ID
            appsecret: 应用密钥
            
        Returns:
            (是否成功, 响应数据, 错误消息)
        """
        endpoint = "/exe/v1/goods/pending"
        return self.api_client.get(endpoint, appid, appsecret)
    
    def upload_file(self, appid: str, appsecret: str, goods_id: int, file_type: str,
                   field_name: str, file_path: str, original_url: str, goods_platform_id: int = None) -> <PERSON><PERSON>[bool, Dict[str, Any], str]:
        """
        上传文件到服务器

        Args:
            appid: 应用ID
            appsecret: 应用密钥
            goods_id: SQLite数据库中的商品ID
            file_type: 文件类型 (image, video, pdf)
            field_name: 字段名
            file_path: 本地文件路径
            original_url: 原始URL
            goods_platform_id: 平台商品ID (Laravel后端实际需要的值)

        Returns:
            (是否成功, 响应数据, 错误消息)
        """
        if not os.path.exists(file_path):
            return False, {}, f"文件不存在: {file_path}"

        endpoint = "/exe/v1/goods/upload-file"

        # 准备文件上传数据
        try:
            # 映射文件类型到Laravel验证规则期望的值
            normalized_file_type = self._normalize_file_type(file_type)

            # 确保goods_id为字符串类型，符合multipart/form-data格式要求
            data = {
                'goods_id': str(goods_id),
                'file_type': normalized_file_type,
                'field_type': file_type,
                'field_name': field_name,
                'original_url': original_url
            }

            # 如果提供了goods_platform_id，则添加到请求数据中
            if goods_platform_id is not None:
                data['goods_platform_id'] = str(goods_platform_id)

            # 读取文件内容并准备files字典
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # 获取文件名
            file_name = os.path.basename(file_path)

            # 准备files字典，使用元组格式确保正确的multipart编码
            files = {
                'file': (file_name, file_content, self._get_content_type(file_path))
            }

            # 使用POST方法上传文件
            return self.api_client.post_file(endpoint, appid, appsecret, data, files)

        except Exception as e:
            return False, {}, f"文件本地化失败: {str(e)}"

    def _normalize_file_type(self, file_type: str) -> str:
        """
        将数据库中的文件类型映射到Laravel验证规则期望的值

        Args:
            file_type: 数据库中的文件类型

        Returns:
            Laravel验证规则期望的文件类型 (image, video, pdf)
        """
        # 映射规则：将具体的字段类型映射到通用类型
        type_mapping = {
            # 图片类型
            'goods_pic': 'image',
            'goods_detail': 'image',
            'instruction_images': 'image',
            'sku_thumb': 'image',
            'image': 'image',

            # 视频类型
            'goods_video': 'video',
            'video': 'video',

            # PDF类型
            'goods_pdf': 'pdf',
            'pdf': 'pdf'
        }

        # 返回映射后的类型，如果没有找到映射则默认为image
        return type_mapping.get(file_type.lower(), 'image')

    def _get_content_type(self, file_path: str) -> str:
        """
        根据文件扩展名获取Content-Type

        Args:
            file_path: 文件路径

        Returns:
            Content-Type字符串
        """
        import mimetypes
        content_type, _ = mimetypes.guess_type(file_path)
        return content_type or 'application/octet-stream'

    def update_goods_status(self, appid: str, appsecret: str, goods_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any], str]:
        """
        更新商品状态

        Args:
            appid: 应用ID
            appsecret: 应用密钥
            goods_data: 完整的商品数据（包含本地化后的文件路径）

        Returns:
            (是否成功, 响应数据, 错误消息)
        """
        endpoint = "/exe/v1/goods/update-status"
        
        # 构建符合Laravel接口要求的参数格式
        request_data = {
            'goods_id': goods_data.get('goods_id'),
            'field_updates': {},
            'mark_completed': True  # 默认标记为处理完成
        }
        
        # 从商品数据中提取需要更新的字段
        media_files = goods_data.get('media_files', {})

        
        # 按类型分组处理文件
        type_groups = {
            'goods_pic': [],
            'goods_detail': [],
            'instruction_images': [],
            'sku_thumb': [],
            'sku_skc_gallery': [],
            'goods_video': [],
            'goods_pdf': []
        }
        
        # 处理图片文件
        images = media_files.get('images', [])
        for img in images:
            img_type = img.get('type', '')
            img_url = img.get('url', '')
            img_field = img.get('field', '')
            if img_url:
                if img_type == 'goods_pic':
                    goods_pic = {
                        'url': img_url,
                        'field': img_field
                    }
                    type_groups['goods_pic'].append(goods_pic)
                elif img_type == 'goods_detail':
                    goods_detail = {
                        'url': img_url,
                        'field': img_field
                    }
                    type_groups['goods_detail'].append(goods_detail)
                elif img_type == 'instruction_images':
                    # instruction_images需要包含model_id
                    instruction_img = {
                        'url': img_url,
                        'field': img_field,
                        'model_id': img.get('model_id')
                    }
                    type_groups['instruction_images'].append(instruction_img)
                elif img_type == 'sku_thumb':
                    # sku_thumb需要包含sku_id
                    sku_thumb = {
                        'url': img_url,
                        'field': img_field,
                        'sku_id': img.get('sku_id')
                    }
                    type_groups['sku_thumb'].append(sku_thumb)
                elif img_type == 'sku_skc_gallery':
                    # sku_skc_gallery需要包含sku_id
                    skc_gallery = {
                        'url': img_url,
                        'field': img_field,
                        'sku_id': img.get('sku_id')
                    }
                    type_groups['sku_skc_gallery'].append(skc_gallery)
        
        # 处理视频文件
        videos = media_files.get('videos', [])
        for video in videos:
            video_type = video.get('type', '')
            video_url = video.get('url', '')
            video_field = video.get('field', '')
            
            if video_url and video_type == 'goods_video':
                goods_video = {
                    'url': video_url,
                    'field': video_field
                }
                type_groups['goods_video'].append(goods_video)
        
        # 处理PDF文件
        pdfs = media_files.get('pdfs', [])
        for pdf in pdfs:
            pdf_type = pdf.get('type', '')
            pdf_url = pdf.get('url', '')
            pdf_field = pdf.get('field', '')
            if pdf_url and pdf_type == 'goods_pdf':
                goods_pdf = {
                    'url': pdf_url,
                    'field': pdf_field
                }
                type_groups['goods_pdf'].append(goods_pdf)
        
        # 构建field_updates
        field_updates = {}
        
        # 处理数组类型字段
        if type_groups['goods_pic']:
            field_updates['goods_pic'] = type_groups['goods_pic']
        if type_groups['goods_detail']:
            field_updates['goods_detail'] = type_groups['goods_detail']
        
        # 处理instruction_images（需要model_id）
        if type_groups['instruction_images']:
            field_updates['instruction_images'] = type_groups['instruction_images']
        
        # 处理sku_thumb（需要sku_id）
        if type_groups['sku_thumb']:
            field_updates['sku_thumb'] = type_groups['sku_thumb']
        
        # 处理sku_skc_gallery（需要sku_id）
        if type_groups['sku_skc_gallery']:
            field_updates['sku_skc_gallery'] = type_groups['sku_skc_gallery']
        
        # 处理单个URL字段
        if type_groups['goods_video']:
            field_updates['goods_video'] = type_groups['goods_video'][0]  # 单个视频URL
        if type_groups['goods_pdf']:
            field_updates['goods_pdf'] = type_groups['goods_pdf'][0]  # 单个PDF URL
        
        # 合并到请求数据中
        request_data['field_updates'] = field_updates

        # 调用更新商品状态接口
        success, response_data, error_msg = self.api_client.put(endpoint, appid, appsecret, json_data=request_data)

        # 如果更新失败，调用设置错误状态接口
        if not success:
            goods_id = goods_data.get('goods_id')
            if goods_id:
                try:
                    # 调用设置图片本地化错误状态接口
                    error_success, _, error_error_msg = self.set_image_local_error(appid, appsecret, goods_id)
                    if error_success:
                        # 在原错误消息中添加错误状态设置成功的信息
                        error_msg = f"{error_msg} (已标记为错误状态)"
                    else:
                        # 如果设置错误状态也失败，记录但不影响原始错误
                        error_msg = f"{error_msg} (设置错误状态失败: {error_error_msg})"
                except Exception as e:
                    # 设置错误状态时发生异常，记录但不影响原始错误
                    error_msg = f"{error_msg} (设置错误状态异常: {str(e)})"

        return success, response_data, error_msg

    def set_image_local_error(self, appid: str, appsecret: str, goods_id: int) -> Tuple[bool, Dict[str, Any], str]:
        """
        设置商品图片本地化错误状态

        Args:
            appid: 应用ID
            appsecret: 应用密钥
            goods_id: 商品ID

        Returns:
            (是否成功, 响应数据, 错误消息)
        """
        endpoint = "/exe/v1/goods/set-image-local-error"

        request_data = {
            'goods_id': goods_id
        }

        return self.api_client.post(endpoint, appid, appsecret, json_data=request_data)

    def validate_config(self, appid: str, appsecret: str) -> Tuple[bool, str, dict]:
        """
        验证配置是否有效，并返回用户信息

        Args:
            appid: 应用ID
            appsecret: 应用密钥

        Returns:
            (是否有效, 错误消息, 用户信息)
        """
        # 直接调用商品接口进行验证（这是唯一可用的验证方式）
        success, data, error_msg = self.get_pending_goods(appid, appsecret)

        if success:
            # 从商品接口的响应中提取用户信息
            extracted_user_data = {}
            if isinstance(data, dict) and data.get('code') == 200:
                goods_data = data.get('data', {})
                if 'user_id' in goods_data:
                    extracted_user_data['user_id'] = goods_data['user_id']
                if 'phone' in goods_data:
                    extracted_user_data['phone'] = goods_data['phone']

            return True, "配置验证成功", extracted_user_data
        else:
            return False, error_msg or "配置验证失败", {}


