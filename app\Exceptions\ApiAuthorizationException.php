<?php

declare(strict_types=1);

namespace App\Exceptions;

/**
 * API权限异常
 * 用于EXE API接口的权限不足场景
 */
class ApiAuthorizationException extends \Exception
{
    protected $code = 403;
    protected $message = '无权限访问该资源';

    public function __construct(?string $message = null, ?int $code = null, ?\Throwable $previous = null)
    {
        parent::__construct(
            $message ?? $this->message,
            $code ?? $this->code,
            $previous
        );
    }

    /**
     * 获取异常的响应数据
     *
     * @return array
     */
    public function getResponseData(): array
    {
        return [
            'code' => $this->getCode(),
            'message' => $this->getMessage(),
            'data' => null,
            'errors' => [
                'type' => 'authorization_error',
                'detail' => '您没有权限访问该资源'
            ]
        ];
    }
}