<?php
namespace App\Service\User;

use App\Exceptions\MyException;
use App\Models\User\User as UserModel;
use App\Service\BaseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ApiCredentialService extends BaseService
{
    /**
     * 为用户生成或更新 API 凭证
     *
     * @param $user_id 用户id
     * @return array 返回包含 appid、appsecret、appstatus 的数组
     * @throws MyException 当生成失败时抛出异常
     */
    public function generateCredentials(int $user_id): array
    {
        DB::beginTransaction();
        try {
            // 使用排他锁防止并发问题
            $user = UserModel::where('id', $user_id)->lockForUpdate()->first();
            
            if (!$user) {
                throw new MyException('用户不存在');
            }

            // 验证用户的 appstatus 字段值是否为 1，只有为 1 才允许操作
            if ($user->appstatus != 1) {
                throw new MyException('用户API访问权限已被禁用，无法生成凭证');
            }

            // 检查用户是否已有 appid
            if (!empty($user->appid)) {
                // 用户已有 appid，仅更新 appsecret
                $appSecret = $this->generateAppSecret();
                
                $user->appsecret = $appSecret;
                $user->appstatus = 1; // 确保状态为启用
                $user->save();
                
                $result = [
                    'appid' => $user->appid,
                    'appsecret' => $appSecret,
                    'appstatus' => 1
                ];
            } else {
                // 用户没有 appid，生成新的 appid 和 appsecret
                $appId = $this->generateAppId();
                $appSecret = $this->generateAppSecret();
                
                $user->appid = $appId;
                $user->appsecret = $appSecret;
                $user->appstatus = 1;
                $user->save();
                
                $result = [
                    'appid' => $appId,
                    'appsecret' => $appSecret,
                    'appstatus' => 1
                ];
            }

            DB::commit();
            return $result;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('API凭证生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成唯一的 AppID
     *
     * @return string 生成的 AppID
     * @throws MyException 当无法生成唯一 AppID 时抛出异常
     */
    private function generateAppId(): string
    {
        $maxAttempts = 50; // 最大尝试次数
        $attempts = 0;
        
        do {
            // 格式: KJF + 10位纯数字随机字符串 + 6位纯数字随机字符串
            $randomNum1 = str_pad(mt_rand(0, 9999999999), 10, '0', STR_PAD_LEFT);
            $randomNum2 = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
            $appId = 'KJF' . $randomNum1 . $randomNum2;
            
            // 确保长度不超过32字符
            if (strlen($appId) > 32) {
                $appId = substr($appId, 0, 32);
            }
            
            // 检查唯一性
            if ($this->isAppIdUnique($appId)) {
                return $appId;
            }
            
            $attempts++;
            
            // 如果重复，稍微延迟一下再重试
            if ($attempts > 10) {
                usleep(1000); // 1毫秒延迟
            }
            
        } while ($attempts < $maxAttempts);
        
        throw new MyException('无法生成唯一的AppID，请重试');
    }

    /**
     * 生成安全的 AppSecret
     *
     * @return string 生成的 AppSecret
     */
    private function generateAppSecret(): string
    {
        // 生成32位随机字符串
        $randomStr = Str::random(32);
        
        // 进行 SHA256 哈希处理，确保安全性和固定长度
        $appSecret = hash('sha256', $randomStr . time() . mt_rand());
        
        return $appSecret;
    }

    /**
     * 验证 AppID 的唯一性
     *
     * @param string $appId 要验证的 AppID
     * @return bool 如果唯一返回 true，否则返回 false
     */
    private function isAppIdUnique(string $appId): bool
    {
        return !UserModel::where('appid', $appId)->exists();
    }
}