<template>
  <div class="app-container">
    <!-- 顶部标题栏 -->
    <div class="header-bar">
      <div class="app-title">{{ appName }} - v{{ currentVersion }}</div>
      <div class="header-actions">
        <div class="pin-icon" @click="togglePopupWindow" title="切换独立窗口">
          <el-icon><Expand /></el-icon>
        </div>
      </div>
    </div>

    <div class="chrome_container">
      <!-- 登录/注册表单 -->
      <div v-if="!is_login" class="content-area">
        <div class="card auth-card">
          <div class="auth-tabs">
            <div
              :class="['auth-tab', activeTab === 'login' ? 'active' : '']"
              @click="activeTab = 'login'"
            >
              登录
            </div>
            <div
              :class="['auth-tab', activeTab === 'register' ? 'active' : '']"
              @click="activeTab = 'register'"
            >
              注册
            </div>
          </div>

          <!-- 登录表单 -->
          <el-form v-if="activeTab === 'login'" :model="loginForm" :rules="loginRules" ref="loginFormRef">
            <el-form-item prop="phone">
              <el-input
                v-model="loginForm.phone"
                placeholder="手机号"
              ></el-input>
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="密码"
              ></el-input>
            </el-form-item>

            <el-button type="primary" class="auth-btn" @click="handleLogin" :loading="loginLoading">登录</el-button>
          </el-form>

          <!-- 注册表单 -->
          <el-form v-else :model="registerForm" :rules="registerRules" ref="registerFormRef">
            <el-form-item prop="phone">
              <el-input
                v-model="registerForm.phone"
                placeholder="手机号"
              ></el-input>
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="密码"
              ></el-input>
            </el-form-item>

            <el-form-item prop="confirmPassword">
              <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="确认密码"
              ></el-input>
            </el-form-item>

            <el-form-item prop="captcha">
              <div class="captcha-container">
                <el-input
                  v-model="registerForm.captcha"
                  placeholder="验证码"
                  class="captcha-input"
                ></el-input>
                <div class="captcha-image" @click="refreshCaptcha">
                  <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                  <div v-else class="captcha-loading">{{ captchaLoading ? '加载中...' : '点击获取' }}</div>
                </div>
              </div>
            </el-form-item>

            <el-button type="primary" class="auth-btn" @click="handleRegister" :loading="registerLoading">注册</el-button>
          </el-form>
        </div>

        <div class="card qrcode-card">
          <div class="qrcode-container">
            <img :src="config.apiKfUrl" alt="客服二维码" class="qrcode-img" />
            <div class="qrcode-text">咨询请扫码添加客服</div>
          </div>
        </div>
      </div>

      <!-- 登录后显示用户中心 -->
      <div v-else class="content-area">
        <div class="card user-card">
          <div class="user-header">
            <div class="user-title">
              欢迎使用
            </div>
          </div>

          <div class="user-info">
            <div class="info-row">
              <span class="info-label">手机号码:</span>
              <span class="info-value">{{ userInfo.phone }}
                <span v-if="userInfo.isVip" class="vip-icon-small">👑</span>
              </span>
            </div>
            <div class="info-row">
              <span class="info-label">账号类型:</span>
              <span class="info-value" :class="{ 'sub-account': userInfo.isSub, 'main-account': !userInfo.isSub }">
                {{ userInfo.isSub ? '子账号' : '主账号' }}
              </span>
            </div>
            <div class="info-row">
              <span class="info-label">VIP会员:</span>
              <span class="info-value" :class="{ 'vip-active': userInfo.isVip, 'vip-inactive': !userInfo.isVip }">
                {{ userInfo.isVip ? '已开通' : '未开通' }}
              </span>
            </div>
            <div v-if="userInfo.isVip" class="info-row">
              <span class="info-label">当前积分:</span>
              <span class="info-value points-value">{{ userInfo.points || 0 }}</span>
            </div>
            <div v-if="userInfo.isVip" class="info-row">
              <span class="info-label">有效期至:</span>
              <span class="info-value expiry-date-value">{{ userInfo.expiryDate || '未设置' }}</span>
            </div>
          </div>

          <div class="menu-list">
            <!-- 子账号用户只显示用户中心菜单 -->
            <template v-if="userInfo.isSub">
              <div class="menu-item" @click="openUserCenter">
                <span>用户中心</span>
              </div>
            </template>
            
            <!-- 主账号用户显示原有菜单逻辑 -->
            <template v-else>
              <div v-if="userInfo.isVip" class="menu-item" @click="openUserCenter">
                <span>用户中心</span>
              </div>

              <div v-if="!userInfo.isVip" class="menu-item" @click="openCardUsage">
                <span>卡密激活</span>
              </div>

              <div class="menu-item" @click="openTemuHomepage">
                <span>Temu首页</span>
              </div>
              <div class="menu-item" @click="openN11ProductList">
                <span>N11商品列表</span>
              </div>
            </template>
          </div>

          <div class="logout-btn" @click="handleLogout">退出登录</div>
        </div>

        <!-- 添加客服二维码卡片 -->
        <div class="card qrcode-card">
          <div class="qrcode-container">
            <img :src="config.apiKfUrl" alt="客服二维码" class="qrcode-img" />
            <div class="qrcode-text">咨询请扫码添加客服</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage, ElForm, ElFormItem, ElInput, ElButton, ElIcon } from 'element-plus';
import { Expand } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { registerUser, getCaptcha, loginUser } from '@/utils/protocol/user/user';
import * as utils_new from '@/utils/new/utils';
import config from '@/config';

// 从manifest.json获取应用信息
const appManifest = chrome.runtime.getManifest();
const appName = ref(appManifest.name);
const currentVersion = ref(appManifest.version);

// 登录状态
const is_login = ref(false);
const loginFormRef = ref<FormInstance>();
const registerFormRef = ref<FormInstance>();
const activeTab = ref('login'); // 默认显示登录表单
const loginLoading = ref(false); // 登录按钮加载状态
const registerLoading = ref(false); // 注册按钮加载状态

// 用户信息
const userInfo = reactive({
  phone: '',
  expiryDate: '',
  isVip: true,
  userId: undefined as number | undefined,
  isAdmin: false,
  isCardAdmin: false,
  isSub: false,          // 新增：是否是子账号
  appId: undefined as string | undefined,      // 新增：应用ID
  appStatus: undefined as number | undefined,  // 新增：应用状态
  token: '',
  points: 0
});

// 登录表单
const loginForm = reactive({
  phone: '',
  password: ''
});

// 注册表单
const registerForm = reactive({
  phone: '',
  password: '',
  confirmPassword: '',
  captcha: ''
});

// 验证码相关
const captchaImage = ref('');
const captchaKey = ref('');
const captchaLoading = ref(false);

// 登录表单验证规则
const loginRules = reactive<FormRules>({
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
});

// 注册表单验证规则
const registerRules = reactive<FormRules>({
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
});



// 登录处理
const handleLogin = () => {
  loginFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        // 设置加载状态
        loginLoading.value = true;

        // 调用登录API
        const result = await loginUser(
          loginForm.phone,
          loginForm.password
        );

        if (result) {
          // 登录成功，更新状态
          is_login.value = true;
          userInfo.phone = result.phone;
          userInfo.expiryDate = result.vip_end_time;
          userInfo.isVip = result.is_vip === 1;
          userInfo.isAdmin = result.is_admin === 1;
          userInfo.isCardAdmin = result.is_card_admin === 1;
          userInfo.isSub = result.is_sub === 1;          // 新增：子账号状态
          userInfo.appId = result.appid;                 // 新增：应用ID
          userInfo.appStatus = result.appstatus;         // 新增：应用状态
          userInfo.token = result.token;
          userInfo.userId = result.id || undefined;
          userInfo.points = result.points || 0;
          // 存储登录信息到chrome.storage.sync (用于UI显示)
          await utils_new.setSyncStorageData({
            is_login: true,
            phone: userInfo.phone,
            expiryDate: userInfo.expiryDate,
            isVip: userInfo.isVip,
            is_admin: userInfo.isAdmin,
            is_card_admin: userInfo.isCardAdmin,
            is_sub: userInfo.isSub,           // 新增：子账号状态
            app_id: userInfo.appId,           // 新增：应用ID
            app_status: userInfo.appStatus,   // 新增：应用状态
            userId: userInfo.userId,
            points: userInfo.points
          });

          // 存储token到chrome.storage.local (用于API请求认证)
          await utils_new.setLocalStorageData({
            token: userInfo.token
          });
          console.log("登录成功，更新用户token:", userInfo.token);

          // 发送登录成功消息到所有相关页面
          try {
            // 发送消息到所有标签页的content scripts
            chrome.tabs.query({}, (tabs: any) => {
              tabs.forEach((tab: any) => {
                if (tab.id) {
                  chrome.tabs.sendMessage(tab.id, {
                    type: 'USER_LOGIN_SUCCESS',
                    timestamp: Date.now(),
                    userInfo: {
                      phone: userInfo.phone,
                      isVip: userInfo.isVip,
                      expiryDate: userInfo.expiryDate,
                      isAdmin: userInfo.isAdmin,
                      isCardAdmin: userInfo.isCardAdmin,
                      isSub: userInfo.isSub,           // 新增：子账号状态
                      appId: userInfo.appId,           // 新增：应用ID
                      appStatus: userInfo.appStatus,   // 新增：应用状态
                      userId: userInfo.userId,
                      points: userInfo.points
                    }
                  }).catch(() => {
                    // 忽略无法发送消息的标签页（如chrome://页面）
                  });
                }
              });
            });

            // 发送消息到background script
            chrome.runtime.sendMessage({
              type: 'USER_LOGIN_SUCCESS',
              timestamp: Date.now(),
              userInfo: {
                phone: userInfo.phone,
                isVip: userInfo.isVip,
                expiryDate: userInfo.expiryDate,
                isAdmin: userInfo.isAdmin,
                isCardAdmin: userInfo.isCardAdmin,
                isSub: userInfo.isSub,           // 新增：子账号状态
                appId: userInfo.appId,           // 新增：应用ID
                appStatus: userInfo.appStatus,   // 新增：应用状态
                userId: userInfo.userId,
                points: userInfo.points
              }
            }).catch(() => {
              // 忽略发送失败的情况
            });

            console.log('登录成功通知已发送到所有页面');
          } catch (error) {
            console.error('发送登录成功通知失败:', error);
          }

          ElMessage.success('登录成功');
        }
      } catch (error) {
        console.error('登录失败:', error);
        ElMessage.error('登录失败，请检查账号密码');
      } finally {
        // 无论成功还是失败，都关闭加载状态
        loginLoading.value = false;
      }
    }
  });
};

// 获取验证码
const fetchCaptcha = async () => {
  try {
    captchaLoading.value = true;
    const result = await getCaptcha();
    if(result && result){
      // 检查image_base64是否已包含data:image前缀，如果没有则添加
      let imageBase64 = result.image_base64;
      if (imageBase64 && !imageBase64.startsWith('data:image')) {
        imageBase64 = `data:image/png;base64,${imageBase64}`;
      }
      captchaImage.value = imageBase64;
      captchaKey.value = result.image_key;

      console.log('验证码图片URL:', captchaImage.value.substring(0, 50) + '...');
    }
  } catch (error) {
    console.error('获取验证码失败:', error);
    ElMessage.error('获取验证码失败，请刷新重试');
  } finally {
    captchaLoading.value = false;
  }
};

// 刷新验证码
const refreshCaptcha = () => {
  captchaImage.value = '';
  fetchCaptcha();
};

// 注册处理
const handleRegister = () => {
  registerFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        // 设置加载状态
        registerLoading.value = true;

        // 调用注册API
        const result = await registerUser(
          registerForm.phone,
          registerForm.password,
          registerForm.captcha,
          captchaKey.value
        );
        if(result){
          if(result.result == 40001){
            refreshCaptcha();
            ElMessage.error('验证码错误，请重新输入');
            return;
          }
          ElMessage.success('注册成功，请登录');
          activeTab.value = 'login';
          loginForm.phone = registerForm.phone;

          // 清空注册表单
          registerForm.password = '';
          registerForm.confirmPassword = '';
          registerForm.captcha = '';
        }
      } catch (error) {
        console.error('注册失败:', error);
        // 刷新验证码
        refreshCaptcha();
      } finally {
        // 无论成功还是失败，都关闭加载状态
        registerLoading.value = false;
      }
    }
  });
};

// 退出登录
const handleLogout = async () => {
  is_login.value = false;

  // 清除存储的登录信息
  await utils_new.setSyncStorageData({
    is_login: false
  });
  chrome.storage.sync.remove(['phone', 'expiryDate', 'isVip', 'is_admin', 'is_card_admin']);
  chrome.storage.local.remove(['token']);

  // 发送退出登录消息到所有相关页面
  try {
    // 发送消息到所有标签页的content scripts
    chrome.tabs.query({}, (tabs: any) => {
      tabs.forEach((tab: any) => {
        if (tab.id) {
          chrome.tabs.sendMessage(tab.id, {
            type: 'USER_LOGOUT',
            timestamp: Date.now()
          }).catch(() => {
            // 忽略无法发送消息的标签页（如chrome://页面）
          });
        }
      });
    });

    // 发送消息到background script
    chrome.runtime.sendMessage({
      type: 'USER_LOGOUT',
      timestamp: Date.now()
    }).catch(() => {
      // 忽略发送失败的情况
    });

    console.log('退出登录通知已发送到所有页面');
  } catch (error) {
    console.error('发送退出登录通知失败:', error);
  }

  ElMessage.success('已退出登录');
};

// 打开Temu首页
const openTemuHomepage = () => {
  chrome.tabs.create({ url: 'https://www.temu.com' });
};

// 打开N11商品列表
const openN11ProductList = () => {
  const targetUrl = 'https://so.n11.com/selleroffice/v2/product/products';
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs: any) => {
    const currentTab = tabs[0];
    if (currentTab && currentTab.id && currentTab.url && currentTab.url.includes('so.n11.com/selleroffice/v2/product/products')) {
      chrome.tabs.reload(currentTab.id);
    } else {
      chrome.tabs.create({ url: targetUrl });
    }
  });
};

// 打开用户中心
const openUserCenter = () => {
  // 使用chrome-extension://路径打开本地页面
  const extensionId = chrome.runtime.id;

  // 创建新标签页打开用户中心
  chrome.tabs.create({
    url: `chrome-extension://${extensionId}/web/home.html`
  });
};

// 打开卡密管理
const openCardManagement = () => {
  // 使用chrome-extension://路径打开本地页面
  const extensionId = chrome.runtime.id;

  // 创建新标签页打开卡密管理页面
  chrome.tabs.create({
    url: `chrome-extension://${extensionId}/web/home.html#/card-management`
  });
};

// 打开卡密激活
const openCardUsage = () => {
  // 使用chrome-extension://路径打开本地页面
  const extensionId = chrome.runtime.id;

  // 创建新标签页打开卡密激活页面
  chrome.tabs.create({
    url: `chrome-extension://${extensionId}/web/home.html#/card-usage`
  });
};

// 切换独立窗口
const togglePopupWindow = () => {
  try {
    // 创建一个新的独立窗口，使用较小的尺寸以确保内容居中显示
    const popupUrl = chrome.runtime.getURL('popup.html');

    chrome.windows.create({
      url: popupUrl,
      type: 'popup',
      width: 380,  // 减小窗口宽度
      height: 580, // 减小窗口高度
      focused: true
    }, (createdWindow: any) => {
      console.log('独立窗口已创建:', createdWindow);

      // 关闭当前popup
      if (createdWindow) {
        window.close();
      }
    });
  } catch (error) {
    console.error('创建独立窗口失败:', error);
    ElMessage.error('创建独立窗口失败');
  }
};

// 监听标签页切换，当切换到注册页面时获取验证码
watch(() => activeTab.value, (newValue) => {
  if (newValue === 'register') {
    fetchCaptcha();
  }
});

// 处理登录失效的函数
const handleLoginExpired = async (reason?: string) => {
  console.log('收到登录失效通知，更新UI状态', reason ? `原因: ${reason}` : '');

  // 更新UI状态
  is_login.value = false;
  userInfo.phone = '';
  userInfo.expiryDate = '';
  userInfo.isVip = false;
  userInfo.isAdmin = false;
  userInfo.isCardAdmin = false;
  userInfo.isSub = false;          // 新增：重置子账号状态
  userInfo.appId = undefined;      // 新增：重置应用ID
  userInfo.appStatus = undefined;  // 新增：重置应用状态
  userInfo.token = '';
  userInfo.userId = undefined;
  userInfo.points = 0;

  // 根据不同原因显示不同的提示消息
  if (reason === 'empty_phone') {
    //ElMessage.warning('您的登录已失效，请重新登录');
  } else {
    ElMessage.warning('您的登录已失效，请重新登录');
  }
};

// 处理卡密激活成功的函数
const handleCardUsageSuccess = async (cardUsageResult: any) => {
  console.log('收到卡密激活成功通知，更新用户信息', cardUsageResult);

  try {
    // 延迟一下再获取，确保后端数据已更新
    setTimeout(async () => {
      try {
        // 重新获取用户信息以确保数据最新
        const syncData = await utils_new.getSyncStorageData(['is_login', 'phone', 'expiryDate', 'isVip', 'is_admin', 'is_card_admin', 'is_sub', 'app_id', 'app_status', 'userId', 'points']) as {
          is_login?: boolean;
          phone?: string;
          expiryDate?: string;
          isVip?: boolean;
          is_admin?: boolean;
          is_card_admin?: boolean;
          is_sub?: boolean;
          app_id?: string;
          app_status?: number;
          userId?: number;
          points?: number;
        };

        if (syncData.is_login && syncData.phone) {
          // 更新用户信息显示
          userInfo.phone = syncData.phone;
          userInfo.expiryDate = syncData.expiryDate || '';
          userInfo.isVip = syncData.isVip || false;
          userInfo.isAdmin = syncData.is_admin || false;
          userInfo.isCardAdmin = syncData.is_card_admin || false;
          userInfo.isSub = syncData.is_sub || false;          // 新增：子账号状态
          userInfo.appId = syncData.app_id;                   // 新增：应用ID
          userInfo.appStatus = syncData.app_status;           // 新增：应用状态
          userInfo.userId = syncData.userId || undefined;
          userInfo.points = syncData.points || 0;

          console.log('用户信息已更新:', {
            phone: userInfo.phone,
            isVip: userInfo.isVip,
            expiryDate: userInfo.expiryDate
          });

          ElMessage.success('用户信息已更新');
        }
      } catch (error) {
        console.error('更新用户信息失败:', error);
      }
    }, 1000); // 延迟1秒
  } catch (error) {
    console.error('处理卡密激活成功通知失败:', error);
  }
};

// 处理用户信息更新的函数
const handleUserInfoUpdated = async () => {
  console.log('收到用户信息更新通知，刷新用户信息');

  try {
    // 重新获取用户信息以确保数据最新
    const syncData = await utils_new.getSyncStorageData(['is_login', 'phone', 'expiryDate', 'isVip', 'is_admin', 'is_card_admin', 'is_sub', 'app_id', 'app_status', 'userId', 'points']) as {
      is_login?: boolean;
      phone?: string;
      expiryDate?: string;
      isVip?: boolean;
      is_admin?: boolean;
      is_card_admin?: boolean;
      is_sub?: boolean;
      app_id?: string;
      app_status?: number;
      userId?: number;
      points?: number;
    };

    if (syncData.is_login && syncData.phone) {
      // 更新用户信息显示
      userInfo.phone = syncData.phone;
      userInfo.expiryDate = syncData.expiryDate || '';
      userInfo.isVip = syncData.isVip || false;
      userInfo.isAdmin = syncData.is_admin || false;
      userInfo.isCardAdmin = syncData.is_card_admin || false;
      userInfo.isSub = syncData.is_sub || false;          // 新增：子账号状态
      userInfo.appId = syncData.app_id;                   // 新增：应用ID
      userInfo.appStatus = syncData.app_status;           // 新增：应用状态
      userInfo.userId = syncData.userId || undefined;
      userInfo.points = syncData.points || 0;

      console.log('popup用户信息已更新:', {
        phone: userInfo.phone,
        isVip: userInfo.isVip,
        points: userInfo.points,
        expiryDate: userInfo.expiryDate
      });
    }
  } catch (error) {
    console.error('刷新popup用户信息失败:', error);
  }
};

// 监听登录失效消息
const setupLoginExpiredListener = () => {
  // 监听来自background script或content script的消息
  const messageListener = (message: any, _sender: any, _sendResponse: any) => {
    if (message.type === 'LOGIN_EXPIRED') {
      console.log('popup收到登录失效消息:', message);
      handleLoginExpired(message.reason);
    } else if (message.type === 'CARD_USAGE_SUCCESS') {
      console.log('popup收到卡密激活成功消息:', message);
      handleCardUsageSuccess(message.cardUsageResult);
    } else if (message.type === 'USER_INFO_UPDATED') {
      console.log('popup收到用户信息更新消息:', message);
      handleUserInfoUpdated();
    }
  };
  chrome.runtime.onMessage.addListener(messageListener);

  // 监听storage变化
  const storageListener = (changes: any, namespace: any) => {
    if (namespace === 'sync') {
      // 检查登录状态变化
      if (changes.is_login) {
        const newValue = changes.is_login.newValue;
        if (newValue === false && is_login.value === true) {
          console.log('检测到storage中登录状态变化，更新UI');
          handleLoginExpired();
        }
      }

      // 检查手机号码变化，如果手机号码被清空且当前显示为已登录状态，则执行退出登录
      if (changes.phone && is_login.value === true) {
        const newPhone = changes.phone.newValue;
        if (!newPhone || newPhone.trim() === '') {
          console.log('检测到storage中手机号码被清空，执行退出登录');
          handleLoginExpired('empty_phone');
        }
      }
    }
  };
  chrome.storage.onChanged.addListener(storageListener);
};

// 监听VIP权限不足消息
const setupVipPermissionListener = () => {
  // 监听来自content script的VIP权限消息
  const messageListener = (message: any, _sender: any, _sendResponse: any) => {
    if (message.type === 'VIP_PERMISSION_REQUIRED') {
      console.log('popup收到VIP权限不足消息:', message);

      // 显示VIP权限不足提示
      ElMessage({
        message: message.message || '您不是VIP会员或积分不足，请升级会员后使用',
        type: 'warning',
        duration: 3000,
      });

      // 跳转到卡密激活页面（打开新标签页）
      openCardUsagePage();
    }
  };
  chrome.runtime.onMessage.addListener(messageListener);
};

// 打开卡密激活页面
const openCardUsagePage = () => {
  // 使用chrome-extension://路径打开本地页面
  const extensionId = chrome.runtime.id;
  const cardUsageUrl = `chrome-extension://${extensionId}/web/index.html#/card-usage`;

  chrome.tabs.create({
    url: cardUsageUrl,
    active: true
  }, (tab: any) => {
    console.log('卡密激活页面已打开:', tab);
  });
};

// 组件挂载时检查登录状态
onMounted(async () => {
  try {
    // 设置登录失效监听器
    setupLoginExpiredListener();

    // 设置VIP权限监听器
    setupVipPermissionListener();

    // 从chrome.storage获取登录状态
    const syncData = await utils_new.getSyncStorageData(['is_login', 'phone', 'expiryDate', 'isVip', 'is_admin', 'is_card_admin', 'is_sub', 'app_id', 'app_status', 'userId', 'points']) as {
      is_login?: boolean;
      phone?: string;
      expiryDate?: string;
      isVip?: boolean;
      is_admin?: boolean;
      is_card_admin?: boolean;
      is_sub?: boolean;
      app_id?: string;
      app_status?: number;
      userId?: number;
      points?: number;
    };

    if (syncData.is_login) {
      // 检查用户手机号码是否存在，如果为空则表示登录已失效
      if (!syncData.phone || syncData.phone.trim() === '') {
        console.log('检测到用户手机号码为空，登录已失效，执行退出登录');
        await handleLoginExpired('empty_phone');
        return;
      }

      is_login.value = true;
      userInfo.phone = syncData.phone || '';
      userInfo.expiryDate = syncData.expiryDate || '';
      userInfo.isVip = syncData.isVip || false;
      userInfo.isAdmin = syncData.is_admin || false;
      userInfo.isCardAdmin = syncData.is_card_admin || false;
      userInfo.isSub = syncData.is_sub || false;          // 新增：子账号状态
      userInfo.appId = syncData.app_id;                   // 新增：应用ID
      userInfo.appStatus = syncData.app_status;           // 新增：应用状态
      userInfo.userId = syncData.userId || undefined;
      userInfo.points = syncData.points || 0;

      // 获取token
      const localData = await utils_new.getLocalStorageData(['token']) as {
        token?: string;
      };
      if (localData.token) {
        userInfo.token = localData.token;
      }
    }

    // 获取扩展版本号
    currentVersion.value = chrome.runtime.getManifest().version;

    // 设置文档标题
    document.title = `独立窗口-${appName.value} - v${currentVersion.value}`;
  } catch (error) {
    console.error('初始化失败', error);
  }
});
</script>

<style scoped>
/* 全局样式 */
.app-container {
  width: 360px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #eef4ff;
  overflow: hidden;
  margin: 0 auto; /* 居中显示 */
  padding: 0;
  box-sizing: border-box;
}

/* 顶部标题栏 */
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  background-color: #eef4ff;
  border-bottom: 1px solid #e8e8e8;
}

.app-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.pin-icon {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.pin-icon:hover {
  background-color: #f0f0f0;
}

.pin-icon .el-icon {
  font-size: 18px;
  color: #409EFF;
  cursor: pointer;
}

/* 内容区域 */
.chrome_container {
  padding: 0;
  background-color: #eef4ff;
}

.content-area {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 12px;
}

/* 登录/注册表单 */
.auth-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.auth-tab {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  cursor: pointer;
  font-size: 16px;
  color: #606266;
  position: relative;
}

.auth-tab.active {
  color: #409EFF;
  font-weight: bold;
}

.auth-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 2px;
  background-color: #409EFF;
}

.auth-btn {
  width: auto;
  min-width: 80px;
  margin-top: 10px;
  margin-bottom: 5px;
  padding: 8px 20px;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* 二维码卡片 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px 0;
}

.qrcode-img {
  max-width: 128px;
  max-height: 128px;
  width: auto;
  height: auto;
  margin-bottom: 10px;
  border-radius: 4px;
}

.qrcode-text {
  text-align: center;
  color: #606266;
  font-size: 14px;
}

/* 用户中心 */
.user-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}

.user-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10px;
  color: #333;
}

.vip-icon {
  color: gold;
  margin-left: 5px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.info-label {
  width: 80px; /* 固定宽度 */
  text-align: right; /* 右对齐 */
  color: #606266;
  margin-right: 8px;
}

.info-value {
  flex: 1;
  color: #333;
}

.vip-active {
  color: #67c23a; /* 绿色 - 已开通 */
  font-weight: bold;
}

.vip-inactive {
  color: #e6a23c; /* 橙色 - 未开通 */
  font-weight: bold;
}

.sub-account {
  color: #409EFF; /* 蓝色 - 子账号 */
  font-weight: bold;
}

.main-account {
  color: #67c23a; /* 绿色 - 主账号 */
  font-weight: bold;
}

.vip-icon-small {
  color: gold;
  margin-left: 5px;
  font-size: 12px;
}

.expiry-date-value {
  color: #409EFF;
}

.points-value {
  color: #E6A23C;
  font-weight: bold;
}

.menu-list {
  margin-top: 10px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 8px;
  background-color: #f5f7fa;
  transition: background-color 0.3s;
}

.menu-item:hover {
  background-color: #e6f1fc;
}

.menu-item i {
  margin-right: 10px;
  color: #409EFF;
}

.logout-btn {
  text-align: center;
  color: #F56C6C;
  cursor: pointer;
  margin-top: 15px;
  font-size: 14px;
}

/* 验证码样式 */
.captcha-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 100px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.captcha-loading {
  font-size: 12px;
  color: #909399;
}
</style>
