import { createApp } from 'vue';
import App from './App.vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';

// 创建应用实例
const app = createApp(App);

// 使用ElementPlus
app.use(ElementPlus);

// 挂载应用
const container = document.createElement('div');
container.id = 'temu-search-extension';
document.body.appendChild(container);

app.mount(container);

console.log('Temu搜索页面扩展已加载');
