// 网络请求监听工具文件 - 基于Manifest V3的chrome.debugger API
declare const chrome: any;

import type { N11RejectedProduct, N11RejectedProductsResponse } from '@/utils/n11/n11RejectedApi';

/**
 * 网络监听器配置
 */
export interface NetworkListenerConfig {
  tabId: number;
  targetUrl?: string;
  timeout?: number;
  forceDetach?: boolean; // 是否启用智能debugger冲突检测和处理
}

/**
 * 网络监听结果
 */
export interface NetworkListenerResult {
  success: boolean;
  message: string;
  data?: any;
  fallbackMode?: boolean; // 是否为回退模式
}

/**
 * 捕获的请求数据
 */
export interface CapturedRequestData {
  requestId: string;
  url: string;
  method: string;
  postData?: any;
  timestamp: number;
}

/**
 * 捕获的响应数据
 */
export interface CapturedResponseData {
  requestId: string;
  url: string;
  statusCode: number;
  responseBody: any;
  timestamp: number;
}

/**
 * 全局监听器状态管理
 */
class NetworkListenerRegistry {
  private static listeners: Map<number, NetworkListener> = new Map();
  
  static register(tabId: number, listener: NetworkListener): void {
    // 如果已存在监听器，先清理
    if (this.listeners.has(tabId)) {
      const existingListener = this.listeners.get(tabId);
      if (existingListener) {
        existingListener.forceStop().catch(console.error);
      }
    }
    this.listeners.set(tabId, listener);
  }
  
  static unregister(tabId: number): void {
    this.listeners.delete(tabId);
  }
  
  static get(tabId: number): NetworkListener | undefined {
    return this.listeners.get(tabId);
  }
  
  static async cleanupAll(): Promise<void> {
    const promises = Array.from(this.listeners.values()).map(listener => 
      listener.forceStop().catch(console.error)
    );
    await Promise.all(promises);
    this.listeners.clear();
  }
}

/**
 * 网络监听器类
 */
export class NetworkListener {
  private tabId: number;
  private isAttached: boolean = false;
  private isListening: boolean = false;
  private capturedRequests: Map<string, CapturedRequestData> = new Map();
  private capturedResponses: CapturedResponseData[] = [];
  private responseCallback?: (data: CapturedResponseData) => void;
  private targetUrl: string;
  private forceDetach: boolean = false;
  private attachRetries: number = 0;
  private maxAttachRetries: number = 3;

  constructor(config: NetworkListenerConfig) {
    this.tabId = config.tabId;
    this.targetUrl = config.targetUrl || 'https://so.n11.com/api/seller-product-query/api/v1/product/internal/search';
    this.forceDetach = config.forceDetach || false;
    
    // 注册到全局监听器管理器
    NetworkListenerRegistry.register(this.tabId, this);
  }

  /**
   * 启动网络监听
   */
  async start(responseCallback?: (data: CapturedResponseData) => void): Promise<NetworkListenerResult> {
    try {
      this.responseCallback = responseCallback;

      // 附加到标签页（内置智能冲突检测和处理）
      const attachResult = await this.attachToTabWithRetry();
      if (!attachResult.success) {
        return attachResult;
      }

      // 启用网络域
      const enableResult = await this.enableNetworkDomain();
      if (!enableResult.success) {
        await this.detach();
        return enableResult;
      }

      // 设置事件监听器
      this.setupEventListeners();

      this.isListening = true;
      console.log(`网络监听器已启动，监听标签页 ${this.tabId}`);

      return { success: true, message: '网络监听器启动成功' };

    } catch (error: any) {
      console.error('启动网络监听器失败:', error);
      await this.cleanup();
      return { success: false, message: `启动失败: ${error.message}` };
    }
  }

  /**
   * 停止网络监听
   */
  async stop(): Promise<NetworkListenerResult> {
    try {
      await this.cleanup();
      console.log('网络监听器已停止');
      return { success: true, message: '网络监听器停止成功' };
    } catch (error: any) {
      console.error('停止网络监听器失败:', error);
      return { success: false, message: `停止失败: ${error.message}` };
    }
  }

  /**
   * 强制停止网络监听（用于清理）
   */
  async forceStop(): Promise<void> {
    try {
      this.isListening = false;
      await this.detach();
      NetworkListenerRegistry.unregister(this.tabId);
    } catch (error) {
      console.error('强制停止网络监听器失败:', error);
    }
  }

  /**
   * 获取捕获的响应数据
   */
  getCapturedResponses(): CapturedResponseData[] {
    return [...this.capturedResponses];
  }

  /**
   * 清空捕获的数据
   */
  clearCapturedData(): void {
    this.capturedRequests.clear();
    this.capturedResponses = [];
  }

  /**
   * 智能强制分离已存在的debugger连接
   * 只有在确实存在冲突时才执行分离操作
   */
  private async forceDetachExistingDebugger(): Promise<boolean> {
    try {
      console.log(`尝试智能分离标签页 ${this.tabId} 的debugger连接...`);
      
      // 尝试分离，正确处理错误
      let detachSuccess = false;
      for (let i = 0; i < 3; i++) {
        try {
          await new Promise<void>((resolve, reject) => {
            chrome.debugger.detach({ tabId: this.tabId }, () => {
              if (chrome.runtime.lastError) {
                const errorMessage = chrome.runtime.lastError.message || '';
                // 如果错误是"未附加"，说明本来就没有绑定，这是正常情况
                if (errorMessage.includes('not attached') || errorMessage.includes('Debugger is not attached')) {
                  console.log(`标签页 ${this.tabId} 本来就没有绑定debugger，无需分离`);
                  resolve(); // 这种情况下认为是成功的
                  return;
                }
                reject(new Error(errorMessage));
              } else {
                console.log(`成功分离标签页 ${this.tabId} 的debugger连接`);
                detachSuccess = true;
                resolve();
              }
            });
          });
          
          // 如果成功分离或确认没有绑定，跳出循环
          break;
          
        } catch (error: any) {
          console.warn(`第 ${i + 1} 次分离尝试失败:`, error.message);
          if (i === 2) { // 最后一次尝试
            console.error('强制分离失败，但会继续尝试附加');
            return false;
          }
          // 短暂延迟后重试
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
      
      console.log(`智能分离操作完成，成功分离: ${detachSuccess}`);
      return true;
    } catch (error) {
      console.warn('智能分离debugger时发生错误，但会继续尝试附加:', error);
      return false;
    }
  }

  /**
   * 带重试机制的附加到标签页
   */
  private async attachToTabWithRetry(): Promise<NetworkListenerResult> {
    for (let attempt = 0; attempt < this.maxAttachRetries; attempt++) {
      try {
        const result = await this.attachToTab();
        if (result.success) {
          return result;
        }
        
        // 如果是debugger冲突错误，尝试智能强制分离后重试
        if (result.message.includes('Another debugger is already attached') || 
            result.message.includes('already attached')) {
          
          console.log(`检测到debugger冲突，尝试第 ${attempt + 1} 次重试...`);
          
          // 智能强制分离（只有在确实存在冲突时才分离）
          const detachResult = await this.forceDetachExistingDebugger();
          if (!detachResult) {
            console.warn('智能分离失败，但继续尝试附加');
          }
          
          // 延迟后重试
          await new Promise(resolve => setTimeout(resolve, 500 * (attempt + 1)));
          continue;
        }
        
        // 其他错误直接返回
        return result;
        
      } catch (error: any) {
        console.error(`附加重试 ${attempt + 1} 失败:`, error);
        
        if (attempt === this.maxAttachRetries - 1) {
          return { 
            success: false, 
            message: `附加失败，已重试 ${this.maxAttachRetries} 次: ${error.message}`,
            fallbackMode: true
          };
        }
        
        // 延迟后重试
        await new Promise(resolve => setTimeout(resolve, 500 * (attempt + 1)));
      }
    }
    
    return { 
      success: false, 
      message: `附加失败，已重试 ${this.maxAttachRetries} 次`,
      fallbackMode: true
    };
  }

  /**
   * 附加到标签页
   */
  private async attachToTab(): Promise<NetworkListenerResult> {
    return new Promise((resolve) => {
      chrome.debugger.attach({ tabId: this.tabId }, '1.3', () => {
        if (chrome.runtime.lastError) {
          const errorMessage = chrome.runtime.lastError.message || '';
          console.error('附加debugger失败:', chrome.runtime.lastError);
          
          // 特殊处理debugger冲突错误
          if (errorMessage.includes('Another debugger is already attached') || 
              errorMessage.includes('already attached')) {
            resolve({ 
              success: false, 
              message: `Debugger冲突: ${errorMessage}`,
              fallbackMode: false // 冲突错误可以重试
            });
          } else {
            resolve({ 
              success: false, 
              message: `附加失败: ${errorMessage}`,
              fallbackMode: true // 其他错误建议回退
            });
          }
          return;
        }

        this.isAttached = true;
        console.log(`成功附加到标签页 ${this.tabId}`);
        resolve({ success: true, message: '附加成功' });
      });
    });
  }

  /**
   * 启用网络域
   */
  private async enableNetworkDomain(): Promise<NetworkListenerResult> {
    return new Promise((resolve) => {
      chrome.debugger.sendCommand(
        { tabId: this.tabId },
        'Network.enable',
        {},
        () => {
          if (chrome.runtime.lastError) {
            console.error('启用Network域失败:', chrome.runtime.lastError);
            resolve({ 
              success: false, 
              message: `启用Network域失败: ${chrome.runtime.lastError.message}` 
            });
            return;
          }

          console.log('Network域已启用');
          resolve({ success: true, message: 'Network域启用成功' });
        }
      );
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听请求开始事件
    chrome.debugger.onEvent.addListener((source: any, method: string, params: any) => {
      if (source.tabId !== this.tabId) return;

      switch (method) {
        case 'Network.requestWillBeSent':
          this.handleRequestWillBeSent(params);
          break;
        case 'Network.responseReceived':
          this.handleResponseReceived(params);
          break;
        case 'Network.loadingFinished':
          this.handleLoadingFinished(params);
          break;
      }
    });
  }

  /**
   * 处理请求开始事件
   */
  private handleRequestWillBeSent(params: any): void {
    try {
      const { requestId, request } = params;
      const { url, method, postData } = request;

      // 只关注目标URL的请求
      if (!url.includes('seller-product-query/api/v1/product/internal/search')) {
        return;
      }

      console.log('捕获到目标请求:', url, method);

      const capturedRequest: CapturedRequestData = {
        requestId,
        url,
        method,
        postData: postData ? JSON.parse(postData) : undefined,
        timestamp: Date.now()
      };

      this.capturedRequests.set(requestId, capturedRequest);

    } catch (error) {
      console.error('处理请求开始事件失败:', error);
    }
  }

  /**
   * 处理响应接收事件
   */
  private handleResponseReceived(params: any): void {
    try {
      const { requestId, response } = params;
      const request = this.capturedRequests.get(requestId);

      if (!request) return;

      console.log('捕获到目标响应:', response.status, request.url);

      // 检查是否是成功响应
      if (response.status >= 200 && response.status < 300) {
        // 响应接收完成后会触发loadingFinished事件，届时获取响应体
      }

    } catch (error) {
      console.error('处理响应接收事件失败:', error);
    }
  }

  /**
   * 处理加载完成事件
   */
  private handleLoadingFinished(params: any): void {
    try {
      const { requestId } = params;
      const request = this.capturedRequests.get(requestId);

      if (!request) return;

      // 获取响应体
      this.getResponseBody(requestId, request);

    } catch (error) {
      console.error('处理加载完成事件失败:', error);
    }
  }

  /**
   * 获取响应体
   */
  private getResponseBody(requestId: string, request: CapturedRequestData): void {
    chrome.debugger.sendCommand(
      { tabId: this.tabId },
      'Network.getResponseBody',
      { requestId },
      (result: any) => {
        if (chrome.runtime.lastError) {
          console.error('获取响应体失败:', chrome.runtime.lastError);
          return;
        }

        try {
          let responseBody = result.body;
          
          // 如果响应体是base64编码的，需要解码
          if (result.base64Encoded) {
            responseBody = atob(responseBody);
          }

          // 尝试解析JSON
          let parsedBody: any;
          try {
            parsedBody = JSON.parse(responseBody);
          } catch (e) {
            console.error('解析响应JSON失败:', e);
            return;
          }

          // 检查是否是重新上传商品的响应
          if (this.isRejectedProductsResponse(parsedBody, request)) {
            const capturedResponse: CapturedResponseData = {
              requestId,
              url: request.url,
              statusCode: 200,
              responseBody: parsedBody,
              timestamp: Date.now()
            };

            this.capturedResponses.push(capturedResponse);
            console.log('成功捕获重新上传商品数据:', parsedBody.totalElements || 0, '条');

            // 调用回调函数
            if (this.responseCallback) {
              this.responseCallback(capturedResponse);
            }
          }

        } catch (error) {
          console.error('处理响应体失败:', error);
        }
      }
    );
  }

  /**
   * 检查是否是重新上传商品的响应
   */
  private isRejectedProductsResponse(responseBody: any, request: CapturedRequestData): boolean {
    try {
      // 检查请求参数
      if (request.postData && request.postData.tab === 'catalog_rejected') {
        return true;
      }

      // 检查响应结构
      if (responseBody && 
          typeof responseBody === 'object' &&
          'content' in responseBody &&
          Array.isArray(responseBody.content) &&
          'totalElements' in responseBody) {
        return true;
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 分离debugger
   */
  private async detach(): Promise<void> {
    if (!this.isAttached) return;

    return new Promise((resolve) => {
      chrome.debugger.detach({ tabId: this.tabId }, () => {
        if (chrome.runtime.lastError) {
          console.error('分离debugger失败:', chrome.runtime.lastError);
        } else {
          console.log(`已从标签页 ${this.tabId} 分离debugger`);
        }
        this.isAttached = false;
        resolve();
      });
    });
  }

  /**
   * 清理资源
   */
  private async cleanup(): Promise<void> {
    this.isListening = false;
    this.responseCallback = undefined;
    await this.detach();
    this.clearCapturedData();
    NetworkListenerRegistry.unregister(this.tabId);
  }
}

/**
 * 全局清理函数：清理所有网络监听器
 */
export async function cleanupAllNetworkListeners(): Promise<void> {
  await NetworkListenerRegistry.cleanupAll();
}

/**
 * 创建网络监听器实例
 */
export function createNetworkListener(config: NetworkListenerConfig): NetworkListener {
  return new NetworkListener(config);
}

/**
 * 将捕获的响应数据转换为N11重新上传商品格式
 */
export function convertCapturedDataToN11Products(capturedData: CapturedResponseData[]): N11RejectedProduct[] {
  const allProducts: N11RejectedProduct[] = [];

  for (const data of capturedData) {
    try {
      const response = data.responseBody as N11RejectedProductsResponse;
      if (response && response.content && Array.isArray(response.content)) {
        allProducts.push(...response.content);
      }
    } catch (error) {
      console.error('转换数据格式失败:', error);
    }
  }

  return allProducts;
}

/**
 * 工具函数：等待网络响应
 */
export function waitForNetworkResponse(
  listener: NetworkListener, 
  timeout: number = 10000
): Promise<CapturedResponseData[]> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const responses: CapturedResponseData[] = [];

    // 设置响应回调
    listener.start((data) => {
      responses.push(data);
    });

    // 定期检查是否有响应或超时
    const checkInterval = setInterval(() => {
      const currentResponses = listener.getCapturedResponses();
      
      if (currentResponses.length > 0) {
        clearInterval(checkInterval);
        resolve(currentResponses);
        return;
      }

      if (Date.now() - startTime > timeout) {
        clearInterval(checkInterval);
        reject(new Error('等待网络响应超时'));
        return;
      }
    }, 500);
  });
} 