/**
 * Chrome存储工具函数
 * 提供对chrome.storage.sync和chrome.storage.local的读写操作
 */

interface UserInfo {
  isLogin?: boolean;
  phone?: string;
  expiryDate?: string;
  isVip?: boolean;
  isAdmin?: boolean;
  isCardAdmin?: boolean;
  isSub?: boolean;       // 新增：是否是子账号
  appId?: string;        // 新增：应用ID
  appStatus?: number;    // 新增：应用状态
  token?: string;
  userId?: number;
  points?: number;
}

/**
 * 从chrome.storage.sync中获取数据
 * @param {string|string[]} keys 要获取的键或键数组
 * @returns {Promise<object>} 包含请求的键值对的对象
 */
export const getSyncStorage = (keys: string | string[]): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (!chrome.storage) {
      console.error('chrome.storage API不可用');
      reject(new Error('chrome.storage API不可用'));
      return;
    }
    
    chrome.storage.sync.get(keys, (result) => {
      if (chrome.runtime.lastError) {
        console.error('获取同步存储数据失败:', chrome.runtime.lastError);
        reject(chrome.runtime.lastError);
      } else {
        resolve(result);
      }
    });
  });
};

/**
 * 将数据保存到chrome.storage.sync
 * @param {object} data 要保存的键值对对象
 * @returns {Promise<void>}
 */
export const setSyncStorage = (data: Record<string, any>): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!chrome.storage) {
      console.error('chrome.storage API不可用');
      reject(new Error('chrome.storage API不可用'));
      return;
    }
    
    chrome.storage.sync.set(data, () => {
      if (chrome.runtime.lastError) {
        console.error('保存同步存储数据失败:', chrome.runtime.lastError);
        reject(chrome.runtime.lastError);
      } else {
        resolve();
      }
    });
  });
};

/**
 * 从chrome.storage.local中获取数据
 * @param {string|string[]} keys 要获取的键或键数组
 * @returns {Promise<object>} 包含请求的键值对的对象
 */
export const getLocalStorage = (keys: string | string[]): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (!chrome.storage) {
      console.error('chrome.storage API不可用');
      reject(new Error('chrome.storage API不可用'));
      return;
    }
    
    chrome.storage.local.get(keys, (result) => {
      if (chrome.runtime.lastError) {
        console.error('获取本地存储数据失败:', chrome.runtime.lastError);
        reject(chrome.runtime.lastError);
      } else {
        resolve(result);
      }
    });
  });
};

/**
 * 将数据保存到chrome.storage.local
 * @param {object} data 要保存的键值对对象
 * @returns {Promise<void>}
 */
export const setLocalStorage = (data: Record<string, any>): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!chrome.storage) {
      console.error('chrome.storage API不可用');
      reject(new Error('chrome.storage API不可用'));
      return;
    }
    
    chrome.storage.local.set(data, () => {
      if (chrome.runtime.lastError) {
        console.error('保存本地存储数据失败:', chrome.runtime.lastError);
        reject(chrome.runtime.lastError);
      } else {
        resolve();
      }
    });
  });
};

/**
 * 获取用户信息（从sync和local存储中）
 * @returns {Promise<UserInfo>} 包含用户信息的对象
 */
export const getUserInfo = async (): Promise<UserInfo> => {
  try {
    // 从 sync storage 获取用户基本信息
    const syncData = await getSyncStorage(['isLogin', 'is_login', 'phone', 'expiryDate', 'isVip', 'isAdmin', 'is_admin', 'is_card_admin', 'is_sub', 'app_id', 'app_status', 'userId', 'points']);

    // 从 local storage 获取 token
    const localData = await getLocalStorage(['token']);

    // 处理登录状态的兼容性（支持 isLogin 和 is_login 两种格式）
    const isLogin = syncData.isLogin || syncData.is_login || false;
    const isAdmin = syncData.isAdmin || syncData.is_admin || false;
    const isCardAdmin = syncData.isCardAdmin || syncData.is_card_admin || false;
    const isSub = syncData.isSub || syncData.is_sub || false;

    return {
      isLogin,
      phone: syncData.phone || '',
      expiryDate: syncData.expiryDate || '',
      isVip: syncData.isVip || false,
      isAdmin,
      isCardAdmin,
      isSub,                                    // 新增：子账号状态
      appId: syncData.appId || syncData.app_id, // 新增：应用ID
      appStatus: syncData.appStatus || syncData.app_status, // 新增：应用状态
      token: localData.token || '',
      userId: syncData.userId || undefined,
      points: syncData.points || 0
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      isLogin: false,
      phone: '',
      expiryDate: '',
      isVip: false,
      isAdmin: false,
      isCardAdmin: false,
      isSub: false,          // 新增：默认不是子账号
      appId: undefined,      // 新增：应用ID
      appStatus: undefined,  // 新增：应用状态
      token: '',
      userId: undefined,
      points: 0
    };
  }
};

/**
 * 保存用户信息（分别保存到sync和local存储）
 * @param {UserInfo} userInfo 用户信息对象
 * @returns {Promise<void>}
 */
export const saveUserInfo = async (userInfo: UserInfo): Promise<void> => {
  try {
    // 分离需要存储到不同位置的数据
    const syncData: Record<string, any> = {};
    const localData: Record<string, any> = {};
    
    // 基本信息存储到 sync storage
    if (userInfo.isLogin !== undefined) syncData.isLogin = userInfo.isLogin;
    if (userInfo.phone !== undefined) syncData.phone = userInfo.phone;
    if (userInfo.expiryDate !== undefined) syncData.expiryDate = userInfo.expiryDate;
    if (userInfo.isVip !== undefined) syncData.isVip = userInfo.isVip;
    if (userInfo.isAdmin !== undefined) syncData.isAdmin = userInfo.isAdmin;
    if (userInfo.isCardAdmin !== undefined) syncData.isCardAdmin = userInfo.isCardAdmin;
    if (userInfo.isSub !== undefined) syncData.isSub = userInfo.isSub;           // 新增：子账号状态
    if (userInfo.appId !== undefined) syncData.appId = userInfo.appId;           // 新增：应用ID
    if (userInfo.appStatus !== undefined) syncData.appStatus = userInfo.appStatus; // 新增：应用状态
    if (userInfo.userId !== undefined) syncData.userId = userInfo.userId;
    if (userInfo.points !== undefined) syncData.points = userInfo.points;
    
    // token 存储到 local storage
    if (userInfo.token !== undefined) localData.token = userInfo.token;
    
    // 执行存储操作
    if (Object.keys(syncData).length > 0) {
      await setSyncStorage(syncData);
    }
    
    if (Object.keys(localData).length > 0) {
      await setLocalStorage(localData);
    }
    
    console.log('用户信息保存成功');
  } catch (error) {
    console.error('保存用户信息失败:', error);
    throw error;
  }
};
