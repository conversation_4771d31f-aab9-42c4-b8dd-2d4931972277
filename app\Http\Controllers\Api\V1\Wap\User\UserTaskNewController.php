<?php

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\UserTaskNewService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class UserTaskNewController extends Controller
{
    protected UserTaskNewService $userTaskNewService;

    public function __construct(UserTaskNewService $userTaskNewService)
    {
        $this->userTaskNewService = $userTaskNewService;
        parent::__construct();
    }

    /**
     * 生成任务详情记录
     */
    public function generateTaskDetails(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $params = $request->only(['task_id', 'offset', 'limit']);
        $result = $this->userTaskNewService->generateTaskDetails($userId, $params);
        return $this->apiSuccess($result, '生成任务详情成功');

    }

    /**
     * 获取下一个待AI改写的任务详情
     */
    public function getNextAiTask(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $taskId = $request->input('task_id');
        
        if (!$taskId || !is_numeric($taskId)) {
            return $this->apiError('任务ID参数无效');
        }
        
        $result = $this->userTaskNewService->getNextAiTask($userId, (int)$taskId);
        return $this->apiSuccess($result);

    }

    /**
     * 更新任务详情AI改写结果
     */
    public function updateAiResult(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $params = $request->only(['task_detail_id', 'goods_name_ai', 'goods_property_ai']);
        
        $result = $this->userTaskNewService->updateAiResult($userId, $params);
        return $this->apiSuccess($result, '更新AI改写结果成功');

    }

    /**
     * 获取用户随机AI Key
     */
    public function getRandomAiKey(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $result = $this->userTaskNewService->getRandomAiKey($userId);
        return $this->apiSuccess($result);

    }

    /**
     * 获取任务AI改写进度
     */
    public function getAiProgress(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $taskId = $request->input('task_id');
        
        if (!$taskId || !is_numeric($taskId)) {
            return $this->apiError('任务ID参数无效');
        }
        
        $result = $this->userTaskNewService->getAiProgress($userId, (int)$taskId);
        return $this->apiSuccess($result);

    }

    /**
     * 完成任务AI改写
     */
    public function completeAiTask(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $taskId = $request->input('task_id');
        
        if (!$taskId || !is_numeric($taskId)) {
            return $this->apiError('任务ID参数无效');
        }
        
        $result = $this->userTaskNewService->completeAiTask($userId, (int)$taskId);
        return $this->apiSuccess($result, '完成AI改写任务成功');

    }
} 