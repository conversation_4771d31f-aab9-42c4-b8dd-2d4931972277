#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理器
支持多种输出模式：GUI界面、文件、静默模式
"""

import os
from datetime import datetime
from typing import Optional, Callable
from config.settings import AppSettings

class LogManager:
    """日志管理器类"""

    def __init__(self):
        # 使用动态配置方法获取日志输出模式
        self.output_mode = AppSettings.get_default_log_output()
        self.gui_callback: Optional[Callable[[str, str], None]] = None
        self.status_bar_callback: Optional[Callable[[str, str], None]] = None

    def get_output_mode(self) -> str:
        """获取当前输出模式"""
        return self.output_mode
    
    def set_gui_callback(self, callback: Callable[[str, str], None]):
        """设置GUI回调函数"""
        self.gui_callback = callback

    def set_status_bar_callback(self, callback: Callable[[str, str], None]):
        """设置状态栏回调函数"""
        self.status_bar_callback = callback
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        # 格式化消息
        timestamp = datetime.now().strftime(AppSettings.LOG_DATE_FORMAT)
        formatted_message = AppSettings.LOG_FORMAT.format(
            timestamp=timestamp,
            level=self._get_level_text(level),
            message=message
        )

        # 总是输出到状态栏（如果有回调函数），即使在静默模式下
        if self.status_bar_callback:
            self.status_bar_callback(formatted_message, level)

        # 静默模式下不输出到GUI或文件
        if self.output_mode == AppSettings.LOG_OUTPUT_SILENT:
            return

        if self.output_mode == AppSettings.LOG_OUTPUT_GUI:
            self._log_to_gui(formatted_message, level)
        elif self.output_mode == AppSettings.LOG_OUTPUT_FILE:
            self._log_to_file(formatted_message)
    
    def _get_level_text(self, level: str) -> str:
        """获取级别文本"""
        level_map = {
            "ERROR": "错误",
            "SUCCESS": "成功",
            "INFO": "信息",
            "WARNING": "警告"
        }
        return level_map.get(level, "信息")
    
    def _log_to_gui(self, message: str, level: str):
        """输出到GUI界面"""
        if self.gui_callback:
            self.gui_callback(message, level)
    
    def _log_to_file(self, message: str):
        """输出到文件"""
        try:
            log_file_path = AppSettings.get_log_file_path()
            with open(log_file_path, 'a', encoding='utf-8') as f:
                f.write(message + '\n')
        except Exception:
            pass  # 忽略写入失败
    
    def clear_log_file(self):
        """清空日志文件"""
        try:
            log_file_path = AppSettings.get_log_file_path()
            if os.path.exists(log_file_path):
                with open(log_file_path, 'w', encoding='utf-8') as f:
                    f.write('')
        except Exception:
            pass

# 全局日志管理器实例
log_manager = LogManager()


def log_message(message: str, level: str = "INFO"):
    """全局日志函数"""
    log_manager.log(message, level)

def set_gui_log_callback(callback: Callable[[str, str], None]):
    """设置GUI日志回调"""
    log_manager.set_gui_callback(callback)


def set_status_bar_log_callback(callback: Callable[[str, str], None]):
    """设置状态栏日志回调函数"""
    log_manager.set_status_bar_callback(callback)
