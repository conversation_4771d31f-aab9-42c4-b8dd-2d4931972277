/**
 * 任务处理器 - Chrome扩展版本
 * 用于处理正常任务的执行逻辑，包括获取分类属性和上传商品
 */
import { 
  getN11CategoryAttributes, 
  uploadProductToN11, 
  processProductAttributes,
  type ProductData,
  type ProductAttribute,
  type N11CategoryResponse
} from './n11ProductApi'
import { updateTask, type TaskUpdateParams, getRetryUploadParams, type TaskStartResponse } from './taskApi'

// 商品上传结果接口
export interface ProductUploadResult {
  taskDetailId: number
  productName: string
  productImage: string
  price: string
  success: boolean
  message: string
  thirdTaskId?: number
  error?: string
}

// 任务处理器配置
export interface TaskProcessorConfig {
  debugMode?: boolean
  onProgress?: (current: number, total: number) => void
  onProductStart?: (product: ProductUploadResult) => void
  onProductComplete?: (product: ProductUploadResult) => void
  onError?: (error: string) => void
}

/**
 * 获取配置中的调试模式设置
 */
const getDebugMode = (): Promise<boolean> => {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey: 'n11DebugMode'
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        console.warn('获取调试模式配置失败，使用默认值true');
        resolve(true);
        return;
      }
      // 修复：background返回的是url字段，不是value字段
      if (response && response.hasOwnProperty('url')) {
        resolve(response.url);
      } else {
        console.warn('获取调试模式配置失败，使用默认值true:', response);
        resolve(true);
      }
    });
  });
}

/**
 * 处理单个商品重新上传
 * @param taskDetailId 任务详情ID
 * @param config 配置选项
 * @returns 处理结果
 */
export const processSingleRetryUpload = async (
  taskDetailId: number,
  config: TaskProcessorConfig = {}
): Promise<ProductUploadResult> => {
  const { onProductStart, onProductComplete, onError } = config

  // 获取调试模式设置
  const debugMode = config.debugMode ?? await getDebugMode()
  console.log('debugMode------------', debugMode)

  try {
    // 获取重新上传参数
    const taskData: TaskStartResponse = await getRetryUploadParams(taskDetailId)
    
    // 构建商品上传结果对象
    const productResult: ProductUploadResult = {
      taskDetailId: taskDetailId,
      productName: taskData.goods_name || '未知商品',
      productImage: taskData.thumb_url || '',
      price: `${taskData.price_third} ${taskData.currentcy}`,
      success: false,
      message: '正在处理中...'
    }

    // 通知开始处理
    if (onProductStart) {
      onProductStart(productResult)
    }

    // 更新任务状态为处理中
    await updateTask({
      task_detail_id: taskDetailId,
      task_id: taskData.task_id!,
      task_over: 2, // 进行中
      task_num: 0
    })

    // 获取分类属性
    let categoryAttributes: N11CategoryResponse | null = null
    try {
      categoryAttributes = await getN11CategoryAttributes(
        taskData.category_id!,
        taskData.store_info!.app_key
      )
    } catch (error: any) {
      // 分类属性获取失败
      productResult.success = false
      productResult.message = '商品属性获取失败'

      await updateTask({
        task_detail_id: taskDetailId,
        task_id: taskData.task_id!,
        third_task_id: 0,
        third_type: 'n11',
        third_status: 'FAILED',
        third_result: '商品属性获取失败',
        task_over: 1, // 完成
        task_num: 1
      })

      if (onProductComplete) {
        onProductComplete(productResult)
      }

      return productResult
    }

    if (!categoryAttributes) {
      // 分类属性为空
      productResult.success = false
      productResult.message = '商品属性获取失败'

      await updateTask({
        task_detail_id: taskDetailId,
        task_id: taskData.task_id!,
        third_task_id: 0,
        third_type: 'n11',
        third_status: 'FAILED',
        third_result: '商品属性获取失败',
        task_over: 1, // 完成
        task_num: 1
      })

      if (onProductComplete) {
        onProductComplete(productResult)
      }

      return productResult
    }

    // 处理商品属性
    const attributes = processProductAttributes(
      categoryAttributes.categoryAttributes,
      taskData.spec_key_values || '',
      taskData.store_info!.brand
    )
   // 货币类型转换逻辑
    const originalCurrency = taskData.currentcy?.toUpperCase() || ''
    let convertedCurrency = originalCurrency

    if (originalCurrency === '$') {
      convertedCurrency = 'USD'
    } else if (originalCurrency === '€') {
      convertedCurrency = 'EUR'
    }

    // 构建商品数据
    const productData = {
      title: taskData.goods_name!,
      description: taskData.goods_info?.goods_property || taskData.goods_name!,
      categoryId: taskData.category_id!,
      currencyType: convertedCurrency,
      productMainId: taskData.product_main_id!,
      preparingDay: taskData.store_info!.preparing_day,
      shipmentTemplate: taskData.store_info!.shipment_template,
      stockCode: taskData.stock_code!,
      quantity: taskData.store_info!.quantity,
      salePrice: parseFloat(taskData.price_third!),
      listPrice: parseFloat(taskData.price_list_third!),
      vatRate: taskData.store_info!.vat_rate,
      images: taskData.images || [],
      attributes: attributes
    }

    // 上传商品到N11
    const uploadResult = await uploadProductToN11(
      productData,
      taskData.store_info!.app_key,
      taskData.store_info!.app_secret,
      taskData.store_info!.integrator_name,
      taskDetailId,
      debugMode
    )
    // 处理上传结果
    if (uploadResult.error) {
      // 上传失败
      productResult.success = false
      productResult.message = uploadResult.error
      productResult.error = uploadResult.response

      await updateTask({
        task_detail_id: taskDetailId,
        task_id: taskData.task_id!,
        third_task_id: 0,
        third_type: 'n11',
        third_status: 'FAILED',
        third_result: uploadResult.error,
        task_over: 1, // 完成
        task_num: 1
      })
    } else {
      // 上传成功
      productResult.success = true
      productResult.message = uploadResult.reasons?.[0] || '上传成功'
      productResult.thirdTaskId = uploadResult.id

      if(productResult.message.includes('sku işlenmeye alındı')){
        productResult.message = '上传成功'
      }

      await updateTask({
        task_detail_id: taskDetailId,
        task_id: taskData.task_id!,
        third_task_id: uploadResult.id || 0,
        third_type: 'n11',
        third_status: uploadResult.status || 'SUCCESS',
        third_result: uploadResult.reasons?.[0] || '上传成功',
        task_over: 1, // 完成
        task_num: 1
      })
    }

    // 通知处理完成
    if (onProductComplete) {
      onProductComplete(productResult)
    }

    return productResult

  } catch (error: any) {
    const errorMessage = error.message || '处理失败'
    
    const productResult: ProductUploadResult = {
      taskDetailId: taskDetailId,
      productName: '未知商品',
      productImage: '',
      price: '',
      success: false,
      message: errorMessage,
      error: error.toString()
    }

    // 更新任务状态为失败
    try {
      await updateTask({
        task_detail_id: taskDetailId,
        task_id: 0, // 无法获取task_id时使用0
        third_task_id: 0,
        third_type: 'n11',
        third_status: 'ERROR',
        third_result: errorMessage,
        task_over: 1, // 完成
        task_num: 1
      })
    } catch (updateError) {
      console.error('更新任务状态失败:', updateError)
    }

    if (onError) {
      onError(errorMessage)
    }

    if (onProductComplete) {
      onProductComplete(productResult)
    }

    return productResult
  }
}
