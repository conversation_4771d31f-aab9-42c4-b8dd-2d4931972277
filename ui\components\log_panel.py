#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志显示面板组件
"""

from PyQt5.QtWidgets import QGroupBox, QVBoxLayout, QTextEdit, QPushButton
from PyQt5.QtGui import QFont
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from utils.logger import log_message, log_manager
from config.settings import AppSettings


class LogPanel(QGroupBox):
    """日志显示面板组件"""
    
    def __init__(self, parent=None):
        super().__init__("操作日志", parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)

        # 清空日志按钮
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        layout.addWidget(self.clear_log_btn)

    def append_log(self, message: str, level: str):
        """添加日志消息"""
        if self.log_text:
            self.log_text.append(message)

            # 自动滚动到底部
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.End)
            self.log_text.setTextCursor(cursor)

    def clear_log(self):
        """清空日志"""
        # 基于配置文件的设置决定清空行为
        current_log_output = AppSettings.get_default_log_output()
        if current_log_output == AppSettings.LOG_OUTPUT_GUI and self.log_text:
            self.log_text.clear()
            log_message("日志已清空")
        elif current_log_output == AppSettings.LOG_OUTPUT_FILE:
            log_manager.clear_log_file()
            log_message("日志文件已清空")
