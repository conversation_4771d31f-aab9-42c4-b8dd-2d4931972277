#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度显示面板组件
"""

from PyQt5.QtWidgets import (QGroupBox, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QProgressBar, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from utils.logger import log_message


class ProgressPanel(QGroupBox):
    """进度显示面板组件"""
    
    # 信号定义
    stop_requested = pyqtSignal()  # 停止请求信号
    
    def __init__(self, parent=None):
        super().__init__("处理进度", parent)
        self.progress_area_height = 115
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 6, 8, 8)
        layout.setSpacing(3)

        # 等待处理提示标签（初始状态显示）
        self.waiting_label = QLabel("等待处理商品...")
        self.waiting_label.setAlignment(Qt.AlignCenter)
        self.waiting_label.setStyleSheet("color: #666666; font-size: 14px; font-weight: bold; padding: 20px;")
        layout.addWidget(self.waiting_label)

        # 商品级别进度
        goods_layout = QHBoxLayout()
        goods_layout.setSpacing(6)
        goods_layout.addWidget(QLabel("当前商品:"))
        self.goods_progress_label = QLabel("等待开始...")
        self.goods_progress_label.setStyleSheet("color: #2E8B57; font-weight: bold;")
        goods_layout.addWidget(self.goods_progress_label)
        goods_layout.addStretch()
        layout.addLayout(goods_layout)

        # 文件级别进度
        file_layout = QHBoxLayout()
        file_layout.setSpacing(6)
        file_layout.addWidget(QLabel("文件进度:"))
        self.file_progress_label = QLabel("等待开始...")
        file_layout.addWidget(self.file_progress_label)
        file_layout.addStretch()
        layout.addLayout(file_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setFixedHeight(20)
        layout.addWidget(self.progress_bar)

        # 停止按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(6)
        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.clicked.connect(self.on_stop_clicked)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setFixedHeight(28)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 设置固定高度
        self.setFixedHeight(self.progress_area_height)
        
        # 初始状态：隐藏进度相关组件，只显示等待提示
        self.set_waiting_state()

    def set_waiting_state(self):
        """设置进度区域为等待状态"""
        self.waiting_label.setVisible(True)
        self.goods_progress_label.setVisible(False)
        self.file_progress_label.setVisible(False)
        self.progress_bar.setVisible(False)
        self.stop_btn.setVisible(False)

    def set_active_state(self):
        """设置进度区域为活动状态"""
        self.waiting_label.setVisible(False)
        self.goods_progress_label.setVisible(True)
        self.file_progress_label.setVisible(True)
        self.progress_bar.setVisible(True)
        self.stop_btn.setVisible(True)
        self.stop_btn.setEnabled(True)

    def on_stop_clicked(self):
        """停止按钮点击事件"""
        # 显示确认对话框
        reply = QMessageBox.question(self, "确认停止", "您确定要停止该操作吗？",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            log_message("正在停止处理...")
            self.stop_requested.emit()

    def update_goods_progress(self, progress_info: dict):
        """更新商品级别进度"""
        goods_platform_id = progress_info.get('goods_platform_id', '')
        goods_name = progress_info.get('goods_name', '')
        pending_count = progress_info.get('pending_count', 0)

        # 截断过长的商品名称
        if len(goods_name) > 50:
            goods_name = goods_name[:47] + "..."

        progress_text = f"ID: {goods_platform_id} | {goods_name}"
        if pending_count > 0:
            progress_text += f" | 剩余: {pending_count}个"

        self.goods_progress_label.setText(progress_text)

        # 确保进度面板处于活动状态，以便显示商品信息
        # 这是一个关键的恢复机制，确保从等待状态正确切换到活动状态
        if self.waiting_label.isVisible():
            self.set_active_state()
            log_message("进度面板从等待状态切换到活动状态", "INFO")

    def update_file_progress(self, progress_info: dict):
        """更新文件级别进度"""
        stage = progress_info.get('stage', '')
        current = progress_info.get('current', 0)
        total = progress_info.get('total', 0)
        file_type = progress_info.get('file_type', '')
        progress_percent = progress_info.get('progress_percent', 0)

        if stage == 'download':
            stage_text = "下载"
        elif stage == 'upload':
            stage_text = "本地化"
        else:
            stage_text = "处理"

        file_type_text = {
            'image': '图片',
            'video': '视频',
            'pdf': 'PDF'
        }.get(file_type, '文件')

        progress_text = f"正在{stage_text}第{current}个{file_type_text}，共{total}个"
        self.file_progress_label.setText(progress_text)
        self.progress_bar.setValue(progress_percent)

    def reset_progress(self):
        """重置进度显示"""
        self.goods_progress_label.setText("等待开始...")
        self.file_progress_label.setText("等待开始...")
        self.progress_bar.setValue(0)
        self.stop_btn.setEnabled(False)
        self.set_waiting_state()

    def activate_progress(self):
        """激活进度显示（确保从等待状态切换到活动状态）"""
        if self.waiting_label.isVisible():
            self.set_active_state()

    def test_progress_display(self):
        """测试进度显示功能"""
        if self.isVisible() and not self.waiting_label.isVisible():
            # 当前正在显示进度，切换回等待状态
            self.reset_progress()
            log_message("测试：隐藏进度区域", "INFO")
        else:
            # 当前未显示进度或显示等待状态，切换到进度显示
            self.set_active_state()
            # 模拟进度更新
            self.goods_progress_label.setText("测试商品: ID12345 | 测试商品名称")
            self.file_progress_label.setText("正在下载第1个图片，共5个")
            self.progress_bar.setValue(20)
            log_message("测试：显示进度区域", "INFO")
