import { ref, reactive } from 'vue'
import { getUserInfo, saveUserInfo } from './storage'
import { fetchUserInfo } from './api'
import { setLoginExpired } from './loginState'
import { ElNotification } from 'element-plus'

interface UserInfo {
  isLogin: boolean;
  phone: string;
  expiryDate: string;
  isVip: boolean;
  isAdmin: boolean;
  isCardAdmin: boolean;
  isSub: boolean;        // 新增：是否是子账号
  appId?: string;        // 新增：应用ID
  appStatus?: number;    // 新增：应用状态
  token: string;
  userId?: number;
  points?: number;
}

// 用户信息状态
export const userInfo = reactive<UserInfo>({
  isLogin: false,
  phone: '',
  expiryDate: '',
  isVip: false,
  isAdmin: false,
  isCardAdmin: false,
  isSub: false,          // 新增：默认不是子账号
  appId: undefined,      // 新增：应用ID
  appStatus: undefined,  // 新增：应用状态
  token: '',
  userId: undefined,
  points: 0
})

// 用户信息加载状态
export const isUserInfoLoading = ref(false)

// 事件监听器
const eventListeners: Array<() => void> = []

/**
 * 添加用户信息变化监听器
 */
export const onUserInfoChange = (callback: () => void) => {
  eventListeners.push(callback)

  // 返回取消监听的函数
  return () => {
    const index = eventListeners.indexOf(callback)
    if (index > -1) {
      eventListeners.splice(index, 1)
    }
  }
}

/**
 * 触发用户信息变化事件
 */
const triggerUserInfoChange = () => {
  eventListeners.forEach(callback => {
    try {
      callback()
    } catch (error) {
      console.error('用户信息变化监听器执行失败:', error)
    }
  })
}

/**
 * 更新用户信息
 */
export const updateUserInfo = (newUserInfo: Partial<UserInfo>) => {
  Object.assign(userInfo, newUserInfo)
  triggerUserInfoChange()
}

/**
 * 清空用户信息
 */
export const clearUserInfo = () => {
  updateUserInfo({
    isLogin: false,
    phone: '',
    expiryDate: '',
    isVip: false,
    isAdmin: false,
    isCardAdmin: false,
    isSub: false,          // 新增：重置子账号状态
    appId: undefined,      // 新增：重置应用ID
    appStatus: undefined,  // 新增：重置应用状态
    token: '',
    userId: undefined,
    points: 0
  })
}

/**
 * 从缓存加载用户信息
 */
export const loadUserInfoFromCache = async () => {
  try {
    const cachedInfo = await getUserInfo()

    if (cachedInfo.isLogin && cachedInfo.phone) {
      Object.assign(userInfo, {
        isLogin: true,
        phone: cachedInfo.phone,
        expiryDate: cachedInfo.expiryDate || '',
        isVip: cachedInfo.isVip || false,
        isAdmin: cachedInfo.isAdmin || false,
        isCardAdmin: cachedInfo.isCardAdmin || false,
        isSub: cachedInfo.isSub || false,          // 新增：子账号状态
        appId: cachedInfo.appId,                   // 新增：应用ID
        appStatus: cachedInfo.appStatus,           // 新增：应用状态
        token: cachedInfo.token || '',
        userId: cachedInfo.userId || undefined,
        points: cachedInfo.points || 0
      })

      console.log('从缓存加载用户信息成功:', {
        phone: userInfo.phone,
        isVip: userInfo.isVip,
        isAdmin: userInfo.isAdmin,
        isCardAdmin: userInfo.isCardAdmin,
        expiryDate: userInfo.expiryDate,
        userId: userInfo.userId
      })

      // 触发用户信息变化事件
      triggerUserInfoChange()
    }
  } catch (error) {
    console.error('从缓存加载用户信息失败:', error)
  }
}

/**
 * 快速初始化用户信息（用于路由守卫）
 */
export const initUserInfoForRouter = async (): Promise<boolean> => {
  try {
    // 如果用户信息已经有手机号，说明已经初始化过了
    if (userInfo.phone) {
      return true
    }

    // 从缓存快速加载用户信息
    await loadUserInfoFromCache()

    // 返回是否成功加载到用户信息
    return !!userInfo.phone
  } catch (error) {
    console.error('快速初始化用户信息失败:', error)
    return false
  }
}

/**
 * 从API获取并更新用户信息（只在App.vue中调用）
 */
export const fetchAndUpdateUserInfo = async () => {
  if (isUserInfoLoading.value) {
    console.log('用户信息正在加载中，跳过重复请求')
    return
  }

  try {
    isUserInfoLoading.value = true

    // 首先从缓存获取基本信息
    const cachedInfo = await getUserInfo()

    // 如果有token，则请求后端验证用户信息
    if (cachedInfo.token) {
      try {
        console.log('正在从后端验证用户信息...')
        const apiResponse = await fetchUserInfo()
        if (apiResponse && apiResponse.phone) {
          const serverUserInfo = apiResponse

          // 更新用户信息
          const newUserInfo = {
            isLogin: true,
            phone: serverUserInfo.phone || '',
            expiryDate: serverUserInfo.vip_end_time || '',
            isVip: serverUserInfo.is_vip === 1 || false,
            isAdmin: serverUserInfo.is_admin === 1 || false,
            isCardAdmin: serverUserInfo.is_card_admin === 1 || false,
            isSub: serverUserInfo.is_sub === 1 || false,        // 新增：子账号状态
            appId: serverUserInfo.appid,                         // 新增：应用ID
            appStatus: serverUserInfo.appstatus,                 // 新增：应用状态
            token: cachedInfo.token,
            userId: serverUserInfo.id || undefined,
            points: serverUserInfo.points || 0
          }

          updateUserInfo(newUserInfo)

          // 同步更新缓存
          await saveUserInfo(newUserInfo)

          console.log('用户信息已从后端更新:', serverUserInfo)
        }
      } catch (apiError) {
        ElNotification({
          title: '提示',
          message: '系统错误,请联系管理员',
          type: 'error',
          duration: 3000
        });
        return false;
      }
    } else {
      // 没有token，使用缓存信息
      updateUserInfo({
        isLogin: cachedInfo.isLogin || false,
        phone: cachedInfo.phone || '',
        expiryDate: cachedInfo.expiryDate || '',
        isVip: cachedInfo.isVip || false,
        isAdmin: cachedInfo.isAdmin || false,
        isCardAdmin: cachedInfo.isCardAdmin || false,
        isSub: cachedInfo.isSub || false,          // 新增：子账号状态
        appId: cachedInfo.appId,                   // 新增：应用ID
        appStatus: cachedInfo.appStatus,           // 新增：应用状态
        token: '',
        userId: cachedInfo.userId || undefined,
        points: cachedInfo.points || 0
      })
      console.log('未找到token，使用缓存用户信息')
    }

    console.log('用户信息加载完成:', {
      isLogin: userInfo.isLogin,
      phone: userInfo.phone,
      isVip: userInfo.isVip,
      hasToken: !!userInfo.token
    })
  } catch (error) {
    console.error('加载用户信息失败:', error)
    ElNotification({
      title: '错误',
      message: '加载用户信息失败',
      type: 'error',
      duration: 3000
    })
  } finally {
    isUserInfoLoading.value = false
  }
}

/**
 * 处理用户退出登录
 */
export const handleUserLogout = async () => {
  console.log('收到用户退出登录通知')

  // 清空用户信息
  clearUserInfo()

  // 清除存储的登录信息
  await saveUserInfo({
    isLogin: false,
    phone: '',
    expiryDate: '',
    isVip: false,
    isAdmin: false,
    isCardAdmin: false,
    isSub: false,          // 新增：重置子账号状态
    appId: undefined,      // 新增：重置应用ID
    appStatus: undefined,  // 新增：重置应用状态
    token: ''
  })

  // 设置登录失效状态
  setLoginExpired(true)

  // 跳转到登录失效页面
  if (window.location.hash !== '#/login-expired') {
    window.location.hash = '#/login-expired'
  }

  ElNotification({
    title: '提示',
    message: '您已退出登录，请重新登录',
    type: 'warning',
    duration: 3000
  })
}

/**
 * 强制刷新用户信息（用于积分变化后的实时更新）
 */
export const forceRefreshUserInfo = async (): Promise<boolean> => {
  try {
    console.log('强制刷新用户信息（积分可能已变化）')

    // 从缓存获取token
    const cachedInfo = await getUserInfo()

    if (!cachedInfo.token) {
      console.log('未找到token，无法刷新用户信息')
      return false
    }

    // 请求后端验证用户信息
    const apiResponse = await fetchUserInfo()
    if (apiResponse && apiResponse.phone) {
      const serverUserInfo = apiResponse

      // 更新用户信息
      const newUserInfo = {
        isLogin: true,
        phone: serverUserInfo.phone || '',
        expiryDate: serverUserInfo.vip_end_time || '',
        isVip: serverUserInfo.is_vip === 1 || false,
        isAdmin: serverUserInfo.is_admin === 1 || false,
        isCardAdmin: serverUserInfo.is_card_admin === 1 || false,
        isSub: serverUserInfo.is_sub === 1 || false,        // 新增：子账号状态
        appId: serverUserInfo.appid,                         // 新增：应用ID
        appStatus: serverUserInfo.appstatus,                 // 新增：应用状态
        token: cachedInfo.token,
        userId: serverUserInfo.id || undefined,
        points: serverUserInfo.points || 0
      }

      updateUserInfo(newUserInfo)

      // 同步更新缓存
      await saveUserInfo(newUserInfo)

      // 通知popup页面刷新用户信息
      notifyPopupRefreshUserInfo()

      console.log('用户信息强制刷新成功:', serverUserInfo)
      return true
    } else {
      console.log('后端返回的用户信息无效')
      return false
    }
  } catch (error) {
    console.error('强制刷新用户信息失败:', error)
    return false
  }
}

/**
 * 通知popup页面刷新用户信息
 */
export const notifyPopupRefreshUserInfo = () => {
  try {
    chrome.runtime.sendMessage({
      type: 'USER_INFO_UPDATED',
      source: 'web',
      timestamp: Date.now()
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.log('发送用户信息更新通知失败:', chrome.runtime.lastError.message)
      } else {
        console.log('用户信息更新通知已发送到popup')
      }
    })
  } catch (error) {
    console.error('发送用户信息更新通知失败:', error)
  }
}
