/**
 * 统一错误处理工具类
 */
import { ElMessage, ElNotification } from 'element-plus'

// 错误类型枚举
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType
  code?: number
  message: string
  details?: any
  timestamp: number
}

// 网络错误检测
const isNetworkError = (error: any): boolean => {
  return (
    error.name === 'NetworkError' ||
    error.message?.includes('网络') ||
    error.message?.includes('network') ||
    error.message?.includes('fetch') ||
    error.code === 'NETWORK_ERROR'
  )
}

// API错误检测
const isApiError = (error: any): boolean => {
  return (
    error.code && typeof error.code === 'number' ||
    error.status && typeof error.status === 'number'
  )
}

// 权限错误检测
const isPermissionError = (error: any): boolean => {
  return (
    error.code === 401 ||
    error.code === 403 ||
    error.message?.includes('权限') ||
    error.message?.includes('unauthorized') ||
    error.message?.includes('forbidden')
  )
}

/**
 * 解析错误类型和信息
 */
export const parseError = (error: any): ErrorInfo => {
  const timestamp = Date.now()
  
  // 用户取消操作
  if (error === 'cancel' || error?.message === 'cancel') {
    return {
      type: ErrorType.VALIDATION_ERROR,
      message: '操作已取消',
      timestamp
    }
  }
  
  // 网络错误
  if (isNetworkError(error)) {
    return {
      type: ErrorType.NETWORK_ERROR,
      message: '网络连接失败，请检查网络设置后重试',
      details: error,
      timestamp
    }
  }
  
  // 权限错误
  if (isPermissionError(error)) {
    return {
      type: ErrorType.PERMISSION_ERROR,
      code: error.code || error.status,
      message: '权限不足，请重新登录后重试',
      details: error,
      timestamp
    }
  }
  
  // API业务错误
  if (isApiError(error)) {
    return {
      type: ErrorType.API_ERROR,
      code: error.code || error.status,
      message: error.message || '服务器处理失败，请稍后重试',
      details: error,
      timestamp
    }
  }
  
  // Error对象
  if (error instanceof Error) {
    return {
      type: ErrorType.UNKNOWN_ERROR,
      message: error.message || '操作失败，请稍后重试',
      details: error,
      timestamp
    }
  }
  
  // 字符串错误
  if (typeof error === 'string') {
    return {
      type: ErrorType.UNKNOWN_ERROR,
      message: error,
      timestamp
    }
  }
  
  // 未知错误
  return {
    type: ErrorType.UNKNOWN_ERROR,
    message: '发生未知错误，请稍后重试',
    details: error,
    timestamp
  }
}

/**
 * 显示错误消息
 */
export const showError = (error: any, context?: string) => {
  const errorInfo = parseError(error)
  
  // 用户取消操作不显示错误
  if (error === 'cancel' || error?.message === 'cancel') {
    return
  }
  
  const contextPrefix = context ? `${context}: ` : ''
  const message = `${contextPrefix}${errorInfo.message}`
  
  // 根据错误类型选择不同的显示方式
  switch (errorInfo.type) {
    case ErrorType.NETWORK_ERROR:
      ElNotification({
        title: '网络错误',
        message: message,
        type: 'error',
        duration: 5000,
        showClose: true
      })
      break
      
    case ErrorType.PERMISSION_ERROR:
      ElNotification({
        title: '权限错误',
        message: message,
        type: 'warning',
        duration: 5000,
        showClose: true
      })
      break
      
    case ErrorType.API_ERROR:
      ElMessage.error({
        message: message,
        duration: 4000,
        showClose: true
      })
      break
      
    default:
      ElMessage.error({
        message: message,
        duration: 3000,
        showClose: true
      })
  }
  
  // 记录错误日志
  console.error(`[${errorInfo.type}] ${context || 'Unknown Context'}:`, {
    message: errorInfo.message,
    code: errorInfo.code,
    details: errorInfo.details,
    timestamp: new Date(errorInfo.timestamp).toISOString()
  })
}

/**
 * 显示成功消息
 */
export const showSuccess = (message: string, duration: number = 3000) => {
  ElMessage.success({
    message,
    duration,
    showClose: true
  })
}

/**
 * 显示警告消息
 */
export const showWarning = (message: string, duration: number = 3000) => {
  ElMessage.warning({
    message,
    duration,
    showClose: true
  })
}

/**
 * 显示信息消息
 */
export const showInfo = (message: string, duration: number = 3000) => {
  ElMessage.info({
    message,
    duration,
    showClose: true
  })
}

/**
 * 加载状态管理器
 */
export class LoadingManager {
  private loadingStates: Map<string, boolean> = new Map()
  private callbacks: Map<string, (loading: boolean) => void> = new Map()
  
  /**
   * 设置加载状态
   */
  setLoading(key: string, loading: boolean) {
    this.loadingStates.set(key, loading)
    const callback = this.callbacks.get(key)
    if (callback) {
      callback(loading)
    }
  }
  
  /**
   * 获取加载状态
   */
  getLoading(key: string): boolean {
    return this.loadingStates.get(key) || false
  }
  
  /**
   * 注册加载状态回调
   */
  onLoadingChange(key: string, callback: (loading: boolean) => void) {
    this.callbacks.set(key, callback)
  }
  
  /**
   * 移除加载状态回调
   */
  offLoadingChange(key: string) {
    this.callbacks.delete(key)
  }
  
  /**
   * 清除所有加载状态
   */
  clearAll() {
    this.loadingStates.clear()
    this.callbacks.clear()
  }
}

// 全局加载状态管理器实例
export const globalLoadingManager = new LoadingManager()

/**
 * 异步操作包装器，自动处理加载状态和错误
 */
export const withLoadingAndError = async <T>(
  operation: () => Promise<T>,
  options: {
    loadingKey?: string
    context?: string
    successMessage?: string
    showSuccess?: boolean
  } = {}
): Promise<T | null> => {
  const { loadingKey, context, successMessage, showSuccess: shouldShowSuccess = false } = options
  
  try {
    // 设置加载状态
    if (loadingKey) {
      globalLoadingManager.setLoading(loadingKey, true)
    }
    
    // 执行操作
    const result = await operation()
    
    // 显示成功消息
    if (shouldShowSuccess && successMessage) {
      showSuccess(successMessage)
    }
    
    return result
  } catch (error) {
    // 显示错误消息
    showError(error, context)
    return null
  } finally {
    // 清除加载状态
    if (loadingKey) {
      globalLoadingManager.setLoading(loadingKey, false)
    }
  }
}