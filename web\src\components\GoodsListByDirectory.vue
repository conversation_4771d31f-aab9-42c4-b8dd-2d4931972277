<template>
  <div class="goods-list-by-directory">
    <!-- 页面头部组件 -->
    <PageHeader
      :current-directory-name="currentDirectoryName"
      :selected-goods="selectedGoods"
      @go-back="goBackToDirectoryList"
      @batch-operation="showBatchDialog"
      @batch-delete="showBatchDeleteDialog"
      @move-operation="showMoveDialog"
    />

    <!-- 统计卡片组件 -->
    <StatisticsCard
      :statistics-data="statisticsData"
      :loading="statisticsLoading"
      :current-directory-name="currentDirectoryName"
      :checking-images="checkingImages"
      @quick-collection-settings="handleQuickCollectionSettings"
      @quick-check-images="handleQuickCheckImages"
      @quick-publish-goods="handleQuickPublishGoods"
    />

    <!-- 采集设置卡片 -->
    <div class="collection-settings-section">
      <CollectionSettingsCard />
    </div>

    <!-- 搜索表单组件 -->
    <SearchForm
      v-model="searchForm"
      :checking-images="checkingImages"
      @search="handleSearch"
      @reset="handleReset"
      @check-images="handleCheckImages"
      @publish-goods="showPublishDialog"
    />

    <!-- 商品表格组件 -->
    <GoodsTable
      :goods-list="goodsList"
      :loading="loading"
      :pagination="pagination"
      :sort-field="sortField"
      :sort-order="sortOrder"
      :user-info="userInfo"
      @selection-change="handleSelectionChange"
      @sort="handleSort"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @image-preview="handleImagePreview"
      @sku-detail="showSkuDialog"
      @sku-image-preview="handleSkuImagePreview"
      @price-adjustment="showPriceAdjustmentDialog"
      @price-logs="showPriceAdjustmentLogs"
      @copy-link="handleCopyGoodsLink"
      @platform-categories-dialog="showPlatformCategoriesDialog"
      @edit-category-relation="showEditCategoryRelationDialog"
      @delete-goods="handleDeleteSingleGoods"
    />



    <!-- 批量操作对话框组件 -->
    <BatchOperationDialog
      v-model:batch-visible="batchDialogVisible"
      v-model:move-visible="moveDialogVisible"
      :selected-goods="selectedGoods"
      :available-directories="availableDirectories"
      :batch-submitting="batchSubmitting"
      :move-submitting="moveSubmitting"
      @batch-submit="handleBatchSubmit"
      @move-submit="handleMoveSubmit"
    />



    <!-- SKU相关组件 -->
    <SkuComponents
      v-model:sku-visible="skuDialogVisible"
      v-model:sku-image-preview-visible="skuImagePreviewVisible"
      v-model:price-adjustment-visible="priceAdjustmentDialogVisible"
      v-model:price-logs-visible="priceLogsDialogVisible"
      :current-sku-list="currentSkuList"
      :current-sku-image-url="currentSkuImageUrl"
      :current-sku-name="currentSkuName"
      :current-goods-for-price-adjustment="currentGoodsForPriceAdjustment"
      :current-goods-for-price-logs="currentGoodsForPriceLogs"
      :price-adjustment-form="priceAdjustmentForm"
      :price-adjustment-submitting="priceAdjustmentSubmitting"
      :price-adjustment-logs="priceAdjustmentLogs"
      :price-logs-loading="priceLogsLoading"
      :price-logs-pagination="priceLogsPagination"
      @sku-image-preview="handleSkuImagePreview"
      @batch-price-adjustment="handleBatchPriceAdjustment"
      @price-adjustment-submit="handlePriceAdjustmentSubmit"
      @price-logs-page-size-change="handlePriceLogsPageSizeChange"
      @price-logs-current-change="handlePriceLogsCurrentChange"
      @update:price-adjustment-form="handlePriceAdjustmentFormUpdate"
    />

    <!-- 图片预览组件 -->
    <ImagePreview
      v-model:visible="imagePreviewVisible"
      v-model:current-index="currentImageIndex"
      :image-list="currentImageList"
      :goods-name="currentGoodsName"
    />


    <!-- 平台分类组件 -->
    <PlatformCategories
      v-model:platform-categories-visible="platformCategoriesDialogVisible"
      v-model:category-link-visible="categoryLinkDialogVisible"
      :current-platform-relation="currentPlatformRelation"
      :current-goods-for-category-link="currentGoodsForCategoryLink"
      :current-temu-category="currentTemuCategory"
      :available-platforms="availablePlatforms"
      @category-link-success="handleCategoryLinkSuccess"
    />

    <!-- 商品发布设置对话框 -->
    <el-dialog
      v-model="publishDialogVisible"
      title="商品发布设置"
      width="850px"
      :close-on-click-modal="false"
    >
      <GoodsPublishSettings
        v-if="publishDialogVisible"
        :selected-goods="selectedGoods"
        :current-directory-name="currentDirectoryName"
        :directory-id="currentDirectoryId"
        @close="publishDialogVisible = false"
        @success="handlePublishSuccess"
      />
    </el-dialog>



    <!-- 采集设置对话框 -->
    <CollectionSettingsDialog
      v-model="showSettingsDialog"
      :available-directories="settingsDialogDirectories"
      :loading="settingsDialogLoading"
      :initial-data="settingsDialogInitialData"
      @confirm="handleSettingsConfirm"
      @create-directory="handleCreateDirectory"
    />

    <!-- 修改AI分类结果对话框 -->
    <EditCategoryRelationDialog
      v-model="editCategoryRelationDialogVisible"
      :current-temu-category="currentGoodsForCategoryEdit?.temuCategory || { cat_id: 0, cat_name: '', front_cat_2_path_name: '' }"
      :current-related-categories="currentGoodsForCategoryEdit?.relatedCategories || []"
      :goods-id="currentGoodsForCategoryEdit?.goodsId || 0"
      @success="handleEditCategoryRelationSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
// 只保留主组件需要的图标
import { useRouter, useRoute } from 'vue-router'
import {
  getGoodsList,
  batchUpdateGoods,
  adjustSkuPrice,
  getPriceAdjustmentLogs,
  type Goods as GoodsType,
  type GoodsListParams,
  type AdjustSkuPriceParams,
  type PriceAdjustmentLogsParams,
  type PriceAdjustmentLog
} from '../utils/goodsApi'
import { getDirectoryList, type Directory } from '../utils/directoryApi'
import { getCategoryDetail } from '../utils/categoryApi'
import { userInfo } from '../utils/userStore'
import GoodsPublishSettings from './GoodsPublishSettings.vue'
import ThirdPartyLinkage from './ThirdPartyLinkage.vue'
import CollectionSettingsCard from './CollectionSettingsCard.vue'
import CollectionSettingsDialog from './CollectionSettingsDialog.vue'
// 导入新的子组件
import PageHeader from './goodsList/PageHeader.vue'
import StatisticsCard from './goodsList/StatisticsCard.vue'
import SearchForm from './goodsList/SearchForm.vue'
import GoodsTable from './goodsList/GoodsTable.vue'
import BatchOperationDialog from './goodsList/BatchOperationDialog.vue'
import SkuComponents from './goodsList/SkuComponents.vue'
import ImagePreview from './goodsList/ImagePreview.vue'
import PlatformCategories from './goodsList/PlatformCategories.vue'
import EditCategoryRelationDialog from './goodsList/EditCategoryRelationDialog.vue'
import { getNeedImageProcessGoods, processGoodsImages } from '../utils/imageProcessApi'
import { getGoodsStatistics, type GoodsStatisticsParams, type GoodsStatisticsResponse } from '../utils/goodsStatisticsApi'
import {
  getUserCollectionSettings,
  saveUserCollectionSettings,
  getUserAvailableDirectories,
  type Directory as SettingsDirectory
} from '../utils/collectionSettingsApi'
import { type GoodsWithPlatformRelations } from '../types/platform'

// 接口定义 - 使用 GoodsType 作为基础类型，添加平台关联信息
interface Goods extends Omit<GoodsType, 'id'>, GoodsWithPlatformRelations {
  id: number  // 确保 id 是必需的
}

interface SearchForm {
  goods_name: string
  goods_id: number | null | undefined
  status: number | null | undefined
  price_adjusted: string | null | undefined
  cat_adjusted: string | null | undefined
  time_type: string
  date_range: string[] | null | undefined
  sub_account_name: string
  sub_account_phone: string
  only_sub_account: boolean
}

interface BatchForm {
  status: number | null | undefined
}

interface MoveForm {
  directory_id: number | null | undefined
}

// 响应式数据
const loading = ref(false)
const batchSubmitting = ref(false)
const moveSubmitting = ref(false)
const goodsList = ref<Goods[]>([])
const selectedGoods = ref<Goods[]>([])
const availableDirectories = ref<Directory[]>([])

// 采集设置相关数据
const showSettingsDialog = ref(false)
const settingsDialogDirectories = ref<SettingsDirectory[]>([])
const settingsDialogLoading = ref(false)
const settingsDialogInitialData = ref<any>(null)

// 统计相关数据
const statisticsLoading = ref(false)
const statisticsData = ref<GoodsStatisticsResponse | null>(null)
let statisticsTimer: ReturnType<typeof setTimeout> | null = null

// 排序相关数据
const sortField = ref('id')
const sortOrder = ref('desc')

// 价格调整相关数据
const priceAdjustmentDialogVisible = ref(false)
const priceAdjustmentSubmitting = ref(false)
const currentGoodsForPriceAdjustment = ref<Goods | null>(null)
// const priceAdjustmentFormRef = ref() // 已移到子组件中
const priceAdjustmentForm = reactive({
  skus: [] as Array<{
    id: number
    spec_values: string
    original_price: number
    new_price: number
    currency: string
    thumb_url: string
  }>,
  batch_price: null as number | null
})

// 价格调整日志相关数据
const priceLogsDialogVisible = ref(false)
const priceLogsLoading = ref(false)
const currentGoodsForPriceLogs = ref<Goods | null>(null)
const priceAdjustmentLogs = ref<PriceAdjustmentLog[]>([])
const priceLogsPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})





const router = useRouter()
const route = useRoute()

// SKU相关数据
const currentSkuList = ref<any[]>([])
const skuDialogVisible = ref(false)

// 图片预览相关数据
const currentImageList = ref<string[]>([])
const currentImageIndex = ref(0)
const currentGoodsName = ref('')
const imagePreviewVisible = ref(false)

// SKU图片预览相关数据
const skuImagePreviewVisible = ref(false)
const currentSkuImageUrl = ref('')
const currentSkuName = ref('')

// 平台分类相关数据
const currentPlatformRelation = ref<any>(null)
const platformCategoriesDialogVisible = ref(false)

// 分类关联相关数据
const currentGoodsForCategoryLink = ref<Goods | null>(null)
const currentTemuCategory = ref<any>(null)
const categoryLinkDialogVisible = ref(false)

// 发布商品相关数据
const publishDialogVisible = ref(false)

// 修改AI分类结果相关数据
const editCategoryRelationDialogVisible = ref(false)
const currentGoodsForCategoryEdit = ref<{
  goodsId: number
  temuCategory: {
    cat_id: number
    cat_name: string
    front_cat_2_path_name: string
  }
  relatedCategories: Array<{
    id: number
    name: string
    name_tl: string
    path_name: string
    path_name_tl: string
  }>
} | null>(null)

// 图片处理相关数据
const checkingImages = ref(false)
const processingImages = ref(false)
let currentImageProcessNotification: any = null

// 可用平台列表
const availablePlatforms = ref([
  {
    id: 1,
    name: 'N11',
    code: 'n11',
    apiUrl: 'apiCatN11ListUrl'
  }
])

// 当前目录信息
const currentDirectoryId = ref<number>(0)
const currentDirectoryName = ref<string>('')

// 搜索表单
const searchForm = ref<SearchForm>({
  goods_name: '',
  goods_id: null,
  status: null,
  price_adjusted: null,
  cat_adjusted: null,
  time_type: 'created_at',
  date_range: null,
  sub_account_name: '',
  sub_account_phone: '',
  only_sub_account: false
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 对话框状态
const batchDialogVisible = ref(false)
const moveDialogVisible = ref(false)

// 表单数据
const batchForm = reactive<BatchForm>({
  status: undefined
})

const moveForm = reactive<MoveForm>({
  directory_id: undefined
})

// const batchFormRef = ref() // 已移到子组件中
const moveFormRef = ref()

// 表单验证规则 - 已移到子组件中
// const moveFormRules = {
//   directory_id: [
//     { required: true, message: '请选择目标目录', trigger: 'change' }
//   ]
// }

// 获取序号 - 已移到子组件中
// const getIndex = (index: number) => {
//   return (pagination.currentPage - 1) * pagination.pageSize + index + 1
// }



// 返回目录列表
const goBackToDirectoryList = () => {
  router.push({ name: 'GoodsDirectoryList' })
}

// 加载可用目录列表
const loadAvailableDirectories = async () => {
  try {
    const response = await getDirectoryList({ page: 1, pageSize: 100, status: 1 })
    availableDirectories.value = response.list
  } catch (error) {
    console.error('获取目录列表失败:', error)
  }
}

// 加载商品列表
const loadGoodsList = async () => {
  loading.value = true
  try {
    const params: GoodsListParams = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      goods_name: searchForm.value.goods_name || undefined,
      goods_id: (searchForm.value.goods_id && !isNaN(Number(searchForm.value.goods_id))) ? Number(searchForm.value.goods_id) : undefined,
      status: (searchForm.value.status === null || searchForm.value.status === undefined) ? undefined : searchForm.value.status,
      directory_id: currentDirectoryId.value,
      price_adjusted: searchForm.value.price_adjusted || undefined,
      cat_adjusted: searchForm.value.cat_adjusted || undefined,
      time_type: searchForm.value.time_type || undefined,
      start_date: searchForm.value.date_range?.[0] || undefined,
      end_date: searchForm.value.date_range?.[1] || undefined,
      sub_account_name: searchForm.value.sub_account_name || undefined,
      sub_account_phone: searchForm.value.sub_account_phone || undefined,
      only_sub_account: searchForm.value.only_sub_account || undefined,
      sort_field: sortField.value,
      sort_order: sortOrder.value
    }

    const response = await getGoodsList(params)
    goodsList.value = response.list as Goods[]
    pagination.total = response.pagination.total
    pagination.totalPages = response.pagination.totalPages
    pagination.hasNext = response.pagination.hasNext
    pagination.hasPrevious = response.pagination.hasPrevious
  } catch (error) {
    console.log('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadGoodsList()
  loadGoodsStatistics() // 搜索后更新统计
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm.value, {
    goods_name: '',
    goods_id: null,
    status: null,
    price_adjusted: null,
    cat_adjusted: null,
    time_type: 'created_at',
    date_range: null,
    sub_account_name: '',
    sub_account_phone: '',
    only_sub_account: false
  })
  pagination.currentPage = 1
  loadGoodsList()
  loadGoodsStatistics() // 重置后更新统计
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadGoodsList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadGoodsList()
}

// 选择变化
const handleSelectionChange = (selection: Goods[]) => {
  selectedGoods.value = selection
}

// 显示批量设置对话框
const showBatchDialog = () => {
  batchForm.status = undefined
  batchDialogVisible.value = true
}

// 显示移动对话框
const showMoveDialog = () => {
  moveForm.directory_id = undefined
  moveDialogVisible.value = true
}

// 显示批量删除对话框
const showBatchDeleteDialog = () => {
  if (selectedGoods.value.length === 0) {
    ElMessage.warning('请先选择要删除的商品')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedGoods.value.length} 个商品吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(() => {
    handleBatchDeleteGoods()
  }).catch(() => {
    // 用户取消删除
  })
}

// 处理批量删除商品（通过设置状态为0）
const handleBatchDeleteGoods = async (goodsList?: Goods[]) => {
  try {
    const targetGoods = goodsList || selectedGoods.value
    const ids = targetGoods.map(goods => goods.id)

    const updateData = {
      ids,
      status: 0  // 设置状态为0（禁用）相当于删除
    }

    await batchUpdateGoods(updateData)

    const message = `已成功删除 ${targetGoods.length} 个商品。`
    ElNotification({
      title: '删除成功',
      message,
      type: 'success'
    })

    // 清空选中的商品并重新加载列表
    selectedGoods.value = []
    loadGoodsList()
  } catch (error) {
    console.error('删除商品失败:', error)
    ElMessage.error('删除商品失败')
  }
}

// 处理单个商品删除
const handleDeleteSingleGoods = (goods: Goods) => {
  ElMessageBox.confirm(
    `确定要删除商品 "${goods.goods_name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(() => {
    handleBatchDeleteGoods([goods])
  }).catch(() => {
    // 用户取消删除
  })
}

// 复制商品链接
const handleCopyGoodsLink = async (row: Goods) => {
  if (!row.source_url) {
    ElMessage.warning('该商品没有原始链接')
    return
  }

  try {
    await navigator.clipboard.writeText(row.source_url)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    // 降级处理：使用传统方法复制
    const textArea = document.createElement('textarea')
    textArea.value = row.source_url
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('链接已复制到剪贴板')
    } catch (err) {
      ElMessage.error('复制失败，请手动复制链接')
    }
    document.body.removeChild(textArea)
  }
}

// 批量设置提交
const handleBatchSubmit = async (formData: BatchForm) => {
  try {
    if (formData.status === undefined || formData.status === null) {
      ElMessage.warning('请选择要设置的状态')
      return
    }

    batchSubmitting.value = true

    const ids = selectedGoods.value.map(goods => goods.id)
    const updateData: any = {
      ids,
      status: formData.status
    }

    await batchUpdateGoods(updateData)

    const statusText = formData.status === 1 ? '正常' : '禁用'
    const message = `已成功设置 ${selectedGoods.value.length} 个商品的状态为${statusText}。`

    ElNotification({
      title: '批量设置成功',
      message,
      type: 'success'
    })

    batchDialogVisible.value = false
    selectedGoods.value = []
    loadGoodsList()
  } catch (error) {
    console.error('批量设置失败:', error)
    ElMessage.error('批量设置失败')
  } finally {
    batchSubmitting.value = false
  }
}

// 移动商品提交
const handleMoveSubmit = async (formData: MoveForm) => {
  try {
    if (formData.directory_id === undefined || formData.directory_id === null) {
      ElMessage.warning('请选择目标目录')
      return
    }

    moveSubmitting.value = true

    const ids = selectedGoods.value.map(goods => goods.id)
    const updateData: any = {
      ids,
      directory_id: formData.directory_id
    }

    await batchUpdateGoods(updateData)

    const targetDirectoryName = formData.directory_id === 0
      ? '未分类'
      : availableDirectories.value.find(d => d.id === formData.directory_id)?.name || '未知目录'

    const message = `已成功移动 ${selectedGoods.value.length} 个商品到"${targetDirectoryName}"。`

    ElNotification({
      title: '移动成功',
      message,
      type: 'success'
    })

    moveDialogVisible.value = false
    selectedGoods.value = []
    loadGoodsList()
  } catch (error) {
    console.error('移动商品失败:', error)
    ElMessage.error('移动商品失败')
  } finally {
    moveSubmitting.value = false
  }
}



// 显示平台分类详情对话框
const showPlatformCategoriesDialog = (relation: any) => {
  currentPlatformRelation.value = relation
  platformCategoriesDialogVisible.value = true
}

// 显示修改AI分类结果对话框
const showEditCategoryRelationDialog = (row: Goods) => {
  // 准备TEMU分类信息
  const temuCategory = {
    cat_id: row.cat_id,
    cat_name: row.cat_name,
    front_cat_2_path_name: row.front_cat_2_path_name || ''
  }

  // 准备已关联的N11分类信息
  const relatedCategories = []
  if (row.platform_relations && Array.isArray(row.platform_relations)) {
    const n11Relations = row.platform_relations.filter((relation: any) => relation.third_platform_id === 2)
    for (const relation of n11Relations) {
      if (relation.third_platform_categories && Array.isArray(relation.third_platform_categories)) {
        relatedCategories.push(...relation.third_platform_categories.map((cat: any) => ({
          id: cat.id,
          name: cat.name,
          name_tl: cat.name_tl || '',
          path_name: cat.path_name || '',
          path_name_tl: cat.path_name_tl || ''
        })))
      }
    }
  }

  currentGoodsForCategoryEdit.value = {
    goodsId: row.id,
    temuCategory,
    relatedCategories
  }

  editCategoryRelationDialogVisible.value = true
}

// 处理修改AI分类结果成功
const handleEditCategoryRelationSuccess = () => {
  editCategoryRelationDialogVisible.value = false
  currentGoodsForCategoryEdit.value = null
  ElMessage.success('关联分类更新成功')
  // 刷新商品列表以显示最新的关联分类信息
  loadGoodsList()
}

// 处理图片预览点击
const handleImagePreview = (row: Goods) => {
  if (row.goods_pic && row.goods_pic.length > 0) {
    currentImageList.value = row.goods_pic
    currentImageIndex.value = 0
    currentGoodsName.value = row.goods_name
    imagePreviewVisible.value = true
  }
}

// 显示SKU详情对话框
const showSkuDialog = (row: Goods) => {
  currentSkuList.value = row.formatted_skus || []
  skuDialogVisible.value = true
}

// 处理SKU图片预览
const handleSkuImagePreview = (skuItem: any) => {
  if (skuItem.thumb_url_h500 || skuItem.thumb_url) {
    currentSkuImageUrl.value = skuItem.thumb_url_h500 || skuItem.thumb_url
    currentSkuName.value = skuItem.sku || 'SKU图片'
    skuImagePreviewVisible.value = true
  }
}

// 处理关联商品分类
const handleCategoryLink = async (row: Goods) => {
  try {
    // 根据商品的cat_id获取Temu分类信息
    const categoryInfo = await getCategoryDetail(row.cat_id)
    if (categoryInfo) {
      currentGoodsForCategoryLink.value = row
      currentTemuCategory.value = {
        id: categoryInfo.id,
        name: categoryInfo.name,
        path_name: categoryInfo.path_name,
        level: categoryInfo.level,
        is_leaf: categoryInfo.is_leaf
      }
      categoryLinkDialogVisible.value = true
    } else {
      ElMessage.warning('无法获取商品分类信息')
    }
  } catch (error) {
    console.error('获取商品分类信息失败:', error)
    ElMessage.error('获取商品分类信息失败')
  }
}

// 处理关联商品分类成功后的回调
const handleCategoryLinkSuccess = () => {
  categoryLinkDialogVisible.value = false
  currentGoodsForCategoryLink.value = null
  currentTemuCategory.value = null
  ElMessage.success('商品分类关联成功')
  // 刷新商品列表以显示最新的关联分类信息
  loadGoodsList()
}

// 检查商品图片
const handleCheckImages = async () => {
  try {
    checkingImages.value = true

    // 获取需要处理图片的商品ID列表
    const response = await getNeedImageProcessGoods(currentDirectoryId.value)
    console.log('获取需要处理图片的商品列表响应:', response)

    // 根据实际返回的数据结构获取商品ID列表
    let goodsIds = []
    if (response && response.goods_ids) {
      goodsIds = response.goods_ids
    }

    if (goodsIds.length === 0) {
      ElMessage.success('没有商品图片需要处理')
      return
    }

    // 仅显示提示信息，不自动处理图片
    ElMessageBox.alert(
      `检测到当前目录下有${goodsIds.length}个商品的图片未处理完成，请先使用跨境蜂助手操作本地化图片`,
      '图片处理提醒',
      {
        confirmButtonText: '确定',
        type: 'warning'
      }
    )


    // 开始处理图片
    //await startBatchImageProcessing(goodsIds)

  } catch (error) {
    console.error('检查商品图片失败:', error)
    ElMessage.error(`检查商品图片失败: ${error.message || error}`)
  } finally {
    checkingImages.value = false
  }
}

// 批量处理商品图片
const startBatchImageProcessing = async (goodsIds: number[]) => {
  processingImages.value = true
  let processedCount = 0
  console.log('开始批量处理商品图片------------:', goodsIds)
  try {
    for (let i = 0; i < goodsIds.length; i++) {
      const goodsId = goodsIds[i]
      const currentIndex = i + 1
      const remainingCount = goodsIds.length - currentIndex

      // 关闭之前的通知
      if (currentImageProcessNotification) {
        currentImageProcessNotification.close()
        currentImageProcessNotification = null
      }

      // 显示当前处理的商品信息
      currentImageProcessNotification = ElNotification({
        title: '批量处理商品图片',
        message: `共${goodsIds.length}个商品，当前处理第${currentIndex}个，商品ID：${goodsId}，剩余${remainingCount}个`,
        type: 'info',
        duration: 0
      })

      // 处理单个商品的图片
      await processSingleGoodsImages(goodsId, currentIndex, goodsIds.length)
      processedCount++
    }

    // 处理完成
    if (currentImageProcessNotification) {
      currentImageProcessNotification.close()
      currentImageProcessNotification = null
    }

    ElNotification({
      title: '图片处理完成',
      message: `已成功处理${processedCount}个商品的图片`,
      type: 'success',
      duration: 0
    })

    // 刷新商品列表
    loadGoodsList()

  } catch (error) {
    console.error('批量处理图片失败------------:', error)
    if (currentImageProcessNotification) {
      currentImageProcessNotification.close()
      currentImageProcessNotification = null
    }
    ElMessage.error('批量处理图片失败')
  } finally {
    processingImages.value = false
  }
}

// 处理单个商品的图片
const processSingleGoodsImages = async (goodsId: number, currentIndex: number, totalCount: number) => {
  try {
    // 第一次请求获取处理信息
    const initialResult = await processGoodsImages(goodsId, 1)
    console.log(`商品${goodsId}初始处理结果:`, initialResult)

    // 根据实际返回的数据结构获取处理信息
    let processInfo = null
    if (initialResult && initialResult.directory_name !== undefined) {
      // 直接返回的业务数据
      processInfo = initialResult
    }

    if (!processInfo) {
      console.error(`商品${goodsId}处理信息获取失败，返回数据:`, initialResult)
      throw new Error('获取图片处理信息失败：返回数据格式不正确')
    }

    let currentStep = 2
    let isCompleted = processInfo.img_local_status === 1

    // 如果已经处理完成，直接返回
    if (isCompleted) {
      console.log(`商品${goodsId}图片已处理完成`)
      return
    }

    // 更新通知显示处理信息
    if (currentImageProcessNotification) {
      currentImageProcessNotification.close()
    }

    const mediaInfo = []
    if (processInfo.total_images > 0) {
      mediaInfo.push(`${processInfo.total_images}张图片`)
    }
    if (processInfo.total_videos > 0) {
      mediaInfo.push(`${processInfo.total_videos}个视频`)
    }
    if (processInfo.total_pdfs > 0) {
      mediaInfo.push(`${processInfo.total_pdfs}个PDF文件`)
    }

    const mediaText = mediaInfo.length > 0 ? `，包含${mediaInfo.join('、')}` : ''

    currentImageProcessNotification = ElNotification({
      title: '批量处理商品图片',
      message: `共${totalCount}个商品，当前处理第${currentIndex}个，商品ID：${goodsId}${mediaText}`,
      type: 'info',
      duration: 0
    })

    // 循环处理直到完成
    const maxSteps = processInfo.estimated_steps || 10
    let processedSteps = 0

    while (!isCompleted && processedSteps < maxSteps + 5) {
      try {
        const result = await processGoodsImages(goodsId, currentStep)
        console.log(`商品${goodsId}步骤${currentStep}处理结果:`, result)

        // 根据实际返回的数据结构获取处理结果
        let data = null
        if (result && result.directory_name !== undefined) {
          // 直接返回的业务数据
          data = result
        }

        if (!data) {
          console.error(`商品${goodsId}步骤${currentStep}处理失败，返回数据:`, result)
          throw new Error('图片处理请求失败：返回数据格式不正确')
        }

        isCompleted = data.img_local_status === 1

        // 更新进度通知
        if (!isCompleted) {
          const totalMedia = (data.total_images || 0) + (data.total_videos || 0) + (data.total_pdfs || 0)
          const processedMedia = (data.processed_images || 0) + (data.processed_videos || 0) + (data.processed_pdfs || 0)
          const progress = totalMedia > 0 ? Math.min(100, Math.round(processedMedia / totalMedia * 100)) : 0

          if (currentImageProcessNotification) {
            currentImageProcessNotification.close()
          }

          currentImageProcessNotification = ElNotification({
            title: '批量处理商品图片',
            message: `共${totalCount}个商品，当前处理第${currentIndex}个，商品ID：${goodsId}，进度：${progress}% (${data.processed_images || 0}/${data.total_images || 0}张图片, ${data.processed_videos || 0}/${data.total_videos || 0}个视频, ${data.processed_pdfs || 0}/${data.total_pdfs || 0}个PDF文件)`,
            type: 'info',
            duration: 0
          })
        }

        currentStep++
        processedSteps++

        if (isCompleted) {
          console.log(`商品${goodsId}图片处理完成`)
          break
        }

        // 短暂延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 500))

      } catch (stepError) {
        console.error(`商品${goodsId}图片处理步骤 ${currentStep} 失败:`, stepError)
        currentStep++
        processedSteps++

        if (processedSteps >= maxSteps + 3) {
          console.warn(`商品${goodsId}处理步骤超过最大限制，停止处理`)
          break
        }
      }
    }

  } catch (error) {
    console.error(`处理商品${goodsId}图片失败:`, error)
    throw error
  }
}

// 显示发布设置对话框（修改为先检查图片）
const showPublishDialog = async () => {
  try {
    // 先检查是否有需要处理的图片
    const response = await getNeedImageProcessGoods(currentDirectoryId.value)
    console.log('发布前检查图片响应:', response)

    // 根据实际返回的数据结构获取商品ID列表
    let goodsIds = []
    if (response && response.goods_ids) {
      goodsIds = response.goods_ids
    } else if (response && response.data && response.data.goods_ids) {
      goodsIds = response.data.goods_ids
    }

    if (goodsIds.length > 0) {
      // 仅显示提示信息，强制用户手动操作
      ElMessageBox.alert(
        `检测到当前目录下有${goodsIds.length}个商品的图片未处理完成，请先使用跨境蜂助手操作本地化图片`,
        '图片处理提醒',
        {
          confirmButtonText: '确定',
          type: 'warning'
        }
      )
      return // 强制返回，用户需手动处理图片后再次点击发布
    }

    // 没有需要处理的图片，直接显示发布对话框
    publishDialogVisible.value = true

  } catch (error) {
    console.log('检查图片状态失败:', error)
    return false;
    /* console.error('检查图片状态失败:', error)
    ElMessage.warning(`检查图片状态失败: ${error.message || error}，将直接进入发布流程`)
    // 检查失败，直接显示发布对话框
    publishDialogVisible.value = true */
  }
}

// 发布成功回调
const handlePublishSuccess = () => {
  publishDialogVisible.value = false
  // 使用 router.push 进行路由跳转
  setTimeout(() => {
    router.push('/tasks')
  }, 1000)
}

// 移除了 handleSettingsUpdated 函数，因为 CollectionSettingsCard 现在是独立组件

// 快捷操作方法
const handleQuickCollectionSettings = async () => {
  try {
    // 直接显示采集设置对话框
    settingsDialogLoading.value = true

    // 加载目录数据
    const response = await getUserAvailableDirectories()
    settingsDialogDirectories.value = response.list || []

    // 加载当前设置
    const settings = await getUserCollectionSettings()
    settingsDialogInitialData.value = settings

    // 显示对话框
    showSettingsDialog.value = true
  } catch (error) {
    console.error('加载采集设置失败:', error)
    ElMessage.warning('加载采集设置失败，将滚动到设置区域')

    // 备用方案：滚动到采集设置区域
    const collectionSettingsSection = document.querySelector('.collection-settings-section')
    if (collectionSettingsSection) {
      collectionSettingsSection.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
      // 添加高亮效果
      collectionSettingsSection.classList.add('highlight-flash')
      setTimeout(() => {
        collectionSettingsSection.classList.remove('highlight-flash')
      }, 2000)
    }
  } finally {
    settingsDialogLoading.value = false
  }
}

const handleQuickCheckImages = () => {
  // 直接调用原有的检查图片方法
  handleCheckImages()
}

const handleQuickPublishGoods = () => {
  // 直接调用原有的发布商品方法
  showPublishDialog()
}

// 加载商品统计数据
const loadGoodsStatistics = async () => {
  // 清除之前的定时器
  if (statisticsTimer) {
    clearTimeout(statisticsTimer)
  }

  // 防抖处理，避免频繁请求，增加延迟时间减少与拖拽操作的冲突
  statisticsTimer = setTimeout(async () => {
    try {
      statisticsLoading.value = true
      statisticsData.value = null

      // 使用 requestAnimationFrame 确保在下一个渲染帧执行，避免与 ResizeObserver 冲突
      await new Promise(resolve => requestAnimationFrame(resolve))

      // 构建统计参数 - 按全部获取该目录下的总数据
      const params: GoodsStatisticsParams = {
        directory_id: currentDirectoryId.value,
        time_range: 'all'
      }

      const response = await getGoodsStatistics(params)
      statisticsData.value = response
    } catch (error) {
      console.error('获取商品统计失败:', error)
      statisticsData.value = null
    } finally {
      statisticsLoading.value = false
    }
  }, 500) // 增加到 500ms 防抖，减少与拖拽操作的冲突
}

// 处理采集设置确认
const handleSettingsConfirm = async (data: any) => {
  try {
    settingsDialogLoading.value = true
    const result = await saveUserCollectionSettings(data)

    ElMessage.success('设置保存成功')
    showSettingsDialog.value = false

    // CollectionSettingsCard 组件会自动重新加载设置，无需手动处理
  } catch (error) {
    console.error('保存采集设置失败:', error)
    ElMessage.error('保存采集设置失败')
  } finally {
    settingsDialogLoading.value = false
  }
}

// 处理创建目录
const handleCreateDirectory = () => {
  // 这里可以添加创建目录的逻辑，或者使用已有的目录创建对话框
  ElMessage.info('请使用目录管理功能创建新目录')
}

// 排序处理
const handleSort = (field: string, order: string) => {
  sortField.value = field
  sortOrder.value = order
  // 重新加载商品列表
  pagination.currentPage = 1
  loadGoodsList()
}

// 显示价格调整对话框
const showPriceAdjustmentDialog = (row: Goods) => {
  currentGoodsForPriceAdjustment.value = row

  // 初始化SKU价格调整表单
  priceAdjustmentForm.skus = row.formatted_skus?.map((sku) => ({
    id: sku.id, // 使用实际的SKU ID
    spec_values: sku.sku,
    original_price: sku.price,
    new_price: sku.price,
    currency: sku.currentcy,
    thumb_url: sku.thumb_url || ''
  })) || []

  // 每次打开对话框时重置批量价格输入框为空
  priceAdjustmentForm.batch_price = null

  priceAdjustmentDialogVisible.value = true
}

// 批量调整价格
const handleBatchPriceAdjustment = () => {
  if (!priceAdjustmentForm.batch_price || priceAdjustmentForm.batch_price <= 0) {
    ElMessage.warning('请输入批量调整价格')
    return
  }

  if (priceAdjustmentForm.batch_price < 0.01) {
    ElMessage.error('价格不能小于0.01')
    return
  }

  // 将批量价格应用到所有SKU
  priceAdjustmentForm.skus.forEach(sku => {
    sku.new_price = priceAdjustmentForm.batch_price!
  })

  ElMessage.success('sku价格已批量填入，请点击确定调整按钮')
}

// 提交价格调整
const handlePriceAdjustmentSubmit = async () => {
  if (!currentGoodsForPriceAdjustment.value) return

  // 验证所有价格不能小于0.01
  const invalidPrices = priceAdjustmentForm.skus.filter(sku => sku.new_price < 0.01)
  if (invalidPrices.length > 0) {
    ElMessage.error('所有商品价格不能小于0.01')
    return
  }

  try {
    priceAdjustmentSubmitting.value = true

    // 构建调整参数
    const adjustments = priceAdjustmentForm.skus
      .filter(sku => Math.abs(sku.new_price - sku.original_price) >= 0.01)
      .map(sku => ({
        sku_id: sku.id,
        new_price: sku.new_price
      }))

    if (adjustments.length === 0) {
      ElMessage.warning('您没有调整商品SKU价格')
      return
    }

    const params: AdjustSkuPriceParams = {
      goods_id: currentGoodsForPriceAdjustment.value.id!,
      sku_adjustments: adjustments
    }

    const result = await adjustSkuPrice(params)
    ElMessage.success(result.message)

    // 关闭对话框并重新加载商品列表
    priceAdjustmentDialogVisible.value = false
    loadGoodsList()

  } catch (error) {
    console.error('价格调整失败:', error)
    ElMessage.error('价格调整失败')
  } finally {
    priceAdjustmentSubmitting.value = false
  }
}

// 显示价格调整日志
const showPriceAdjustmentLogs = async (row: Goods) => {
  currentGoodsForPriceLogs.value = row
  priceLogsDialogVisible.value = true

  // 重置分页
  priceLogsPagination.currentPage = 1
  priceLogsPagination.pageSize = 10

  // 加载日志
  await loadPriceAdjustmentLogs()
}

// 加载价格调整日志
const loadPriceAdjustmentLogs = async () => {
  if (!currentGoodsForPriceLogs.value) return

  try {
    priceLogsLoading.value = true

    const params: PriceAdjustmentLogsParams = {
      goods_id: currentGoodsForPriceLogs.value.id!,
      page: priceLogsPagination.currentPage,
      pageSize: priceLogsPagination.pageSize
    }

    const response = await getPriceAdjustmentLogs(params)
    priceAdjustmentLogs.value = response.list
    priceLogsPagination.total = response.pagination.total

  } catch (error) {
    console.error('获取价格调整日志失败:', error)
    ElMessage.error('获取价格调整日志失败')
  } finally {
    priceLogsLoading.value = false
  }
}

// 价格调整日志分页处理
const handlePriceLogsPageSizeChange = (pageSize: number) => {
  priceLogsPagination.pageSize = pageSize
  priceLogsPagination.currentPage = 1
  loadPriceAdjustmentLogs()
}

const handlePriceLogsCurrentChange = (page: number) => {
  priceLogsPagination.currentPage = page
  loadPriceAdjustmentLogs()
}

// 获取价格变化样式类
const getPriceChangeClass = (changeType: string) => {
  switch (changeType) {
    case 'increase':
      return 'price-increase'
    case 'decrease':
      return 'price-decrease'
    default:
      return 'price-unchanged'
  }
}

// 处理价格调整表单更新
const handlePriceAdjustmentFormUpdate = (formData: any) => {
  Object.assign(priceAdjustmentForm, formData)
}

// 组件挂载时初始化
onMounted(() => {
  // 添加 ResizeObserver 错误捕获，抑制无害的警告
  const originalError = window.console.error
  window.console.error = (...args) => {
    if (args[0] && typeof args[0] === 'string' &&
        args[0].includes('ResizeObserver loop completed with undelivered notifications')) {
      // 抑制 ResizeObserver 循环警告，这是一个已知的无害警告
      return
    }
    originalError.apply(console, args)
  }

  // 获取路由参数
  currentDirectoryId.value = parseInt(route.params.directoryId as string) || 0
  currentDirectoryName.value = (route.query.directoryName as string) || '未知目录'

  // 初始化排序字段
  sortField.value = 'id'
  sortOrder.value = 'desc'

  // 加载数据
  loadAvailableDirectories()
  loadGoodsList()
  loadGoodsStatistics()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (statisticsTimer) {
    clearTimeout(statisticsTimer)
    statisticsTimer = null
  }
})
</script>

<style scoped>
  .goods-list-by-directory {
    padding: 20px;
  }
</style>
