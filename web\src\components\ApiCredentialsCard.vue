<template>
  <div class="api-credentials-card">
    <div class="card-header">
      <h3>API凭证管理</h3>
      <div class="header-actions">
        <el-button 
          v-if="!hasCredentials"
          type="primary" 
          size="small" 
          @click="handleGenerateCredentials"
          :loading="loading"
        >
          <el-icon><Key /></el-icon>
          获取凭证
        </el-button>
        <el-button
          v-else
          type="success" 
          size="small" 
          @click="handleViewCredentials"
          :loading="loading"
        >
          <el-icon><View /></el-icon>
          查看凭证
        </el-button>
        <el-button 
          v-if="hasCredentials"
          type="warning" 
          size="small" 
          @click="handleResetCredentials"
          :loading="loading"
        >
          <el-icon><RefreshRight /></el-icon>
          重置凭证
        </el-button>
      </div>
    </div>
    
    <div class="card-content">
      <div v-if="hasCredentials" class="credentials-info">
        <!-- AppID 信息卡片 -->
        <div class="credential-card">
          <div class="credential-card-header">
            <div class="credential-icon">
              <el-icon><Key /></el-icon>
            </div>
            <div class="credential-title">
              <span class="title-text">AppID</span>
              <span class="title-desc">应用标识符</span>
            </div>
          </div>
          <div class="credential-card-content">
            <div class="credential-display-wrapper">
              <span class="credential-display">{{ props.userInfo.appid }}</span>
              <el-button
                type="text"
                size="small"
                class="copy-btn"
                @click="copyToClipboard(props.userInfo.appid || '', 'AppID')"
                title="复制AppID"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <!-- 状态信息卡片 -->
        <div class="credential-card">
          <div class="credential-card-header">
            <div class="credential-icon status-icon">
              <el-icon><CircleCheckFilled /></el-icon>
            </div>
            <div class="credential-title">
              <span class="title-text">凭证状态</span>
              <span class="title-desc">当前使用状态</span>
            </div>
          </div>
          <div class="credential-card-content">
            <el-tag
              :type="props.userInfo.appstatus === 1 ? 'success' : 'warning'"
              size="large"
              class="status-tag"
            >
              {{ props.userInfo.appstatus === 1 ? '正常使用' : '待激活' }}
            </el-tag>
          </div>
        </div>

        <!-- 权限信息卡片 -->
        <div class="credential-card">
          <div class="credential-card-header">
            <div class="credential-icon permission-icon">
              <el-icon><Lock /></el-icon>
            </div>
            <div class="credential-title">
              <span class="title-text">API权限</span>
              <span class="title-desc">可用功能权限</span>
            </div>
          </div>
          <div class="credential-card-content">
            <div class="permission-list">
              <div class="permission-item">
                <el-icon class="permission-check"><CircleCheckFilled /></el-icon>
                <span>API数据调用</span>
              </div>
              <div class="permission-item">
                <el-icon class="permission-check"><CircleCheckFilled /></el-icon>
                <span>商品信息查询</span>
              </div>
              <div class="permission-item">
                <el-icon class="permission-check"><CircleCheckFilled /></el-icon>
                <span>商品图片处理</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作提示卡片 -->
        <div class="credential-card tips-card">
          <div class="credential-card-header">
            <div class="credential-icon tips-icon">
              <el-icon><InfoFilled /></el-icon>
            </div>
            <div class="credential-title">
              <span class="title-text">使用提示</span>
              <span class="title-desc">重要操作说明</span>
            </div>
          </div>
          <div class="credential-card-content">
            <div class="tips-content">
              <p>• 请妥善保管您的API凭证，避免泄露</p>
              <p>• 如需重置凭证，请点击上方"重置凭证"按钮</p>
              <p>• 凭证重置后，旧凭证将立即失效</p>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else-if="loading" class="credentials-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在处理...</span>
      </div>
      
      <div v-else class="credentials-empty">
        <div class="empty-icon">
          <el-icon><Key /></el-icon>
        </div>
        <div class="empty-text">
          <p class="empty-title">尚未生成API凭证</p>
          <p class="empty-description">点击"获取凭证"按钮生成您的API访问凭证</p>
        </div>
      </div>
    </div>

    <!-- API凭证显示弹窗 -->
    <ApiCredentialsDialog
      v-model="showCredentialsDialog"
      :credentials="currentCredentials"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Key,
  View,
  RefreshRight,
  DocumentCopy,
  CircleCheckFilled,
  Loading,
  Lock,
  InfoFilled
} from '@element-plus/icons-vue'
import ApiCredentialsDialog from './ApiCredentialsDialog.vue'
import { generateApiCredentials, type ApiCredentialsData } from '../utils/apiCredentialsApi'
import { fetchUserInfo } from '../utils/api'

interface UserInfo {
  id: number
  phone: string
  is_vip: number
  vip_end_time: string
  is_admin: number
  is_card_admin: number
  is_sub: number
  appid: string | null
  appstatus: number
  token: string
  points: number
}

// Props
interface Props {
  userInfo: UserInfo
}

// Emits
interface Emits {
  'credentials-updated': [userInfo: UserInfo]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const showCredentialsDialog = ref(false)
const currentCredentials = ref<ApiCredentialsData>({
  appid: '',
  appsecret: '',
  created_at: '',
  updated_at: ''
})

// 计算属性
const hasCredentials = computed(() => {
  return props.userInfo.appid && props.userInfo.appid.trim() !== ''
})

const maskedAppId = computed(() => {
  if (!props.userInfo.appid) return ''
  const appid = props.userInfo.appid
  if (appid.length <= 8) return appid
  return appid.substring(0, 4) + '****' + appid.substring(appid.length - 4)
})

/**
 * 生成API凭证
 */
const handleGenerateCredentials = async () => {
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      '生成API凭证后，您将获得用于API调用的AppID和AppSecret。请注意：<br />' +
      '• AppSecret仅在首次生成时显示，请务必妥善保存<br />' +
      '• 请勿将凭证信息泄露给他人<br />' +
      '• 如果凭证丢失，您可以重置生成新的凭证<br />' +
      '• 重置凭证将使旧凭证失效<br />' +
      '确定要生成API凭证吗？',
      '确认生成API凭证',
      {
        confirmButtonText: '确定生成',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customClass: 'api-credentials-confirm-dialog'
      }
    )

    loading.value = true

    const credentials = await generateApiCredentials()

    // 更新当前凭证信息
    currentCredentials.value = credentials

    // 刷新用户信息
    await refreshUserInfo()

    // 显示凭证弹窗
    showCredentialsDialog.value = true

    ElMessage.success('API凭证生成成功')
  } catch (error: any) {
    if (error === 'cancel') {
      return // 用户取消操作
    }
    console.error('生成API凭证失败:', error)
    ElMessage.error(error.message || '生成API凭证失败')
  } finally {
    loading.value = false
  }
}

/**
 * 查看API凭证
 */
const handleViewCredentials = () => {
  if (!props.userInfo.appid) {
    ElMessage.warning('未找到API凭证信息')
    return
  }
  
  // 设置当前凭证信息（查看时不显示secret）
  currentCredentials.value = {
    appid: props.userInfo.appid,
    appsecret: '', // 查看时不显示secret
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  showCredentialsDialog.value = true
}

/**
 * 重置API凭证
 */
const handleResetCredentials = async () => {
  try {
    await ElMessageBox.confirm(
      '重置凭证将使当前的API凭证失效，您需要使用新的凭证进行API调用。<br />确定要继续吗？',
      '确认重置API凭证',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )
    
    loading.value = true
    
    const credentials = await generateApiCredentials()
    
    // 更新当前凭证信息
    currentCredentials.value = credentials
    
    // 刷新用户信息
    await refreshUserInfo()
    
    // 显示凭证弹窗
    showCredentialsDialog.value = true
    
    ElMessage.success('API凭证重置成功')
  } catch (error: any) {
    if (error === 'cancel') {
      return // 用户取消操作
    }
    console.error('重置API凭证失败:', error)
    ElMessage.error(error.message || '重置API凭证失败')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新用户信息
 */
const refreshUserInfo = async () => {
  try {
    const userInfo = await fetchUserInfo()
    emit('credentials-updated', userInfo)
  } catch (error) {
    console.error('刷新用户信息失败:', error)
  }
}

/**
 * 复制到剪贴板
 */
const copyToClipboard = async (text: string, label: string) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      textArea.remove()
    }
    
    ElMessage.success(`${label} 已复制到剪贴板`)
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动选择复制')
  }
}

// 暴露方法供父组件调用
defineExpose({
  generateCredentials: handleGenerateCredentials,
  resetCredentials: handleResetCredentials,
  hasCredentials
})
</script>

<style scoped>
.api-credentials-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.api-credentials-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #eef4ff;
  padding: 15px 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-actions .el-button {
  border-radius: 6px;
  font-weight: 500;
}

.card-content {
  padding: 20px;
  min-height: 180px;
}

.credentials-info {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

@media (max-width: 1200px) {
  .credentials-info {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .credentials-info {
    grid-template-columns: 1fr;
  }
}

/* 新的卡片式布局样式 */
.credential-card {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  min-height: 140px;
  display: flex;
  flex-direction: column;
}

.credential-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.credential-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.credential-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.credential-icon.status-icon {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.credential-icon.permission-icon {
  background: linear-gradient(135deg, #e6a23c, #f7ba2a);
}

.credential-icon.tips-icon {
  background: linear-gradient(135deg, #0ea5e9, #38bdf8);
}

.credential-title {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 0;
}

.title-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
  word-wrap: break-word;
}

.title-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  word-wrap: break-word;
}

.credential-card-content {
  padding-left: 44px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.credential-display-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.status-tag {
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 6px;
}

.permission-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #606266;
}

.permission-check {
  color: #67c23a;
  font-size: 14px;
}

.tips-card {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border: 1px solid #bae6fd;
}

.tips-card:hover {
  border-color: #0ea5e9;
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.1);
}

.tips-content {
  font-size: 13px;
  color: #0369a1;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tips-content p {
  margin: 0;
  padding: 2px 0;
}

.info-row {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 80px;
  color: #666;
  font-weight: 500;
  font-size: 14px;
}

.info-value {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
}

.credential-display {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  letter-spacing: 0.5px;
}

.copy-btn {
  padding: 4px;
  color: #666;
  transition: color 0.2s;
}

.copy-btn:hover {
  color: #409eff;
}

.permission-info {
  display: flex;
  gap: 6px;
}

.permission-tag {
  background: #e8f4fd;
  color: #409eff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.credentials-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #666;
  min-height: 80px;
}

.credentials-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 12px;
  color: #d0d0d0;
  font-size: 32px;
}

.empty-text {
  color: #999;
}

.empty-title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
}

.empty-description {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-tag) {
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 6px;
}

:deep(.el-button--text) {
  padding: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .credential-card {
    padding: 12px;
  }

  .credential-card-header {
    gap: 10px;
  }

  .credential-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .credential-card-content {
    padding-left: 38px;
  }

  .credential-display-wrapper {
    padding: 6px 10px;
  }

  .credential-display {
    font-size: 12px;
    word-break: break-all;
  }

  .tips-card {
    padding: 12px;
  }

  .tips-content {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .card-content {
    padding: 16px;
  }

  .header-actions {
    flex-direction: column;
    gap: 6px;
  }

  .header-actions .el-button {
    width: 100%;
    justify-content: center;
  }

  .credential-card {
    padding: 10px;
  }

  .credential-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .credential-card-content {
    padding-left: 0;
    margin-top: 8px;
  }

  .credential-display-wrapper {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }

  .copy-btn {
    align-self: flex-end;
  }
}

/* 确认对话框样式 */
:deep(.api-credentials-confirm-dialog) {
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

:deep(.api-credentials-confirm-dialog .el-message-box__header) {
  padding: 20px 24px 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.api-credentials-confirm-dialog .el-message-box__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

:deep(.api-credentials-confirm-dialog .el-message-box__content) {
  padding: 20px 24px;
  line-height: 1.6;
  color: #606266;
}

:deep(.api-credentials-confirm-dialog .el-message-box__btns) {
  padding: 16px 24px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.api-credentials-confirm-dialog .el-button) {
  border-radius: 6px;
  font-weight: 500;
  min-width: 80px;
}
</style>