<template>
  <!-- 批量删除确认对话框 -->
  <el-dialog
    v-model="batchDialogVisible"
    title="批量删除商品"
    width="500px"
  >
    <div class="batch-delete-info">
      <p style="color: #e6a23c; font-weight: bold; margin-bottom: 15px;">
        <el-icon><Warning /></el-icon>
        将批量删除以下 {{ selectedGoods.length }} 个商品：
      </p>
      <ul class="selected-goods" style="max-height: 200px; overflow-y: auto;">
        <li v-for="goods in selectedGoods" :key="goods.id">
          {{ goods.goods_name }}
        </li>
      </ul>
      <p style="color: #f56c6c; font-size: 14px; margin-top: 15px;">
        注意：删除操作不可恢复，请确认后再操作！
      </p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleBatchCancel">取消</el-button>
        <el-button type="danger" @click="handleBatchSubmit" :loading="batchSubmitting">
          确认删除
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 移动到目录对话框 -->
  <el-dialog
    v-model="moveDialogVisible"
    title="移动到目录"
    width="500px"
  >
    <el-form
      ref="moveFormRef"
      :model="moveForm"
      :rules="moveFormRules"
      label-width="100px"
    >
      <el-form-item label="目标目录" prop="directory_id">
        <el-select v-model="moveForm.directory_id" placeholder="请选择目标目录" style="width: 100%">
          <el-option label="未分类" :value="0" />
          <el-option
            v-for="directory in availableDirectories"
            :key="directory.id"
            :label="directory.name"
            :value="directory.id"
          />
        </el-select>
      </el-form-item>
      <div class="batch-info">
        <p>将移动以下 {{ selectedGoods.length }} 个商品：</p>
        <ul class="selected-goods">
          <li v-for="goods in selectedGoods" :key="goods.id">
            {{ goods.goods_name }}
          </li>
        </ul>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleMoveCancel">取消</el-button>
        <el-button type="primary" @click="handleMoveSubmit" :loading="moveSubmitting">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import { type Goods as GoodsType } from '../../utils/goodsApi'

// 定义接口 - 使用 GoodsType 作为基础类型，确保 id 是必需的
interface Goods extends Omit<GoodsType, 'id'> {
  id: number  // 确保 id 是必需的
}

interface Directory {
  id: number
  name: string
  [key: string]: any
}

interface BatchForm {
  // 批量删除不需要额外参数，保留接口结构便于扩展
}

interface MoveForm {
  directory_id: number | null | undefined
}

// Props
interface Props {
  batchVisible: boolean
  moveVisible: boolean
  selectedGoods: Goods[]
  availableDirectories: Directory[]
  batchSubmitting: boolean
  moveSubmitting: boolean
}

const props = withDefaults(defineProps<Props>(), {
  batchVisible: false,
  moveVisible: false,
  selectedGoods: () => [],
  availableDirectories: () => [],
  batchSubmitting: false,
  moveSubmitting: false
})

// Events
const emit = defineEmits<{
  'update:batchVisible': [value: boolean]
  'update:moveVisible': [value: boolean]
  batchSubmit: [data: BatchForm]
  moveSubmit: [data: MoveForm]
}>()

// 响应式数据
const batchFormRef = ref()
const moveFormRef = ref()

// 本地对话框状态
const batchDialogVisible = ref(false)
const moveDialogVisible = ref(false)

// 表单数据
const batchForm = reactive<BatchForm>({
  // 批量删除不需要表单数据
})

const moveForm = reactive<MoveForm>({
  directory_id: undefined
})

// 表单验证规则
const moveFormRules = {
  directory_id: [
    { required: true, message: '请选择目标目录', trigger: 'change' }
  ]
}

// 监听 props 变化
watch(() => props.batchVisible, (newValue) => {
  batchDialogVisible.value = newValue
  if (newValue) {
    // 批量删除对话框打开时无需重置表单
  }
})

watch(() => props.moveVisible, (newValue) => {
  moveDialogVisible.value = newValue
  if (newValue) {
    // 重置表单
    moveForm.directory_id = undefined
  }
})

// 监听本地对话框状态变化
watch(batchDialogVisible, (newValue) => {
  emit('update:batchVisible', newValue)
})

watch(moveDialogVisible, (newValue) => {
  emit('update:moveVisible', newValue)
})

// 批量设置处理
const handleBatchCancel = () => {
  batchDialogVisible.value = false
}

const handleBatchSubmit = () => {
  // 批量删除确认，直接提交
  emit('batchSubmit', { ...batchForm })
}

// 移动处理
const handleMoveCancel = () => {
  moveDialogVisible.value = false
}

const handleMoveSubmit = async () => {
  try {
    await moveFormRef.value.validate()
    emit('moveSubmit', { ...moveForm })
  } catch (error) {
    // 验证失败
  }
}
</script>

<style scoped>
.batch-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.batch-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-weight: bold;
}

.selected-goods {
  margin: 0;
  padding-left: 20px;
  max-height: 150px;
  overflow-y: auto;
}

.selected-goods li {
  color: #409eff;
  margin-bottom: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
