<template>
  <!-- SKU详情对话框 -->
  <el-dialog
    v-model="skuDialogVisible"
    title="SKU详情"
    width="700px"
  >
    <div class="sku-detail-container">
      <el-table :data="currentSkuList" stripe>
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column label="缩略图" width="80">
          <template #default="{ row }">
            <div class="sku-detail-image" v-if="row.thumb_url">
              <el-image
                :src="row.thumb_url"
                fit="cover"
                style="width: 50px; height: 50px; border-radius: 4px; cursor: pointer;"
                @click="handleSkuImagePreview(row)"
              >
                <template #error>
                  <div class="sku-image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
            <span v-else class="no-image">无图片</span>
          </template>
        </el-table-column>
        <el-table-column label="SKU" prop="sku" min-width="200" show-overflow-tooltip />
        <el-table-column label="价格" width="120">
          <template #default="{ row }">
            <span class="price-text">{{ row.price }} {{ row.currentcy }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="skuDialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- SKU图片预览对话框 -->
  <el-dialog
    v-model="skuImagePreviewVisible"
    :title="`${currentSkuName} - SKU图片`"
    width="60%"
    top="10vh"
    class="sku-image-preview-dialog"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
  >
    <div class="sku-image-preview-container">
      <el-image
        :src="currentSkuImageUrl"
        fit="contain"
        class="sku-preview-image"
        :lazy="true"
        loading="eager"
      >
        <template #placeholder>
          <div class="image-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>图片加载中...</span>
          </div>
        </template>
        <template #error>
          <div class="image-error">
            <el-icon><Picture /></el-icon>
            <span>图片加载失败</span>
          </div>
        </template>
      </el-image>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="skuImagePreviewVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 价格调整对话框 -->
  <el-dialog
    v-model="priceAdjustmentDialogVisible"
    title="调整SKU价格"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-if="currentGoodsForPriceAdjustment" class="price-adjustment-content">
      <div class="goods-info">
        <h4>{{ currentGoodsForPriceAdjustment.goods_name }}</h4>
        <p>商品ID: {{ currentGoodsForPriceAdjustment.goods_id }}</p>
      </div>

      <el-form ref="priceAdjustmentFormRef" :model="priceAdjustmentForm" label-width="100px">
        <!-- 批量调整价格 -->
        <div v-if="priceAdjustmentForm.skus.length > 1" class="batch-price-section">
          <el-form-item label="批量调整价格">
            <el-input-number
              v-model="priceAdjustmentForm.batch_price"
              :min="0.01"
              :precision="2"
              :step="0.01"
              placeholder="输入价格"
              style="width: 150px; margin-right: 10px;"
            />
            <el-button type="primary" @click="handleBatchPriceAdjustment">
              确定
            </el-button>
          </el-form-item>
        </div>

        <el-divider />

        <div class="sku-price-list">
          <div
            v-for="(sku, index) in priceAdjustmentForm.skus"
            :key="index"
            class="sku-price-item"
          >
            <div class="sku-info">
              <div class="sku-image" v-if="sku.thumb_url">
                <el-image
                  :src="sku.thumb_url"
                  fit="cover"
                  style="width: 40px; height: 40px; border-radius: 4px;"
                >
                  <template #error>
                    <div class="sku-image-slot">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              <div class="sku-details">
                <div class="sku-spec">{{ sku.spec_values }}</div>
                <div class="sku-original-price">原价格: {{ sku.original_price }} {{ sku.currency }}</div>
              </div>
            </div>
            <div class="price-input">
              <el-form-item :label="`新价格`" :prop="`skus.${index}.new_price`">
                <el-input-number
                  v-model="sku.new_price"
                  :min="0.01"
                  :precision="2"
                  :step="0.01"
                  style="width: 150px;"
                />
                <span class="currency">{{ sku.currency }}</span>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="priceAdjustmentDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePriceAdjustmentSubmit" :loading="priceAdjustmentSubmitting">
          确定调整
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 价格调整日志对话框 -->
  <el-dialog
    v-model="priceLogsDialogVisible"
    title="价格调整日志"
    width="900px"
  >
    <div v-if="currentGoodsForPriceLogs" class="price-logs-content">
      <div class="goods-info">
        <h4>{{ currentGoodsForPriceLogs.goods_name }}</h4>
        <p>商品ID: {{ currentGoodsForPriceLogs.goods_id }}</p>
      </div>

      <el-table
        :data="priceAdjustmentLogs"
        v-loading="priceLogsLoading"
        stripe
        border
      >
        <el-table-column label="SKU规格" prop="sku_spec_values" min-width="150" />
        <el-table-column label="原价格" width="100">
          <template #default="{ row }">
            {{ row.old_price }}
          </template>
        </el-table-column>
        <el-table-column label="新价格" width="100">
          <template #default="{ row }">
            {{ row.new_price }}
          </template>
        </el-table-column>
        <el-table-column label="变化" width="120">
          <template #default="{ row }">
            <span :class="getPriceChangeClass(row.price_change_type)">
              {{ row.price_change_description }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="修改时间" prop="modified_at" width="160" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section" style="margin-top: 20px;">
        <el-pagination
          v-model:current-page="priceLogsPagination.currentPage"
          v-model:page-size="priceLogsPagination.pageSize"
          :page-sizes="[10, 20, 50]"
          :total="priceLogsPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePriceLogsPageSizeChange"
          @current-change="handlePriceLogsCurrentChange"
        />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="priceLogsDialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Picture, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 定义接口
interface GoodsType {
  id?: number
  goods_id?: number  // 修改为 number 类型以匹配 goodsApi.ts
  goods_name: string
  formatted_skus?: any[]
  [key: string]: any
}

interface SkuAdjustmentForm {
  skus: Array<{
    id: number
    spec_values: string
    original_price: number
    new_price: number
    currency: string
    thumb_url: string
  }>
  batch_price: number | null
}

interface PriceAdjustmentLog {
  sku_spec_values: string
  old_price: number
  new_price: number
  price_change_type: string
  price_change_description: string
  modified_at: string
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

// Props
interface Props {
  skuVisible: boolean
  skuImagePreviewVisible: boolean
  priceAdjustmentVisible: boolean
  priceLogsVisible: boolean
  currentSkuList: any[]
  currentSkuImageUrl: string
  currentSkuName: string
  currentGoodsForPriceAdjustment: GoodsType | null
  currentGoodsForPriceLogs: GoodsType | null
  priceAdjustmentForm: SkuAdjustmentForm
  priceAdjustmentSubmitting: boolean
  priceAdjustmentLogs: PriceAdjustmentLog[]
  priceLogsLoading: boolean
  priceLogsPagination: Pagination
}

const props = withDefaults(defineProps<Props>(), {
  skuVisible: false,
  skuImagePreviewVisible: false,
  priceAdjustmentVisible: false,
  priceLogsVisible: false,
  currentSkuList: () => [],
  currentSkuImageUrl: '',
  currentSkuName: '',
  currentGoodsForPriceAdjustment: null,
  currentGoodsForPriceLogs: null,
  priceAdjustmentSubmitting: false,
  priceAdjustmentLogs: () => [],
  priceLogsLoading: false
})

// Events
const emit = defineEmits<{
  'update:skuVisible': [value: boolean]
  'update:skuImagePreviewVisible': [value: boolean]
  'update:priceAdjustmentVisible': [value: boolean]
  'update:priceLogsVisible': [value: boolean]
  'update:priceAdjustmentForm': [value: any]
  skuImagePreview: [skuItem: any]
  batchPriceAdjustment: []
  priceAdjustmentSubmit: []
  priceLogsPageSizeChange: [pageSize: number]
  priceLogsCurrentChange: [page: number]
}>()

// 本地对话框状态
const skuDialogVisible = ref(false)
const skuImagePreviewVisible = ref(false)
const priceAdjustmentDialogVisible = ref(false)
const priceLogsDialogVisible = ref(false)

// 表单引用
const priceAdjustmentFormRef = ref()

// 本地表单数据
const priceAdjustmentForm = reactive<SkuAdjustmentForm>({
  skus: [],
  batch_price: null
})

// 监听 props 变化
watch(() => props.skuVisible, (newValue) => {
  skuDialogVisible.value = newValue
})

watch(() => props.skuImagePreviewVisible, (newValue) => {
  skuImagePreviewVisible.value = newValue
})

watch(() => props.priceAdjustmentVisible, (newValue) => {
  priceAdjustmentDialogVisible.value = newValue
})

watch(() => props.priceLogsVisible, (newValue) => {
  priceLogsDialogVisible.value = newValue
})

// 监听本地表单数据变化，同步到父组件
let isUpdatingFromProps = false

watch(() => props.priceAdjustmentForm, (newValue) => {
  if (newValue) {
    isUpdatingFromProps = true
    Object.assign(priceAdjustmentForm, newValue)
    isUpdatingFromProps = false
  }
}, { deep: true, immediate: true })

// 监听本地对话框状态变化
watch(skuDialogVisible, (newValue) => {
  emit('update:skuVisible', newValue)
})

watch(skuImagePreviewVisible, (newValue) => {
  emit('update:skuImagePreviewVisible', newValue)
})

watch(priceAdjustmentDialogVisible, (newValue) => {
  emit('update:priceAdjustmentVisible', newValue)
})

watch(priceLogsDialogVisible, (newValue) => {
  emit('update:priceLogsVisible', newValue)
})

watch(() => priceAdjustmentForm, (newValue) => {
  if (!isUpdatingFromProps) {
    emit('update:priceAdjustmentForm', newValue)
  }
}, { deep: true })

// 处理SKU图片预览
const handleSkuImagePreview = (skuItem: any) => {
  emit('skuImagePreview', skuItem)
}

// 批量调整价格
const handleBatchPriceAdjustment = () => {
  // 检查本地表单和props中的批量价格
  const batchPrice = priceAdjustmentForm.batch_price || props.priceAdjustmentForm.batch_price

  if (!batchPrice || batchPrice <= 0) {
    ElMessage.warning('请输入批量调整价格')
    return
  }

  if (batchPrice < 0.01) {
    ElMessage.error('价格不能小于0.01')
    return
  }

  emit('batchPriceAdjustment')
}

// 提交价格调整
const handlePriceAdjustmentSubmit = () => {
  emit('priceAdjustmentSubmit')
}

// 获取价格变化样式类
const getPriceChangeClass = (changeType: string) => {
  switch (changeType) {
    case 'increase':
      return 'price-increase'
    case 'decrease':
      return 'price-decrease'
    default:
      return 'price-unchanged'
  }
}

// 价格调整日志分页处理
const handlePriceLogsPageSizeChange = (pageSize: number) => {
  emit('priceLogsPageSizeChange', pageSize)
}

const handlePriceLogsCurrentChange = (page: number) => {
  emit('priceLogsCurrentChange', page)
}
</script>

<style scoped>
.sku-detail-container {
  max-height: 400px;
  overflow-y: auto;
}

.sku-detail-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.sku-image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background: #f0f0f0;
  color: #909399;
  font-size: 14px;
  border-radius: 4px;
}

.no-image {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.price-text {
  color: #e6a23c;
  font-weight: bold;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* SKU图片预览对话框样式 */
.sku-image-preview-dialog {
  margin-top: 10vh !important;
}

.sku-image-preview-dialog .el-dialog {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.sku-image-preview-dialog .el-dialog__body {
  padding: 20px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.sku-image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  max-height: 60vh;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  box-sizing: border-box;
}

.sku-preview-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
}

.image-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #909399;
  font-size: 14px;
}

.image-loading .el-icon {
  font-size: 48px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #909399;
  font-size: 14px;
}

.image-error .el-icon {
  font-size: 48px;
}

/* 价格调整相关样式 */
.price-adjustment-content {
  max-height: 500px;
  overflow-y: auto;
}

.batch-price-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.goods-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.goods-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.goods-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.sku-price-list {
  max-height: 200px;
  overflow-y: auto;
}

.sku-price-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
}

.sku-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.sku-image {
  margin-right: 12px;
}

.sku-details {
  flex: 1;
}

.sku-spec {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.sku-original-price {
  font-size: 12px;
  color: #909399;
}

.price-input {
  display: flex;
  align-items: center;
}

.currency {
  margin-left: 8px;
  color: #606266;
  font-size: 14px;
}

/* 价格调整日志样式 */
.price-logs-content {
  max-height: 600px;
}

.price-increase {
  color: #f56c6c;
}

.price-decrease {
  color: #67c23a;
}

.price-unchanged {
  color: #909399;
}

.pagination-section {
  display: flex;
  justify-content: center;
}
</style>
