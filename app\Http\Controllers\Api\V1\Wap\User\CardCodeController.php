<?php
namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Models\User\User;
use App\Service\User\CardCodeService;
use App\Service\User\CardUsageService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CardCodeController extends Controller
{
    protected CardCodeService $cardCodeService;
    protected CardUsageService $cardUsageService;

    public function __construct(CardCodeService $cardCodeService, CardUsageService $cardUsageService)
    {
        $this->cardCodeService = $cardCodeService;
        $this->cardUsageService = $cardUsageService;
        parent::__construct();
    }

    /**
     * 获取卡密列表（分页）
     */
    public function list(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $params = $request->only([
            'page', 'pageSize', 'card_type', 'status', 'card_code', 'card_name',
            'batch_no', 'start_date', 'end_date', 'min_price', 'max_price',
            'min_points', 'max_points', 'is_copied', 'copied_by', 'admin_id',
            'time_type', 'date_range', 'vip_days_unit'
        ]);

        // 添加查看者ID
        $params['viewer_id'] = $userId;
        
        $result = $this->cardCodeService->getCardCodeList($userId, $params);
        
        return $this->apiSuccess($result);
    }

    /**
     * 创建单个卡密
     */
    public function create(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'card_name', 'card_type', 'price', 'points', 'vip_days',
            'vip_days_unit', 'vip_level', 'description', 'valid_until', 'prefix'
        ]);
        
        $requestInfo = [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ];
        
        $result = $this->cardCodeService->createCardCode($userId, $data, $requestInfo);
        
        return $this->apiSuccess($result);
    }

    /**
     * 批量生成卡密
     */
    public function batchCreate(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'card_name', 'card_type', 'price', 'points', 'vip_days',
            'vip_days_unit', 'vip_level', 'description', 'valid_until', 'prefix', 'quantity'
        ]);
        
        $requestInfo = [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ];
        
        $result = $this->cardCodeService->batchCreateCardCodes($userId, $data, $requestInfo);
        
        return $this->apiSuccess($result);
    }

    /**
     * 更新卡密
     */
    public function update(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $cardCodeId = $request->input('id');
        if (!$cardCodeId) {
            return $this->apiError('卡密ID不能为空');
        }
        
        $data = $request->only([
            'card_name', 'price', 'points', 'vip_days', 'vip_level', 
            'status', 'description', 'valid_until'
        ]);
        
        $requestInfo = [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ];
        
        $result = $this->cardCodeService->updateCardCode($userId, $cardCodeId, $data, $requestInfo);
        
        return $this->apiSuccess($result);
    }

    /**
     * 删除卡密
     */
    public function delete(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $cardCodeId = $request->input('id');
        if (!$cardCodeId) {
            return $this->apiError('卡密ID不能为空');
        }
        
        $requestInfo = [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ];
        
        $result = $this->cardCodeService->deleteCardCode($userId, $cardCodeId, $requestInfo);
        
        return $this->apiSuccess(['deleted' => $result]);
    }

    /**
     * 批量删除卡密
     */
    public function batchDelete(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $cardCodeIds = $request->input('ids', []);
        if (empty($cardCodeIds) || !is_array($cardCodeIds)) {
            return $this->apiError('请选择要删除的卡密');
        }
        
        $requestInfo = [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ];
        
        $result = $this->cardCodeService->batchDeleteCardCodes($userId, $cardCodeIds, $requestInfo);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取卡密详情
     */
    public function detail(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $cardCodeId = $request->input('id');
        if (!$cardCodeId) {
            return $this->apiError('卡密ID不能为空');
        }
        
        $result = $this->cardCodeService->getCardCodeDetail($userId, $cardCodeId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取卡密统计信息
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];

        // 获取筛选条件
        $filters = $request->only([
            'card_type', 'status', 'is_copied', 'copied_by', 'admin_id',
            'time_type', 'date_range', 'start_date', 'end_date', 'vip_days_unit'
        ]);

        $result = $this->cardCodeService->getCardCodeStatistics($userId, $filters);

        return $this->apiSuccess($result);
    }

    /**
     * 卡密激活
     */
    public function useCard(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        if($user['is_admin'] == 1){
            return $this->apiError('您是管理员,不需要使用卡密');
        }

        $cardCode = $request->input('card_code');
        if (!$cardCode) {
            return $this->apiError('卡密不能为空');
        }
        
        $requestInfo = [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ];
        
        $result = $this->cardUsageService->useCardCode($userId, $cardCode, $requestInfo, $user['is_vip']);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取用户卡密激活记录
     */
    public function userUsageRecords(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $params = $request->only(['page', 'pageSize', 'card_type', 'start_date', 'end_date']);
        
        $result = $this->cardUsageService->getUserUsageRecords($userId, $params);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取卡密激活记录列表（管理员查看）
     */
    public function usageRecords(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $params = $request->only([
            'page', 'pageSize', 'card_type', 'card_code', 'user_phone', 
            'start_date', 'end_date'
        ]);
        
        $result = $this->cardUsageService->getUsageRecordsList($userId, $params);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取卡密类型选项
     */
    public function cardTypeOptions(Request $request): JsonResponse
    {
        $options = [
            ['value' => 1, 'label' => '有效期卡'],
            ['value' => 2, 'label' => '积分卡']
        ];
        
        return $this->apiSuccess($options);
    }

    /**
     * 获取卡密状态选项
     */
    public function statusOptions(Request $request): JsonResponse
    {
        $options = [
            ['value' => 0, 'label' => '已使用'],
            ['value' => 1, 'label' => '未使用'],
            ['value' => 2, 'label' => '已禁用']
        ];
        
        return $this->apiSuccess($options);
    }

    /**
     * 复制卡密
     */
    public function copy(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];

        $cardCodeId = $request->input('id');
        if (!$cardCodeId) {
            return $this->apiError('卡密ID不能为空');
        }

        $memo = $request->input('memo', '');
        $ipAddress = $request->ip();
        $userAgent = $request->userAgent();

        $result = $this->cardCodeService->copyCardCode($userId, $cardCodeId, $memo, $ipAddress, $userAgent);

        return $this->apiSuccess($result);
    }

    /**
     * 获取VIP时长单位选项
     */
    public function vipDaysUnitOptions(Request $request): JsonResponse
    {
        $options = [
            ['value' => 'year', 'label' => '年'],
            ['value' => 'month', 'label' => '月'],
            ['value' => 'day', 'label' => '天']
        ];

        return $this->apiSuccess($options);
    }

    /**
     * 获取复制状态选项
     */
    public function copyStatusOptions(Request $request): JsonResponse
    {
        $options = [
            ['value' => 0, 'label' => '未复制'],
            ['value' => 1, 'label' => '已复制']
        ];

        return $this->apiSuccess($options);
    }

    /**
     * 获取成员列表（卡密管理员）
     */
    public function memberOptions(Request $request): JsonResponse
    {
        $members = User::where('status', 1)
                      ->where('is_card_admin', 1)
                      ->select('id', 'phone')
                      ->get()
                      ->map(function ($user) {
                          return [
                              'value' => $user->id,
                              'label' => $user->phone
                          ];
                      });

        return $this->apiSuccess($members);
    }
}
