<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'deepseek' => [
        'api_key' => env('DEEPSEEK_API_KEY'),
        'api_url' => env('DEEPSEEK_API_URL', 'https://api.deepseek.com'),
    ],

    'goods_directory' => [
        'start_number' => env('GOOD_DIRECTORY_START_NUMBER', 101790),
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Process Service Configuration
    |--------------------------------------------------------------------------
    |
    | 图片处理服务配置
    | 用于控制文件下载的行为和日志记录
    |
    */
    'image_process' => [
        // 是否启用下载进度日志（调试时开启，生产环境建议关闭）
        'enable_download_progress_log' => env('DOWNLOAD_PROGRESS_LOG_ENABLED', false),

        // 文件大小阈值（字节），超过此大小使用流式下载
        'file_size_threshold' => env('FILE_SIZE_THRESHOLD', 500 * 1024), // 500KB

        // 流式下载的缓冲区大小（字节）
        'stream_chunk_size' => env('STREAM_CHUNK_SIZE', 64 * 1024), // 64KB

        // 下载重试次数
        'retry_times' => env('DOWNLOAD_RETRY_TIMES', 2),

        // 连接超时时间（秒）
        'connect_timeout' => env('DOWNLOAD_CONNECT_TIMEOUT', 60),

        // 最大重定向次数
        'max_redirects' => env('DOWNLOAD_MAX_REDIRECTS', 5),

        // User-Agent字符串
        'user_agent' => env('DOWNLOAD_USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),

        // 下载超时时间配置（秒）
        'timeouts' => [
            'image' => env('IMAGE_DOWNLOAD_TIMEOUT', 300), // 5分钟
            'video' => env('VIDEO_DOWNLOAD_TIMEOUT', 300), // 5分钟
            'pdf' => env('PDF_DOWNLOAD_TIMEOUT', 300), // 5分钟
        ],

        // 代理配置
        'proxy' => [
            // 代理总开关
            'enabled' => filter_var(env('PROXY_ENABLED', false), FILTER_VALIDATE_BOOLEAN),

            // 各文件类型的代理开关
            'image_enabled' => filter_var(env('PROXY_IMAGE_ENABLED', false), FILTER_VALIDATE_BOOLEAN),
            'video_enabled' => filter_var(env('PROXY_VIDEO_ENABLED', false), FILTER_VALIDATE_BOOLEAN),
            'pdf_enabled' => filter_var(env('PROXY_PDF_ENABLED', false), FILTER_VALIDATE_BOOLEAN),

            // 代理服务器配置
            'host' => env('PROXY_HOST'),
            'port' => env('PROXY_PORT') ? (int) env('PROXY_PORT') : null,

            // 代理认证配置（可选）
            'username' => env('PROXY_USERNAME'),
            'password' => env('PROXY_PASSWORD'),
        ],

        // SSL配置
        'ssl' => [
            // SSL版本配置 (可选值: auto, tls1.2, tls1.3, sslv3)
            'version' => env('SSL_VERSION', 'tls1.2'),

            // 是否启用SSL验证
            'verify_peer' => filter_var(env('SSL_VERIFY_PEER', false), FILTER_VALIDATE_BOOLEAN),
            'verify_host' => filter_var(env('SSL_VERIFY_HOST', false), FILTER_VALIDATE_BOOLEAN),

            // 自定义SSL密码套件
            'cipher_list' => env('SSL_CIPHER_LIST', 'ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS'),
        ],
    ],

];