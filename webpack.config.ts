import { resolve, posix } from 'path'
import HtmlWebpackPlugin from 'html-webpack-plugin'
import { VueLoaderPlugin } from 'vue-loader'
import Components from 'unplugin-vue-components/webpack'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import type { Configuration } from 'webpack'
import AutoImport from 'unplugin-auto-import/webpack'
import webpack from 'webpack'
import Dotenv from 'dotenv-webpack'
import CopyWebpackPlugin from 'copy-webpack-plugin'
import TerserPlugin from 'terser-webpack-plugin';
import { EsbuildPlugin } from 'esbuild-loader'
function assetsPath(_path) {
  const assetsSubDirectory = process.env.NODE_ENV === 'production'
    ? './static'
    : 'static'
  return posix.join(assetsSubDirectory, _path)
}

console.log('process.env.NODE_ENV',process.env.NODE_ENV);
const mode: 'production' | 'development' =
  (process.env.NODE_ENV as any) ?? 'development'

const isProduction = mode === 'production';

console.log('webpack   mode=============',mode)
console.log('webpack  isProduction=====',isProduction)
console.log('webpack  BTYPE=====',process.env.BTYPE)

const outputDir = isProduction ? 'tsa_build' : 'tsa';

console.log('webpack  outputDir=====',outputDir);

const envFile = isProduction ? '.env.production' : '.env.development';

console.log('webpack  envFile=====',envFile);


const outputPath = resolve(__dirname, `./${outputDir}`);

console.log('webpack  outputPath=====',outputPath);

const config: Configuration = {
  devtool: isProduction ? false : 'cheap-module-source-map',
  mode,
  stats: {
    errorDetails: true,
    children: true,
  },
  entry: {
    'background': resolve('src', 'pages/background'),
    'option': resolve('src', 'pages/option'),
    'popup': resolve('src', 'pages/popup'),
    'temu_detail': resolve('src', 'pages/temu/detail'),
    //'n11_product_list': resolve('src', 'pages/n11/product_list'),
    'n11_product_list_dom': resolve('src', 'pages/n11/product_list_dom'),
    'temu_search': resolve('src', 'pages/temu/search'),
  },
  output: {
    path: outputPath,
    publicPath: './',
    filename: '[name].main.js'
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      'static': resolve('static'),
    },
    extensions: ['.ts', '.js', '.mjs', '.json'],
  },
  module: {
    rules: [
      {
        test: /\.mjs$/i,
        resolve: { byDependency: { esm: { fullySpecified: false } } },
      },
      { test: /\.vue$/, loader: 'vue-loader' },
      {
        test: /\.m?[tj]s$/,
        exclude: /node_modules/,
        loader: 'babel-loader',
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 100000,
          name: assetsPath('img/[name].[hash:7].[ext]')
        }
      },
      { test: /\.css$/, use: ['style-loader', 'css-loader'] },
      { test: /\.less$/, use: ['style-loader', 'css-loader', 'less-loader'] },//less的loader
      { test: /\.scss$/, use: ['style-loader', 'css-loader', 'sass-loader'] },//scss的loader
    ],
  },
  plugins: [
    new webpack.ProvidePlugin({
      $: 'jquery',
      jQuery: 'jquery'
    }),
    new VueLoaderPlugin(),
    new HtmlWebpackPlugin({
      filename: 'popup.html',
      template: 'src/pages/popup/popup.html',
      inject: 'body',
      chunks: ["popup"],
      minify: { //压缩
        removeComments: true,
        collapseWhitespace: true,
      }
    }),
    new Dotenv({
      path: resolve(__dirname, envFile),
    }),
    new webpack.DefinePlugin({
      '__VUE_OPTIONS_API__': true,
      '__VUE_PROD_DEVTOOLS__': false,
      '__VUE_PROD_HYDRATION_MISMATCH_DETAILS__': false
    }),
    new HtmlWebpackPlugin({
      filename: 'option.html',
      template: 'src/pages/option/option.html',
      inject: 'body',
      chunks: ["option"],
      minify: { //压缩
        removeComments: true,
        collapseWhitespace: true,
      }
    }),
    new CopyWebpackPlugin([{
      from: resolve(__dirname, 'src/manifest.json'),
      to: '',
      transform(content, path) {
        const manifest = JSON.parse(content.toString());

        // 在开发环境下为名称添加【开发版】标识
        if (!isProduction) {
          manifest.name = manifest.name + '【开发版】';
          if (manifest.action && manifest.action.default_title) {
            manifest.action.default_title = manifest.action.default_title + '【开发版】';
          }
        }

        return JSON.stringify(manifest, null, 2);
      }
    },
    {
      from: resolve(__dirname, 'static/'),
      to: 'static/'
    },
    {
      from: resolve(__dirname, 'web/'),
      to: 'web/',
      ignore: ['node_modules/**', 'src/**', 'webpack.config.js', 'webpack.config.ts', 'package.json', 'package-lock.json', 'tsconfig.json', 'dev.html', '.babelrc', '.gitignore']
    },
    ...(isProduction ? [{
      from: resolve(__dirname, 'web/dist/'),
      to: 'web/dist/',
      force: true
    }] : [])
    ], {
      copyUnmodified: true
    }),
    AutoImport({
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/, // .vue
        /\.md$/ // .md
      ],
      imports: [
        'vue', {}
      ],
      dts: 'src/types/auto-imports.d.ts',
      resolvers: [],
      eslintrc: {
        enabled: false, // Default `false`
        filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
        globalsPropValue: true // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
      }
    }),
    Components({
      resolvers: [ElementPlusResolver({})],
      dts: 'types/components.d.ts',
    }),
  ],

  performance: isProduction ? {
    hints: false
  } : false,
  optimization: isProduction ? {
    minimize: true,
    minimizer: [
      new EsbuildPlugin({
        target: 'es2015', // 指定目标环境
        css: true,  // 允许压缩 CSS
        minify: true,
        minifyWhitespace: true,
        minifyIdentifiers: true,
        minifySyntax: true,
        drop: ['console', 'debugger'], // 移除 console 和 debugger
        treeShaking: true,
      })
    ],
  } : {
    minimize: false
    }
}

export default config
