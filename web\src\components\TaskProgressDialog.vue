<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    center
  >
    <!-- 总体进度 -->
    <div class="progress-header">
      <div class="overall-progress">
        <el-progress
          :percentage="overallProgress"
          :stroke-width="20"
          :text-inside="true"
          :status="progressStatus"
        />
        <div class="progress-text">
          {{ currentProgress }} / {{ totalProgress }} 个商品
        </div>
      </div>
    </div>

    <!-- 当前处理的商品信息 -->
    <div v-if="currentProduct" class="current-product">
      <h4>当前处理商品</h4>
      <div class="product-info">
        <div class="product-image">
          <el-image
            :src="currentProduct.productImage"
            fit="cover"
            style="width: 80px; height: 80px; border-radius: 8px;"
            :preview-src-list="[currentProduct.productImage]"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </div>
        <div class="product-details">
          <div class="product-name">{{ currentProduct.productName }}</div>
          <div class="product-price">价格: {{ currentProduct.price }}</div>
          <div class="product-status" :class="getProductStatusClass(currentProduct)">
            <el-icon v-if="currentProduct.success"><Check /></el-icon>
            <el-icon v-else-if="isProductProcessing(currentProduct)"><Loading class="is-loading" /></el-icon>
            <el-icon v-else-if="getProductStatusClass(currentProduct) === 'warning'"><Warning /></el-icon>
            <el-icon v-else><Close /></el-icon>
            {{ currentProduct.message }}
          </div>
        </div>
      </div>
    </div>

    <!-- 处理状态 -->
    <div class="processing-status">
      <div v-if="isProcessing" class="status-item processing">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>{{ processingMessage }}</span>
      </div>
      <div v-else-if="isCompleted" class="status-item completed">
        <el-icon><CircleCheck /></el-icon>
        <span>所有任务已完成！</span>
      </div>
      <div v-else-if="hasError" class="status-item error">
        <el-icon><CircleClose /></el-icon>
        <span>{{ errorMessage }}</span>
      </div>
    </div>

    <!-- 处理历史记录 -->
    <div v-if="processHistory.length > 0" class="process-history">
      <h4>处理记录</h4>
      <div class="history-list">
        <div
          v-for="(item, index) in displayHistory"
          :key="index"
          class="history-item"
          :class="getStatusClass(item.success)"
        >
          <div class="history-image">
            <el-image
              :src="item.productImage"
              fit="cover"
              style="width: 40px; height: 40px; border-radius: 4px;"
            >
              <template #error>
                <div class="image-slot-small">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </div>
          <div class="history-content">
            <div class="history-name">{{ item.productName }}</div>
            <div class="history-price">{{ item.price }}</div>
            <div class="history-result">{{ item.message }}</div>
          </div>
          <div class="history-status">
            <el-icon v-if="item.success"><Check /></el-icon>
            <el-icon v-else><Close /></el-icon>
          </div>
        </div>
      </div>
      <div v-if="historyDisplayInfo.showCount > 5" class="show-more">
        <el-button text @click="showAllHistory = !showAllHistory">
          {{ showAllHistory ? '收起' : historyDisplayInfo.message }}
        </el-button>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="isCompleted || hasError" type="primary" @click="handleClose">
          确定
        </el-button>
        <el-button v-if="isProcessing" type="danger" @click="handleCancel">
          取消任务
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Picture, Check, Close, Loading, CircleCheck, CircleClose, Warning } from '@element-plus/icons-vue'
import type { ProductUploadResult } from '../utils/taskProcessor'

// Props
interface Props {
  visible: boolean
  taskTitle?: string
  currentProgress?: number
  totalProgress?: number
  currentProduct?: ProductUploadResult | null
  isProcessing?: boolean
  isCompleted?: boolean
  hasError?: boolean
  errorMessage?: string
  processingMessage?: string
  processHistory?: ProductUploadResult[]
  dialogTitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  taskTitle: '任务执行中',
  currentProgress: 0,
  totalProgress: 0,
  currentProduct: null,
  isProcessing: false,
  isCompleted: false,
  hasError: false,
  errorMessage: '',
  processingMessage: '正在处理商品...',
  processHistory: () => [],
  dialogTitle: '任务执行进度'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'close': []
  'cancel': []
}>()

// 响应式数据
const showAllHistory = ref(false)
const maxHistoryDisplay = 100 // 最多显示100条记录，避免浏览器卡顿

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const overallProgress = computed(() => {
  if (props.totalProgress === 0) return 0
  return Math.round((props.currentProgress / props.totalProgress) * 100)
})

const progressStatus = computed(() => {
  if (props.hasError) return 'exception'
  if (props.isCompleted) return 'success'
  return undefined
})

const displayHistory = computed(() => {
  // 限制最多显示100条记录，避免浏览器卡顿
  const limitedHistory = props.processHistory.slice(-maxHistoryDisplay)

  // 反转数组，使最新的记录显示在前面
  const reversedHistory = [...limitedHistory].reverse()

  if (showAllHistory.value) {
    return reversedHistory
  }
  // 只显示最近5条（已经反转，所以是前5条）
  return reversedHistory.slice(0, 5)
})

const historyDisplayInfo = computed(() => {
  const totalCount = props.processHistory.length
  const limitedCount = Math.min(totalCount, maxHistoryDisplay)

  if (totalCount > maxHistoryDisplay) {
    return {
      showCount: limitedCount,
      totalCount,
      hasMore: true,
      message: `显示最近 ${limitedCount} 条记录（共 ${totalCount} 条）`
    }
  } else {
    return {
      showCount: limitedCount,
      totalCount,
      hasMore: false,
      message: limitedCount > 5 ? `查看全部 (${limitedCount})` : ''
    }
  }
})

// 方法
const getStatusClass = (success: boolean) => {
  return success ? 'success' : 'error'
}

const isProductProcessing = (product: ProductUploadResult) => {
  return product.message.includes('正在')
  /* return product.message === '正在处理中...' ||
         product.message === '正在AI改写中...' ||
         product.message === '正在上传到N11平台...' */
}

const getProductStatusClass = (product: ProductUploadResult) => {
  if (product.success) return 'success'
  if (isProductProcessing(product)) return 'processing'
  if (product.message === 'AI改写失败，将在下次继续处理') return 'warning'
  return 'error'
}

const handleClose = () => {
  emit('close')
}

const handleCancel = () => {
  emit('cancel')
}

// 监听对话框关闭
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    showAllHistory.value = false
  }
})
</script>

<style scoped>
.progress-header {
  margin-bottom: 20px;
}

.progress-header h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
  text-align: center;
}

.overall-progress {
  margin-bottom: 10px;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.current-product {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.current-product h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.product-image {
  flex-shrink: 0;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background: #f5f7fa;
  color: #909399;
  border-radius: 8px;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
}

.product-price {
  color: #e6a23c;
  font-weight: 500;
  margin-bottom: 5px;
}

.product-status {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.product-status.success {
  color: #67c23a;
}

.product-status.processing {
  color: #409eff;
}

.product-status.warning {
  color: #e6a23c;
}

.product-status.error {
  color: #f56c6c;
}

.processing-status {
  margin-bottom: 20px;
  text-align: center;
}

.status-item {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
}

.status-item.processing {
  background: #e1f3ff;
  color: #409eff;
}

.status-item.completed {
  background: #f0f9ff;
  color: #67c23a;
}

.status-item.error {
  background: #fef0f0;
  color: #f56c6c;
}

.process-history {
  margin-bottom: 20px;
}

.process-history h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
  min-height: auto;
}

.history-item.success {
  border-left: 3px solid #67c23a;
}

.history-item.error {
  border-left: 3px solid #f56c6c;
}

.history-image {
  flex-shrink: 0;
}

.image-slot-small {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: #f5f7fa;
  color: #909399;
  border-radius: 4px;
}

.history-content {
  flex: 1;
  min-width: 0;
}

.history-name {
  font-size: 13px;
  color: #333;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
  margin-bottom: 2px;
}

.history-price {
  font-size: 12px;
  color: #e6a23c;
  font-weight: 500;
  margin-bottom: 2px;
  line-height: 1.3;
}

.history-result {
  font-size: 12px;
  color: #666;
}

.history-status {
  flex-shrink: 0;
  font-size: 16px;
}

.history-status .success {
  color: #67c23a;
}

.history-status .error {
  color: #f56c6c;
}

.show-more {
  text-align: center;
  margin-top: 10px;
}

.dialog-footer {
  text-align: center;
}

/* 滚动条样式 */
.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
