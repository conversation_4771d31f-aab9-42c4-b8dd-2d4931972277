<template>
    <div class="chrome_container">
      <!-- 只在商品详情页才显示采集设置相关功能 -->
      <template v-if="isGoodsDetailPage">
        <!-- 状态显示组件 -->
        <StatusDisplay
          v-if="showStatusDisplay"
          :user-info="userInfo"
          :current-settings="currentSettings"
          @open-settings="handleOpenSettings"
          @duplicate-check-toggle="handleDuplicateCheckToggle"
        />

        <!-- 手动确认组件 -->
        <ManualConfirm
          v-if="showManualConfirm"
          :loading="collectingGoods"
          :directory-name="currentDirectoryName"
          @confirm="handleManualCollect"
        />

        <!-- 采集设置弹窗 -->
        <CollectionSettingsDialog
          v-model="showSettingsDialog"
          :available-directories="availableDirectories"
          :loading="savingSettings"
          :initial-data="{
            default_directory_id: currentSettings?.default_directory_id,
            collection_mode: currentSettings?.collection_mode,
            no_remind_until: currentSettings?.no_remind_until
          }"
          @confirm="handleSaveSettings"
          @create-directory="showCreateDirectoryDialog = true"
        />

        <!-- 临时目录选择弹窗 -->
        <TempDirectoryDialog
          v-model="showTempDirectoryDialog"
          :available-directories="availableDirectories"
          :loading="savingTempDirectory"
          :current-settings="currentSettings"
          @confirm="handleTempDirectoryConfirm"
          @create-directory="showCreateDirectoryDialog = true"
        />

        <!-- 新增目录弹窗 -->
        <CreateDirectoryDialog
          v-model="showCreateDirectoryDialog"
          :loading="creatingDirectory"
          @confirm="handleCreateDirectory"
        />
      </template>
    </div>
</template>

<script setup lang='ts'>
  import { onBeforeMount, onMounted, ref, reactive, nextTick } from 'vue';
  import { ElNotification, ElMessage } from 'element-plus';
  import { observeTemuProductPage, submitGoodsData } from '@/utils/temu';
  import { showSecurityVerificationNotification } from '@/utils/temuSecurity';
  import {
    checkUserNeedSetup,
    getUserCollectionSettings,
    saveUserCollectionSettings,
    getUserAvailableDirectories,
    type CollectionSettings,
    type Directory,
    type SaveSettingsData
  } from '@/utils/collectionSettingsApi';
  import {
    userInfo as globalUserInfo,
    checkUserLoginStatus,
    initUserStore,
    cleanupUserStore,
    onUserInfoChange
  } from '@/utils/userStore';
  import { createDirectory } from '@/utils/directoryApi';
  import { isTemuGoodsDetailPage } from '@/utils/temu';
  import { checkAndShowGoodsDuplicateAlert } from '@/utils/goodsValidationApi';

  // 导入组件
  import StatusDisplay from './components/StatusDisplay.vue';
  import ManualConfirm from './components/ManualConfirm.vue';
  import CollectionSettingsDialog from './components/CollectionSettingsDialog.vue';
  import TempDirectoryDialog from './components/TempDirectoryDialog.vue';
  import CreateDirectoryDialog from './components/CreateDirectoryDialog.vue';

  // 响应式数据
  const cleanup = ref<(() => void) | null>(null);
  const userInfo = ref<any>(null);
  let userInfoChangeCleanup: (() => void) | null = null;
  let isCheckingUserSetup = ref(false); // 防止重复检查
  let lastCheckTime = 0; // 防抖时间戳
  const CHECK_DEBOUNCE_TIME = 2000; // 2秒防抖
  const currentSettings = ref<CollectionSettings | null>(null);
  const showStatusDisplay = ref(false);
  const showManualConfirm = ref(false);
  const showSettingsDialog = ref(false);
  const showTempDirectoryDialog = ref(false);
  const showCreateDirectoryDialog = ref(false);
  const savingSettings = ref(false);
  const savingTempDirectory = ref(false);
  const creatingDirectory = ref(false);
  const collectingGoods = ref(false); // 采集商品防抖
  const availableDirectories = ref<Directory[]>([]);
  const pendingGoodsData = ref<any>(null);
  const isGoodsDetailPage = ref(false); // 是否为商品详情页
  const tempDirectoryId = ref<number | null>(null); // 临时选择的目录ID
  const isSettingsFromStatusBar = ref(false); // 标记设置弹窗是否来自状态栏
  const currentDirectoryName = ref<string>(''); // 当前目录名称
  const enableDuplicateCheck = ref<boolean>(false); // 商品ID重复检查开关状态

  // 获取插件名称
  const getPluginName = (): Promise<string> => {
    return new Promise((resolve) => {
      chrome.runtime.getManifest ?
        resolve(chrome.runtime.getManifest().name) :
        resolve('跨境蜂');
    });
  };

  // 显示登录提示
  const showLoginNotification = async () => {
    const pluginName = await getPluginName();
    ElNotification({
      title: pluginName,
      message: '请先登录',
      type: 'warning',
      duration: 5000,
      position: 'top-right'
    });
  };

  // 加载可用目录列表
  const loadAvailableDirectories = async () => {
    try {
      console.log('开始加载目录列表...');
      const response = await getUserAvailableDirectories();
      console.log('目录列表API响应:', response);
      console.log('availableDirectories', response.list);
      availableDirectories.value = response.list;
      console.log('设置后的availableDirectories.value:', availableDirectories.value);
    } catch (error) {
      console.error('获取目录列表失败:', error);
    }
  };

  // 检查并处理用户设置
  const checkAndHandleUserSetup = async (showErrorMessage = true) => {
    try {
      // 检查登录状态
      const loginStatus = await checkUserLoginStatus();
      if (!loginStatus.isLoggedIn) {
        showLoginNotification();
        return false;
      }

      // 使用全局用户信息
      userInfo.value = {
        name: globalUserInfo.phone,
        phone: globalUserInfo.phone,
        isVip: globalUserInfo.isVip,
        isAdmin: globalUserInfo.isAdmin,
        expiryDate: globalUserInfo.expiryDate
      };

      // 检查是否需要设置
      console.log('------------------checkUserNeedSetup------------');
      const setupCheck = await checkUserNeedSetup();
      console.log('setupCheck', setupCheck);
      currentSettings.value = setupCheck.settings;

      if (setupCheck.need_setup) {
        // 需要设置，加载目录列表并显示设置弹窗
        // 这种情况不是从状态栏打开的，所以不设置标记
        isSettingsFromStatusBar.value = false;
        await loadAvailableDirectories();
        showSettingsDialog.value = true;
        return false;
      }

      // 设置完成，显示状态栏
      showStatusDisplay.value = true;

      // 如果是"不采集"模式，不启动商品观测
      if (currentSettings.value.collection_mode === 3) {
        console.log('当前为不采集模式，跳过商品观测');
        return false; // 返回false表示不启动商品观测
      }

      return true;
    } catch (error) {
      console.error('检查用户设置失败:', error);

      // 只在允许显示错误消息时才显示
      if (showErrorMessage) {
        ElMessage.error('检查用户设置失败');
      }

      return false;
    }
  };

  // 处理打开设置对话框
  const handleOpenSettings = async () => {
    try {
      // 标记设置弹窗来自状态栏
      isSettingsFromStatusBar.value = true;
      // 加载目录列表
      await loadAvailableDirectories();
      // 显示设置对话框
      showSettingsDialog.value = true;
    } catch (error) {
      console.error('打开设置失败:', error);
      ElMessage.error('加载目录列表失败');
    }
  };

  // 保存设置
  const handleSaveSettings = async (data: SaveSettingsData) => {
    try {
      savingSettings.value = true;

      await saveUserCollectionSettings(data);

      // 重新获取设置
      currentSettings.value = await getUserCollectionSettings();

      showSettingsDialog.value = false;
      showStatusDisplay.value = true;

      ElMessage.success('设置保存成功');

      // 如果是从状态栏打开的设置弹窗，不触发商品观测
      if (isSettingsFromStatusBar.value) {
        console.log('从状态栏打开的设置弹窗，不触发商品观测');
        isSettingsFromStatusBar.value = false; // 重置标记
      } else {
        // 根据新的采集模式决定是否启动商品观测
        if (currentSettings.value.collection_mode === 3) {
          // 不采集模式，停止商品观测
          if (cleanup.value) {
            cleanup.value();
            cleanup.value = null;
          }
          console.log('设置为不采集模式，已停止商品观测');
        } else {
          // 启动或重启商品观测
          await startProductObservation();

          // 如果有待处理的商品数据，根据模式处理
          if (pendingGoodsData.value) {
            await handleGoodsData(pendingGoodsData.value);
          }
        }
      }
    } catch (error) {
      console.error('保存设置失败:', error);
      ElMessage.error('保存设置失败');
    } finally {
      savingSettings.value = false;
    }
  };

  // 处理临时目录确认
  const handleTempDirectoryConfirm = async (data: { directory_id: number; no_remind_24h: boolean }) => {
    try {
      savingTempDirectory.value = true;

      if (data.no_remind_24h) {
        // 如果选择了24小时不提醒，保存到数据库
        await saveUserCollectionSettings({
          default_directory_id: data.directory_id,
          collection_mode: currentSettings.value?.collection_mode || 1,
          no_remind_24h: true
        });

        // 重新获取设置
        currentSettings.value = await getUserCollectionSettings();
        ElMessage.success('目录设置已保存，24小时内不再提醒');
      } else {
        // 仅作为临时目录使用
        tempDirectoryId.value = data.directory_id;
        ElMessage.success('已选择临时目录');
      }

      showTempDirectoryDialog.value = false;

      // 处理待处理的商品数据
      if (pendingGoodsData.value) {
        const goodsData = pendingGoodsData.value;
        pendingGoodsData.value = null; // 先清空待处理数据，避免重复处理
        await handleGoodsData(goodsData);
      }
    } catch (error) {
      console.error('保存临时目录失败:', error);
      ElMessage.error('保存临时目录失败');
    } finally {
      savingTempDirectory.value = false;
    }
  };

  // 创建目录
  const handleCreateDirectory = async (data: { name: string; description: string }) => {
    try {
      creatingDirectory.value = true;

      await createDirectory({
        name: data.name,
        description: data.description,
        status: 1
      });

      // 重新加载目录列表
      await loadAvailableDirectories();

      showCreateDirectoryDialog.value = false;

      ElMessage.success('目录创建成功');
    } catch (error) {
      console.error('创建目录失败:', error);
      ElMessage.error('创建目录失败');
    } finally {
      creatingDirectory.value = false;
    }
  };

  // 根据目录ID获取目录名称
  const getDirectoryNameById = async (directoryId: number): Promise<string> => {
    // 如果目录列表为空，先加载
    if (availableDirectories.value.length === 0) {
      try {
        await loadAvailableDirectories();
      } catch (error) {
        console.error('加载目录列表失败:', error);
      }
    }

    const directory = availableDirectories.value.find(dir => dir.id === directoryId);
    return directory ? directory.name : '未知目录';
  };

  // 检查是否需要临时目录选择
  const needTempDirectorySelection = () => {
    // 如果已经有临时目录ID，不需要再选择
    if (tempDirectoryId.value) {
      return false;
    }

    if (!currentSettings.value) {
      return true; // 没有设置
    }

    const settings = currentSettings.value;

    // 检查是否在免提醒期内
    if (settings.no_remind_until) {
      const remindTime = new Date(settings.no_remind_until);
      const now = new Date();
      if (now >= remindTime) {
        return true; // 已到期，需要重新选择
      }
      return false; // 还在免提醒期内
    }

    // 如果没有设置免提醒时间（no_remind_until为null），且是自动或手动采集模式
    if ((settings.collection_mode === 1 || settings.collection_mode === 2)) {
      return true; // 需要临时目录选择
    }

    return false;
  };

  // 处理商品数据
  const handleGoodsData = async (goodsData: any) => {
    console.log('handleGoodsData 开始处理，tempDirectoryId:', tempDirectoryId.value, 'currentSettings:', currentSettings.value);

    if (needTempDirectorySelection()) {
      // 需要选择临时目录，先加载目录列表
      try {
        await loadAvailableDirectories();
        console.log('临时目录选择前，可用目录数量:', availableDirectories.value.length);
        showTempDirectoryDialog.value = true;
        pendingGoodsData.value = goodsData;
      } catch (error) {
        console.error('加载目录列表失败:', error);
        ElMessage.error('加载目录列表失败');
      }
      return;
    }

    if (!currentSettings.value) {
      console.error('currentSettings为空，无法处理商品数据');
      return;
    }

    // 确定使用的目录ID（临时目录优先）
    const directoryId = tempDirectoryId.value || currentSettings.value.default_directory_id;

    // 添加目录ID到商品数据
    goodsData.directory_id = directoryId;

    if (currentSettings.value.collection_mode === 1) {
      // 自动采集模式
      try {
        // 获取目录名称用于提示
        const directoryName = await getDirectoryNameById(directoryId);
        await submitGoodsData(goodsData, directoryName);
        tempDirectoryId.value = null;
      } catch (error) {
        console.error('商品采集失败:', error);
      }
    } else if (currentSettings.value.collection_mode === 2) {
      // 手动确认模式
      pendingGoodsData.value = goodsData;
      // 获取目录名称用于显示
      currentDirectoryName.value = await getDirectoryNameById(directoryId);
      showManualConfirm.value = true;
    }
    // 模式3（不采集）在这里不会被调用，因为不会启动商品观测
  };

  // 处理重复检查开关变化
  const handleDuplicateCheckToggle = (enabled: boolean) => {
    console.log('接收到重复检查开关变化事件:', enabled);
    enableDuplicateCheck.value = enabled;
  };

  // 从本地存储加载重复检查开关状态
  const loadDuplicateCheckState = () => {
    try {
      const cached = localStorage.getItem('temu_duplicate_check_enabled');
      if (cached !== null) {
        enableDuplicateCheck.value = cached === 'true';
      } else {
        // 默认值为 false
        enableDuplicateCheck.value = false;
      }
      console.log('加载商品ID重复检查状态:', enableDuplicateCheck.value);
    } catch (error) {
      console.warn('加载商品ID重复检查状态失败:', error);
      enableDuplicateCheck.value = false;
    }
  };

  // 手动采集确认
  const handleManualCollect = async () => {
    if (collectingGoods.value || !pendingGoodsData.value) {
      return;
    }

    try {
      collectingGoods.value = true;

      // 获取目录名称用于提示
      const directoryId = pendingGoodsData.value.directory_id;
      const directoryName = await getDirectoryNameById(directoryId);
      await submitGoodsData(pendingGoodsData.value, directoryName);
      showManualConfirm.value = false;
      pendingGoodsData.value = null;
      // 清除临时目录ID
      tempDirectoryId.value = null;
    } catch (error) {
      console.error('采集商品失败:', error);
      ElMessage.error('采集商品失败');
    } finally {
      collectingGoods.value = false;
    }
  };

  // 启动商品观测
  const startProductObservation = async () => {
    // 如果已经有观测在运行，先停止
    if (cleanup.value) {
      cleanup.value();
      cleanup.value = null;
    }

    // 启动新的观测
    const { cleanup: cleanupFn } = observeTemuProductPage({
      onDataExtracted: async (goodsData) => {
        console.log('成功提取商品数据:', goodsData);
        goodsData.source_url = window.location.href;
        await handleGoodsData(goodsData);
      },
      onSecurityVerification: () => {
        showSecurityVerificationNotification();
      },
      onError: (message) => {
        ElNotification({
          title: '错误',
          message,
          type: 'error',
          duration: 0
        });
      }
    });
    cleanup.value = cleanupFn;
  };

  onMounted(async () => {
    // 检查是否为商品详情页
    isGoodsDetailPage.value = isTemuGoodsDetailPage();

    if (!isGoodsDetailPage.value) {
      console.log('非商品详情页，跳过所有检查');
      return;
    }

    // 初始化用户信息管理
    await initUserStore();

    // 检查用户登录状态
    const loginStatus = await checkUserLoginStatus();
    if (!loginStatus.isLoggedIn) {
      console.log('用户未登录，显示登录提示，跳过商品验证');
      showLoginNotification();
      return;
    }

    // 加载重复检查开关状态
    loadDuplicateCheckState();

    // 用户已登录，根据开关状态决定是否执行商品ID重复检查（第一优先级任务）
    if (enableDuplicateCheck.value) {
      try {
        console.log('用户已登录且开启重复检查，开始进行商品ID重复检查...');
        await checkAndShowGoodsDuplicateAlert();
        console.log('商品ID重复检查完成，继续后续流程');
      } catch (error) {
        console.error('商品ID重复检查过程中发生错误:', error);
        // 检查失败时不阻止后续流程，只记录错误
      }
    } else {
      console.log('商品ID重复检查开关已关闭，跳过重复检查');
    }

    // 监听用户信息变化
    userInfoChangeCleanup = onUserInfoChange(() => {
      console.log('用户信息发生变化，重新检查设置');

      // 只在商品详情页才处理
      if (!isGoodsDetailPage.value) {
        return;
      }

      // 防抖处理：避免短时间内重复检查
      const now = Date.now();
      if (now - lastCheckTime < CHECK_DEBOUNCE_TIME) {
        console.log('防抖：跳过重复的用户信息变化检查');
        return;
      }
      lastCheckTime = now;

      // 防止重复检查
      if (isCheckingUserSetup.value) {
        console.log('正在检查用户设置中，跳过重复检查');
        return;
      }

      // 当用户信息变化时，重新检查设置
      if (globalUserInfo.isLogin) {
        // 异步执行，避免阻塞
        setTimeout(async () => {
          try {
            isCheckingUserSetup.value = true;

            // 用户登录后，根据开关状态决定是否执行商品ID重复检查（第一优先级任务）
            if (enableDuplicateCheck.value) {
              try {
                console.log('用户信息变化后，用户已登录且开启重复检查，开始进行商品ID重复检查...');
                await checkAndShowGoodsDuplicateAlert();
                console.log('用户信息变化后商品ID重复检查完成，继续后续流程');
              } catch (error) {
                console.error('用户信息变化后商品ID重复检查过程中发生错误:', error);
                // 检查失败时不阻止后续流程，只记录错误
              }
            } else {
              console.log('用户信息变化后，商品ID重复检查开关已关闭，跳过重复检查');
            }

            const canProceed = await checkAndHandleUserSetup(false); // 不显示错误消息，避免死循环

            if (canProceed) {
              await startProductObservation();
            }
          } catch (error) {
            console.error('用户信息变化后检查设置失败:', error);
            // 不显示错误消息，避免死循环
          } finally {
            isCheckingUserSetup.value = false;
          }
        }, 100);
      } else {
        // 用户退出登录，清理状态
        showStatusDisplay.value = false;
        showManualConfirm.value = false;
        showSettingsDialog.value = false;
        showTempDirectoryDialog.value = false;
        userInfo.value = null;
        currentSettings.value = null;
        pendingGoodsData.value = null;
        tempDirectoryId.value = null;
        isCheckingUserSetup.value = false;

        // 停止商品观测
        if (cleanup.value) {
          cleanup.value();
          cleanup.value = null;
        }
      }
    });

    // 检查用户设置
    try {
      isCheckingUserSetup.value = true;
      const canProceed = await checkAndHandleUserSetup();

      // 只有在可以继续且不是"不采集"模式时才启动商品观测
      if (canProceed) {
        await startProductObservation();
      }
    } catch (error) {
      console.error('初始化检查用户设置失败:', error);
      // 初始化失败时显示一次错误消息
      ElMessage.error('初始化用户设置失败，请刷新页面重试');
    } finally {
      isCheckingUserSetup.value = false;
    }
  });

  onBeforeMount(() => {
    // 组件卸载时执行清理
    if (cleanup.value) {
      cleanup.value();
    }

    // 清理用户信息变化监听器
    if (userInfoChangeCleanup) {
      userInfoChangeCleanup();
    }

    // 清理用户信息管理
    cleanupUserStore();
  });
</script>

<style scoped>
.chrome_container {
  position: relative;
  z-index: 10000;
}

/* 全局 Select 下拉选项层级修复 - 针对 Chrome 扩展环境 */
:global(.el-select-dropdown) {
  z-index: 10004 !important;
}

/* 更强制的全局样式 */
:deep(.el-popper) {
  z-index: 10004 !important;
}

/* Chrome 扩展环境下的特殊处理 */
:global(body .el-select-dropdown) {
  z-index: 10004 !important;
}

:global(body .el-popper) {
  z-index: 10004 !important;
}

/* 商品重复提示弹窗样式 */
:global(.goods-exists-alert) {
  z-index: 10005 !important;
}

:global(.goods-exists-alert .el-message-box) {
  z-index: 10005 !important;
}

:global(.goods-exists-alert .el-overlay) {
  z-index: 10004 !important;
}

/* 弹窗样式优化 */
.el-dialog {
  border-radius: 8px;
}
</style>

<style lang='less' scoped>
@import "@/style/main.css";
.select-width {
  width: 850px;
}
.dialog-footer-2{
  padding: 10px;
  text-align: left;
  box-sizing: border-box;
}
.clickable {
  cursor: pointer;
}

.option_asyn{
  color: #67c23a;
}

.no_comment{
  color: #e6a23c;
}
</style>
