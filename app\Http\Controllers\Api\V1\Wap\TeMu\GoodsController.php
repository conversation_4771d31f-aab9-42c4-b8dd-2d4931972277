<?php
namespace App\Http\Controllers\Api\V1\Wap\TeMu;

use App\Http\Controllers\Api\Controller;
use App\Service\TeMu\GoodsService;

class GoodsController extends Controller{

    protected GoodsService $goodsService;

    public function __construct(GoodsService $goodsService){
        $this->goodsService = $goodsService;
    }

    public function goodsAdd(\Illuminate\Http\Request $request)
    {
        $data = $request->all();

        $user = $request->attributes->get('user');
        $user_id = $user['id'] ?? 0;
        $user_id = intval($user_id);
        $user_pid = $user['pid'] ?? 0;
        $user_pid = intval($user_pid);
        $data['user_id'] = $user_id;
        $data['user_pid'] = $user_pid;
        $result = $this->goodsService->saveGoodsData($data);
        return $this->apiSuccess($result, '商品数据添加成功');
    }

    /**
     * 检查商品ID是否存在
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkGoodsExists(\Illuminate\Http\Request $request)
    {
        $goodsId = $request->input('goods_id');

        if (empty($goodsId)) {
            return $this->apiError('商品ID不能为空');
        }

        $user = $request->attributes->get('user');
        $userId = $user['id'] ?? 0;
        $userPid = $user['pid'] ?? 0;

        // 如果是子账号，使用主账号ID进行查询
        $effectiveUserId = ($userPid > 0) ? $userPid : $userId;

        $exists = $this->goodsService->checkGoodsExists($goodsId, $effectiveUserId, $user['id']);

        return $this->apiSuccess(['exists' => $exists], $exists ? '商品已存在' : '商品不存在');
    }

    /**
     * 批量检查商品ID是否存在
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchCheckGoodsExists(\Illuminate\Http\Request $request)
    {
        $goodsIds = $request->input('goods_ids', []);

        if (!is_array($goodsIds) || empty($goodsIds)) {
            return $this->apiError('商品ID数组不能为空');
        }

        // 限制最大检查数量
        if (count($goodsIds) > 100) {
            return $this->apiError('单次最多检查100个商品ID');
        }

        $user = $request->attributes->get('user');
        $userId = $user['id'] ?? 0;
        $userPid = $user['pid'] ?? 0;

        // 如果是子账号，使用主账号ID进行查询
        $effectiveUserId = ($userPid > 0) ? $userPid : $userId;

        $result = $this->goodsService->batchCheckGoodsExists($goodsIds, $effectiveUserId, $user['id']);

        return $this->apiSuccess(['goods_map' => $result], '批量检查完成');
    }
}
