<?php

namespace App\Service\User;

use App\Models\User\User;
use Illuminate\Support\Str;
use App\Service\BaseService;
use App\Utils\CardCodeUtils;
use App\Exceptions\MyException;
use App\Models\User\CardCodeModel;
use Illuminate\Support\Facades\DB;
use App\Models\User\CardUsageRecordModel;
use Illuminate\Support\Facades\Validator;
use App\Models\User\CardOperationLogModel;

class CardCodeService extends BaseService
{
    /**
     * 获取卡密列表（分页）
     */
    public function getCardCodeList(int $adminId, array $params): array
    {
        // 验证管理员权限
        $admin = User::find($adminId);
        if (!$admin || !$admin->isCardAdmin()) {
            throw new MyException('无卡密管理权限');
        }

        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));
        
        // 获取筛选参数
        $cardType = $params['card_type'] ?? null;
        $status = $params['status'] ?? null;
        $isCopied = $params['is_copied'] ?? null;
        $copiedBy = $params['copied_by'] ?? null;
        $adminId = $params['admin_id'] ?? null;
        $cardCode = $params['card_code'] ?? null;
        $cardName = $params['card_name'] ?? null;
        $batchNo = $params['batch_no'] ?? null;
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;
        $minPrice = $params['min_price'] ?? null;
        $maxPrice = $params['max_price'] ?? null;
        $minPoints = $params['min_points'] ?? null;
        $maxPoints = $params['max_points'] ?? null;
        $vipDaysUnit = $params['vip_days_unit'] ?? null;
        $viewerId = $params['viewer_id'] ?? null;
        $timeType = $params['time_type'] ?? 'created_at'; // 时间类型
        $dateRange = $params['date_range'] ?? null; // 时间范围

        // 构建查询，预加载关联数据
        $query = CardCodeModel::with(['admin', 'usedBy', 'copiedBy']);

        // 按卡密类型筛选
        if (is_numeric($cardType) && in_array($cardType, [1, 2])) {
            $query->byCardType($cardType);
        }

        // 按状态筛选
        if (is_numeric($status) && in_array($status, [0, 1, 2])) {
            $query->byStatus($status);
        }

        // 按复制状态筛选
        if (is_numeric($isCopied) && in_array($isCopied, [0, 1])) {
            $query->byCopyStatus($isCopied);
        }

        // 按复制人筛选
        if (is_numeric($copiedBy)) {
            $query->where('copied_by', $copiedBy);
        }

        // 按创建人筛选
        if (is_numeric($adminId)) {
            $query->where('admin_id', $adminId);
        }

        // 按卡密筛选
        if ($cardCode) {
            $query->byCardCode($cardCode);
        }

        // 按卡密名称筛选
        if ($cardName) {
            $query->byCardName($cardName);
        }

        // 按批次号筛选
        if ($batchNo) {
            $query->byBatchNo($batchNo);
        }

        // 按时间类型和时间范围筛选
        if ($startDate || $endDate) {
            $query->byTimeTypeAndRange($timeType, $startDate, $endDate);
        }

        // 按价格范围筛选
        if ($minPrice !== null || $maxPrice !== null) {
            $query->byPriceRange($minPrice, $maxPrice);
        }

        // 按积分范围筛选
        if ($minPoints !== null || $maxPoints !== null) {
            $query->byPointsRange($minPoints, $maxPoints);
        }

        // 按卡密单位筛选
        if ($vipDaysUnit) {
            $query->byVipDaysUnit($vipDaysUnit);
        }

        // 获取总数
        $total = $query->count();

        // 分页查询
        $list = $query->with(['admin:id,phone'])
                     ->orderBy('id', 'desc')
                     ->offset(($page - 1) * $pageSize)
                     ->limit($pageSize)
                     ->get();

        // 格式化数据
        $formattedList = $list->map(function ($item) use ($viewerId) {
            if(empty($item->copied_by)){
                $item->copied_by = 0;
            }
            return [
                'id' => $item->id,
                'card_code' => $this->maskCardCode($item->card_code, $viewerId, $item->copied_by), // 脱敏处理
                'card_name' => $item->card_name,
                'card_type' => $item->card_type,
                'card_type_text' => $item->card_type_text,
                'price' => $item->price,
                'points' => $item->points,
                'vip_days' => $item->vip_days,
                'vip_days_unit' => $item->vip_days_unit,
                'vip_level' => $item->vip_level,
                'status' => $item->status,
                'status_text' => $item->status_text,
                'used_at' => $item->used_at?->format('Y-m-d H:i:s'),
                'used_by' => $item->used_by,
                'used_by_phone' => $item->usedBy?->phone ?? '',
                'is_copied' => $item->is_copied,
                'copied_at' => $item->copied_at?->format('Y-m-d H:i:s'),
                'copied_by' => $item->copied_by,
                'copied_by_phone' => $item->copiedBy?->phone ?? '',
                'batch_no' => $item->batch_no,
                'description' => $item->description,
                'valid_until' => $item->valid_until?->format('Y-m-d H:i:s'),
                'admin_phone' => $item->admin?->phone ?? '',
                'is_valid' => $item->isValid(),
                'can_delete' => $item->canDelete() && $item->status == 1 && $item->is_copied == 0, // 已使用或已复制不能删除
                'created_at' => $item->created_at,
                'updated_at' => $item->updated_at
            ];
        });

        return [
            'list' => $formattedList,
            'pagination' => [
                'page' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => ceil($total / $pageSize)
            ]
        ];
    }

    /**
     * 创建单个卡密
     */
    public function createCardCode(int $adminId, array $data, array $requestInfo = []): array
    {
        // 验证管理员权限
        $admin = User::find($adminId);
        if (!$admin || !$admin->isCardAdmin()) {
            throw new MyException('无卡密管理权限');
        }

        // 验证数据
        $validator = Validator::make($data, [
            'card_name' => 'required|string|max:100',
            'card_type' => 'required|integer|in:1,2',
            'price' => 'required|numeric|min:0|max:999999.99',
            'points' => 'required|integer|min:0|max:999999999',
            'vip_days' => 'integer|min:0|max:36500',
            'vip_days_unit' => 'string|in:year,month,day',
            'vip_level' => 'integer|min:1|max:10',
            'description' => 'string|max:500',
            'valid_until' => 'nullable|date|after:now',
            'prefix' => 'string|max:3|regex:/^[a-zA-Z][a-zA-Z0-9]*$/'
        ]);

        if ($validator->fails()) {
            throw new MyException('参数验证失败：' . $validator->errors()->first());
        }

        // 设置默认值
        $prefix = $data['prefix'] ?? 'KJF';
        $vipDays = $data['vip_days'] ?? 0;
        $vipDaysUnit = $data['vip_days_unit'] ?? CardCodeModel::VIP_UNIT_DAY;
        $vipLevel = $data['vip_level'] ?? 1;

        // 对于纯积分卡，VIP相关字段应为0
        if ($data['card_type'] == CardCodeModel::TYPE_POINTS_CARD) {
            $vipDays = 0;
            $vipDaysUnit = CardCodeModel::VIP_UNIT_DAY;
            $vipLevel = 0;
        }

        DB::beginTransaction();
        try {
            // 生成唯一卡密
            $cardCode = CardCodeUtils::generateUniqueCardCode($prefix);

            // 处理 valid_until 字段，确保空值存储为 null
            $validUntil = null;
            if (!empty($data['valid_until']) && $data['valid_until'] !== '') {
                try {
                    $validUntil = \Carbon\Carbon::parse($data['valid_until']);
                } catch (\Exception $e) {
                    // 如果日期解析失败，保持为 null
                    $validUntil = null;
                }
            }

            // 创建卡密
            $cardCodeModel = CardCodeModel::create([
                'card_code' => $cardCode,
                'card_name' => $data['card_name'],
                'card_type' => $data['card_type'],
                'price' => $data['price'],
                'points' => $data['points'],
                'vip_days' => $vipDays,
                'vip_days_unit' => $vipDaysUnit,
                'vip_level' => $vipLevel,
                'status' => CardCodeModel::STATUS_UNUSED,
                'is_copied' => CardCodeModel::COPY_STATUS_NOT_COPIED,
                'batch_no' => '',
                'description' => $data['description'] ?? '',
                'valid_until' => $validUntil,
                'admin_id' => $adminId
            ]);

            // 记录操作日志
            CardOperationLogModel::logOperation([
                'admin_id' => $adminId,
                'admin_phone' => $admin->phone,
                'operation_type' => CardOperationLogModel::OPERATION_CREATE,
                'card_code_id' => $cardCodeModel->id,
                'card_code' => $cardCode,
                'operation_count' => 1,
                'operation_data' => [
                    'card_data' => $cardCodeModel->toArray(),
                    'request_data' => $data
                ],
                'ip_address' => $requestInfo['ip'] ?? '',
                'user_agent' => $requestInfo['user_agent'] ?? '',
                'result_status' => CardOperationLogModel::RESULT_SUCCESS,
                'memo' => '创建单个卡密'
            ]);

            DB::commit();

            return [
                'id' => $cardCodeModel->id,
                'card_code' => $cardCode,
                'card_name' => $cardCodeModel->card_name,
                'card_type' => $cardCodeModel->card_type,
                'card_type_text' => $cardCodeModel->card_type_text,
                'price' => $cardCodeModel->price,
                'points' => $cardCodeModel->points,
                'vip_days' => $cardCodeModel->vip_days,
                'vip_level' => $cardCodeModel->vip_level,
                'status' => $cardCodeModel->status,
                'status_text' => $cardCodeModel->status_text,
                'description' => $cardCodeModel->description,
                'valid_until' => $cardCodeModel->valid_until?->format('Y-m-d H:i:s'),
                'created_at' => $cardCodeModel->created_at
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            // 记录失败日志
            CardOperationLogModel::logOperation([
                'admin_id' => $adminId,
                'admin_phone' => $admin->phone,
                'operation_type' => CardOperationLogModel::OPERATION_CREATE,
                'operation_count' => 1,
                'operation_data' => ['request_data' => $data],
                'ip_address' => $requestInfo['ip'] ?? '',
                'user_agent' => $requestInfo['user_agent'] ?? '',
                'result_status' => CardOperationLogModel::RESULT_FAILED,
                'error_message' => $e->getMessage(),
                'memo' => '创建单个卡密失败'
            ]);

            throw new MyException('创建卡密失败：' . $e->getMessage());
        }
    }

    /**
     * 批量生成卡密
     */
    public function batchCreateCardCodes(int $adminId, array $data, array $requestInfo = []): array
    {
        // 验证管理员权限
        $admin = User::find($adminId);
        if (!$admin || !$admin->isCardAdmin()) {
            throw new MyException('无卡密管理权限');
        }

        // 验证数据
        $validator = Validator::make($data, [
            'card_name' => 'required|string|max:100',
            'card_type' => 'required|integer|in:1,2',
            'price' => 'required|numeric|min:0|max:999999.99',
            'points' => 'required|integer|min:0|max:999999999',
            'vip_days' => 'integer|min:0|max:36500',
            'vip_days_unit' => 'string|in:year,month,day',
            'vip_level' => 'integer|min:0|max:10',
            'description' => 'string|max:500',
            'valid_until' => 'nullable|date|after:now',
            'prefix' => 'string|max:3|regex:/^[a-zA-Z][a-zA-Z0-9]*$/',
            'quantity' => 'required|integer|min:1|max:1000'
        ]);

        if ($validator->fails()) {
            throw new MyException('参数验证失败：' . $validator->errors()->first());
        }

        // 设置默认值
        $prefix = $data['prefix'] ?? 'KJF';
        $quantity = $data['quantity'];
        $vipDays = $data['vip_days'] ?? 0;
        $vipDaysUnit = $data['vip_days_unit'] ?? CardCodeModel::VIP_UNIT_DAY;
        $vipLevel = $data['vip_level'] ?? 1;

        if($vipLevel <= 0){
            $vipLevel = 1;
        }

        // 对于纯积分卡，VIP相关字段应为0
        if ($data['card_type'] == CardCodeModel::TYPE_POINTS_CARD) {
            $vipDays = 0;
            $vipDaysUnit = CardCodeModel::VIP_UNIT_DAY;
            $vipLevel = 1;
        }

        // 生成批次号
        $batchNo = 'BATCH_' . date('YmdHis') . '_' . Str::random(6);

        // 处理 valid_until 字段，确保空值存储为 null
        $validUntil = null;
        if (!empty($data['valid_until']) && $data['valid_until'] !== '') {
            try {
                $validUntil = \Carbon\Carbon::parse($data['valid_until']);
            } catch (\Exception $e) {
                // 如果日期解析失败，保持为 null
                $validUntil = null;
            }
        }

        DB::beginTransaction();
        try {
            $createdCards = [];
            $failedCount = 0;

            for ($i = 0; $i < $quantity; $i++) {
                try {
                    // 生成唯一卡密
                    $cardCode = CardCodeUtils::generateUniqueCardCode($prefix);

                    // 创建卡密
                    $cardCodeModel = CardCodeModel::create([
                        'card_code' => $cardCode,
                        'card_name' => $data['card_name'],
                        'card_type' => $data['card_type'],
                        'price' => $data['price'],
                        'points' => $data['points'],
                        'vip_days' => $vipDays,
                        'vip_days_unit' => $vipDaysUnit,
                        'vip_level' => $vipLevel,
                        'status' => CardCodeModel::STATUS_UNUSED,
                        'is_copied' => CardCodeModel::COPY_STATUS_NOT_COPIED,
                        'batch_no' => $batchNo,
                        'description' => $data['description'] ?? '',
                        'valid_until' => $validUntil,
                        'admin_id' => $adminId
                    ]);

                    $createdCards[] = [
                        'id' => $cardCodeModel->id,
                        'card_code' => $cardCode,
                        'card_name' => $cardCodeModel->card_name,
                        'card_type' => $cardCodeModel->card_type,
                        'price' => $cardCodeModel->price,
                        'points' => $cardCodeModel->points,
                        'vip_days' => $cardCodeModel->vip_days,
                        'vip_level' => $cardCodeModel->vip_level,
                        'batch_no' => $batchNo,
                        'created_at' => $cardCodeModel->created_at
                    ];

                } catch (\Exception $e) {
                    $failedCount++;
                    // 如果失败数量过多，停止生成
                    if ($failedCount > $quantity * 0.1) { // 失败率超过10%
                        throw new MyException('批量生成失败率过高，已停止生成');
                    }
                }
            }

            $successCount = count($createdCards);

            // 记录操作日志
            CardOperationLogModel::logOperation([
                'admin_id' => $adminId,
                'admin_phone' => $admin->phone,
                'operation_type' => CardOperationLogModel::OPERATION_BATCH_CREATE,
                'batch_no' => $batchNo,
                'operation_count' => $successCount,
                'operation_data' => [
                    'request_data' => $data,
                    'success_count' => $successCount,
                    'failed_count' => $failedCount,
                    'batch_no' => $batchNo
                ],
                'ip_address' => $requestInfo['ip'] ?? '',
                'user_agent' => $requestInfo['user_agent'] ?? '',
                'result_status' => $successCount > 0 ? CardOperationLogModel::RESULT_SUCCESS : CardOperationLogModel::RESULT_FAILED,
                'memo' => "批量生成卡密，成功{$successCount}个，失败{$failedCount}个"
            ]);

            DB::commit();

            return [
                'batch_no' => $batchNo,
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'total_requested' => $quantity,
                'created_cards' => $createdCards
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            // 记录失败日志
            CardOperationLogModel::logOperation([
                'admin_id' => $adminId,
                'admin_phone' => $admin->phone,
                'operation_type' => CardOperationLogModel::OPERATION_BATCH_CREATE,
                'batch_no' => $batchNo,
                'operation_count' => 0,
                'operation_data' => ['request_data' => $data],
                'ip_address' => $requestInfo['ip'] ?? '',
                'user_agent' => $requestInfo['user_agent'] ?? '',
                'result_status' => CardOperationLogModel::RESULT_FAILED,
                'error_message' => $e->getMessage(),
                'memo' => '批量生成卡密失败'
            ]);

            throw new MyException('批量生成卡密失败：' . $e->getMessage());
        }
    }



    /**
     * 更新卡密
     */
    public function updateCardCode(int $adminId, int $cardCodeId, array $data, array $requestInfo = []): array
    {
        // 验证管理员权限
        $admin = User::find($adminId);
        if (!$admin || !$admin->isCardAdmin()) {
            throw new MyException('无卡密管理权限');
        }

        // 查找卡密
        $cardCode = CardCodeModel::find($cardCodeId);
        if (!$cardCode) {
            throw new MyException('卡密不存在');
        }

        // 已使用或已复制的卡密不能修改核心信息
        if ($cardCode->status === CardCodeModel::STATUS_USED || $cardCode->is_copied === CardCodeModel::COPY_STATUS_COPIED) {
            // 只允许修改描述和有效期
            $allowedFields = ['description', 'valid_until'];
            $data = array_intersect_key($data, array_flip($allowedFields));
        }

        // 验证数据
        $rules = [
            'card_name' => 'string|max:100',
            'price' => 'numeric|min:0|max:999999.99',
            'points' => 'integer|min:0|max:999999999',
            'vip_days' => 'integer|min:0|max:36500',
            'vip_level' => 'integer|min:1|max:10',
            'status' => 'integer|in:0,1,2',
            'description' => 'string|max:500',
            'valid_until' => 'nullable|date'
        ];

        $validator = Validator::make($data, $rules);
        if ($validator->fails()) {
            throw new MyException('参数验证失败：' . $validator->errors()->first());
        }

        DB::beginTransaction();
        try {
            // 记录原始数据
            $originalData = $cardCode->toArray();

            // 对于纯积分卡，确保VIP相关字段为0
            if ($cardCode->card_type == CardCodeModel::TYPE_POINTS_CARD) {
                if (isset($data['vip_days'])) $data['vip_days'] = 0;
                if (isset($data['vip_level'])) $data['vip_level'] = 0;
            }

            // 处理 valid_until 字段，确保空值存储为 null
            if (isset($data['valid_until'])) {
                if (empty($data['valid_until']) || $data['valid_until'] === '') {
                    $data['valid_until'] = null;
                } else {
                    try {
                        $data['valid_until'] = \Carbon\Carbon::parse($data['valid_until']);
                    } catch (\Exception $e) {
                        // 如果日期解析失败，设置为 null
                        $data['valid_until'] = null;
                    }
                }
            }

            // 更新卡密
            $cardCode->fill($data);
            $cardCode->save();

            // 记录操作日志
            CardOperationLogModel::logOperation([
                'admin_id' => $adminId,
                'admin_phone' => $admin->phone,
                'operation_type' => CardOperationLogModel::OPERATION_UPDATE,
                'card_code_id' => $cardCodeId,
                'card_code' => $cardCode->card_code,
                'operation_count' => 1,
                'operation_data' => [
                    'original_data' => $originalData,
                    'updated_data' => $data,
                    'final_data' => $cardCode->fresh()->toArray()
                ],
                'ip_address' => $requestInfo['ip'] ?? '',
                'user_agent' => $requestInfo['user_agent'] ?? '',
                'result_status' => CardOperationLogModel::RESULT_SUCCESS,
                'memo' => '更新卡密信息'
            ]);

            DB::commit();

            return [
                'id' => $cardCode->id,
                'card_code' => $cardCode->card_code,
                'card_name' => $cardCode->card_name,
                'card_type' => $cardCode->card_type,
                'card_type_text' => $cardCode->card_type_text,
                'price' => $cardCode->price,
                'points' => $cardCode->points,
                'vip_days' => $cardCode->vip_days,
                'vip_level' => $cardCode->vip_level,
                'status' => $cardCode->status,
                'status_text' => $cardCode->status_text,
                'description' => $cardCode->description,
                'valid_until' => $cardCode->valid_until?->format('Y-m-d H:i:s'),
                'updated_at' => $cardCode->updated_at
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            // 记录失败日志
            CardOperationLogModel::logOperation([
                'admin_id' => $adminId,
                'admin_phone' => $admin->phone,
                'operation_type' => CardOperationLogModel::OPERATION_UPDATE,
                'card_code_id' => $cardCodeId,
                'card_code' => $cardCode->card_code,
                'operation_count' => 1,
                'operation_data' => ['request_data' => $data],
                'ip_address' => $requestInfo['ip'] ?? '',
                'user_agent' => $requestInfo['user_agent'] ?? '',
                'result_status' => CardOperationLogModel::RESULT_FAILED,
                'error_message' => $e->getMessage(),
                'memo' => '更新卡密失败'
            ]);

            throw new MyException('更新卡密失败：' . $e->getMessage());
        }
    }

    /**
     * 删除卡密
     */
    public function deleteCardCode(int $adminId, int $cardCodeId, array $requestInfo = []): bool
    {
        // 验证管理员权限
        $admin = User::find($adminId);
        if (!$admin || !$admin->isCardAdmin()) {
            throw new MyException('无卡密管理权限');
        }

        // 查找卡密
        $cardCode = CardCodeModel::find($cardCodeId);
        if (!$cardCode) {
            throw new MyException('卡密不存在');
        }

        // 检查是否可以删除
        if (!$cardCode->canDelete()) {
            throw new MyException('已使用的卡密不能删除');
        }

        DB::beginTransaction();
        try {
            // 记录原始数据
            $originalData = $cardCode->toArray();

            // 删除卡密
            $cardCode->delete();

            // 记录操作日志
            CardOperationLogModel::logOperation([
                'admin_id' => $adminId,
                'admin_phone' => $admin->phone,
                'operation_type' => CardOperationLogModel::OPERATION_DELETE,
                'card_code_id' => $cardCodeId,
                'card_code' => $originalData['card_code'],
                'operation_count' => 1,
                'operation_data' => [
                    'deleted_data' => $originalData
                ],
                'ip_address' => $requestInfo['ip'] ?? '',
                'user_agent' => $requestInfo['user_agent'] ?? '',
                'result_status' => CardOperationLogModel::RESULT_SUCCESS,
                'memo' => '删除卡密'
            ]);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();

            // 记录失败日志
            CardOperationLogModel::logOperation([
                'admin_id' => $adminId,
                'admin_phone' => $admin->phone,
                'operation_type' => CardOperationLogModel::OPERATION_DELETE,
                'card_code_id' => $cardCodeId,
                'card_code' => $cardCode->card_code,
                'operation_count' => 1,
                'operation_data' => [],
                'ip_address' => $requestInfo['ip'] ?? '',
                'user_agent' => $requestInfo['user_agent'] ?? '',
                'result_status' => CardOperationLogModel::RESULT_FAILED,
                'error_message' => $e->getMessage(),
                'memo' => '删除卡密失败'
            ]);

            throw new MyException('删除卡密失败：' . $e->getMessage());
        }
    }

    /**
     * 获取卡密详情
     */
    public function getCardCodeDetail(int $adminId, int $cardCodeId): array
    {
        // 验证管理员权限
        $admin = User::find($adminId);
        if (!$admin || !$admin->isCardAdmin()) {
            throw new MyException('无卡密管理权限');
        }

        // 查找卡密，预加载关联数据
        $cardCode = CardCodeModel::with(['admin:id,phone', 'usedBy:id,phone', 'copiedBy:id,phone', 'usageRecords.user:id,phone'])
                                 ->find($cardCodeId);
        if (!$cardCode) {
            throw new MyException('卡密不存在');
        }

        // 获取使用记录
        $usageRecords = $cardCode->usageRecords->map(function ($record) {
            return [
                'id' => $record->id,
                'user_phone' => $record->user?->phone ?? '',
                'points_added' => $record->points_added,
                'vip_days_added' => $record->vip_days_added,
                'vip_level_set' => $record->vip_level_set,
                'points_change_text' => $record->points_change_text,
                'vip_status_change_text' => $record->vip_status_change_text,
                'vip_end_time_change_text' => $record->vip_end_time_change_text,
                'usage_effect_summary' => $record->usage_effect_summary,
                'ip_address' => $record->ip_address,
                'used_at' => $record->used_at->format('Y-m-d H:i:s'),
                'created_at' => $record->created_at
            ];
        });
        if(empty($cardCode->copied_by)){
            $cardCode->copied_by = 0;
        }
        return [
            'id' => $cardCode->id,
            'card_code' => $this->maskCardCode($cardCode->card_code, $adminId, $cardCode->copied_by), // 根据查看者脱敏
            'card_name' => $cardCode->card_name,
            'card_type' => $cardCode->card_type,
            'card_type_text' => $cardCode->card_type_text,
            'price' => $cardCode->price,
            'points' => $cardCode->points,
            'vip_days' => $cardCode->vip_days,
            'vip_days_unit' => $cardCode->vip_days_unit,
            'vip_level' => $cardCode->vip_level,
            'status' => $cardCode->status,
            'status_text' => $cardCode->status_text,
            'used_at' => $cardCode->used_at?->format('Y-m-d H:i:s'),
            'used_by' => $cardCode->used_by,
            'used_by_phone' => $cardCode->usedBy?->phone ?? '',
            'is_copied' => $cardCode->is_copied,
            'copied_at' => $cardCode->copied_at?->format('Y-m-d H:i:s'),
            'copied_by' => $cardCode->copied_by,
            'copied_by_phone' => $cardCode->copiedBy?->phone ?? '',
            'batch_no' => $cardCode->batch_no,
            'description' => $cardCode->description,
            'valid_until' => $cardCode->valid_until?->format('Y-m-d H:i:s'),
            'admin_phone' => $cardCode->admin?->phone ?? '',
            'is_valid' => $cardCode->isValid(),
            'can_delete' => $cardCode->canDelete() && $cardCode->status == 1 && $cardCode->is_copied == 0,
            'usage_count' => $usageRecords->count(),
            'usage_records' => $usageRecords,
            'created_at' => $cardCode->created_at,
            'updated_at' => $cardCode->updated_at
        ];
    }

    /**
     * 批量删除卡密
     */
    public function batchDeleteCardCodes(int $adminId, array $cardCodeIds, array $requestInfo = []): array
    {
        // 验证管理员权限
        $admin = User::find($adminId);
        if (!$admin || !$admin->isCardAdmin()) {
            throw new MyException('无卡密管理权限');
        }

        if (empty($cardCodeIds)) {
            throw new MyException('请选择要删除的卡密');
        }

        // 查找卡密
        $cardCodes = CardCodeModel::whereIn('id', $cardCodeIds)->get();
        if ($cardCodes->isEmpty()) {
            throw new MyException('未找到要删除的卡密');
        }

        $successCount = 0;
        $failedCount = 0;
        $failedReasons = [];

        DB::beginTransaction();
        try {
            foreach ($cardCodes as $cardCode) {
                try {
                    // 检查是否可以删除
                    if (!$cardCode->canDelete()) {
                        $failedCount++;
                        $failedReasons[] = "卡密 {$cardCode->card_code} 已使用，不能删除";
                        continue;
                    }

                    // 记录原始数据
                    $originalData = $cardCode->toArray();

                    // 删除卡密
                    $cardCode->delete();
                    $successCount++;

                    // 记录操作日志
                    CardOperationLogModel::logOperation([
                        'admin_id' => $adminId,
                        'admin_phone' => $admin->phone,
                        'operation_type' => CardOperationLogModel::OPERATION_DELETE,
                        'card_code_id' => $cardCode->id,
                        'card_code' => $cardCode->card_code,
                        'operation_count' => 1,
                        'operation_data' => [
                            'deleted_data' => $originalData,
                            'batch_operation' => true
                        ],
                        'ip_address' => $requestInfo['ip'] ?? '',
                        'user_agent' => $requestInfo['user_agent'] ?? '',
                        'result_status' => CardOperationLogModel::RESULT_SUCCESS,
                        'memo' => '批量删除卡密'
                    ]);

                } catch (\Exception $e) {
                    $failedCount++;
                    $failedReasons[] = "卡密 {$cardCode->card_code} 删除失败：{$e->getMessage()}";
                }
            }

            DB::commit();

            return [
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'total_requested' => count($cardCodeIds),
                'failed_reasons' => $failedReasons
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('批量删除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取卡密统计信息
     */
    public function getCardCodeStatistics(int $adminId, array $filters = []): array
    {
        // 验证管理员权限
        $admin = User::find($adminId);
        if (!$admin || !$admin->isCardAdmin()) {
            throw new MyException('无卡密管理权限');
        }

        // 构建基础查询，应用筛选条件
        $baseQuery = CardCodeModel::query();

        // 应用筛选条件
        if (!empty($filters['card_type'])) {
            $baseQuery->where('card_type', $filters['card_type']);
        }
        if (isset($filters['is_copied'])) {
            $baseQuery->where('is_copied', $filters['is_copied']);
        }
        if (!empty($filters['copied_by'])) {
            $baseQuery->where('copied_by', $filters['copied_by']);
        }
        if (!empty($filters['admin_id'])) {
            $baseQuery->where('admin_id', $filters['admin_id']);
        }

        // 时间筛选
        $timeType = $filters['time_type'] ?? 'created_at';
        $startDate = $filters['start_date'] ?? null;
        $endDate = $filters['end_date'] ?? null;
        if ($startDate || $endDate) {
            $baseQuery->byTimeTypeAndRange($timeType, $startDate, $endDate);
        }

        // 卡密单位筛选
        if (!empty($filters['vip_days_unit'])) {
            $baseQuery->byVipDaysUnit($filters['vip_days_unit']);
        }

        // 基础统计
        $totalCount = (clone $baseQuery)->count();
        $unusedCount = (clone $baseQuery)->unused()->count();
        $usedCount = (clone $baseQuery)->used()->count();
        $disabledCount = (clone $baseQuery)->disabled()->count();

        // 按类型统计
        $vipCardCount = (clone $baseQuery)->byCardType(CardCodeModel::TYPE_VIP_CARD)->count();
        $pointsCardCount = (clone $baseQuery)->byCardType(CardCodeModel::TYPE_POINTS_CARD)->count();

        // 复制状态统计
        $copiedCount = (clone $baseQuery)->where('is_copied', 1)->count();
        $notCopiedCount = (clone $baseQuery)->where('is_copied', 0)->count();

        // 金额统计
        $totalAmount = (clone $baseQuery)->sum('price');
        $unusedAmount = (clone $baseQuery)->unused()->sum('price');
        $usedAmount = (clone $baseQuery)->used()->sum('price');
        $disabledAmount = (clone $baseQuery)->disabled()->sum('price');
        $copiedAmount = (clone $baseQuery)->where('is_copied', 1)->sum('price');
        $notCopiedAmount = (clone $baseQuery)->where('is_copied', 0)->sum('price');

        // 今日统计
        $todayCreated = CardCodeModel::whereDate('created_at', today())->count();
        $todayUsed = CardUsageRecordModel::whereDate('used_at', today())->count();

        // 本月统计
        $monthCreated = CardCodeModel::whereMonth('created_at', now()->month)
                                   ->whereYear('created_at', now()->year)
                                   ->count();
        $monthUsed = CardUsageRecordModel::whereMonth('used_at', now()->month)
                                        ->whereYear('used_at', now()->year)
                                        ->count();

        return [
            'total_count' => $totalCount,
            'unused_count' => $unusedCount,
            'used_count' => $usedCount,
            'disabled_count' => $disabledCount,
            'vip_card_count' => $vipCardCount,
            'points_card_count' => $pointsCardCount,
            'today_created' => $todayCreated,
            'today_used' => $todayUsed,
            'month_created' => $monthCreated,
            'month_used' => $monthUsed,
            'copied_count' => $copiedCount,
            'not_copied_count' => $notCopiedCount,
            'total_amount' => round($totalAmount, 2),
            'unused_amount' => round($unusedAmount, 2),
            'used_amount' => round($usedAmount, 2),
            'disabled_amount' => round($disabledAmount, 2),
            'copied_amount' => round($copiedAmount, 2),
            'not_copied_amount' => round($notCopiedAmount, 2)
        ];
    }

    /**
     * 复制卡密并标记状态
     */
    public function copyCardCode(int $adminId, int $cardCodeId, string $memo = '', string $ipAddress = '', string $userAgent = ''): array
    {
        // 验证管理员权限
        $admin = User::find($adminId);
        if (!$admin || !$admin->isCardAdmin()) {
            throw new MyException('无卡密管理权限');
        }

        // 查找卡密
        $cardCodeModel = CardCodeModel::find($cardCodeId);
        if (!$cardCodeModel) {
            throw new MyException('卡密不存在');
        }

        if($cardCodeModel->is_copied === CardCodeModel::COPY_STATUS_COPIED){
            throw new MyException('卡密已被复制过,无法再次操作');
        }

        if($cardCodeModel->status === CardCodeModel::STATUS_USED){
            throw new MyException('卡密已被使用,无法再次操作');
        }

        DB::beginTransaction();
        try {
            // 更新复制状态
            $cardCodeModel->update([
                'is_copied' => CardCodeModel::COPY_STATUS_COPIED,
                'copied_at' => now(),
                'copied_by' => $adminId
            ]);

            // 追加备注信息
            $adminInfo = "【管理员{$admin->phone}于" . now()->format('Y-m-d H:i:s') . "复制卡密信息】";
            $newDescription = $cardCodeModel->description;
            if (!empty($memo)) {
                $newDescription .= "\n" . $memo;
            }
            $newDescription .= "\n" . $adminInfo;

            $cardCodeModel->update(['description' => $newDescription]);

            // 记录操作日志
            CardOperationLogModel::logOperation([
                'admin_id' => $adminId,
                'admin_phone' => $admin->phone,
                'operation_type' => CardOperationLogModel::OPERATION_COPY,
                'card_code_id' => $cardCodeModel->id,
                'card_code' => $cardCodeModel->card_code,
                'operation_count' => 1,
                'operation_data' => [
                    'memo' => $memo,
                    'copied_at' => now()->toDateTimeString()
                ],
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'result_status' => CardOperationLogModel::RESULT_SUCCESS,
                'memo' => '复制卡密：' . $memo
            ]);

            DB::commit();

            return [
                'id' => $cardCodeModel->id,
                'card_code' => $cardCodeModel->card_code,
                'is_copied' => true,
                'copied_at' => $cardCodeModel->copied_at->toDateTimeString(),
                'description' => $cardCodeModel->description
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('复制卡密失败：' . $e->getMessage());
        }
    }

    /**
     * 卡密脱敏处理
     * 格式：隐藏中间15个字符，用5个星号表示
     * 例如：kjf-ABCDEFGHIJKLMNOPQR -> KJF-ABC*****QR
     */
    private function maskCardCode(string $cardCode, ?int $viewerId = null, ?int $copiedBy = null): string
    {
        // 如果查看者是复制人，不脱敏
        if ($viewerId) {
            // 如果传递了复制人ID，直接比对，避免数据库查询
            if ($copiedBy !== null) {
                if ($copiedBy == $viewerId) {
                    return $cardCode;
                }
            } else {
                // 没有传递复制人ID时，才进行数据库查询（区分大小写）
                $cardCodeModel = CardCodeModel::byCardCode($cardCode)->first();
                if ($cardCodeModel && $cardCodeModel->copied_by == $viewerId) {
                    return $cardCode;
                }
            }
        }

        $cardLength = strlen($cardCode);

        // 如果卡密长度小于等于15，无法隐藏15个字符，直接返回原卡密
        if ($cardLength <= 15) {
            return $cardCode;
        }

        // 计算要隐藏的中间15个字符的起始位置
        // 保证前后都有字符显示
        $hideStart = floor(($cardLength - 15) / 2);
        $hideEnd = $hideStart + 15;

        // 构建脱敏后的卡密：前面部分 + 5个星号 + 后面部分
        $prefix = substr($cardCode, 0, $hideStart);
        $suffix = substr($cardCode, $hideEnd);

        return $prefix . str_repeat('*', 5) . $suffix;
    }
}
