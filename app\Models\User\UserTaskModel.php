<?php

namespace App\Models\User;

use App\Models\BaseModel;
use App\Models\User\UserGoodsDirectoryModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserTaskModel extends BaseModel
{
    protected $table = 'user_task';

    protected $fillable = [
        'user_id',
        'is_selected',
        'selected_ids',
        'time_range',
        'day_start',
        'day_end',
        'sort_order',
        'execute_type',
        'directory_id',
        'user_account_id',
        'account_setting_type',
        'currentcy',
        'exchange_rate_currency',
        'price_rate',
        'price_add',
        'price_subtract',
        'quantity',
        'vat_rate',
        'preparing_day',
        'task_count',
        'task_num',
        'latest_goods_id',
        'latest_time',
        'task_over',
        'goods_count',
        'sku_count',
        'rejected_count',
        'goods_ai_name_status',
        'goods_statistics_status',
        'points_deduction_status',
        'latest_ai_goods_id',
        'memo',
        'price_min',
        'price_max'
    ];

    // 关联店铺账号
    public function store(): BelongsTo
    {
        return $this->belongsTo(UserAccountModel::class, 'user_account_id', 'id');
    }

    // 关联商品目录
    public function directory(): BelongsTo
    {
        return $this->belongsTo(UserGoodsDirectoryModel::class, 'directory_id', 'id');
    }

    // 关联任务详情
    public function details(): HasMany
    {
        return $this->hasMany(UserTaskDetailModel::class, 'task_id', 'id');
    }

    // 作用域：按用户ID筛选
    public function scopeByUserId($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    // 作用域：按完成状态筛选
    public function scopeByTaskOver($query, int $taskOver)
    {
        return $query->where('task_over', $taskOver);
    }
}