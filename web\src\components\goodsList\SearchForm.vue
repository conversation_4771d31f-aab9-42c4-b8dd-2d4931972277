<template>
  <div class="search-section">
    <el-form :model="searchForm" inline class="search-form">
      <el-form-item label="时间类型">
        <el-select v-model="searchForm.time_type" placeholder="请选择" clearable style="width: 120px;" @change="handleInput">
          <el-option label="创建时间" value="created_at" />
          <el-option label="更新时间" value="updated_at" />
        </el-select>
      </el-form-item>
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="searchForm.date_range"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
          style="width: 240px;"
          @change="handleInput"
        />
      </el-form-item>
      <el-form-item label="商品ID">
        <el-input v-model="searchForm.goods_id" placeholder="请输入商品ID" clearable type="number"
          @input="handleGoodsIdInput" />
      </el-form-item>
      <el-form-item label="商品名称">
        <el-input v-model="searchForm.goods_name" placeholder="请输入商品名称" clearable @input="handleInput" />
      </el-form-item>
      <el-form-item label="子账号姓名">
        <el-input v-model="searchForm.sub_account_name" placeholder="请输入子账号姓名" clearable @input="handleInput" />
      </el-form-item>
      <el-form-item label="子账号手机号">
        <el-input v-model="searchForm.sub_account_phone" placeholder="请输入子账号手机号" clearable @input="handleInput" />
      </el-form-item>
      <!-- 状态检索选项已隐藏，但保留数据传递逻辑便于后续恢复 -->
      <!-- <el-form-item label="状态" class="status-item">
        <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 180px;"
          @change="handleInput">
          <el-option label="正常" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="调整价格">
        <el-select v-model="searchForm.price_adjusted" placeholder="请选择" clearable style="width: 120px;"
          @change="handleInput">
          <el-option label="已调整" value="1" />
          <el-option label="未调整" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="调整分类">
        <el-select v-model="searchForm.cat_adjusted" placeholder="请选择" clearable style="width: 120px;"
          @change="handleInput">
          <el-option label="已调整" value="1" />
          <el-option label="未调整" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="searchForm.only_sub_account" @change="handleInput">子账号</el-checkbox>
        <el-button type="primary" @click="handleSearch" style="margin-left: 10px;">
          <el-icon>
            <Search />
          </el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset" style="margin-right: 20px;">
          <el-icon>
            <Refresh />
          </el-icon>
          重置
        </el-button>
        <el-button type="info" @click="handleCheckImages" :loading="checkingImages">
          <el-icon>
            <Picture />
          </el-icon>
          检查商品图片
        </el-button>
        <el-button type="warning" @click="handlePublishGoods">
          <el-icon>
            <Upload />
          </el-icon>
          发布商品
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import { Search, Refresh, Picture, Upload } from '@element-plus/icons-vue'

// 搜索表单接口
interface SearchFormData {
  goods_name: string
  goods_id: number | null | undefined
  status: number | null | undefined
  price_adjusted: string | null | undefined
  cat_adjusted: string | null | undefined
  time_type: string | null | undefined
  date_range: string[] | null | undefined
  sub_account_name: string
  sub_account_phone: string
  only_sub_account: boolean
}

// Props
interface Props {
  modelValue: SearchFormData
  checkingImages: boolean
}

const props = withDefaults(defineProps<Props>(), {
  checkingImages: false
})

// Events
const emit = defineEmits<{
  'update:modelValue': [value: SearchFormData]
  search: []
  reset: []
  checkImages: []
  publishGoods: []
}>()

// 本地搜索表单数据
const searchForm = reactive<SearchFormData>({
  goods_name: '',
  goods_id: null,
  status: null,
  price_adjusted: null,
  cat_adjusted: null,
  time_type: 'created_at',
  date_range: null,
  sub_account_name: '',
  sub_account_phone: '',
  only_sub_account: false
})

// 监听 props 变化，同步到本地数据
watch(() => props.modelValue, (newValue) => {
  Object.assign(searchForm, newValue)
}, { immediate: true, deep: true })

// 处理输入变化
const handleInput = () => {
  emit('update:modelValue', { ...searchForm })
}

// 处理商品ID输入
const handleGoodsIdInput = (value: string | number | null) => {
  // 如果输入为空，设置为null
  if (value === '' || value === null) {
    searchForm.goods_id = null
  } else {
    // 否则转换为数字
    const numValue = Number(value)
    searchForm.goods_id = isNaN(numValue) ? null : numValue
  }
  emit('update:modelValue', { ...searchForm })
}

// 处理搜索
const handleSearch = () => {
  // 先更新父组件的搜索表单数据
  emit('update:modelValue', { ...searchForm })
  // 然后触发搜索
  emit('search')
}

// 处理重置
const handleReset = () => {
  Object.assign(searchForm, {
    goods_name: '',
    goods_id: null,
    status: null,
    price_adjusted: null,
    cat_adjusted: null,
    time_type: 'created_at',
    date_range: null,
    sub_account_name: '',
    sub_account_phone: '',
    only_sub_account: false
  })
  emit('update:modelValue', { ...searchForm })
  emit('reset')
}

// 处理检查图片
const handleCheckImages = () => {
  emit('checkImages')
}

// 处理发布商品
const handlePublishGoods = () => {
  emit('publishGoods')
}
</script>

<style scoped>
.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>
