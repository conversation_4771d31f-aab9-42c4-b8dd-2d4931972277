/**
 * 商品筛选工具
 * 负责根据价格、销量和本地商品状态筛选商品
 */

import { ProductFilterSettings } from './settingsManager';
import { 
  globalErrorHandler, 
  globalProgressManager, 
  globalResultManager,
  safeExecute,
  handleDOMParseFailure,
  DetailedErrorType,
  ErrorSeverity,
  FilterResultFeedback,
  ProgressFeedback
} from './filterErrorHandler';

/**
 * 筛选结果
 */
export interface FilterResult {
  /** 筛选后剩余的商品数量 */
  remainingCount: number;
  /** 被删除的商品数量 */
  filteredCount: number;
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
}

/**
 * 商品数据
 */
export interface ProductData {
  element: Element;
  price?: number;
  sales?: number;
  isLocal: boolean;
}

/**
 * DOM选择器常量
 * 与productDeduplicator.ts保持完全一致，确保筛选和去重使用相同的DOM获取逻辑
 */
const DOM_SELECTORS = {
  // 商品容器 - 与去重功能使用相同选择器
  PRODUCTS_CONTAINER: '.contentContainer .js-search-goodsList .autoFitList',
  // 单个商品 - 与去重功能使用相同选择器
  PRODUCT_ITEM: '.EKDT7a3v',
  // 价格元素
  PRICE_ELEMENT: '[data-type="price"][aria-label]',
  // 销量元素  
  SALES_ELEMENT: '[aria-label*="sold"]',
  // 本地商品标识
  LOCAL_INDICATOR: '[role="button"] span'
} as const;

/**
 * 筛选错误类型
 */
export enum FilterErrorType {
  DOM_NOT_FOUND = 'DOM_NOT_FOUND',
  INVALID_PRICE_FORMAT = 'INVALID_PRICE_FORMAT',
  INVALID_SALES_FORMAT = 'INVALID_SALES_FORMAT',
  FILTER_EXECUTION_FAILED = 'FILTER_EXECUTION_FAILED',
  DOM_PARSE_FAILED = 'DOM_PARSE_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

/**
 * 筛选错误类
 */
export class FilterError extends Error {
  constructor(
    public type: FilterErrorType,
    message: string,
    public details?: any,
    public userMessage?: string
  ) {
    super(message);
    this.name = 'FilterError';
  }

  /**
   * 获取用户友好的错误消息
   */
  getUserMessage(): string {
    if (this.userMessage) {
      return this.userMessage;
    }

    switch (this.type) {
      case FilterErrorType.DOM_NOT_FOUND:
        return '未找到商品容器，请确保在Temu搜索页面执行筛选';
      case FilterErrorType.DOM_PARSE_FAILED:
        return '商品信息解析失败，页面可能还未完全加载';
      case FilterErrorType.INVALID_PRICE_FORMAT:
        return '价格格式无效，请检查输入的价格范围';
      case FilterErrorType.INVALID_SALES_FORMAT:
        return '销量格式无效，请检查输入的销量范围';
      case FilterErrorType.VALIDATION_ERROR:
        return '筛选条件验证失败，请检查输入的筛选条件';
      case FilterErrorType.NETWORK_ERROR:
        return '网络连接异常，请检查网络连接后重试';
      case FilterErrorType.PERMISSION_DENIED:
        return '权限不足，无法执行筛选操作';
      case FilterErrorType.TIMEOUT_ERROR:
        return '筛选操作超时，请重试';
      case FilterErrorType.FILTER_EXECUTION_FAILED:
      default:
        return '筛选执行失败，请重试';
    }
  }

  /**
   * 获取错误的严重程度
   */
  getSeverity(): 'error' | 'warning' | 'info' {
    switch (this.type) {
      case FilterErrorType.DOM_NOT_FOUND:
      case FilterErrorType.FILTER_EXECUTION_FAILED:
      case FilterErrorType.NETWORK_ERROR:
      case FilterErrorType.PERMISSION_DENIED:
        return 'error';
      case FilterErrorType.DOM_PARSE_FAILED:
      case FilterErrorType.TIMEOUT_ERROR:
      case FilterErrorType.VALIDATION_ERROR:
        return 'warning';
      case FilterErrorType.INVALID_PRICE_FORMAT:
      case FilterErrorType.INVALID_SALES_FORMAT:
        return 'info';
      default:
        return 'error';
    }
  }
}

/**
 * 解析商品价格
 * 支持TL和其他货币格式的价格解析
 * @param element 商品DOM元素
 * @returns 解析后的价格数字，解析失败返回null
 */
export const parseProductPrice = (element: Element, index?: number): number | null => {
  try {
    if (!element) {
      handleDOMParseFailure(null, 'price', index);
      return null;
    }
    
    const priceElement = element.querySelector(DOM_SELECTORS.PRICE_ELEMENT);
    if (!priceElement) {
      // 尝试备用选择器
      const fallbackPriceElement = element.querySelector('[aria-label*="price"], [aria-label*="Price"], [data-testid*="price"]');
      if (!fallbackPriceElement) {
        handleDOMParseFailure(element, 'price', index);
        return null;
      }
      console.log('使用备用价格选择器');
    }
    
    const targetElement = priceElement || element.querySelector('[aria-label*="price"], [aria-label*="Price"], [data-testid*="price"]');
    const ariaLabel = targetElement?.getAttribute('aria-label');
    
    if (!ariaLabel) {
      handleDOMParseFailure(targetElement, 'price', index);
      return null;
    }
    
    console.log('解析价格aria-label:', ariaLabel);
    
    // 提取价格数字部分 - 更严格的正则表达式
    const priceMatch = ariaLabel.match(/[\d,.]+/);
    if (!priceMatch) {
      const error = new Error(`无法从aria-label中提取价格数字: ${ariaLabel}`);
      globalErrorHandler.handleError(error, { 
        type: 'price_parse', 
        ariaLabel, 
        element: element.outerHTML?.substring(0, 200),
        index 
      });
      return null;
    }
    
    let priceStr = priceMatch[0];
    let price: number;
    
    // 根据货币类型处理价格格式
    if (ariaLabel.toUpperCase().includes('TL')) {
      // TL货币处理：小数点替换为空字符串，逗号替换为小数点
      // 例如: "12.345,67 TL" -> "12345.67"
      priceStr = priceStr.replace(/\./g, '').replace(/,/g, '.');
      price = parseFloat(priceStr);
      console.log('TL价格解析结果:', priceMatch[0], '->', priceStr, '->', price);
    } else {
      // 其他货币：逗号作为千位分隔符移除，小数点保持不变
      // 例如: "$12,345.67" -> "12345.67"
      priceStr = priceStr.replace(/,/g, '');
      price = parseFloat(priceStr);
      console.log('其他货币价格解析结果:', priceMatch[0], '->', priceStr, '->', price);
    }
    
    // 验证解析结果
    if (isNaN(price) || price < 0) {
      const error = new Error(`价格解析结果无效: ${price}`);
      globalErrorHandler.handleError(error, { 
        type: 'price_validation', 
        originalValue: priceMatch[0],
        processedValue: priceStr,
        finalValue: price,
        index 
      });
      return null;
    }
    
    // 合理性检查 - 价格不应该超过极大值
    if (price > 1000000) {
      const error = new Error(`价格异常过大，可能解析错误: ${price}`);
      globalErrorHandler.handleError(error, { 
        type: 'price_range_check', 
        price,
        ariaLabel,
        index 
      });
      return null;
    }
    
    return price;
    
  } catch (error) {
    globalErrorHandler.handleError(error, { 
      type: 'price_parse_exception', 
      element: element.outerHTML?.substring(0, 200),
      index 
    });
    return null;
  }
};

/**
 * 解析商品销量
 * 支持K+格式和普通数字的销量解析
 * @param element 商品DOM元素
 * @returns 解析后的销量数字，解析失败返回null
 */
export const parseProductSales = (element: Element, index?: number): number | null => {
  try {
    if (!element) {
      handleDOMParseFailure(null, 'sales', index);
      return null;
    }
    
    const salesElement = element.querySelector(DOM_SELECTORS.SALES_ELEMENT);
    if (!salesElement) {
      // 尝试备用选择器
      const fallbackSalesElement = element.querySelector('[aria-label*="sold"], [aria-label*="Sold"], [data-testid*="sales"]');
      if (!fallbackSalesElement) {
        handleDOMParseFailure(element, 'sales', index);
        return null;
      }
      console.log('使用备用销量选择器');
    }
    
    const targetElement = salesElement || element.querySelector('[aria-label*="sold"], [aria-label*="Sold"], [data-testid*="sales"]');
    const ariaLabel = targetElement?.getAttribute('aria-label');
    
    if (!ariaLabel) {
      handleDOMParseFailure(targetElement, 'sales', index);
      return null;
    }
    
    console.log('解析销量aria-label:', ariaLabel);
    
    // 移除"sold"后缀（不区分大小写）
    let salesStr = ariaLabel.replace(/sold$/i, '').trim();
    
    // 土耳其数字格式处理：当有效数字位数≤3位且包含逗号时，将逗号替换为小数点
    const handleTurkishNumberFormat = (input: string): string => {
      // 检查是否包含逗号
      if (!input.includes(',')) {
        return input;
      }
      
      // 计算有效数字位数（只计算数字，不包括逗号和小数点）
      const effectiveDigits = input.replace(/[,.]/g, '').replace(/[^\d]/g, '');
      
      // 如果有效数字位数≤3位，处理逗号替换
      if (effectiveDigits.length <= 3) {
        // 将逗号替换为小数点
        return input.replace(',', '.');
      }
      
      // 有效数字位数>3位，保持原样（可能是千位分隔符）
      return input;
    };
    
    // 应用土耳其数字格式处理
    salesStr = handleTurkishNumberFormat(salesStr);
    
    // 处理K+格式（例如: "44K+", "9.5K+", "1.2K+"）
    const kPlusMatch = salesStr.match(/^([\d.]+)K\+?$/i);
    if (kPlusMatch) {
      const numStr = kPlusMatch[1];
      const num = parseFloat(numStr);
      
      if (isNaN(num)) {
        console.warn('K+格式中的数字无效:', numStr);
        return null;
      }
      
      // 根据需求，K+格式且数字≤9.9才符合筛选条件
      if (num <= 9.9) {
        const sales = Math.floor(num * 1000);
        console.log('K+格式销量解析结果:', salesStr, '->', sales);
        return sales;
      } else {
        console.warn('K+格式销量超出范围 (>9.9K):', salesStr);
        return null; // 不符合筛选条件，按需求返回null
      }
    }
    
    // 处理纯K格式（例如: "44K", "9K"）
    const kMatch = salesStr.match(/^([\d.]+)K$/i);
    if (kMatch) {
      const numStr = kMatch[1];
      const num = parseFloat(numStr);
      
      if (isNaN(num)) {
        console.warn('K格式中的数字无效:', numStr);
        return null;
      }
      
      const sales = Math.floor(num * 1000);
      console.log('K格式销量解析结果:', salesStr, '->', sales);
      return sales;
    }
    
    // 处理普通数字格式（移除所有非数字字符）
    const numericStr = salesStr.replace(/[^\d]/g, '');
    if (numericStr) {
      const num = parseInt(numericStr);
      if (!isNaN(num) && num >= 0) {
        console.log('普通数字销量解析结果:', salesStr, '->', num);
        return num;
      }
    }
    
    // 处理带逗号的数字格式（例如: "1,234"）
    const commaNumMatch = salesStr.match(/^[\d,]+$/);
    if (commaNumMatch) {
      const num = parseInt(salesStr.replace(/,/g, ''));
      if (!isNaN(num) && num >= 0) {
        console.log('逗号分隔数字销量解析结果:', salesStr, '->', num);
        return num;
      }
    }
    
    const error = new Error(`无法解析销量格式: ${salesStr} (原始: ${ariaLabel})`);
    globalErrorHandler.handleError(error, { 
      type: 'sales_parse_format', 
      salesStr,
      ariaLabel,
      element: element.outerHTML?.substring(0, 200),
      index 
    });
    return null;
    
  } catch (error) {
    globalErrorHandler.handleError(error, { 
      type: 'sales_parse_exception', 
      element: element.outerHTML?.substring(0, 200),
      index 
    });
    return null;
  }
};

/**
 * 检测是否为本地商品
 * 查找包含"Local"文本的span元素
 * @param element 商品DOM元素
 * @returns 是否为本地商品
 */
export const isLocalProduct = (element: Element, index?: number): boolean => {
  try {
    if (!element) {
      handleDOMParseFailure(null, 'local', index);
      return false;
    }
    
    // 主要检测方法：查找role="button"且子元素为包含"Local"文本的span的DOM元素
    const buttonElements = element.querySelectorAll('[role="button"]');
    
    for (let i = 0; i < buttonElements.length; i++) {
      const button = buttonElements[i];
      const spans = button.querySelectorAll('span');
      for (let i = 0; i < spans.length; i++) {
        const span = spans[i];
        const textContent = span.textContent?.trim();
        if (textContent && textContent.toLowerCase().includes('local')) {
          console.log('发现本地商品标识 (button>span):', textContent);
          return true;
        }
      }
    }
    
    // 备用检测方法1：直接查找包含"Local"文本的span元素
    const allSpans = element.querySelectorAll('span');
    for (let i = 0; i < allSpans.length; i++) {
      const span = allSpans[i];
      const textContent = span.textContent?.trim();
      if (textContent && textContent.toLowerCase().includes('local')) {
        console.log('发现本地商品标识 (span):', textContent);
        return true;
      }
    }
    
    // 备用检测方法2：查找包含"Local"文本的任何元素
    const localElements = element.querySelectorAll('*');
    for (let i = 0; i < localElements.length; i++) {
      const el = localElements[i];
      const textContent = el.textContent?.trim();
      if (textContent && textContent.toLowerCase() === 'local') {
        console.log('发现本地商品标识 (任意元素):', textContent);
        return true;
      }
    }
    
    // 备用检测方法3：检查特定的CSS类名或属性
    const localIndicators = [
      '[data-local="true"]',
      '[class*="local"]',
      '[class*="Local"]',
      '[title*="local"]',
      '[title*="Local"]'
    ];
    
    for (const selector of localIndicators) {
      const localElement = element.querySelector(selector);
      if (localElement) {
        console.log('发现本地商品标识 (属性/类名):', selector);
        return true;
      }
    }
    
    return false;
    
  } catch (error) {
    globalErrorHandler.handleError(error, { 
      type: 'local_detection_exception', 
      element: element.outerHTML?.substring(0, 200),
      index 
    });
    return false;
  }
};

/**
 * 筛选进度回调函数类型
 */
export type FilterProgressCallback = (progress: FilterProgress) => void;

/**
 * 筛选进度信息
 */
export interface FilterProgress {
  /** 当前阶段 */
  stage: 'initializing' | 'parsing' | 'filtering' | 'cleanup' | 'completed';
  /** 当前阶段描述 */
  stageDescription: string;
  /** 总商品数量 */
  totalCount: number;
  /** 已处理的商品数量 */
  processedCount: number;
  /** 进度百分比 (0-100) */
  percentage: number;
  /** 当前处理的商品索引 */
  currentIndex?: number;
  /** 已删除的商品数量 */
  deletedCount: number;
  /** 错误数量 */
  errorCount: number;
  /** 是否完成 */
  isCompleted: boolean;
  /** 预估剩余时间（毫秒） */
  estimatedTimeRemaining?: number;
}

/**
 * 筛选统计信息
 */
export interface FilterStatistics {
  /** 按价格筛选删除的商品数量 */
  priceFiltered: number;
  /** 按销量筛选删除的商品数量 */
  salesFiltered: number;
  /** 按本地商品筛选删除的商品数量 */
  localFiltered: number;
  /** 解析失败的商品数量 */
  parseErrors: number;
  /** DOM操作失败的数量 */
  domErrors: number;
  /** 处理时间（毫秒） */
  processingTime: number;
  /** 平均每个商品处理时间（毫秒） */
  averageProcessingTime: number;
  /** 成功处理的商品数量 */
  successCount: number;
}

/**
 * 增强的筛选结果
 */
export interface EnhancedFilterResult extends FilterResult {
  /** 详细统计信息 */
  statistics?: FilterStatistics;
}

/**
 * 获取商品容器和商品元素
 * 复用现有商品去重功能中的商品DOM获取逻辑
 * 使用与productDeduplicator.ts完全相同的选择器确保一致性
 * @returns 商品容器和商品元素列表
 */
export const getProductElements = (): {
  container: Element | null;
  products: NodeListOf<Element>;
  count: number;
} => {
  // 使用与去重功能完全相同的选择器
  const container = document.querySelector('.contentContainer .js-search-goodsList .autoFitList');
  const products = container ? container.querySelectorAll('.EKDT7a3v') : document.querySelectorAll('');
  
  return {
    container,
    products,
    count: products.length
  };
};

/**
 * 验证筛选设置的有效性
 * @param settings 筛选设置
 * @returns 验证结果
 */
export const validateFilterSettings = (settings: ProductFilterSettings): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  // 验证价格筛选设置
  if (settings.priceFilter.minPrice !== undefined) {
    if (isNaN(settings.priceFilter.minPrice) || settings.priceFilter.minPrice < 0) {
      errors.push('最小价格必须是非负数');
    }
  }
  
  if (settings.priceFilter.maxPrice !== undefined) {
    if (isNaN(settings.priceFilter.maxPrice) || settings.priceFilter.maxPrice < 0) {
      errors.push('最大价格必须是非负数');
    }
  }
  
  if (settings.priceFilter.minPrice !== undefined && 
      settings.priceFilter.maxPrice !== undefined && 
      settings.priceFilter.minPrice > settings.priceFilter.maxPrice) {
    errors.push('最小价格不能大于最大价格');
  }
  
  // 验证销量筛选设置
  if (settings.salesFilter.minSales !== undefined) {
    if (!Number.isInteger(settings.salesFilter.minSales) || settings.salesFilter.minSales < 0) {
      errors.push('最小销量必须是非负整数');
    }
  }
  
  if (settings.salesFilter.maxSales !== undefined) {
    if (!Number.isInteger(settings.salesFilter.maxSales) || settings.salesFilter.maxSales < 0) {
      errors.push('最大销量必须是非负整数');
    }
  }
  
  if (settings.salesFilter.minSales !== undefined && 
      settings.salesFilter.maxSales !== undefined && 
      settings.salesFilter.minSales > settings.salesFilter.maxSales) {
    errors.push('最小销量不能大于最大销量');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 用户反馈消息类型
 */
export interface UserFeedbackMessage {
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message: string;
  details?: string[];
  duration?: number;
  showDetails?: boolean;
}

/**
 * 创建筛选结果摘要
 * 用于用户反馈和日志记录
 * @param result 筛选结果
 * @returns 结果摘要字符串
 */
export const createFilterSummary = (result: EnhancedFilterResult): string => {
  if (!result.success) {
    return `筛选失败: ${result.error || '未知错误'}`;
  }
  
  const { remainingCount, filteredCount, statistics } = result;
  const total = remainingCount + filteredCount;
  
  let summary = `筛选完成: 共${total}个商品，删除${filteredCount}个，保留${remainingCount}个`;
  
  if (statistics) {
    const details: string[] = [];
    if (statistics.priceFiltered > 0) {
      details.push(`价格筛选删除${statistics.priceFiltered}个`);
    }
    if (statistics.salesFiltered > 0) {
      details.push(`销量筛选删除${statistics.salesFiltered}个`);
    }
    if (statistics.localFiltered > 0) {
      details.push(`本地商品删除${statistics.localFiltered}个`);
    }
    if (statistics.parseErrors > 0) {
      details.push(`解析失败${statistics.parseErrors}个`);
    }
    if (statistics.domErrors > 0) {
      details.push(`DOM操作失败${statistics.domErrors}个`);
    }
    
    if (details.length > 0) {
      summary += ` (${details.join('，')})`;
    }
    
    summary += `，耗时${statistics.processingTime}ms`;
  }
  
  return summary;
};

/**
 * 创建用户友好的反馈消息
 * @param result 筛选结果
 * @returns 用户反馈消息
 */
export const createUserFeedbackMessage = (result: EnhancedFilterResult): UserFeedbackMessage => {
  if (!result.success) {
    return {
      type: 'error',
      title: '筛选失败',
      message: result.error || '筛选过程中发生未知错误',
      details: ['请检查网络连接和页面状态', '如果问题持续存在，请刷新页面后重试'],
      duration: 5000,
      showDetails: true
    };
  }
  
  const { remainingCount, filteredCount, statistics } = result;
  const total = remainingCount + filteredCount;
  
  if (filteredCount === 0) {
    return {
      type: 'info',
      title: '筛选完成',
      message: `所有 ${total} 个商品都符合筛选条件`,
      duration: 3000
    };
  }
  
  // 构建详细信息
  const details: string[] = [];
  if (statistics) {
    if (statistics.priceFiltered > 0) {
      details.push(`• 价格筛选：删除 ${statistics.priceFiltered} 个商品`);
    }
    if (statistics.salesFiltered > 0) {
      details.push(`• 销量筛选：删除 ${statistics.salesFiltered} 个商品`);
    }
    if (statistics.localFiltered > 0) {
      details.push(`• 本地商品：删除 ${statistics.localFiltered} 个商品`);
    }
    if (statistics.parseErrors > 0) {
      details.push(`• 解析失败：${statistics.parseErrors} 个商品`);
    }
  }
  
  return {
    type: 'success',
    title: '筛选完成',
    message: `成功筛选 ${total} 个商品，删除 ${filteredCount} 个，保留 ${remainingCount} 个`,
    details,
    duration: 4000,
    showDetails: details.length > 0
  };
};

/**
 * 创建进度消息
 * @param progress 进度信息
 * @returns 进度消息字符串
 */
export const createProgressMessage = (progress: FilterProgress): string => {
  const { stage, processedCount, totalCount, deletedCount, errorCount } = progress;
  
  switch (stage) {
    case 'initializing':
      return '正在初始化筛选环境...';
    case 'parsing':
      return '正在解析商品信息...';
    case 'filtering':
      return `正在筛选商品 (${processedCount}/${totalCount})，已删除 ${deletedCount} 个`;
    case 'cleanup':
      return '正在清理和整理结果...';
    case 'completed':
      return `筛选完成：处理 ${totalCount} 个商品，删除 ${deletedCount} 个${errorCount > 0 ? `，错误 ${errorCount} 个` : ''}`;
    default:
      return '正在处理...';
  }
};

/**
 * 处理筛选错误
 * @param error 错误对象
 * @param context 错误上下文
 * @returns 错误处理结果
 */
export const handleFilterError = (error: any, context: string = '筛选操作'): {
  error: FilterError;
  userMessage: UserFeedbackMessage;
} => {
  let filterError: FilterError;
  
  if (error instanceof FilterError) {
    filterError = error;
  } else if (error instanceof Error) {
    filterError = new FilterError(
      FilterErrorType.FILTER_EXECUTION_FAILED,
      error.message,
      { originalError: error, context },
      `${context}失败：${error.message}`
    );
  } else {
    filterError = new FilterError(
      FilterErrorType.FILTER_EXECUTION_FAILED,
      '未知错误',
      { originalError: error, context },
      `${context}失败：发生未知错误`
    );
  }
  
  // 使用全局错误处理器处理错误
  const errorDetails = globalErrorHandler.handleError(filterError, { context });
  
  // 创建用户友好的反馈消息
  const userMessage: UserFeedbackMessage = {
    type: 'error',
    title: `${context}失败`,
    message: filterError.getUserMessage(),
    details: errorDetails.suggestions,
    duration: 5000,
    showDetails: true
  };
  
  return { error: filterError, userMessage };
};

/**
 * 判断是否应该执行筛选
 * @param filterSettings 筛选设置
 * @returns 是否应该执行筛选
 */
export const shouldExecuteFilter = (filterSettings: ProductFilterSettings): boolean => {
  const hasPriceFilter = filterSettings.priceFilter.minPrice !== undefined || filterSettings.priceFilter.maxPrice !== undefined;
  const hasSalesFilter = filterSettings.salesFilter.minSales !== undefined || filterSettings.salesFilter.maxSales !== undefined;
  const hasLocalFilter = filterSettings.removeLocalProducts;
  
  return hasPriceFilter || hasSalesFilter || hasLocalFilter;
};

/**
 * 验证筛选与去重的集成环境
 * @returns 验证结果
 */
export const validateFilterDeduplicationIntegration = (): {
  isValid: boolean;
  message: string;
  details?: string[];
} => {
  try {
    // 检查DOM环境
    const { container, products } = getProductElements();
    
    if (!container) {
      return {
        isValid: false,
        message: '未找到商品容器，请确保在Temu搜索页面执行',
        details: ['请刷新页面后重试', '确保页面完全加载']
      };
    }
    
    if (products.length === 0) {
      return {
        isValid: false,
        message: '未找到商品，页面可能还未加载完成',
        details: ['等待页面加载完成后重试', '尝试滚动页面加载更多商品']
      };
    }
    
    return {
      isValid: true,
      message: `集成环境验证通过，找到 ${products.length} 个商品`
    };
    
  } catch (error) {
    return {
      isValid: false,
      message: '集成环境验证失败',
      details: ['页面DOM结构可能发生变化', '请联系技术支持']
    };
  }
};

/**
 * 获取剩余商品数量
 * @returns 剩余商品数量
 */
export const getRemainingProductCount = (): number => {
  try {
    const { products } = getProductElements();
    return products.length;
  } catch (error) {
    console.error('获取剩余商品数量失败:', error);
    return 0;
  }
};

/**
 * 创建集成处理报告
 * @param filterResult 筛选结果
 * @param deduplicationResult 去重结果
 * @returns 集成处理报告
 */
export const createIntegratedProcessingReport = (
  filterResult: EnhancedFilterResult | null,
  deduplicationResult: any
): {
  summary: string;
  details: {
    filter: {
      executed: boolean;
      filteredCount: number;
      errorCount: number;
    };
    deduplication: {
      executed: boolean;
      deletedCount: number;
      errorCount: number;
    };
    total: {
      processedCount: number;
      deletedCount: number;
      errorCount: number;
    };
  };
} => {
  const filterExecuted = filterResult !== null;
  const filterFilteredCount = filterResult?.filteredCount || 0;
  const filterErrorCount = filterResult?.statistics?.parseErrors || 0;
  
  const deduplicationExecuted = deduplicationResult !== null;
  const deduplicationDeletedCount = deduplicationResult?.deletedCount || 0;
  const deduplicationErrorCount = deduplicationResult?.success === false ? 1 : 0;
  
  const totalDeletedCount = filterFilteredCount + deduplicationDeletedCount;
  const totalErrorCount = filterErrorCount + deduplicationErrorCount;
  const totalProcessedCount = (filterResult?.remainingCount || 0) + filterFilteredCount;
  
  let summary = '处理完成：';
  const summaryParts: string[] = [];
  
  if (filterExecuted && filterFilteredCount > 0) {
    summaryParts.push(`筛选删除${filterFilteredCount}个`);
  }
  
  if (deduplicationExecuted && deduplicationDeletedCount > 0) {
    summaryParts.push(`去重删除${deduplicationDeletedCount}个`);
  }
  
  if (summaryParts.length > 0) {
    summary += summaryParts.join('，');
  } else {
    summary += '没有发现需要删除的商品';
  }
  
  if (totalErrorCount > 0) {
    summary += `，发生${totalErrorCount}个错误`;
  }
  
  return {
    summary,
    details: {
      filter: {
        executed: filterExecuted,
        filteredCount: filterFilteredCount,
        errorCount: filterErrorCount
      },
      deduplication: {
        executed: deduplicationExecuted,
        deletedCount: deduplicationDeletedCount,
        errorCount: deduplicationErrorCount
      },
      total: {
        processedCount: totalProcessedCount,
        deletedCount: totalDeletedCount,
        errorCount: totalErrorCount
      }
    }
  };
};

/**
 * 全局筛选执行器
 * 提供统一的筛选执行接口，集成错误处理和进度反馈
 */
export class GlobalFilterExecutor {
  private progressCallback?: FilterProgressCallback;
  private isExecuting = false;
  
  /**
   * 设置进度回调函数
   */
  setProgressCallback(callback: FilterProgressCallback): void {
    this.progressCallback = callback;
  }
  
  /**
   * 执行筛选
   */
  async execute(filterSettings: ProductFilterSettings): Promise<EnhancedFilterResult> {
    if (this.isExecuting) {
      throw new FilterError(
        FilterErrorType.FILTER_EXECUTION_FAILED,
        '筛选正在执行中，请等待完成',
        {},
        '筛选正在执行中，请稍候'
      );
    }
    
    this.isExecuting = true;
    
    try {
      const result = await filterProducts(filterSettings, this.progressCallback);
      return result;
    } finally {
      this.isExecuting = false;
    }
  }
  
  /**
   * 检查是否正在执行
   */
  isRunning(): boolean {
    return this.isExecuting;
  }
  
  /**
   * 取消执行（如果支持）
   */
  cancel(): void {
    // 目前不支持取消，但可以在未来扩展
    console.warn('筛选取消功能暂未实现');
  }
}

/**
 * 全局筛选执行器实例
 */
export const globalFilterExecutor = new GlobalFilterExecutor();
/**
 * 
执行商品筛选（带进度回调和错误处理）
 * 根据筛选设置删除不符合条件的商品DOM元素
 * @param filterSettings 筛选设置
 * @param progressCallback 进度回调函数
 * @returns 筛选结果
 */
export const filterProducts = async (
  filterSettings: ProductFilterSettings,
  progressCallback?: FilterProgressCallback
): Promise<EnhancedFilterResult> => {
  const startTime = Date.now();
  
  // 初始化进度信息
  const updateProgress = (stage: FilterProgress['stage'], processedCount: number, totalCount: number, deletedCount: number = 0, errorCount: number = 0) => {
    if (!progressCallback) return;
    
    const percentage = totalCount > 0 ? Math.round((processedCount / totalCount) * 100) : 0;
    const elapsed = Date.now() - startTime;
    const estimatedTimeRemaining = processedCount > 0 && processedCount < totalCount 
      ? Math.round((elapsed / processedCount) * (totalCount - processedCount))
      : undefined;
    
    const stageDescriptions = {
      initializing: '正在初始化筛选环境...',
      parsing: '正在解析商品信息...',
      filtering: `正在筛选商品 (${processedCount}/${totalCount})...`,
      cleanup: '正在清理和整理结果...',
      completed: '筛选完成'
    };
    
    const progressData: ProgressFeedback = {
      stage: stageDescriptions[stage],
      progress: percentage,
      message: stageDescriptions[stage],
      details: `已处理 ${processedCount}/${totalCount} 个商品，删除 ${deletedCount} 个，错误 ${errorCount} 个`,
      estimatedTimeRemaining
    };
    
    // 更新全局进度管理器
    globalProgressManager.updateProgress(progressData);
    
    // 调用用户提供的回调
    progressCallback({
      stage,
      stageDescription: stageDescriptions[stage],
      totalCount,
      processedCount,
      percentage,
      currentIndex: processedCount,
      deletedCount,
      errorCount,
      isCompleted: stage === 'completed',
      estimatedTimeRemaining
    });
  };

  // 使用安全执行包装器
  const { success: executionSuccess, result, error: executionError } = await safeExecute(async () => {
    console.log('开始执行商品筛选，设置:', filterSettings);
    
    // 阶段1：初始化
    updateProgress('initializing', 0, 0);
    
    // 验证筛选设置
    if (!filterSettings) {
      throw new FilterError(
        FilterErrorType.VALIDATION_ERROR, 
        '筛选设置不能为空',
        { filterSettings },
        '筛选设置无效，请检查筛选条件'
      );
    }
    
    // 验证筛选设置的有效性
    const validation = validateFilterSettings(filterSettings);
    if (!validation.isValid) {
      throw new FilterError(
        FilterErrorType.VALIDATION_ERROR,
        `筛选设置验证失败: ${validation.errors.join(', ')}`,
        { errors: validation.errors },
        `筛选条件无效: ${validation.errors.join('，')}`
      );
    }
    
    // 复用现有的商品DOM获取逻辑，确保与去重功能一致
    let container: Element | null = null;
    let productElements: NodeListOf<Element> | null = null;
    
    try {
      const result = getProductElements();
      container = result.container;
      productElements = result.products;
    } catch (domError) {
      throw new FilterError(
        FilterErrorType.DOM_PARSE_FAILED,
        'DOM解析失败',
        { error: domError },
        '页面解析失败，请刷新页面后重试'
      );
    }
    
    if (!container) {
      throw new FilterError(
        FilterErrorType.DOM_NOT_FOUND, 
        '未找到商品容器',
        { selector: DOM_SELECTORS.PRODUCTS_CONTAINER },
        '未找到商品容器，请确保在Temu搜索页面执行筛选'
      );
    }
    
    if (!productElements || productElements.length === 0) {
      console.warn('未找到商品元素，可能页面还未加载完成');
      const processingTime = Date.now() - startTime;
      
      updateProgress('completed', 0, 0);
      
      return {
        remainingCount: 0,
        filteredCount: 0,
        success: true,
        statistics: {
          priceFiltered: 0,
          salesFiltered: 0,
          localFiltered: 0,
          parseErrors: 0,
          domErrors: 0,
          processingTime,
          averageProcessingTime: 0,
          successCount: 0
        }
      };
    }
    
    const initialCount = productElements.length;
    console.log(`找到 ${initialCount} 个商品，开始筛选...`);
    
    // 统计信息
    const statistics: FilterStatistics = {
      priceFiltered: 0,
      salesFiltered: 0,
      localFiltered: 0,
      parseErrors: 0,
      domErrors: 0,
      processingTime: 0,
      averageProcessingTime: 0,
      successCount: 0
    };
    
    let filteredCount = 0;
    let errorCount = 0;
    
    // 检查是否有任何筛选条件启用
    const hasPriceFilter = filterSettings.priceFilter.minPrice !== undefined || filterSettings.priceFilter.maxPrice !== undefined;
    const hasSalesFilter = filterSettings.salesFilter.minSales !== undefined || filterSettings.salesFilter.maxSales !== undefined;
    const hasLocalFilter = filterSettings.removeLocalProducts;
    
    if (!hasPriceFilter && !hasSalesFilter && !hasLocalFilter) {
      console.log('未启用任何筛选条件，跳过筛选');
      const processingTime = Date.now() - startTime;
      
      updateProgress('completed', initialCount, initialCount);
      
      return {
        remainingCount: initialCount,
        filteredCount: 0,
        success: true,
        statistics: {
          ...statistics,
          processingTime,
          successCount: initialCount
        }
      };
    }
    
    // 阶段2：解析和筛选
    updateProgress('parsing', 0, initialCount);
    
    // 遍历每个商品进行筛选
    const elementsArray = Array.from(productElements);
    const batchSize = 10; // 批量处理，避免阻塞UI
    
    for (let batchStart = 0; batchStart < elementsArray.length; batchStart += batchSize) {
      const batchEnd = Math.min(batchStart + batchSize, elementsArray.length);
      const batch = elementsArray.slice(batchStart, batchEnd);
      
      // 处理当前批次
      for (let i = 0; i < batch.length; i++) {
        const globalIndex = batchStart + i;
        const element = batch[i];
        let shouldRemove = false;
        const reasons: string[] = [];
        let filterType: 'price' | 'sales' | 'local' | 'parse_error' | null = null;
        
        try {
          // 价格筛选
          if (hasPriceFilter) {
            try {
              const price = parseProductPrice(element);
              if (price !== null) {
                if (filterSettings.priceFilter.minPrice !== undefined && price < filterSettings.priceFilter.minPrice) {
                  shouldRemove = true;
                  filterType = 'price';
                  reasons.push(`价格${price}低于最小值${filterSettings.priceFilter.minPrice}`);
                }
                if (filterSettings.priceFilter.maxPrice !== undefined && price > filterSettings.priceFilter.maxPrice) {
                  shouldRemove = true;
                  filterType = 'price';
                  reasons.push(`价格${price}高于最大值${filterSettings.priceFilter.maxPrice}`);
                }
              } else {
                console.warn(`商品${globalIndex}: 无法解析价格，跳过价格筛选`);
                statistics.parseErrors++;
                errorCount++;
              }
            } catch (priceError) {
              console.error(`商品${globalIndex}: 价格解析异常:`, priceError);
              statistics.parseErrors++;
              errorCount++;
            }
          }
          
          // 销量筛选
          if (!shouldRemove && hasSalesFilter) {
            try {
              const sales = parseProductSales(element);
              if (sales !== null) {
                if (filterSettings.salesFilter.minSales !== undefined && sales < filterSettings.salesFilter.minSales) {
                  shouldRemove = true;
                  filterType = 'sales';
                  reasons.push(`销量${sales}低于最小值${filterSettings.salesFilter.minSales}`);
                }
                if (filterSettings.salesFilter.maxSales !== undefined && sales > filterSettings.salesFilter.maxSales) {
                  shouldRemove = true;
                  filterType = 'sales';
                  reasons.push(`销量${sales}高于最大值${filterSettings.salesFilter.maxSales}`);
                }
              } else {
                // 无法解析销量且设置了销量筛选条件，则删除
                shouldRemove = true;
                filterType = 'parse_error';
                reasons.push('无法解析销量格式');
                statistics.parseErrors++;
                errorCount++;
              }
            } catch (salesError) {
              console.error(`商品${globalIndex}: 销量解析异常:`, salesError);
              shouldRemove = true;
              filterType = 'parse_error';
              reasons.push('销量解析异常');
              statistics.parseErrors++;
              errorCount++;
            }
          }
          
          // 本地商品筛选
          if (!shouldRemove && hasLocalFilter) {
            try {
              if (isLocalProduct(element)) {
                shouldRemove = true;
                filterType = 'local';
                reasons.push('本地商品');
              }
            } catch (localError) {
              console.error(`商品${globalIndex}: 本地商品检测异常:`, localError);
              errorCount++;
            }
          }
          
          // 删除不符合条件的商品并更新统计
          if (shouldRemove) {
            console.log(`删除商品${globalIndex}: ${reasons.join(', ')}`);
            
            // 更新统计信息
            switch (filterType) {
              case 'price':
                statistics.priceFiltered++;
                break;
              case 'sales':
                statistics.salesFiltered++;
                break;
              case 'local':
                statistics.localFiltered++;
                break;
            }
            
            // 安全删除DOM元素
            try {
              element.remove();
              filteredCount++;
            } catch (removeError) {
              console.error(`删除商品${globalIndex}的DOM元素时发生错误:`, removeError);
              statistics.domErrors++;
              errorCount++;
            }
          } else {
            statistics.successCount++;
          }
          
        } catch (error) {
          console.error(`处理商品${globalIndex}时发生未知错误:`, error);
          statistics.parseErrors++;
          errorCount++;
          // 发生错误时不删除商品，继续处理下一个
        }
        
        // 更新进度
        updateProgress('filtering', globalIndex + 1, initialCount, filteredCount, errorCount);
      }
      
      // 批次间短暂延迟，避免阻塞UI
      if (batchEnd < elementsArray.length) {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }
    
    // 阶段3：清理
    updateProgress('cleanup', initialCount, initialCount, filteredCount, errorCount);
    
    const remainingCount = initialCount - filteredCount;
    const processingTime = Date.now() - startTime;
    statistics.processingTime = processingTime;
    statistics.averageProcessingTime = initialCount > 0 ? processingTime / initialCount : 0;
    
    console.log(`筛选完成: 初始${initialCount}个商品，删除${filteredCount}个，剩余${remainingCount}个`);
    console.log('筛选统计:', statistics);
    
    // 阶段4：完成
    updateProgress('completed', initialCount, initialCount, filteredCount, errorCount);
    globalProgressManager.completeProgress('筛选完成');
    
    // 创建筛选结果反馈
    const filterResultFeedback: FilterResultFeedback = {
      success: true,
      totalProcessed: initialCount,
      filteredCount,
      errorCount,
      warnings: [],
      details: {
        priceFiltered: statistics.priceFiltered,
        salesFiltered: statistics.salesFiltered,
        localFiltered: statistics.localFiltered,
        parseErrors: statistics.parseErrors
      },
      processingTime
    };
    
    // 显示筛选结果
    globalResultManager.showFilterResult(filterResultFeedback);
    
    return {
      remainingCount,
      filteredCount,
      success: true,
      statistics
    };
    
  }, '商品筛选执行');
  
  // 处理执行结果
  if (!executionSuccess || !result) {
    const processingTime = Date.now() - startTime;
    console.error('商品筛选执行失败:', executionError);
    
    // 确保进度回调知道操作已完成（即使失败）
    if (progressCallback) {
      updateProgress('completed', 0, 0, 0, 1);
    }
    
    // 创建失败的筛选结果反馈
    const failureResultFeedback: FilterResultFeedback = {
      success: false,
      totalProcessed: 0,
      filteredCount: 0,
      errorCount: 1,
      warnings: [executionError?.userMessage || '筛选过程中发生未知错误'],
      details: {
        priceFiltered: 0,
        salesFiltered: 0,
        localFiltered: 0,
        parseErrors: 1
      },
      processingTime
    };
    
    // 显示失败结果
    globalResultManager.showFilterResult(failureResultFeedback);
    
    // 如果是FilterError，使用其用户友好的消息
    const errorMessage = executionError?.userMessage || '筛选执行失败，请重试';
    
    return {
      remainingCount: 0,
      filteredCount: 0,
      success: false,
      error: errorMessage,
      statistics: {
        priceFiltered: 0,
        salesFiltered: 0,
        localFiltered: 0,
        parseErrors: 1,
        domErrors: 0,
        processingTime,
        averageProcessingTime: 0,
        successCount: 0
      }
    };
  }
  
  return result;
};