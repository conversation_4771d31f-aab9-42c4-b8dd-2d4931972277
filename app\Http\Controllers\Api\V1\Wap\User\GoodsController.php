<?php
namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\GoodsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class GoodsController extends Controller
{
    protected GoodsService $goodsService;

    public function __construct(GoodsService $goodsService)
    {
        $this->goodsService = $goodsService;
        parent::__construct();
    }

    /**
     * 获取商品列表（分页）
     */
    public function list(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];

        $params = $request->only(['page', 'pageSize', 'type', 'goods_name', 'goods_id', 'status', 'directory_id', 'price_adjusted', 'cat_adjusted', 'sort_field', 'sort_order', 'time_type', 'start_date', 'end_date', 'sub_account_name', 'sub_account_phone', 'only_sub_account']);
        $result = $this->goodsService->getGoodsList($userId, $params);

        return $this->apiSuccess($result);
    }

    /**
     * 创建商品
     */
    public function create(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'type', 'source_url', 'cat_id', 'front_cat_id_1', 'front_cat_id_2', 
            'front_cat_desc', 'mall_id', 'goods_id', 'goods_name', 'goods_detail',
            'goods_video', 'goods_pic', 'goods_sku', 'goods_sku_num', 'goods_property',
            'status', 'skus'
        ]);
        
        $result = $this->goodsService->createGoods($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 更新商品
     */
    public function update(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'id', 'type', 'source_url', 'cat_id', 'front_cat_id_1', 'front_cat_id_2',
            'front_cat_desc', 'mall_id', 'goods_id', 'goods_name', 'goods_detail',
            'goods_video', 'goods_pic', 'goods_sku', 'goods_sku_num', 'goods_property',
            'status', 'skus'
        ]);
        
        $result = $this->goodsService->updateGoods($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 批量更新商品
     */
    public function batchUpdate(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only(['ids', 'status', 'directory_id']);
        $result = $this->goodsService->batchUpdateGoods($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 删除商品
     */
    public function delete(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $goodsId = (int)$request->input('id');
        $result = $this->goodsService->deleteGoods($userId, $goodsId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取商品详情
     */
    public function detail(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $goodsId = (int)$request->input('id');
        $result = $this->goodsService->getGoodsDetail($userId, $goodsId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取需要处理图片的商品ID列表
     */
    public function getNeedImageProcessGoods(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $directoryId = (int)$request->input('directory_id');
        $result = $this->goodsService->getGoodsNeedImageProcess($userId, $directoryId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取商品统计信息
     */
    public function getGoodsStatistics(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];

        $params = $request->only(['directory_id', 'time_range', 'day_start', 'day_end', 'goods_ids','publish_currency','exchange_rate_currency', 'price_min', 'price_max']);
        $result = $this->goodsService->getGoodsStatistics($userId, $params);

        return $this->apiSuccess($result);
    }

    /**
     * 调整SKU价格
     */
    public function adjustSkuPrice(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];

        $data = $request->only(['goods_id', 'sku_adjustments']);
        $result = $this->goodsService->adjustSkuPrice($userId, $data);

        return $this->apiSuccess($result);
    }

    /**
     * 获取价格调整日志
     */
    public function getPriceAdjustmentLogs(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];

        $params = $request->only(['goods_id', 'sku_id', 'page', 'pageSize']);
        $result = $this->goodsService->getPriceAdjustmentLogs($userId, $params);

        return $this->apiSuccess($result);
    }
}