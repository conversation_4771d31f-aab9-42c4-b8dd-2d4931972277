/**
 * Excel导出工具
 * 负责将商品链接导出为Excel文件
 */

import * as XLSX from 'xlsx';
import { processExportLinks, type SearchPageData, type GoodsLinkMap } from './temuSearchUtils';

/**
 * 计算文本宽度（考虑中英文字符差异和URL字符特点）
 * @param text 要计算宽度的文本
 * @returns 文本宽度
 */
export const calculateTextWidth = (text: string): number => {
  let width = 0;
  for (let i = 0; i < text.length; i++) {
    const char = text.charAt(i);
    // 中文字符、全角字符等宽度为2
    if (char.match(/[\u4e00-\u9fa5\uff00-\uffff]/)) {
      width += 2;
    } else {
      // 英文字符、数字、符号等宽度为1，但URL中的字符通常较窄
      width += 0.8;
    }
  }
  return width;
};

/**
 * 导出商品链接到Excel文件
 * @param selectedLinks 选中的商品链接
 * @param fileName 文件名
 * @param searchPageData 搜索页面数据（可选）
 * @param goodsLinkMap 商品链接映射（可选）
 */
export const exportLinksToExcel = async (
  selectedLinks: string[],
  fileName: string,
  searchPageData?: SearchPageData | null,
  goodsLinkMap?: GoodsLinkMap
): Promise<void> => {
  // 处理导出链接（如果有搜索数据）
  let processedLinks = selectedLinks;
  if (searchPageData && goodsLinkMap && Object.keys(goodsLinkMap).length > 0) {
    processedLinks = processExportLinks(
      selectedLinks,
      goodsLinkMap,
      searchPageData.sessionId,
      window.location.href
    );
    console.log('处理后的商品链接:', processedLinks);
  }

  // 创建工作簿
  const workbook = XLSX.utils.book_new();

  // 创建工作表数据
  const worksheetData = processedLinks.map((link: string, index: number) => ({
    '序号': index + 1,
    '商品链接': link
  }));

  // 创建工作表
  const worksheet = XLSX.utils.json_to_sheet(worksheetData);

  // 计算链接的最大宽度来动态设置列宽
  const maxLinkWidth = Math.max(
    ...processedLinks.map((link: string) => calculateTextWidth(link)),
    calculateTextWidth('商品链接') // 包含标题宽度
  );

  // 设置列宽（根据实际字符宽度计算，添加适当边距）
  const linkColumnWidth = Math.min(Math.max(maxLinkWidth + 10, 50), 300000000);
  const colWidths = [
    { wch: 8 }, // 序号列
    { wch: linkColumnWidth }  // 链接列，动态宽度
  ];
  worksheet['!cols'] = colWidths;

  // 设置表头样式
  const headerCells = ['A1', 'B1'];
  headerCells.forEach(cellAddr => {
    if (worksheet[cellAddr]) {
      worksheet[cellAddr].s = {
        font: {
          bold: true,
          color: { rgb: "FFFFFF" }
        },
        fill: {
          fgColor: { rgb: "4F81BD" }
        },
        alignment: {
          horizontal: "center",
          vertical: "center"
        },
        border: {
          top: { style: "thin", color: { rgb: "000000" } },
          bottom: { style: "thin", color: { rgb: "000000" } },
          left: { style: "thin", color: { rgb: "000000" } },
          right: { style: "thin", color: { rgb: "000000" } }
        }
      };
    }
  });

  // 设置超链接功能和样式
  processedLinks.forEach((link: string, index: number) => {
    const rowIndex = index + 2; // 从第2行开始（第1行是标题）
    const linkCellAddress = XLSX.utils.encode_cell({ r: rowIndex - 1, c: 1 }); // B2, B3, B4...
    const indexCellAddress = XLSX.utils.encode_cell({ r: rowIndex - 1, c: 0 }); // A2, A3, A4...

    // 设置链接单元格为超链接
    if (worksheet[linkCellAddress]) {
      worksheet[linkCellAddress].l = {
        Target: link,
        Tooltip: '点击打开商品页面'
      };
      // 设置超链接样式
      worksheet[linkCellAddress].s = {
        font: {
          color: { rgb: "0563C1" }, // 蓝色
          underline: true
        },
        alignment: {
          vertical: "center",
          wrapText: false
        },
        border: {
          top: { style: "thin", color: { rgb: "D9D9D9" } },
          bottom: { style: "thin", color: { rgb: "D9D9D9" } },
          left: { style: "thin", color: { rgb: "D9D9D9" } },
          right: { style: "thin", color: { rgb: "D9D9D9" } }
        }
      };
    }

    // 设置序号单元格样式
    if (worksheet[indexCellAddress]) {
      worksheet[indexCellAddress].s = {
        alignment: {
          horizontal: "center",
          vertical: "center"
        },
        border: {
          top: { style: "thin", color: { rgb: "D9D9D9" } },
          bottom: { style: "thin", color: { rgb: "D9D9D9" } },
          left: { style: "thin", color: { rgb: "D9D9D9" } },
          right: { style: "thin", color: { rgb: "D9D9D9" } }
        }
      };
    }
  });

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(workbook, worksheet, '商品链接');

  // 确保文件名有正确的扩展名
  const finalFileName = fileName.endsWith('.xlsx') ? fileName : `${fileName}.xlsx`;

  // 生成Excel文件
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

  // 创建Blob并下载
  const blob = new Blob([excelBuffer], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
  const url = URL.createObjectURL(blob);

  const a = document.createElement('a');
  a.href = url;
  a.download = finalFileName;
  a.style.display = 'none';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);

  // 清理URL对象
  URL.revokeObjectURL(url);

  console.log(`成功导出 ${processedLinks.length} 个商品链接到 ${finalFileName}`);
  console.log(`列宽设置, 链接列: ${linkColumnWidth} (基于最大宽度: ${maxLinkWidth})`);
};