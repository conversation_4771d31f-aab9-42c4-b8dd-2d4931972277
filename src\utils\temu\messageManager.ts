/**
 * 消息通信管理器 - 处理跨页面消息通信和采集完成通知
 */

// 声明 Chrome API
declare const chrome: any;

export interface CollectionMessage {
  type: 'collection_start' | 'collection_complete' | 'collection_error';
  goodsId?: string;
  goodsName?: string;
  error?: string;
  data?: any;
}

export interface MessageListener {
  id: string;
  callback: (message: CollectionMessage) => void;
}

class MessageManager {
  private listeners: Map<string, MessageListener> = new Map();
  private isInitialized = false;

  /**
   * 初始化消息管理器
   */
  init(): void {
    if (this.isInitialized) {
      return;
    }

    // 监听来自其他页面的消息
    if (chrome && chrome.runtime && chrome.runtime.onMessage) {
      chrome.runtime.onMessage.addListener((message: CollectionMessage, sender: any) => {
        this.handleMessage(message, sender);
      });
    }

    // 监听来自背景脚本转发的消息
    if (chrome && chrome.runtime && chrome.runtime.onMessage) {
      chrome.runtime.onMessage.addListener((message: any, sender: any) => {
        // 检查是否是背景脚本转发的采集完成消息
        if (message.source === 'background-forwarded' && message.type === 'collection_complete') {
          console.log('MessageManager: 收到背景脚本转发的采集完成消息:', message);
          this.handleMessage({
            type: message.type,
            goodsId: message.goodsId,
            goodsName: message.goodsName,
            error: message.error,
            data: message.data
          }, sender);
        }
      });
    }

    // 监听 window.postMessage 作为后备方案
    window.addEventListener('message', this.handleWindowMessage, false);

    this.isInitialized = true;
    console.log('消息管理器已初始化');
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: CollectionMessage, sender: any): void {
    console.log('收到消息:', message, '来自:', sender);

    if (message.type === 'collection_complete') {
      // 通知所有监听器
      this.listeners.forEach(listener => {
        try {
          listener.callback(message);
        } catch (error) {
          console.error('消息监听器执行错误:', error);
        }
      });
    }
  }

  /**
   * 处理来自 window.postMessage 的消息
   */
  private handleWindowMessage = (event: MessageEvent): void => {
    // 验证消息源和结构
    if (event.source !== window || !event.data || event.data.source !== 'temu-content-script') {
      return;
    }

    const message: CollectionMessage = {
      type: event.data.type,
      goodsId: event.data.goodsId,
      goodsName: event.data.goodsName,
      error: event.data.error,
      data: event.data.data
    };

    console.log('收到window.postMessage消息:', message);

    if (message.type === 'collection_complete') {
      // 通知所有监听器
      this.listeners.forEach(listener => {
        try {
          listener.callback(message);
        } catch (error) {
          console.error('window.postMessage监听器执行错误:', error);
        }
      });
    }
  };

  /**
   * 添加消息监听器
   * @param id 监听器ID
   * @param callback 回调函数
   */
  addListener(id: string, callback: (message: CollectionMessage) => void): void {
    this.listeners.set(id, { id, callback });
    console.log(`添加消息监听器: ${id}`);
  }

  /**
   * 移除消息监听器
   * @param id 监听器ID
   */
  removeListener(id: string): void {
    this.listeners.delete(id);
    console.log(`移除消息监听器: ${id}`);
  }

  /**
   * 发送采集完成消息
   * @param goodsId 商品ID
   * @param goodsName 商品名称
   */
  sendCollectionComplete(goodsId: string, goodsName: string): void {
    const message: CollectionMessage = {
      type: 'collection_complete',
      goodsId,
      goodsName
    };

    console.log(`MessageManager: 发送采集完成消息 - 商品ID: ${goodsId}, 商品名称: ${goodsName}`);

    // 尝试使用 chrome.runtime.sendMessage
    let messageSent = false;
    if (chrome && chrome.runtime && chrome.runtime.sendMessage) {
      try {
        chrome.runtime.sendMessage(message, (response: any) => {
          if (chrome.runtime.lastError) {
            console.warn('MessageManager: chrome.runtime.sendMessage 响应错误:', chrome.runtime.lastError.message);
          } else {
            console.log('MessageManager: chrome.runtime.sendMessage 发送成功，响应:', response);
          }
        });
        messageSent = true;
        console.log('MessageManager: 通过 chrome.runtime.sendMessage 发送消息');
      } catch (error) {
        console.error('MessageManager: chrome.runtime.sendMessage 发送失败:', error);
      }
    }

    // 后备方案：使用 window.postMessage
    if (!messageSent) {
      console.log('MessageManager: 使用 window.postMessage 后备方案');
      window.postMessage({
        source: 'temu-content-script',
        ...message
      }, '*');
    }

    // 同时通知本地监听器
    this.handleMessage(message, { tab: { id: 'local' } });
  }

  /**
   * 发送采集错误消息
   * @param goodsId 商品ID
   * @param error 错误信息
   */
  sendCollectionError(goodsId: string, error: string): void {
    const message: CollectionMessage = {
      type: 'collection_error',
      goodsId,
      error
    };

    // 尝试使用 chrome.runtime.sendMessage
    let messageSent = false;
    if (chrome && chrome.runtime && chrome.runtime.sendMessage) {
      try {
        chrome.runtime.sendMessage(message);
        messageSent = true;
      } catch (error) {
        console.error('chrome.runtime.sendMessage 发送失败:', error);
      }
    }

    // 后备方案：使用 window.postMessage
    if (!messageSent) {
      console.log('使用 window.postMessage 后备方案');
      window.postMessage({
        source: 'temu-content-script',
        ...message
      }, '*');
    }
  }

  /**
   * 清理所有监听器
   */
  cleanup(): void {
    this.listeners.clear();
    // 清理 window.postMessage 监听器
    window.removeEventListener('message', this.handleWindowMessage, false);
    console.log('消息管理器已清理');
  }
}

// 创建单例实例
export const messageManager = new MessageManager();

/**
 * 等待采集完成消息
 * @param goodsId 商品ID
 * @param timeout 超时时间（毫秒）
 * @returns Promise<CollectionMessage>
 */
export const waitForCollectionComplete = async (
  goodsId: string,
  timeout: number = 60000
): Promise<CollectionMessage> => {
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      messageManager.removeListener(`wait_${goodsId}`);
      reject(new Error('等待采集完成超时'));
    }, timeout);

    const listenerId = `wait_${goodsId}`;

    messageManager.addListener(listenerId, (message: CollectionMessage) => {
      if (message.goodsId === goodsId && message.type === 'collection_complete') {
        clearTimeout(timer);
        messageManager.removeListener(listenerId);
        resolve(message);
      }
    });
  });
};

/**
 * 初始化消息管理器（如果尚未初始化）
 */
export const initMessageManager = (): void => {
  messageManager.init();
};