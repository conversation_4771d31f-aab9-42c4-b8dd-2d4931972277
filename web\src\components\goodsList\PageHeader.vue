<template>
  <div class="page-header">
    <div class="header-left">
      <div class="custom-breadcrumb">
        <el-button type="text" @click="handleGoBack" class="breadcrumb-back-button">
          <el-icon><ArrowLeft /></el-icon>
          <span>商品目录</span>
        </el-button>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-current-page">{{ currentDirectoryName }}</span>
      </div>
    </div>
    <div class="header-actions">
      <!-- 批量设置状态按钮已隐藏，暂不修复点击无响应的bug -->
      <!-- <el-button type="warning" @click="handleBatchOperation" :disabled="selectedGoods.length === 0">
        <el-icon><Setting /></el-icon>
        批量设置
      </el-button> -->
      <el-button type="danger" @click="handleBatchDelete" :disabled="selectedGoods.length === 0">
        <el-icon><Delete /></el-icon>
        批量删除
      </el-button>
      <!-- 移动到目录功能暂时注释 -->
      <!-- <el-button type="success" @click="handleMoveOperation" :disabled="selectedGoods.length === 0">
        <el-icon><FolderOpened /></el-icon>
        移动到目录
      </el-button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, Setting, Delete } from '@element-plus/icons-vue'
import { type Goods as GoodsType } from '../../utils/goodsApi'

// 定义接口 - 使用 GoodsType 作为基础类型，确保 id 是必需的
interface Goods extends Omit<GoodsType, 'id'> {
  id: number  // 确保 id 是必需的
}

// Props
interface Props {
  currentDirectoryName: string
  selectedGoods: Goods[]
}

const props = withDefaults(defineProps<Props>(), {
  currentDirectoryName: '',
  selectedGoods: () => []
})

// Events
const emit = defineEmits<{
  goBack: []
  batchOperation: []
  batchDelete: []
  moveOperation: []
}>()

// 处理返回
const handleGoBack = () => {
  emit('goBack')
}

// 处理批量操作
const handleBatchOperation = () => {
  emit('batchOperation')
}

// 处理批量删除
const handleBatchDelete = () => {
  emit('batchDelete')
}

// 处理移动操作
const handleMoveOperation = () => {
  emit('moveOperation')
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
}

.custom-breadcrumb {
  display: flex;
  align-items: center;
  font-size: 18px;
  margin-right: 20px; /* 与右侧信息隔开一些距离 */
}

.breadcrumb-back-button {
  font-size: 18px; /* 统一返回按钮字体大小 */
  color: #409EFF; /* Element Plus 主题蓝色 */
  padding: 0; /* 移除默认padding */
  margin-right: 8px;
}

.breadcrumb-back-button .el-icon {
  margin-right: 4px;
}

.breadcrumb-back-button:hover,
.breadcrumb-back-button:focus {
  color: #66b1ff; /* 悬停颜色变浅 */
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #C0C4CC; /* 分隔符颜色 */
}

.breadcrumb-current-page {
  font-weight: 600; /* 当前页面名称加粗 */
  color: #303133; /* 当前页面文字颜色 */
  line-height: 1.2; /* 调整行高，减少占据的高度 */
  max-height: 24px; /* 限制最大高度 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超长文本显示省略号 */
  white-space: nowrap; /* 不换行 */
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}
</style>
