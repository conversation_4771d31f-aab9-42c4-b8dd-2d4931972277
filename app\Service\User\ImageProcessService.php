<?php
namespace App\Service\User;

use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use App\Models\User\GoodsInstructionImagesModel;
use App\Models\User\UserGoodsDirectoryModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

/**
 * 图片处理服务类
 *
 * 优化功能说明：
 * 1. 智能文件大小检测：在下载前检测远程文件大小
 * 2. 条件下载策略：
 *    - 文件 ≤ 500KB：使用传统HTTP下载方式（快速，适合小文件）
 *    - 文件 > 500KB：使用cURL流式下载（内存友好，适合大文件）
 * 3. 流式下载特性：
 *    - 避免大文件导致的内存溢出
 *    - 支持下载进度监控和日志记录
 *    - 自动文件完整性验证
 * 4. 增强的错误处理：
 *    - 智能重试机制
 *    - 详细的错误日志记录
 *    - 失败文件的自动清理
 * 5. 性能优化：
 *    - 64KB缓冲区大小优化
 *    - 针对不同文件类型的超时时间调整
 *    - 进度监控减少日志噪音
 */
class ImageProcessService extends BaseService
{
    // 每次处理的图片数量
    const BATCH_SIZE = 5;
    // 最小会员ID阈值，用于向后兼容
    // 会员ID <= 此阈值时使用旧目录结构（会员ID/商品ID/文件）
    // 会员ID > 此阈值时使用新目录结构（商品ID/文件）
    const MIN_USER_ID_THRESHOLD = 0;

    /**
     * 控制是否将 HTTPS 协议的下载链接转换为 HTTP 协议
     * 默认值为 false（不转换）
     *
     * @var bool
     */
    private $convertHttpsToHttp = true;

    /**
     * 设置是否将 HTTPS 协议转换为 HTTP 协议
     *
     * @param bool $convert 是否转换
     * @return void
     */
    public function setConvertHttpsToHttp(bool $convert): void
    {
        $this->convertHttpsToHttp = $convert;
    }

    /**
     * 获取当前的 HTTPS 到 HTTP 转换设置
     *
     * @return bool
     */
    public function getConvertHttpsToHttp(): bool
    {
        return $this->convertHttpsToHttp;
    }

    /**
     * 转换URL协议（如果配置启用）
     * 将 HTTPS 协议转换为 HTTP 协议
     *
     * @param string $url 原始URL
     * @return string 转换后的URL
     */
    private function convertUrlProtocol(string $url): string
    {
        if ($this->convertHttpsToHttp && strpos($url, 'https://') === 0) {
            return 'http://' . substr($url, 8);
        }
        return $url;
    }

    // 图片下载超时时间（秒）- 从配置文件获取，默认300秒
    private function getImageTimeout(): int
    {
        return (int) config('services.image_process.timeouts.image', 300);
    }

    // 视频下载超时时间（秒）- 从配置文件获取，默认300秒
    private function getVideoTimeout(): int
    {
        return (int) config('services.image_process.timeouts.video', 300);
    }

    // PDF下载超时时间（秒）- 从配置文件获取，默认300秒
    private function getPdfTimeout(): int
    {
        return (int) config('services.image_process.timeouts.pdf', 300);
    }

    // 重试次数 - 从配置文件获取，默认2次
    private function getRetryTimes(): int
    {
        return (int) config('services.image_process.retry_times', 2);
    }

    // 文件大小阈值（500KB）- 从配置文件获取
    private function getFileSizeThreshold(): int
    {
        return (int) config('services.image_process.file_size_threshold', 500 * 1024);
    }

    // 流式下载的块大小（64KB）- 从配置文件获取
    private function getStreamChunkSize(): int
    {
        return (int) config('services.image_process.stream_chunk_size', 64 * 1024);
    }

    // 连接超时时间（秒）- 从配置文件获取，默认60秒
    private function getConnectTimeout(): int
    {
        return (int) config('services.image_process.connect_timeout', 60);
    }

    // 最大重定向次数 - 从配置文件获取，默认5次
    private function getMaxRedirects(): int
    {
        return (int) config('services.image_process.max_redirects', 5);
    }

    // User-Agent字符串 - 从配置文件获取
    private function getUserAgent(): string
    {
        return config('services.image_process.user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    }

    // 下载进度日志开关（默认关闭，调试时开启）
    const ENABLE_DOWNLOAD_PROGRESS_LOG = false;

    /**
     * 动态控制下载进度日志开关
     * 可以通过环境变量或配置文件控制
     *
     * @return bool
     */
    private function isDownloadProgressLogEnabled(): bool
    {
        // 检查配置文件
        $configSetting = config('services.image_process.enable_download_progress_log');
        if ($configSetting !== null) {
            return (bool) $configSetting;
        }
        // 默认使用常量设置
        return self::ENABLE_DOWNLOAD_PROGRESS_LOG;
    }
    /**
     * 处理商品图片本地化
     * 
     * @param int $userId 用户ID
     * @param int $goodsId 商品ID
     * @param int $processStep 处理步骤
     * @return array
     */
    public function processGoodsImages(int $userId, int $goodsId, int $processStep): array
    {
        // 获取商品信息
        $goods = GoodsModel::where('user_id', $userId)
            ->where('id', $goodsId)
            ->where('status', 1)
            ->first();

        if (!$goods) {
            throw new MyException("商品不存在或已删除");
        }

        // 如果已经处理完成，直接返回状态
        if ($goods->img_local_status == 1) {
            return $this->getProcessResult($goods, true);
        }

        DB::beginTransaction();
        try {
            // 获取所有需要处理的图片和视频信息
            $mediaInfo = $this->getMediaInfo($goods);
            
            // 第一次请求时返回统计信息
            if ($processStep == 1) {
                $result = $this->getInitialProcessInfo($mediaInfo, $goods);
                DB::commit();
                return $result;
            }

            // 处理图片和视频
            $processResult = $this->processMediaFiles($goods, $mediaInfo, $processStep);
            
            DB::commit();
            return $processResult;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取商品的所有媒体文件信息
     * 
     * @param GoodsModel $goods
     * @return array
     */
    private function getMediaInfo(GoodsModel $goods): array
    {
        $mediaInfo = [
            'images' => [],
            'videos' => [],
            'pdfs' => [],
            'total_images' => 0,
            'total_videos' => 0,
            'total_pdfs' => 0,
            'processed_images' => 0,
            'processed_videos' => 0,
            'processed_pdfs' => 0
        ];

        // 处理商品主图片
        if (!empty($goods->goods_pic)) {
            $goodsPics = json_decode($goods->goods_pic, true);
            if (is_array($goodsPics)) {
                foreach ($goodsPics as $pic) {
                    if (is_string($pic) && !empty($pic)) {
                        $mediaInfo['images'][] = [
                            'url' => $pic,
                            'type' => 'goods_pic',
                            'field' => 'goods_pic'
                        ];
                    }
                }
            }
        }

        // 处理商品详情图片
        if (!empty($goods->goods_detail)) {
            $goodsDetailPics = json_decode($goods->goods_detail, true);
            if (is_array($goodsDetailPics)) {
                foreach ($goodsDetailPics as $pic) {
                    if (is_string($pic) && !empty($pic)) {
                        $mediaInfo['images'][] = [
                            'url' => $pic,
                            'type' => 'goods_detail',
                            'field' => 'goods_detail'
                        ];
                    }
                }
            }
        }

        // 处理商品视频
        if (!empty($goods->goods_video) && is_string($goods->goods_video)) {
            $mediaInfo['videos'][] = [
                'url' => $goods->goods_video,
                'type' => 'goods_video',
                'field' => 'goods_video'
            ];
        }

        // 处理商品PDF文件
        if (!empty($goods->goods_pdf) && is_string($goods->goods_pdf)) {
            // 同时支持远程URL和本地路径
            if (preg_match('/\.pdf(?:\?.*)?$/i', $goods->goods_pdf)) {
                $mediaInfo['pdfs'][] = [
                    'url' => $goods->goods_pdf,
                    'type' => 'goods_pdf',
                    'field' => 'goods_pdf'
                ];
            }
        }

        // 处理商品介绍图片
        $instructionImages = GoodsInstructionImagesModel::where('user_goods_id', $goods->id)->first();
        if ($instructionImages && !empty($instructionImages->urls)) {
            $urls = json_decode($instructionImages->urls, true);
            if (is_array($urls)) {
                foreach ($urls as $url) {
                    if (is_string($url) && !empty($url)) {
                        $mediaInfo['images'][] = [
                            'url' => $url,
                            'type' => 'instruction_images',
                            'field' => 'urls',
                            'model_id' => $instructionImages->id
                        ];
                    }
                }
            }
        }

        // 处理SKU图片
        $skus = GoodsSkuModel::where('user_goods_id', $goods->id)->get();
        foreach ($skus as $sku) {
            // 处理缩略图
            if (!empty($sku->thumb_url) && is_string($sku->thumb_url)) {
                $mediaInfo['images'][] = [
                    'url' => $sku->thumb_url,
                    'type' => 'sku_thumb',
                    'field' => 'thumb_url',
                    'sku_id' => $sku->id
                ];
            }

            // 处理SKC图片集
            if (!empty($sku->skc_gallery)) {
                $skcGallery = json_decode($sku->skc_gallery, true);
                if (is_array($skcGallery)) {
                    foreach ($skcGallery as $item) {
                        if (isset($item['url']) && is_string($item['url']) && !empty($item['url'])) {
                            $mediaInfo['images'][] = [
                                'url' => $item['url'],
                                'type' => 'sku_skc_gallery',
                                'field' => 'skc_gallery',
                                'sku_id' => $sku->id,
                                'gallery_item' => $item
                            ];
                        }
                    }
                }
            }
        }

        // 统计数量
        $mediaInfo['total_images'] = count($mediaInfo['images']);
        $mediaInfo['total_videos'] = count($mediaInfo['videos']);
        $mediaInfo['total_pdfs'] = count($mediaInfo['pdfs']);

        // 统计已处理数量
        foreach ($mediaInfo['images'] as $image) {
            if (!$this->isRemoteUrl($image['url'])) {
                $mediaInfo['processed_images']++;
            }
        }

        foreach ($mediaInfo['videos'] as $video) {
            if (!$this->isRemoteUrl($video['url'])) {
                $mediaInfo['processed_videos']++;
            }
        }

        foreach ($mediaInfo['pdfs'] as $pdf) {
            if (!$this->isRemoteUrl($pdf['url'])) {
                $mediaInfo['processed_pdfs']++;
            }
        }

        return $mediaInfo;
    }

    /**
     * 获取初始处理信息
     * 
     * @param array $mediaInfo
     * @param GoodsModel $goods
     * @return array
     */
    private function getInitialProcessInfo(array $mediaInfo, GoodsModel $goods): array
    {
        $directoryName = '未分类';
        if ($goods->directory_id > 0) {
            $directory = UserGoodsDirectoryModel::find($goods->directory_id);
            if ($directory) {
                $directoryName = $directory->name;
            }
        }

        return [
            'goods_id' => $goods->id,
            'img_local_status' => $goods->img_local_status,
            'total_images' => $mediaInfo['total_images'],
            'processed_images' => $mediaInfo['processed_images'],
            'total_videos' => $mediaInfo['total_videos'],
            'processed_videos' => $mediaInfo['processed_videos'],
            'total_pdfs' => $mediaInfo['total_pdfs'],
            'processed_pdfs' => $mediaInfo['processed_pdfs'],
            'estimated_steps' => $this->calculateEstimatedSteps($mediaInfo),
            'directory_name' => $directoryName
        ];
    }

    /**
     * 计算预估处理步骤数
     * 
     * @param array $mediaInfo
     * @return int
     */
    private function calculateEstimatedSteps(array $mediaInfo): int
    {
        $remainingImages = $mediaInfo['total_images'] - $mediaInfo['processed_images'];
        $remainingVideos = $mediaInfo['total_videos'] - $mediaInfo['processed_videos'];
        $remainingPdfs = $mediaInfo['total_pdfs'] - $mediaInfo['processed_pdfs'];
        
        $imageSteps = ceil($remainingImages / self::BATCH_SIZE);
        $videoSteps = $remainingVideos > 0 ? 1 : 0;
        $pdfSteps = $remainingPdfs > 0 ? 1 : 0;
        
        return (int) max(1, $imageSteps + $videoSteps + $pdfSteps);
    }

    /**
     * 处理媒体文件
     * 
     * @param GoodsModel $goods
     * @param array $mediaInfo
     * @param int $processStep
     * @return array
     */
    private function processMediaFiles(GoodsModel $goods, array $mediaInfo, int $processStep): array
    {
        $processedCount = 0;
        $allImagesProcessed = true;
        $allVideosProcessed = true;

        // 先处理图片
        $imagesToProcess = [];
        foreach ($mediaInfo['images'] as $image) {
            if ($this->isRemoteUrl($image['url'])) {
                $imagesToProcess[] = $image;
                $allImagesProcessed = false;
                if (count($imagesToProcess) >= self::BATCH_SIZE) {
                    break;
                }
            }
        }

        // 如果还有图片需要处理
        if (!empty($imagesToProcess)) {
            $processedCount = $this->processImages($goods, $imagesToProcess);
        } else {
            // 图片都处理完了，处理视频
            $videosToProcess = [];
            foreach ($mediaInfo['videos'] as $video) {
                if ($this->isRemoteUrl($video['url'])) {
                    $videosToProcess[] = $video;
                    $allVideosProcessed = false;
                    break; // 一次只处理一个视频
                }
            }

            if (!empty($videosToProcess)) {
                $processedCount = $this->processVideos($goods, $videosToProcess);
            } else {
                // 图片和视频都处理完了，处理PDF文件
                $pdfsToProcess = [];
                foreach ($mediaInfo['pdfs'] as $pdf) {
                    if ($this->isRemoteUrl($pdf['url'])) {
                        $pdfsToProcess[] = $pdf;
                        break; // 一次只处理一个PDF
                    }
                }

                if (!empty($pdfsToProcess)) {
                    $processedCount = $this->processPdfFiles($goods, $pdfsToProcess);
                }
            }
        }

        // 重新获取媒体信息以更新统计
        $updatedMediaInfo = $this->getMediaInfo($goods);
        
        // 检查是否全部处理完成
        $isCompleted = ($updatedMediaInfo['processed_images'] >= $updatedMediaInfo['total_images']) && 
                      ($updatedMediaInfo['processed_videos'] >= $updatedMediaInfo['total_videos']) &&
                      ($updatedMediaInfo['processed_pdfs'] >= $updatedMediaInfo['total_pdfs']);

        if ($isCompleted && $goods->img_local_status != 1) {
            $goods->img_local_status = 1;
            $goods->save();
        }

        $directoryName = '未分类';
        if ($goods->directory_id > 0) {
            $directory = UserGoodsDirectoryModel::find($goods->directory_id);
            if ($directory) {
                $directoryName = $directory->name;
            }
        }

        return [
            'goods_id' => $goods->id,
            'img_local_status' => $isCompleted ? 1 : 0,
            'total_images' => $updatedMediaInfo['total_images'],
            'processed_images' => $updatedMediaInfo['processed_images'],
            'total_videos' => $updatedMediaInfo['total_videos'],
            'processed_videos' => $updatedMediaInfo['processed_videos'],
            'total_pdfs' => $updatedMediaInfo['total_pdfs'],
            'processed_pdfs' => $updatedMediaInfo['processed_pdfs'],
            'current_batch_processed' => $processedCount,
            'is_completed' => $isCompleted,
            'directory_name' => $directoryName
        ];
    }

    /**
     * 处理图片（并发下载优化版本）
     *
     * @param GoodsModel $goods
     * @param array $images
     * @return int
     */
    private function processImages(GoodsModel $goods, array $images): int
    {
        $processedCount = 0;
        $updates = [];

        // 记录开始处理日志
        /* Log::channel('image_process')->info("开始批量处理图片", [
            'goods_id' => $goods->goods_id,
            'user_id' => $goods->user_id,
            'image_count' => count($images)
        ]); */

        // 使用Laravel HTTP Pool实现并发下载
        $imageTimeout = $this->getImageTimeout();
        $connectTimeout = $this->getConnectTimeout();
        $maxRedirects = $this->getMaxRedirects();
        $userAgent = $this->getUserAgent();

        $responses = Http::pool(function ($pool) use ($images, $imageTimeout, $connectTimeout, $maxRedirects, $userAgent) {
            $requests = [];
            foreach ($images as $index => $image) {
                $url = $this->convertUrlProtocol($image['url']);
                $downloadUrl = $url . '?imageView2/2/w/602/q/90/format/webp';
                Log::channel('image_process')->info("图片下载地址".$downloadUrl);
                // 为每个请求添加标识，以便后续匹配
                $requests[$index] = $pool->as("image_{$index}")
                    ->timeout($imageTimeout)
                    ->connectTimeout($connectTimeout)
                    ->withoutVerifying()
                    ->withOptions([
                        'curl' => [
                            CURLOPT_TIMEOUT => $imageTimeout,
                            CURLOPT_CONNECTTIMEOUT => $connectTimeout,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_MAXREDIRS => $maxRedirects,
                            CURLOPT_SSL_VERIFYPEER => false,
                            CURLOPT_SSL_VERIFYHOST => false,
                            CURLOPT_USERAGENT => $userAgent
                        ]
                    ])
                    ->get($downloadUrl);
            }
            return $requests;
        });

        // 处理并发下载的结果
        foreach ($images as $index => $image) {
            $response = $responses["image_{$index}"];

            try {
                // 检查响应是否为异常对象
                if ($response instanceof \Exception) {
                    // 网络连接异常或其他异常，标记为移除
                    $updates[] = [
                        'image' => $image,
                        'local_path' => null
                    ];
                    Log::channel('image_process')->error("图片下载异常", [
                        'url' => $image['url'],
                        'error' => $response->getMessage(),
                        'goods_id' => $goods->goods_id,
                        'user_id' => $goods->user_id
                    ]);
                } elseif ($response->successful()) {
                    // 下载成功，保存文件
                    $localPath = $this->saveImageFromResponse($goods->user_id, $goods->goods_id, $image, $response);
                    if ($localPath) {
                        $updates[] = [
                            'image' => $image,
                            'local_path' => $localPath
                        ];
                        $processedCount++;

                        // 记录成功日志
                        /* Log::channel('image_process')->info("图片下载成功", [
                            'url' => $image['url'],
                            'local_path' => $localPath,
                            'goods_id' => $goods->goods_id,
                            'user_id' => $goods->user_id
                        ]); */
                    } else {
                        // 保存失败，标记为移除
                        $updates[] = [
                            'image' => $image,
                            'local_path' => null
                        ];
                    }
                } else {
                    // HTTP请求失败，标记为移除
                    $updates[] = [
                        'image' => $image,
                        'local_path' => null
                    ];
                }
            } catch (\Exception $e) {
                // 处理异常，标记为移除
                $updates[] = [
                    'image' => $image,
                    'local_path' => null
                ];
                Log::channel('image_process')->error("图片处理异常", [
                    'url' => $image['url'],
                    'error' => $e->getMessage(),
                    'goods_id' => $goods->goods_id,
                    'user_id' => $goods->user_id
                ]);
            }
        }

        // 批量更新数据库
        $this->updateImageUrls($goods, $updates);

        // 记录处理完成日志
        /* Log::channel('image_process')->info("图片批量处理完成", [
            'goods_id' => $goods->goods_id,
            'user_id' => $goods->user_id,
            'total_images' => count($images),
            'processed_count' => $processedCount,
            'success_rate' => count($images) > 0 ? round(($processedCount / count($images)) * 100, 2) . '%' : '0%'
        ]); */

        return $processedCount;
    }

    /**
     * 处理视频
     * 
     * @param GoodsModel $goods
     * @param array $videos
     * @return int
     */
    private function processVideos(GoodsModel $goods, array $videos): int
    {
        $processedCount = 0;

        foreach ($videos as $video) {
            try {
                $localPath = $this->downloadVideo($goods->user_id, $goods->goods_id, $video);
                if ($localPath) {
                    // 更新视频URL
                    $goods->goods_video = $localPath;
                    $goods->save();
                    $processedCount++;
                }
            } catch (\Exception $e) {
                // 视频下载失败，清空视频字段
                $goods->goods_video = '';
                $goods->save();
            }
        }

        return $processedCount;
    }

    /**
     * 处理PDF文件
     * 
     * @param GoodsModel $goods
     * @param array $pdfs
     * @return int
     */
    private function processPdfFiles(GoodsModel $goods, array $pdfs): int
    {
        $processedCount = 0;

        foreach ($pdfs as $pdf) {
            try {
                $localPath = $this->downloadPdf($goods->user_id, $goods->goods_id, $pdf);
                if ($localPath) {
                    // 更新PDF URL
                    $goods->goods_pdf = $localPath;
                    $goods->save();
                    $processedCount++;
                }
            } catch (\Exception $e) {
                // PDF下载失败，清空PDF字段
                $goods->goods_pdf = '';
                $goods->save();
            }
        }

        return $processedCount;
    }

    /**
     * 生成文件存储路径
     * 根据会员ID阈值决定使用新旧目录结构
     *
     * @param int $userId 会员ID
     * @param int $goodsId 商品ID
     * @param string $subDir 子目录名称
     * @param string $fileName 文件名
     * @return string 相对路径
     */
    private function generateFilePath(int $userId, int $goodsId, string $subDir, string $fileName): string
    {
        if ($userId <= self::MIN_USER_ID_THRESHOLD) {
            // 使用旧目录结构：会员ID/商品ID/子目录/文件
            return "attachment/{$userId}/{$goodsId}/{$subDir}/{$fileName}";
        } else {
            // 使用新目录结构：商品ID/子目录/文件
            return "attachment/{$goodsId}/{$subDir}/{$fileName}";
        }
    }

    /**
     * 下载PDF文件
     *
     * @param int $userId
     * @param int $goodsId
     * @param array $pdf
     * @return string|null
     */
    private function downloadPdf(int $userId, int $goodsId, array $pdf): ?string
    {
        $url = $this->convertUrlProtocol($pdf['url']);
        
        // 解析文件名，去除参数但保持原始扩展名
        $parsedUrl = parse_url($url);
        if (!isset($parsedUrl['path'])) {
            return null;
        }

        $pathInfo = pathinfo($parsedUrl['path']);
        if (!isset($pathInfo['filename'])) {
            return null;
        }

        // 确保文件名使用小写的.pdf后缀
        $fileName = $pathInfo['filename'] . '.pdf';

        // 确定存储目录（使用新的路径生成逻辑）
        $relativePath = $this->generateFilePath($userId, $goodsId, 'goods_pdf', $fileName);
        $fullPath = public_path($relativePath);

        // 检查文件是否已存在
        if (file_exists($fullPath)) {
            return $relativePath;
        }

        // 创建目录
        $dir = dirname($fullPath);
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new \Exception("无法创建目录: {$dir}");
            }
        }

        // 使用优化的下载方法
        try {
            $pdfTimeout = $this->getPdfTimeout();
            $success = $this->downloadFileOptimized($url, $fullPath, $pdfTimeout);
            return $success ? $relativePath : null;
        } catch (\Exception $e) {
            // 下载失败，记录错误并返回null
            Log::channel('image_process')->error("PDF下载失败", [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 从HTTP响应中保存图片文件
     *
     * @param int $userId
     * @param int $goodsId
     * @param array $image
     * @param \Illuminate\Http\Client\Response $response
     * @return string|null
     */
    private function saveImageFromResponse(int $userId, int $goodsId, array $image, $response): ?string
    {
        $url = $image['url'];
        $type = $image['type'];

        // 解析文件名，保持原始扩展名
        $fileName = $this->extractFileName($url);
        if (!$fileName) {
            return null;
        }

        // 确定存储目录（使用新的路径生成逻辑）
        $subDir = $this->getImageSubDirectory($type);
        $relativePath = $this->generateFilePath($userId, $goodsId, $subDir, $fileName);
        $fullPath = public_path($relativePath);

        // 检查文件是否已存在
        if (file_exists($fullPath)) {
            return $relativePath;
        }

        // 创建目录
        $dir = dirname($fullPath);
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new \Exception("无法创建目录: {$dir}");
            }
        }

        try {
            // 将响应内容写入文件
            file_put_contents($fullPath, $response->body());
            return $relativePath;
        } catch (\Exception $e) {
            // 保存失败，记录错误并返回null
            Log::channel('image_process')->error("720行图片保存失败", [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 下载图片（保留原方法作为备用）
     *
     * @param int $userId
     * @param int $goodsId
     * @param array $image
     * @return string|null
     */
    private function downloadImage(int $userId, int $goodsId, array $image): ?string
    {
        $url = $this->convertUrlProtocol($image['url']);
        $type = $image['type'];

        // 添加图片处理参数
        $downloadUrl = $url . '?imageView2/2/w/602/q/90/format/webp';
        
        // 解析文件名，保持原始扩展名
        $fileName = $this->extractFileName($url);
        if (!$fileName) {
            return null;
        }

        // 确定存储目录（使用新的路径生成逻辑）
        $subDir = $this->getImageSubDirectory($type);
        $relativePath = $this->generateFilePath($userId, $goodsId, $subDir, $fileName);
        $fullPath = public_path($relativePath);

        // 检查文件是否已存在
        if (file_exists($fullPath)) {
            return $relativePath;
        }

        // 创建目录
        $dir = dirname($fullPath);
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new \Exception("无法创建目录: {$dir}");
            }
        }

        // 使用优化的下载方法
        try {
            $imageTimeout = $this->getImageTimeout();
            $success = $this->downloadFileOptimized($downloadUrl, $fullPath, $imageTimeout);
            return $success ? $relativePath : null;
        } catch (\Exception $e) {
            // 下载失败，记录错误并返回null
            Log::channel('image_process')->error("图片下载失败", [
                'url' => $downloadUrl,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 下载视频
     * 
     * @param int $userId
     * @param int $goodsId
     * @param array $video
     * @return string|null
     */
    private function downloadVideo(int $userId, int $goodsId, array $video): ?string
    {
        $url = $this->convertUrlProtocol($video['url']);
        
        // 解析文件名
        $fileName = $this->extractFileName($url);
        if (!$fileName) {
            return null;
        }

        // 确定存储目录（使用新的路径生成逻辑）
        $relativePath = $this->generateFilePath($userId, $goodsId, 'goods_video', $fileName);
        $fullPath = public_path($relativePath);

        // 检查文件是否已存在
        if (file_exists($fullPath)) {
            return $relativePath;
        }

        // 创建目录
        $dir = dirname($fullPath);
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new \Exception("无法创建目录: {$dir}");
            }
        }

        // 使用优化的下载方法（视频文件较大，增加超时时间）
        try {
            $videoTimeout = $this->getVideoTimeout();
            $success = $this->downloadFileOptimized($url, $fullPath, $videoTimeout);
            return $success ? $relativePath : null;
        } catch (\Exception $e) {
            // 下载失败，记录错误并返回null
            Log::channel('image_process')->error("视频下载失败", [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 更新图片URL
     * 
     * @param GoodsModel $goods
     * @param array $updates
     */
    private function updateImageUrls(GoodsModel $goods, array $updates): void
    {
        $goodsPicUpdates = [];
        $goodsDetailUpdates = [];
        $instructionUpdates = [];
        $skuUpdates = [];

        foreach ($updates as $update) {
            $image = $update['image'];
            $localPath = $update['local_path'];

            switch ($image['type']) {
                case 'goods_pic':
                    $goodsPicUpdates[] = [
                        'old_url' => $image['url'],
                        'new_url' => $localPath
                    ];
                    break;

                case 'goods_detail':
                    $goodsDetailUpdates[] = [
                        'old_url' => $image['url'],
                        'new_url' => $localPath
                    ];
                    break;

                case 'instruction_images':
                    $instructionUpdates[] = [
                        'model_id' => $image['model_id'],
                        'old_url' => $image['url'],
                        'new_url' => $localPath
                    ];
                    break;

                case 'sku_thumb':
                    $skuUpdates[] = [
                        'sku_id' => $image['sku_id'],
                        'field' => 'thumb_url',
                        'old_url' => $image['url'],
                        'new_url' => $localPath
                    ];
                    break;

                case 'sku_skc_gallery':
                    $skuUpdates[] = [
                        'sku_id' => $image['sku_id'],
                        'field' => 'skc_gallery',
                        'old_url' => $image['url'],
                        'new_url' => $localPath,
                        'gallery_item' => $image['gallery_item']
                    ];
                    break;
            }
        }

        // 更新商品主图
        if (!empty($goodsPicUpdates)) {
            $this->updateGoodsPic($goods, $goodsPicUpdates);
        }

        // 更新商品详情图片
        if (!empty($goodsDetailUpdates)) {
            $this->updateGoodsDetail($goods, $goodsDetailUpdates);
        }

        // 更新介绍图片
        if (!empty($instructionUpdates)) {
            $this->updateInstructionImages($instructionUpdates);
        }

        // 更新SKU图片
        if (!empty($skuUpdates)) {
            $this->updateSkuImages($skuUpdates);
        }
    }

    /**
     * 更新商品主图
     *
     * @param GoodsModel $goods
     * @param array $updates
     */
    private function updateGoodsPic(GoodsModel $goods, array $updates): void
    {
        $goodsPics = json_decode($goods->goods_pic, true);
        if (!is_array($goodsPics)) {
            return;
        }

        foreach ($updates as $update) {
            $key = array_search($update['old_url'], $goodsPics);
            if ($key !== false) {
                if ($update['new_url'] === null) {
                    // 移除失败的图片
                    unset($goodsPics[$key]);
                } else {
                    // 更新URL
                    $goodsPics[$key] = $update['new_url'];
                }
            }
        }

        $goodsPics = array_values($goodsPics);

        // 如果所有图片都失败了，存储空字符串而不是空数组
        if (empty($goodsPics)) {
            $goods->goods_pic = '';
        } else {
            $goods->goods_pic = json_encode($goodsPics, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }

        $goods->save();
    }

    /**
     * 更新商品详情图片
     *
     * @param GoodsModel $goods
     * @param array $updates
     */
    private function updateGoodsDetail(GoodsModel $goods, array $updates): void
    {
        $goodsDetailPics = json_decode($goods->goods_detail, true);
        if (!is_array($goodsDetailPics)) {
            return;
        }

        foreach ($updates as $update) {
            $key = array_search($update['old_url'], $goodsDetailPics);
            if ($key !== false) {
                if ($update['new_url'] === null) {
                    // 移除失败的图片
                    unset($goodsDetailPics[$key]);
                } else {
                    // 更新URL
                    $goodsDetailPics[$key] = $update['new_url'];
                }
            }
        }

        $goodsDetailPics = array_values($goodsDetailPics);

        // 如果所有图片都失败了，存储空字符串而不是空数组
        if (empty($goodsDetailPics)) {
            $goods->goods_detail = '';
        } else {
            $goods->goods_detail = json_encode($goodsDetailPics, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }

        $goods->save();
    }

    /**
     * 更新介绍图片
     * 
     * @param array $updates
     */
    private function updateInstructionImages(array $updates): void
    {
        $groupedUpdates = [];
        foreach ($updates as $update) {
            $groupedUpdates[$update['model_id']][] = $update;
        }

        foreach ($groupedUpdates as $modelId => $modelUpdates) {
            $model = GoodsInstructionImagesModel::find($modelId);
            if (!$model) {
                continue;
            }

            $urls = json_decode($model->urls, true);
            if (!is_array($urls)) {
                continue;
            }

            foreach ($modelUpdates as $update) {
                $key = array_search($update['old_url'], $urls);
                if ($key !== false) {
                    if ($update['new_url'] === null) {
                        // 移除失败的图片
                        unset($urls[$key]);
                    } else {
                        // 更新URL
                        $urls[$key] = $update['new_url'];
                    }
                }
            }

            $urls = array_values($urls);
            
            // 如果所有图片都失败了，存储空字符串而不是空数组
            if (empty($urls)) {
                $model->urls = '';
            } else {
                $model->urls = json_encode($urls, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            }
            
            $model->save();
        }
    }

    /**
     * 更新SKU图片
     * 
     * @param array $updates
     */
    private function updateSkuImages(array $updates): void
    {
        $groupedUpdates = [];
        foreach ($updates as $update) {
            $groupedUpdates[$update['sku_id']][] = $update;
        }

        foreach ($groupedUpdates as $skuId => $skuUpdates) {
            $sku = GoodsSkuModel::find($skuId);
            if (!$sku) {
                continue;
            }

            foreach ($skuUpdates as $update) {
                if ($update['field'] == 'thumb_url') {
                    if ($update['new_url'] === null) {
                        $sku->thumb_url = '';
                    } else {
                        $sku->thumb_url = $update['new_url'];
                    }
                } elseif ($update['field'] == 'skc_gallery') {
                    $skcGallery = json_decode($sku->skc_gallery, true);
                    if (is_array($skcGallery)) {
                        foreach ($skcGallery as &$item) {
                            if (isset($item['url']) && $item['url'] == $update['old_url']) {
                                if ($update['new_url'] === null) {
                                    // 移除失败的图片项
                                    $item = null;
                                } else {
                                    $item['url'] = $update['new_url'];
                                }
                                break;
                            }
                        }
                        // 移除null项
                        $skcGallery = array_filter($skcGallery);
                        $skcGallery = array_values($skcGallery);
                        
                        // 如果所有图片都失败了，存储空字符串而不是空数组
                        if (empty($skcGallery)) {
                            $sku->skc_gallery = '';
                        } else {
                            $sku->skc_gallery = json_encode($skcGallery, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                        }
                    }
                }
            }

            $sku->save();
        }
    }

    /**
     * 判断是否为远程URL
     * 
     * @param string $url
     * @return bool
     */
    private function isRemoteUrl(string $url): bool
    {
        return strpos($url, 'https://') === 0 || strpos($url, 'http://') === 0;
    }

    /**
     * 提取文件名
     * 
     * @param string $url
     * @return string|null
     */
    private function extractFileName(string $url): ?string
    {
        $parsedUrl = parse_url($url);
        if (!isset($parsedUrl['path'])) {
            return null;
        }

        $pathInfo = pathinfo($parsedUrl['path']);
        if (!isset($pathInfo['filename'])) {
            return null;
        }

        $extension = isset($pathInfo['extension']) ? '.' . $pathInfo['extension'] : '';
        return $pathInfo['filename'] . $extension;
    }

    /**
     * 获取图片子目录
     *
     * @param string $type
     * @return string
     */
    private function getImageSubDirectory(string $type): string
    {
        switch ($type) {
            case 'goods_pic':
            case 'goods_detail':
                // goods_detail 图片存储在与 goods_pic 相同的目录中
                return 'goods_pic';
            case 'instruction_images':
                return 'goods_instruction_images';
            case 'sku_thumb':
            case 'sku_skc_gallery':
                return 'sku';
            default:
                return 'other';
        }
    }

    /**
     * 获取处理结果
     *
     * @param GoodsModel $goods
     * @param bool $isCompleted
     * @return array
     */
    private function getProcessResult(GoodsModel $goods, bool $isCompleted = false): array
    {
        $mediaInfo = $this->getMediaInfo($goods);

        $directoryName = '未分类';
        if ($goods->directory_id > 0) {
            $directory = UserGoodsDirectoryModel::find($goods->directory_id);
            if ($directory) {
                $directoryName = $directory->name;
            }
        }

        return [
            'goods_id' => $goods->id,
            'img_local_status' => $goods->img_local_status,
            'total_images' => $mediaInfo['total_images'],
            'processed_images' => $mediaInfo['processed_images'],
            'total_videos' => $mediaInfo['total_videos'],
            'processed_videos' => $mediaInfo['processed_videos'],
            'total_pdfs' => $mediaInfo['total_pdfs'],
            'processed_pdfs' => $mediaInfo['processed_pdfs'],
            'is_completed' => $isCompleted,
            'directory_name' => $directoryName
        ];
    }

    /**
     * 获取远程文件大小
     *
     * @param string $url
     * @return int|null 文件大小（字节），失败返回null
     */
    private function getRemoteFileSize(string $url): ?int
    {
        // 应用URL协议转换
        $url = $this->convertUrlProtocol($url);

        $timeout = $this->getImageTimeout(); // 使用图片超时设置
        $connectTimeout = $this->getConnectTimeout();
        $maxRedirects = $this->getMaxRedirects();
        $userAgent = $this->getUserAgent();

        try {
            $response = Http::timeout($timeout)
                ->connectTimeout($connectTimeout)
                ->withoutVerifying()
                ->withOptions([
                    'curl' => [
                        CURLOPT_TIMEOUT => $timeout,
                        CURLOPT_CONNECTTIMEOUT => $connectTimeout,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_MAXREDIRS => $maxRedirects,
                        CURLOPT_SSL_VERIFYPEER => false,
                        CURLOPT_SSL_VERIFYHOST => false,
                        CURLOPT_USERAGENT => $userAgent
                    ]
                ])
                ->head($url);

            if ($response->successful()) {
                $contentLength = $response->header('Content-Length');
                return $contentLength ? (int) $contentLength : null;
            }
        } catch (\Exception) {
            // HEAD请求失败，尝试GET请求获取部分内容
            try {
                $response = Http::timeout($timeout)
                    ->connectTimeout($connectTimeout)
                    ->withoutVerifying()
                    ->withOptions([
                        'curl' => [
                            CURLOPT_TIMEOUT => $timeout,
                            CURLOPT_CONNECTTIMEOUT => $connectTimeout,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_MAXREDIRS => $maxRedirects,
                            CURLOPT_SSL_VERIFYPEER => false,
                            CURLOPT_SSL_VERIFYHOST => false,
                            CURLOPT_USERAGENT => $userAgent
                        ]
                    ])
                    ->withHeaders(['Range' => 'bytes=0-0'])
                    ->get($url);

                if ($response->successful()) {
                    $contentRange = $response->header('Content-Range');
                    if ($contentRange && preg_match('/bytes 0-0\/(\d+)/', $contentRange, $matches)) {
                        return (int) $matches[1];
                    }
                }
            } catch (\Exception) {
                // 忽略错误
            }
        }

        return null;
    }

    /**
     * 优化的文件下载方法
     * 根据文件大小选择合适的下载策略
     *
     * @param string $url 下载URL
     * @param string $fullPath 本地完整路径
     * @param int $timeout 超时时间
     * @return bool 下载是否成功
     */
    private function downloadFileOptimized(string $url, string $fullPath, int $timeout): bool
    {
        // 检查文件是否已存在
        if (file_exists($fullPath)) {
            return true;
        }

        // 创建目录
        $dir = dirname($fullPath);
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new \Exception("无法创建目录: {$dir}");
            }
        }

        //直接使用传统方式下载
        //return $this->downloadFileTraditional($url, $fullPath, $timeout);

        //直接使用流式下载 省略掉文件大小判断

        return $this->downloadFileStream($url, $fullPath, $timeout);

        // 获取文件大小
        /* $fileSize = $this->getRemoteFileSize($url);
        $fileSizeThreshold = $this->getFileSizeThreshold();

        // 根据文件大小选择下载策略
        if ($fileSize !== null && $fileSize <= $fileSizeThreshold) {
            // 小文件：使用传统方式下载
            return $this->downloadFileTraditional($url, $fullPath, $timeout);
        } else {
            // 大文件或无法获取大小：使用流式下载
            return $this->downloadFileStream($url, $fullPath, $timeout, $fileSize);
        } */
    }

    /**
     * 传统下载方式（适用于小文件）
     *
     * @param string $url
     * @param string $fullPath
     * @param int $timeout
     * @return bool
     */
    private function downloadFileTraditional(string $url, string $fullPath, int $timeout): bool
    {
        // 应用URL协议转换
        $url = $this->convertUrlProtocol($url);

        $retryTimes = $this->getRetryTimes();
        $connectTimeout = $this->getConnectTimeout();
        $maxRedirects = $this->getMaxRedirects();
        $userAgent = $this->getUserAgent();

        for ($i = 0; $i < $retryTimes; $i++) {
            try {
                $response = Http::timeout($timeout)
                    ->connectTimeout($connectTimeout)
                    ->withoutVerifying()
                    ->withOptions([
                        'curl' => [
                            CURLOPT_TIMEOUT => $timeout,
                            CURLOPT_CONNECTTIMEOUT => $connectTimeout,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_MAXREDIRS => $maxRedirects,
                            CURLOPT_SSL_VERIFYPEER => false,
                            CURLOPT_SSL_VERIFYHOST => false,
                            CURLOPT_USERAGENT => $userAgent
                        ]
                    ])
                    ->get($url);

                if ($response->successful()) {
                    file_put_contents($fullPath, $response->body());
                    return true;
                }
            } catch (\Exception $e) {
                if ($i == $retryTimes - 1) {
                    throw $e;
                }
                sleep(1);
            }
        }

        return false;
    }

    /**
     * 流式下载方式（适用于大文件）
     * 使用 Laravel HTTP 客户端的 sink 选项进行流式下载
     *
     * @param string $url
     * @param string $fullPath
     * @param int $timeout
     * @param int|null $expectedSize 预期文件大小
     * @return bool
     */
    private function downloadFileStream(string $url, string $fullPath, int $timeout, ?int $expectedSize = null): bool
    {
        $retryTimes = $this->getRetryTimes();
        for ($i = 0; $i < $retryTimes; $i++) {
            try {
                return $this->performLaravelStreamDownload($url, $fullPath, $timeout, $expectedSize);
            } catch (\Exception $e) {
                // 清理可能的部分文件
                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }

                if ($i == $retryTimes - 1) {
                    throw $e;
                }
                sleep(2);
            }
        }

        return false;
    }

    /**
     * 使用 Laravel HTTP 客户端执行流式下载
     *
     * @param string $url
     * @param string $fullPath
     * @param int $timeout
     * @param int|null $expectedSize
     * @return bool
     */
    private function performLaravelStreamDownload(string $url, string $fullPath, int $timeout, ?int $expectedSize = null): bool
    {
        // 应用URL协议转换
        $url = $this->convertUrlProtocol($url);

        // 创建文件流资源
        $fileStream = \GuzzleHttp\Psr7\Utils::tryFopen($fullPath, 'w');

        if (!$fileStream) {
            throw new \Exception("无法创建文件: {$fullPath}");
        }

        try {
            // 获取配置参数
            $connectTimeout = $this->getConnectTimeout();
            $maxRedirects = $this->getMaxRedirects();
            $userAgent = $this->getUserAgent();
            $streamChunkSize = $this->getStreamChunkSize();

            // 使用 Laravel HTTP 客户端的 sink 选项进行流式下载
            $response = Http::timeout($timeout)
                ->connectTimeout($connectTimeout)
                ->withoutVerifying()
                ->withOptions([
                    'sink' => $fileStream,
                    'progress' => function ($downloadTotal, $downloaded) use ($url) {
                        // 只有在启用日志时才记录进度
                        if ($this->isDownloadProgressLogEnabled() && $downloadTotal > 0) {
                            $progress = ($downloaded / $downloadTotal) * 100;
                            $this->logDownloadProgress($url, $progress, $downloaded, $downloadTotal);
                        }
                    },
                    'curl' => [
                        CURLOPT_TIMEOUT => $timeout,
                        CURLOPT_CONNECTTIMEOUT => $connectTimeout,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_MAXREDIRS => $maxRedirects,
                        CURLOPT_SSL_VERIFYPEER => false,
                        CURLOPT_SSL_VERIFYHOST => false,
                        CURLOPT_USERAGENT => $userAgent,
                        CURLOPT_BUFFERSIZE => $streamChunkSize,
                        CURLOPT_TCP_NODELAY => true,
                        CURLOPT_FRESH_CONNECT => false,
                        CURLOPT_FORBID_REUSE => false
                    ]
                ])
                ->get($url);

            // 关闭文件流
            fclose($fileStream);

            if (!$response->successful()) {
                // 下载失败，删除部分文件
                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }
                throw new \Exception("下载失败: HTTP {$response->status()}");
            }

            // 验证文件大小（如果已知预期大小）
            if ($expectedSize !== null) {
                $actualSize = filesize($fullPath);
                if ($actualSize !== $expectedSize) {
                    unlink($fullPath);
                    throw new \Exception("文件大小不匹配: 预期 {$expectedSize} 字节，实际 {$actualSize} 字节");
                }
            }

            return true;

        } catch (\Exception $e) {
            // 确保文件流被关闭
            if (is_resource($fileStream)) {
                fclose($fileStream);
            }

            // 清理部分文件
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }

            throw $e;
        }
    }

    /**
     * 记录下载进度
     *
     * @param string $url 下载URL
     * @param float $progress 进度百分比
     * @param int $downloaded 已下载字节数
     * @param int $total 总字节数
     */
    private function logDownloadProgress(string $url, float $progress, int $downloaded, int $total): void
    {
        // 检查日志开关，只有在启用时才记录
        if (!$this->isDownloadProgressLogEnabled()) {
            return;
        }

        // 只在特定进度点记录日志，避免日志过多
        $logPoints = [10, 25, 50, 75, 90, 95];
        $roundedProgress = round($progress);

        if (in_array($roundedProgress, $logPoints)) {
            $downloadedSize = $this->formatFileSize($downloaded);
            $totalSize = $this->formatFileSize($total);

            /* Log::channel('image_process')->info("下载进度", [
                'progress' => $roundedProgress,
                'downloaded' => $downloadedSize,
                'total' => $totalSize,
                'url' => $url
            ]); */
        }
    }

    /**
     * 格式化文件大小
     *
     * @param int $bytes 字节数
     * @return string 格式化后的大小
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}