<template>
  <div
    ref="statisticsCardRef"
    class="draggable-statistics-card"
    :class="{ 'collapsed': isCollapsed }"
    :style="cardPosition"
    @mousedown="startDrag"
  >
    <div class="statistics-header">
      <div class="drag-handle">
        <el-icon><Rank /></el-icon>
      </div>
      <div class="collapse-toggle" @click="toggleCollapse">
        <el-icon v-if="isCollapsed"><DArrowRight /></el-icon>
        <el-icon v-else><DArrowLeft /></el-icon>
      </div>
    </div>

    <div class="statistics-container" v-show="!isCollapsed">
      <div v-if="loading" class="statistics-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>统计中...</span>
      </div>
      <div v-else-if="statisticsData" class="statistics-content">
        <!-- 目录信息显示 -->
        <div class="directory-info">
          <el-icon><Folder /></el-icon>
          <span class="directory-label">目录：</span>
          <span class="directory-name">{{ currentDirectoryName }}</span>
        </div>

        <!-- 统计信息 -->
        <div class="statistics-info">
          <div class="statistics-item">
            <el-icon><Goods /></el-icon>
            <span class="label">商品：</span>
            <span class="value">{{ statisticsData.goods_count }}</span>
          </div>
          <div class="statistics-item">
            <el-icon><SuitcaseLine /></el-icon>
            <span class="label">SKU：</span>
            <span class="value">{{ statisticsData.sku_count }}</span>
          </div>
        </div>

        <!-- 快捷操作按钮 -->
        <div class="quick-actions">
          <el-button
            type="primary"
            size="small"
            @click="handleQuickCollectionSettings"
            class="quick-btn"
          >
            <span>采集设置</span>
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="handleQuickCheckImages"
            :loading="checkingImages"
            class="quick-btn"
          >
            <span>检查图片</span>
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="handleQuickPublishGoods"
            class="quick-btn"
          >
            <span>发布商品</span>
          </el-button>
        </div>
      </div>
      <div v-else class="statistics-empty">
        <el-icon><DataAnalysis /></el-icon>
        <span>暂无统计数据</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { Rank, DArrowRight, DArrowLeft, Loading, Folder, Goods, SuitcaseLine, DataAnalysis } from '@element-plus/icons-vue'

// 定义统计数据接口
interface GoodsStatisticsResponse {
  goods_count: number
  sku_count: number
  [key: string]: any
}

// Props
interface Props {
  statisticsData: GoodsStatisticsResponse | null
  loading: boolean
  currentDirectoryName: string
  checkingImages: boolean
}

const props = withDefaults(defineProps<Props>(), {
  statisticsData: null,
  loading: false,
  currentDirectoryName: '',
  checkingImages: false
})

// Events
const emit = defineEmits<{
  quickCollectionSettings: []
  quickCheckImages: []
  quickPublishGoods: []
}>()

// 响应式数据
const statisticsCardRef = ref<HTMLElement>()
const isCollapsed = ref(false)
const isDragging = ref(false)
const cardPosition = ref<{
  position: 'fixed'
  top?: string
  right?: string
  left?: string
  bottom?: string
  zIndex: number
}>({
  position: 'fixed',
  bottom: '20px',  // 改为底部定位
  left: '20px',    // 改为左侧定位
  zIndex: 1000
})

// 拖拽相关数据
let dragStart = { x: 0, y: 0 }
let cardStart = { x: 0, y: 0 }
let dragAnimationFrame: number | null = null
let windowSize = { width: 0, height: 0 }

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  // 保存折叠状态到本地存储
  localStorage.setItem('statisticsCardCollapsed', isCollapsed.value.toString())
}

// 缓存窗口尺寸
const updateWindowSize = () => {
  windowSize.width = window.innerWidth
  windowSize.height = window.innerHeight
}

// 开始拖拽
const startDrag = (e: MouseEvent) => {
  if ((e.target as HTMLElement).closest('.collapse-toggle')) {
    return // 如果点击的是折叠按钮，不启动拖拽
  }

  isDragging.value = true
  dragStart.x = e.clientX
  dragStart.y = e.clientY

  // 缓存窗口尺寸，避免在拖拽过程中频繁访问
  updateWindowSize()

  // 使用 requestAnimationFrame 延迟获取元素位置，避免同步的 getBoundingClientRect 调用
  requestAnimationFrame(() => {
    const rect = statisticsCardRef.value?.getBoundingClientRect()
    if (rect) {
      cardStart.x = rect.left
      cardStart.y = rect.top
    }
  })

  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
  e.preventDefault()
}

// 拖拽过程中 - 使用 requestAnimationFrame 优化性能
const onDrag = (e: MouseEvent) => {
  if (!isDragging.value) return

  // 取消之前的动画帧
  if (dragAnimationFrame) {
    cancelAnimationFrame(dragAnimationFrame)
  }

  // 使用 requestAnimationFrame 异步更新位置
  dragAnimationFrame = requestAnimationFrame(() => {
    const deltaX = e.clientX - dragStart.x
    const deltaY = e.clientY - dragStart.y

    const newLeft = cardStart.x + deltaX
    const newTop = cardStart.y + deltaY

    // 边界检查 - 使用缓存的窗口尺寸
    const maxX = windowSize.width - 240 // 卡片宽度240px
    const maxY = windowSize.height - 200 // 卡片高度约200px

    cardPosition.value = {
      position: 'fixed' as const,
      left: Math.max(0, Math.min(newLeft, maxX)) + 'px',
      top: Math.max(0, Math.min(newTop, maxY)) + 'px',
      right: 'auto',
      bottom: 'auto',
      zIndex: 1000
    }
  })
}

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)

  // 清理动画帧
  if (dragAnimationFrame) {
    cancelAnimationFrame(dragAnimationFrame)
    dragAnimationFrame = null
  }

  // 保存位置到本地存储
  localStorage.setItem('statisticsCardPosition', JSON.stringify(cardPosition.value))
}

// 初始化卡片位置和状态
const initializeCardState = () => {
  // 恢复折叠状态
  const savedCollapsed = localStorage.getItem('statisticsCardCollapsed')
  if (savedCollapsed !== null) {
    isCollapsed.value = savedCollapsed === 'true'
  }

  // 恢复位置
  const savedPosition = localStorage.getItem('statisticsCardPosition')
  if (savedPosition) {
    try {
      const position = JSON.parse(savedPosition)
      cardPosition.value = { ...cardPosition.value, ...position }
    } catch (error) {
      console.warn('Failed to parse saved card position:', error)
    }
  }
}

// 快捷操作方法
const handleQuickCollectionSettings = () => {
  emit('quickCollectionSettings')
}

const handleQuickCheckImages = () => {
  emit('quickCheckImages')
}

const handleQuickPublishGoods = () => {
  emit('quickPublishGoods')
}

// 组件挂载时初始化
onMounted(() => {
  initializeCardState()
  // 初始化窗口尺寸
  updateWindowSize()
  // 监听窗口尺寸变化
  window.addEventListener('resize', updateWindowSize)
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  window.removeEventListener('resize', updateWindowSize)

  // 清理动画帧
  if (dragAnimationFrame) {
    cancelAnimationFrame(dragAnimationFrame)
    dragAnimationFrame = null
  }
})
</script>

<style scoped>
/* 可拖拽统计信息卡片样式 */
.draggable-statistics-card {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  width: 240px;
  backdrop-filter: blur(10px);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  cursor: move;
  user-select: none;
}

.draggable-statistics-card:hover {
  box-shadow: 0 6px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.draggable-statistics-card.collapsed {
  width: 60px;
}

.draggable-statistics-card.collapsed .statistics-header {
  border-radius: 12px;
}

/* 统计卡片头部 */
.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px 12px 0 0;
}

.drag-handle {
  display: flex;
  align-items: center;
  color: #94a3b8;
  font-size: 14px;
  cursor: move;
}

.drag-handle .el-icon {
  font-size: 16px;
}

.collapse-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.collapse-toggle:hover {
  background: #66b1ff;
  transform: scale(1.1);
}

.collapse-toggle .el-icon {
  font-size: 12px;
}

.statistics-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px 20px;
}

/* 统计内容区域 */
.statistics-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 目录信息显示 */
.directory-info {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border: 1px solid #bae6fd;
  margin-bottom: 4px;
}

.directory-info .el-icon {
  font-size: 16px;
  color: #0ea5e9;
  flex-shrink: 0;
}

.directory-label {
  color: #0369a1;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

.directory-name {
  color: #0c4a6e;
  font-size: 12px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

/* 快捷操作按钮区域 */
.quick-actions {
  display: flex;
  flex-direction: row;
  gap: 4px;
  margin-top: 8px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.quick-btn {
  flex: 1;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 11px;
  transition: all 0.3s ease;
  padding: 0 4px;
}

.quick-btn .el-icon {
  font-size: 14px;
}

.quick-btn span {
  font-weight: 500;
}

.statistics-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
  padding: 8px 0;
}

.statistics-loading .el-icon {
  font-size: 16px;
}

.statistics-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.statistics-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border-left: 4px solid #409eff;
  transition: all 0.3s ease;
}

.statistics-item:hover {
  background: linear-gradient(135deg, #ecf5ff 0%, #d9ecff 100%);
  transform: translateX(4px);
}

.statistics-item .el-icon {
  font-size: 18px;
  color: #409eff;
  flex-shrink: 0;
}

.statistics-item .label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
}

.statistics-item .value {
  font-weight: bold;
  color: #409eff;
  font-size: 18px;
  margin-left: auto;
  text-shadow: 0 1px 2px rgba(64, 158, 255, 0.1);
}

.statistics-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
  padding: 16px 0;
}

.statistics-empty .el-icon {
  font-size: 24px;
  color: #c0c4cc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  /* 移动端可拖拽统计卡片调整 */
  .draggable-statistics-card {
    position: fixed;
    bottom: 10px;
    left: 10px;
    right: 10px;
    width: auto;
    border-radius: 8px;
  }

  .draggable-statistics-card .statistics-container {
    padding: 8px 12px;
  }

  .statistics-info {
    flex-direction: row;
    gap: 8px;
  }

  .statistics-item {
    flex: 1;
    padding: 6px 8px;
    flex-direction: column;
    text-align: center;
    gap: 4px;
  }

  .statistics-item .label {
    font-size: 12px;
  }

  .statistics-item .value {
    font-size: 16px;
    margin-left: 0;
  }

  /* 移动端快捷操作按钮调整 */
  .quick-actions {
    gap: 3px;
  }

  .quick-btn {
    height: 26px;
    font-size: 10px;
    padding: 0 3px;
  }

  .directory-info {
    padding: 6px 8px;
  }

  .directory-label,
  .directory-name {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕统计卡片调整 */
  .draggable-statistics-card {
    bottom: 5px;
    left: 5px;
    right: 5px;
  }

  .draggable-statistics-card .statistics-container {
    padding: 8px 12px;
  }

  .statistics-item .el-icon {
    font-size: 14px;
  }

  .statistics-item .label {
    font-size: 11px;
  }

  .statistics-item .value {
    font-size: 14px;
  }

  /* 超小屏幕快捷操作按钮调整 */
  .quick-btn {
    height: 24px;
    font-size: 9px;
    padding: 0 2px;
  }

  .directory-info {
    padding: 4px 6px;
  }

  .directory-label,
  .directory-name {
    font-size: 10px;
  }
}
</style>
