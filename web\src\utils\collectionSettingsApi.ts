/**
 * 商品采集设置API接口 - Web项目版本
 * 通过background页面发送请求
 */
import { sendRequestViaBackground } from './api'

// 接口类型定义
export interface CollectionSettings {
  has_settings: boolean
  default_directory_id: number
  default_directory_name: string
  collection_mode: number
  collection_mode_name: string
  no_remind_until: string | null
  is_in_no_remind_period: boolean
  is_default_directory_valid: boolean
  need_setup: boolean
}

export interface Directory {
  id: number
  user_id: number
  user_sub_id: number
  name: string
  description: string
  goods_count: number
}

export interface SaveSettingsData {
  default_directory_id: number
  collection_mode: number
  no_remind_24h?: boolean
}

export interface CheckNeedSetupResponse {
  need_setup: boolean
  reason: string
  settings: CollectionSettings
}

/**
 * 获取配置中的API地址
 * @param configKey 配置键名
 * @returns Promise<string> API地址
 */
const getApiUrl = (configKey: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      console.log('获取API地址响应:', response)
      if (response?.url) {
        resolve(response.url);
      } else {
        resolve('');
      }
    });
  });
};

/**
 * 获取用户采集设置
 */
export const getUserCollectionSettings = async (): Promise<CollectionSettings> => {
  const url = await getApiUrl('apiUserCollectionSettingsUrl');
  console.log('获取用户采集设置URL:', url)
  
  return sendRequestViaBackground({
    funName: 'getUserCollectionSettings',
    url,
    method: 'get',
    auth: true
  });
};

/**
 * 保存用户采集设置
 */
export const saveUserCollectionSettings = async (data: SaveSettingsData): Promise<any> => {
  const url = await getApiUrl('apiUserCollectionSettingsUrl');
  console.log('保存用户采集设置URL:', url)
  
  return sendRequestViaBackground({
    funName: 'saveUserCollectionSettings',
    url,
    method: 'post',
    data,
    auth: true
  });
};

/**
 * 获取用户可用目录列表
 */
export const getUserAvailableDirectories = async (): Promise<{ list: Directory[] }> => {
  const url = await getApiUrl('apiUserAvailableDirectoriesUrl');
  console.log('获取用户可用目录URL:', url)
  
  return sendRequestViaBackground({
    funName: 'getUserAvailableDirectories',
    url,
    method: 'get',
    auth: true
  });
};

/**
 * 检查用户是否需要设置采集参数
 */
export const checkUserNeedSetup = async (): Promise<CheckNeedSetupResponse> => {
  const url = await getApiUrl('apiUserCheckNeedSetupUrl');
  console.log('检查用户是否需要设置URL:', url)
  
  return sendRequestViaBackground({
    funName: 'checkUserNeedSetup',
    url,
    method: 'get',
    auth: true
  });
};