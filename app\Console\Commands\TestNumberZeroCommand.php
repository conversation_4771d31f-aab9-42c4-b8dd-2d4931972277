<?php
namespace App\Console\Commands;

use App\Utils\BankUtil;
use App\Models\Card\Card;
use Illuminate\Support\Str;
use App\Jobs\SSEDataDealJob;
use App\Jobs\GoodsDataDealJob;
use Illuminate\Console\Command;
use App\Service\User\UserService;
use App\Service\User\Auth\LoginService;
use Illuminate\Support\Facades\DB;
use App\Service\Index\IndexService;
use App\Utils\Tools;
use App\Utils\GoodsNameTrait;

class TestNumberZeroCommand extends Command
{
    use GoodsNameTrait;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    public function handle()
    {
        $this->test1();
    }

    public function test1(){
        $data = [
            "goodsProperty" => [
                [
                    "key" => "电源方式",
                    "values" => [
                        "USB充电"
                    ]
                ],
                [
                    "key" => "工作电压",
                    "values" => [
                        "36V及以下"
                    ]
                ],
                [
                    "key" => "电池属性",
                    "values" => [
                        "可充电电池"
                    ]
                ],
                [
                    "key" => "可充电电池",
                    "values" => [
                        "锂电池--聚合物"
                    ]
                ],
                [
                    "key" => "主体材质",
                    "values" => [
                        "ABS(ABS树脂)"
                    ]
                ],
                [
                    "key" => "品牌名",
                    "values" => [
                        "PCMOS"
                    ]
                ],
                [
                    "key" => "商品编号",
                    "values" => [
                        "XW1978645"
                    ]
                ],
                [
                    "key" => "产地",
                    "values" => [
                        "Guangdong,China"
                    ]
                ],
                [
                    "key" => "floorListProperties",
                    "values" => [
                        "紧凑设计：该产品的尺寸为1964125毫米（包括主机和笔盖），重量约为142.4克（主机和笔盖），便于携带，非常适合旅行时使用。\n\n可靠的电源：它提供稳定的输出电压为4.2V，确保小型焊接作业的可靠焊接性能。\n\n坚固的外壳：外壳材料坚固，确保设备在日常使用中也能持久耐用。",
                        "LED屏幕：配备LED屏幕，用户可以轻松监控操作状态和设置，从而提升整体用户体验。多功能性：非常适合各种小型焊接项目，特别适合爱好者、电子维修和其他应用。"
                    ]
                ]
            ],
        ];
        $goods_property = '';
        if(isset($data['goodsProperty']) && is_array($data['goodsProperty'])){
            $properties = $data['goodsProperty'];
            $property_text = [];
            $filtered_properties = [];
            
            // 遍历数组找到 floorListProperties 并过滤掉它
            foreach ($properties as $property) {
                if (isset($property['key']) && $property['key'] === 'floorListProperties') {
                    $property_text = $property['values'] ?? [];
                } else {
                    $filtered_properties[] = $property;
                }
            }
            
            $count = count($filtered_properties);
            for ($i = 0; $i < $count; $i++) {
                if ($count > 1 && $i === $count - 2) {
                    continue;
                }
                
                $property = $filtered_properties[$i];
                
                $values_string = '';
                if (isset($property['values'])) {
                    if (is_array($property['values']) && count($property['values']) > 1) {
                        $values_string = implode(',', $property['values']);
                    } elseif (isset($property['values'][0])) {
                        $values_string = (string) $property['values'][0];
                    }
                }
                
                if (!empty($values_string)) {
                     $goods_property .= $property['key'] . ':' . $values_string . ',';
                }
            }
            
            $goods_property = rtrim($goods_property, ',');
            // 处理 floorListProperties 属性，将其以换行符拼接后追加到 goods_property
            if (!empty($property_text) && is_array($property_text)) {
                $str1 = implode("\n", $property_text);
                if (!empty($str1)) {
                    $goods_property .= "\n" . $str1;
                }
            }
        }
    }



}
