/**
 * 日期时间工具函数
 * 提供日期时间相关的实用功能
 */

/**
 * 获取北京时间格式化字符串
 * @returns 格式化的北京时间字符串 (YYYY-MM-DD_HH-MM-SS)
 */
export const getBeijingTimeString = (): string => {
  const now = new Date();
  // 转换为北京时间（UTC+8）
  const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
  const year = beijingTime.getUTCFullYear();
  const month = String(beijingTime.getUTCMonth() + 1).padStart(2, '0');
  const day = String(beijingTime.getUTCDate()).padStart(2, '0');
  const hours = String(beijingTime.getUTCHours()).padStart(2, '0');
  const minutes = String(beijingTime.getUTCMinutes()).padStart(2, '0');
  const seconds = String(beijingTime.getUTCSeconds()).padStart(2, '0');

  return `${year}-${month}-${day}_${hours}-${minutes}-${seconds}`;
};

/**
 * 生成默认的导出文件名
 * @param prefix 文件名前缀，默认为 'Temu商品链接'
 * @returns 带时间戳的文件名
 */
export const generateDefaultFileName = (prefix: string = 'Temu商品链接'): string => {
  const timestamp = getBeijingTimeString();
  return `${prefix}_${timestamp}`;
};

/**
 * 格式化日期为可读字符串
 * @param date 日期对象
 * @param format 格式类型
 * @returns 格式化的日期字符串
 */
export const formatDate = (
  date: Date, 
  format: 'YYYY-MM-DD' | 'YYYY-MM-DD HH:mm:ss' | 'MM-DD HH:mm' = 'YYYY-MM-DD'
): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`;
    case 'YYYY-MM-DD HH:mm:ss':
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    case 'MM-DD HH:mm':
      return `${month}-${day} ${hours}:${minutes}`;
    default:
      return `${year}-${month}-${day}`;
  }
};