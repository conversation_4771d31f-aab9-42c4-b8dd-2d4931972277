/**
 * 筛选错误处理和用户反馈工具
 * 负责处理筛选过程中的各种错误情况和用户反馈
 */

import { ElMessage, ElNotification } from 'element-plus';

/**
 * 错误严重程度
 */
export enum ErrorSeverity {
  LOW = 'low',        // 轻微错误，不影响主要功能
  MEDIUM = 'medium',  // 中等错误，部分功能受影响
  HIGH = 'high',      // 严重错误，主要功能无法使用
  CRITICAL = 'critical' // 致命错误，系统无法继续
}

/**
 * 错误类型详细定义
 */
export enum DetailedErrorType {
  // DOM相关错误
  CONTAINER_NOT_FOUND = 'CONTAINER_NOT_FOUND',
  PRODUCTS_NOT_FOUND = 'PRODUCTS_NOT_FOUND',
  ELEMENT_ACCESS_FAILED = 'ELEMENT_ACCESS_FAILED',
  
  // 数据解析错误
  PRICE_PARSE_FAILED = 'PRICE_PARSE_FAILED',
  SALES_PARSE_FAILED = 'SALES_PARSE_FAILED',
  INVALID_DATA_FORMAT = 'INVALID_DATA_FORMAT',
  
  // 筛选执行错误
  FILTER_SETTINGS_INVALID = 'FILTER_SETTINGS_INVALID',
  FILTER_EXECUTION_TIMEOUT = 'FILTER_EXECUTION_TIMEOUT',
  DOM_MANIPULATION_FAILED = 'DOM_MANIPULATION_FAILED',
  
  // 系统错误
  MEMORY_INSUFFICIENT = 'MEMORY_INSUFFICIENT',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 错误详情接口
 */
export interface ErrorDetails {
  type: DetailedErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  suggestions: string[];
  context?: any;
  timestamp: number;
  recoverable: boolean;
}

/**
 * 进度反馈接口
 */
export interface ProgressFeedback {
  stage: string;
  progress: number; // 0-100
  message: string;
  details?: string;
  estimatedTimeRemaining?: number;
}

/**
 * 筛选结果反馈接口
 */
export interface FilterResultFeedback {
  success: boolean;
  totalProcessed: number;
  filteredCount: number;
  errorCount: number;
  warnings: string[];
  details: {
    priceFiltered: number;
    salesFiltered: number;
    localFiltered: number;
    parseErrors: number;
  };
  processingTime: number;
}

/**
 * 错误处理器类
 */
export class FilterErrorHandler {
  private errorHistory: ErrorDetails[] = [];
  private maxHistorySize = 50;
  
  /**
   * 处理错误
   * @param error 错误对象
   * @param context 错误上下文
   * @returns 错误详情
   */
  handleError(error: any, context?: any): ErrorDetails {
    const errorDetails = this.analyzeError(error, context);
    this.logError(errorDetails);
    this.showUserFeedback(errorDetails);
    this.addToHistory(errorDetails);
    
    return errorDetails;
  }
  
  /**
   * 分析错误类型和严重程度
   */
  private analyzeError(error: any, context?: any): ErrorDetails {
    let type = DetailedErrorType.UNKNOWN_ERROR;
    let severity = ErrorSeverity.MEDIUM;
    let message = '未知错误';
    let userMessage = '处理过程中发生错误，请重试';
    let suggestions: string[] = ['请刷新页面后重试'];
    let recoverable = true;
    
    // 根据错误类型进行分析
    if (error instanceof Error) {
      message = error.message;
      
      // DOM相关错误
      if (message.includes('未找到商品容器') || message.includes('container')) {
        type = DetailedErrorType.CONTAINER_NOT_FOUND;
        severity = ErrorSeverity.HIGH;
        userMessage = '未找到商品容器，请确保在Temu搜索页面执行';
        suggestions = [
          '请确保当前页面是Temu搜索结果页面',
          '等待页面完全加载后再试',
          '刷新页面后重新尝试'
        ];
      } else if (message.includes('未找到商品') || message.includes('products')) {
        type = DetailedErrorType.PRODUCTS_NOT_FOUND;
        severity = ErrorSeverity.MEDIUM;
        userMessage = '未找到商品，可能页面还未加载完成';
        suggestions = [
          '等待页面加载完成后重试',
          '检查搜索结果是否为空',
          '尝试滚动页面加载更多商品'
        ];
      } else if (message.includes('价格') || message.includes('price')) {
        type = DetailedErrorType.PRICE_PARSE_FAILED;
        severity = ErrorSeverity.LOW;
        userMessage = '部分商品价格解析失败，不影响其他功能';
        suggestions = [
          '这是正常现象，系统会跳过无法解析的商品',
          '如果大量商品解析失败，请联系技术支持'
        ];
        recoverable = true;
      } else if (message.includes('销量') || message.includes('sales')) {
        type = DetailedErrorType.SALES_PARSE_FAILED;
        severity = ErrorSeverity.LOW;
        userMessage = '部分商品销量解析失败，不影响其他功能';
        suggestions = [
          '这是正常现象，系统会跳过无法解析的商品',
          '如果大量商品解析失败，请联系技术支持'
        ];
        recoverable = true;
      } else if (message.includes('筛选设置') || message.includes('settings')) {
        type = DetailedErrorType.FILTER_SETTINGS_INVALID;
        severity = ErrorSeverity.MEDIUM;
        userMessage = '筛选设置无效，请检查输入的筛选条件';
        suggestions = [
          '检查价格范围是否合理',
          '检查销量范围是否为正整数',
          '重置筛选设置后重试'
        ];
      } else if (message.includes('权限') || message.includes('permission')) {
        type = DetailedErrorType.PERMISSION_DENIED;
        severity = ErrorSeverity.HIGH;
        userMessage = '权限不足，无法执行操作';
        suggestions = [
          '请确保扩展程序有足够权限',
          '尝试重新安装扩展程序',
          '联系技术支持'
        ];
        recoverable = false;
      }
    }
    
    return {
      type,
      severity,
      message,
      userMessage,
      suggestions,
      context,
      timestamp: Date.now(),
      recoverable
    };
  }
  
  /**
   * 记录错误到控制台
   */
  private logError(errorDetails: ErrorDetails): void {
    const logLevel = this.getLogLevel(errorDetails.severity);
    const logMessage = `[筛选错误] ${errorDetails.type}: ${errorDetails.message}`;
    
    switch (logLevel) {
      case 'error':
        console.error(logMessage, errorDetails);
        break;
      case 'warn':
        console.warn(logMessage, errorDetails);
        break;
      default:
        console.log(logMessage, errorDetails);
    }
  }
  
  /**
   * 获取日志级别
   */
  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'log' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      default:
        return 'log';
    }
  }
  
  /**
   * 显示用户反馈
   */
  private showUserFeedback(errorDetails: ErrorDetails): void {
    const { severity, userMessage, suggestions } = errorDetails;
    
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        ElNotification({
          title: '严重错误',
          message: userMessage,
          type: 'error',
          duration: 0, // 不自动关闭
          position: 'top-right'
        });
        break;
        
      case ErrorSeverity.MEDIUM:
        ElMessage({
          message: userMessage,
          type: 'warning',
          duration: 5000,
          showClose: true
        });
        break;
        
      case ErrorSeverity.LOW:
        ElMessage({
          message: userMessage,
          type: 'info',
          duration: 3000
        });
        break;
    }
    
    // 如果有建议，显示详细信息
    if (suggestions.length > 0 && severity >= ErrorSeverity.MEDIUM) {
      setTimeout(() => {
        ElNotification({
          title: '解决建议',
          message: suggestions.join('\n'),
          type: 'info',
          duration: 8000,
          position: 'bottom-right'
        });
      }, 1000);
    }
  }
  
  /**
   * 添加到错误历史
   */
  private addToHistory(errorDetails: ErrorDetails): void {
    this.errorHistory.unshift(errorDetails);
    
    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }
  
  /**
   * 获取错误历史
   */
  getErrorHistory(): ErrorDetails[] {
    return [...this.errorHistory];
  }
  
  /**
   * 清除错误历史
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
  }
  
  /**
   * 获取错误统计
   */
  getErrorStatistics(): {
    total: number;
    bySeverity: Record<ErrorSeverity, number>;
    byType: Record<DetailedErrorType, number>;
    recentErrors: number; // 最近5分钟的错误数
  } {
    const now = Date.now();
    const fiveMinutesAgo = now - 5 * 60 * 1000;
    
    const bySeverity = Object.values(ErrorSeverity).reduce((acc, severity) => {
      acc[severity] = 0;
      return acc;
    }, {} as Record<ErrorSeverity, number>);
    
    const byType = Object.values(DetailedErrorType).reduce((acc, type) => {
      acc[type] = 0;
      return acc;
    }, {} as Record<DetailedErrorType, number>);
    
    let recentErrors = 0;
    
    this.errorHistory.forEach(error => {
      bySeverity[error.severity]++;
      byType[error.type]++;
      
      if (error.timestamp > fiveMinutesAgo) {
        recentErrors++;
      }
    });
    
    return {
      total: this.errorHistory.length,
      bySeverity,
      byType,
      recentErrors
    };
  }
}

/**
 * 进度反馈管理器
 */
export class ProgressFeedbackManager {
  private currentProgress: ProgressFeedback | null = null;
  private progressCallback?: (progress: ProgressFeedback) => void;
  
  /**
   * 设置进度回调函数
   */
  setProgressCallback(callback: (progress: ProgressFeedback) => void): void {
    this.progressCallback = callback;
  }
  
  /**
   * 更新进度
   */
  updateProgress(progress: Partial<ProgressFeedback>): void {
    this.currentProgress = {
      stage: progress.stage || this.currentProgress?.stage || '处理中',
      progress: progress.progress || this.currentProgress?.progress || 0,
      message: progress.message || this.currentProgress?.message || '',
      details: progress.details,
      estimatedTimeRemaining: progress.estimatedTimeRemaining
    };
    
    // 移除ElMessage，仅通过回调函数传递进度信息
    // 调用回调函数
    if (this.progressCallback) {
      this.progressCallback(this.currentProgress);
    }
    
    console.log('筛选进度更新:', this.currentProgress);
  }
  
  /**
   * 完成进度
   */
  completeProgress(message?: string): void {
    this.updateProgress({
      progress: 100,
      message: message || '处理完成'
    });
    
    // 清除当前进度
    setTimeout(() => {
      this.currentProgress = null;
    }, 1000);
  }
  
  /**
   * 获取当前进度
   */
  getCurrentProgress(): ProgressFeedback | null {
    return this.currentProgress;
  }
}

/**
 * 结果反馈管理器
 */
export class ResultFeedbackManager {
  // 跟踪当前消息实例
  private currentMessage: any = null;
  private warningMessages: any[] = [];
  /**
   * 显示筛选结果
   */
  showFilterResult(result: FilterResultFeedback): void {
    const { success, totalProcessed, filteredCount, errorCount, details, processingTime } = result;
    
    // 关闭之前的消息
    if (this.currentMessage) {
      this.currentMessage.close();
    }
    
    if (success) {
      if (filteredCount > 0) {
        // 成功筛选了商品
        let message = `筛选完成：删除了 ${filteredCount} 个商品`;
        
        // 添加详细信息
        const detailParts: string[] = [];
        if (details.priceFiltered > 0) {
          detailParts.push(`价格筛选${details.priceFiltered}个`);
        }
        if (details.salesFiltered > 0) {
          detailParts.push(`销量筛选${details.salesFiltered}个`);
        }
        if (details.localFiltered > 0) {
          detailParts.push(`本地商品${details.localFiltered}个`);
        }
        
        if (detailParts.length > 0) {
          message += `（${detailParts.join('，')}）`;
        }
        
        this.currentMessage = ElMessage({
          message,
          type: 'success',
          duration: 4000,
          showClose: true
        });
        
        // 显示详细统计
        if (errorCount > 0 || details.parseErrors > 0) {
          setTimeout(() => {
            ElNotification({
              title: '筛选统计',
              message: `处理${totalProcessed}个商品，删除${filteredCount}个，解析失败${details.parseErrors}个，耗时${processingTime}ms`,
              type: 'info',
              duration: 6000,
              position: 'bottom-right'
            });
          }, 1000);
        }
      } else {
        // 没有筛选任何商品
        this.currentMessage = ElMessage({
          message: '筛选完成：所有商品都符合筛选条件',
          type: 'info',
          duration: 3000
        });
      }
    } else {
      // 筛选失败
      this.currentMessage = ElMessage({
        message: '筛选失败，请重试',
        type: 'error',
        duration: 4000,
        showClose: true
      });
    }
    
    // 记录到控制台
    console.log('筛选结果详情:', result);
  }
  
  /**
   * 显示警告信息
   */
  showWarnings(warnings: string[]): void {
    if (warnings.length === 0) return;
    
    // 清除之前的警告消息
    this.warningMessages.forEach(msg => msg.close());
    this.warningMessages = [];
    
    warnings.forEach((warning, index) => {
      setTimeout(() => {
        const msg = ElMessage({
          message: warning,
          type: 'warning',
          duration: 4000
        });
        this.warningMessages.push(msg);
      }, index * 500); // 错开显示时间
    });
  }

  // 清理所有消息
  clearAllMessages(): void {
    if (this.currentMessage) {
      this.currentMessage.close();
      this.currentMessage = null;
    }
    
    this.warningMessages.forEach(msg => msg.close());
    this.warningMessages = [];
  }
}

/**
 * 全局错误处理器实例
 */
export const globalErrorHandler = new FilterErrorHandler();

/**
 * 全局进度管理器实例
 */
export const globalProgressManager = new ProgressFeedbackManager();

/**
 * 全局结果反馈管理器实例
 */
export const globalResultManager = new ResultFeedbackManager();

/**
 * 安全执行函数包装器
 * 自动处理错误和提供用户反馈
 */
export const safeExecute = async <T>(
  operation: () => Promise<T>,
  context?: string
): Promise<{ success: boolean; result?: T; error?: ErrorDetails }> => {
  try {
    const result = await operation();
    return { success: true, result };
  } catch (error) {
    const errorDetails = globalErrorHandler.handleError(error, { context });
    return { success: false, error: errorDetails };
  }
};

/**
 * DOM解析失败处理器
 */
export const handleDOMParseFailure = (
  element: Element | null,
  parseType: 'price' | 'sales' | 'local',
  index?: number
): void => {
  const contextInfo = index !== undefined ? `商品${index}` : '商品';
  const errorMessage = `${contextInfo}的${parseType === 'price' ? '价格' : parseType === 'sales' ? '销量' : '本地标识'}解析失败`;
  
  console.warn(errorMessage, element);
  
  // 记录解析失败的详细信息
  const parseError = new Error(errorMessage);
  globalErrorHandler.handleError(parseError, {
    parseType,
    element: element?.outerHTML?.substring(0, 200) + '...',
    index
  });
};