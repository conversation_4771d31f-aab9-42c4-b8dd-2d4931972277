<template>
  <div class="table-section">
    <el-table
      :data="goodsList"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      border
    >
      <el-table-column type="selection" width="55" />
      <el-table-column width="80">
        <template #header>
          <div class="header-with-sort">
            <span>序号</span>
            <el-icon
              class="sort-arrow"
              :class="{ active: sortField === 'id' && sortOrder === 'desc' }"
              @click="handleSort('id', 'desc')"
            >
              <ArrowDown />
            </el-icon>
          </div>
        </template>
        <template #default="{ $index }">
          <span>{{ getIndex($index) }}</span>
        </template>
      </el-table-column>

      <!-- 商品分类列 -->
      <el-table-column label="商品分类" min-width="250">
        <template #default="{ row }">
          <div class="category-container">
            <!-- 商品ID显示 -->
            <div class="goods-id-container">
              <span class="goods-id-label">商品ID:</span>
              <span
                class="goods-id-value"
                @click="handleCopyGoodsId(row.goods_id)"
                :title="'点击复制商品ID: ' + row.goods_id"
              >
                {{ row.goods_id }}
              </span>
            </div>
            <div  class="category-name">{{ row.cat_name }}</div>
            <div  class="category-path">{{ row.front_cat_2_path_name }}</div>

            <!-- 平台关联分类信息 - 主账号显示AI分类识别 -->
            <div class="platform-relations" v-if="isMainAccount && hasValidPlatformCategories(row)">
              <div class="platform-title">AI分类识别</div>
              <div
                v-for="relation in getPlatformRelations(row)"
                :key="relation.id"
                :class="['platform-item', `platform-item--${relation.cat_type}`]"
              >
                <!-- 平台名称标签 - 右上角显示 -->
                <div :class="['platform-badge', `platform-badge--${relation.cat_type}`]">
                  <span class="category-type-label">{{ getCategoryTypeLabel(relation.cat_type) }}</span>
                </div>

                <div class="platform-categories">
                  <!-- 只显示第一个分类 -->
                  <div
                    v-if="relation.third_platform_categories.length > 0"
                    :class="['category-item', `category-item--${relation.cat_type}`]"
                  >
                    <div class="category-names">
                      <span class="name-cn">{{ relation.third_platform_categories[0].name }}</span>
                      <span class="name-tl">{{ relation.third_platform_categories[0].name_tl }}</span>
                    </div>
                    <div class="category-paths">
                      <span class="path-cn">{{ relation.third_platform_categories[0].path_name }}</span>
                      <span class="path-tl">{{ relation.third_platform_categories[0].path_name_tl }}</span>
                    </div>
                  </div>

                  <!-- 如果超过1个分类，显示更多按钮 -->
                  <el-button
                    v-if="relation.third_platform_categories.length > 1"
                    type="text"
                    size="small"
                    @click="showPlatformCategoriesDialog(relation)"
                    :class="['more-categories-btn', `more-categories-btn--${relation.cat_type}`]"
                  >
                    更多 ({{ relation.third_platform_categories.length - 1 }})
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 商品图片列 -->
      <el-table-column label="商品图片" prop="goods_pic" width="120">
        <template #default="{ row }">
          <div class="image-container">
            <el-image
              :src="getFirstImage(row)"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px; cursor: pointer;"
              @click="handleImagePreview(row)"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div class="image-count" v-if="getImageCount(row) > 0">
              <el-icon><Picture /></el-icon>
              <span>{{ getImageCount(row) }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 商品名称列 -->
      <el-table-column label="商品名称" min-width="250">
        <template #default="{ row }">
          <div class="goods-name-container">
            <div class="goods-name-text" :title="row.goods_name">{{ row.goods_name }}</div>
            <div class="goods-stats">
              <div class="sales-info" v-if="row.goods_sold_quantity !== undefined">
                <el-icon><ShoppingCart /></el-icon>
                <span>销量: {{ row.goods_sold_quantity || 0 }}</span>
              </div>
              <div class="rating-info" v-if="row.goods_score !== undefined">
                <el-icon><Star /></el-icon>
                <span>评分: {{ row.goods_score || 0 }}</span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- SKU数量列 -->
      <el-table-column label="SKU数量" width="100" prop="goods_sku_num"/>

      <!-- SKU和价格列 -->
      <el-table-column label="SKU和价格" min-width="280" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="sku-container">
            <template v-if="row.formatted_skus && row.formatted_skus.length > 0">
              <!-- 显示前3个SKU -->
              <div
                v-for="(skuItem, index) in row.formatted_skus.slice(0, 3)"
                :key="index"
                class="sku-item"
              >
                <div class="sku-content">
                  <div class="sku-image" v-if="skuItem.thumb_url">
                    <el-image
                      :src="skuItem.thumb_url"
                      fit="cover"
                      style="width: 30px; height: 30px; border-radius: 4px; cursor: pointer;"
                      @click="handleSkuImagePreview(skuItem)"
                    >
                      <template #error>
                        <div class="sku-image-slot">
                          <el-icon><Picture /></el-icon>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="sku-info">
                    <div class="sku-text">{{ skuItem.sku }}</div>
                    <div class="sku-price">{{ skuItem.price }} {{ skuItem.currentcy }}</div>
                  </div>
                </div>
              </div>
              <!-- 如果超过3个，显示更多按钮和调整价格按钮（仅主账号） -->
              <div v-if="row.formatted_skus.length > 3" class="sku-actions">
                <el-button
                  type="text"
                  size="small"
                  @click="handleSkuDetail(row)"
                  class="more-sku-btn"
                >
                  更多 ({{ row.formatted_skus.length - 3 }})
                </el-button>
                <el-button
                  v-if="isMainAccount"
                  type="warning"
                  size="small"
                  @click="handlePriceAdjustment(row)"
                  class="adjust-price-btn"
                >
                  调整价格
                </el-button>
              </div>
              <!-- 如果没有更多，只显示调整价格按钮（仅主账号） -->
              <div v-else-if="isMainAccount" class="sku-actions">
                <el-button
                  type="warning"
                  size="small"
                  @click="handlePriceAdjustment(row)"
                  class="adjust-price-btn"
                >
                  调整价格
                </el-button>
              </div>
              <!-- 价格调整信息（仅主账号） -->
              <div v-if="isMainAccount && row.price_adjustment_info?.is_adjusted" class="price-adjustment-info">
                <div class="adjustment-date">{{ formatAdjustmentDate(row.price_adjustment_info.latest_adjustment_time) }} 已调整</div>
                <el-button type="text" size="small" @click="handlePriceLogs(row)" class="adjustment-logs-btn">
                  调整记录
                </el-button>
              </div>
            </template>
            <span v-else class="no-sku">暂无SKU</span>
          </div>
        </template>
      </el-table-column>

      <!-- 更新时间列 -->
      <el-table-column prop="updated_at" width="200">
        <template #header>
          <div class="header-with-sort">
            <span>更新时间</span>
            <div class="sort-arrows">
              <el-icon
                class="sort-arrow up"
                :class="{ active: sortField === 'updated_at' && sortOrder === 'asc' }"
                @click="handleSort('updated_at', 'asc')"
              >
                <ArrowUp />
              </el-icon>
              <el-icon
                class="sort-arrow down"
                :class="{ active: sortField === 'updated_at' && sortOrder === 'desc' }"
                @click="handleSort('updated_at', 'desc')"
              >
                <ArrowDown />
              </el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div class="update-time-container">
            <!-- 主账号显示操作人信息 -->
            <div v-if="isMainAccount" class="operator-info">
              {{ row.operator_info?.type || '主账号操作' }}
              <span v-if="row.operator_info?.name" class="operator-name">
                {{ row.operator_info.name }}
              </span>
            </div>
            <div class="create-time">创建: {{ row.created_at }}</div>
            <div class="update-time">更新: {{ row.updated_at }}</div>
          </div>
        </template>
      </el-table-column>

      <!-- 操作按钮列 -->
      <el-table-column label="操作" width="350" fixed="right">
        <template #default="{ row }">
          <div class="operation-buttons">
            <el-button type="primary" size="small" @click="handleCopyLink(row)">
              复制商品链接
            </el-button>
            <!-- 主账号专属操作：仅当商品有N11平台关联时显示编辑关联分类按钮 -->
            <el-button
              v-if="isMainAccount && hasN11PlatformRelation(row)"
              type="warning"
              size="small"
              @click="handleEditCategory(row)"
            >
              编辑关联分类
            </el-button>
            <!-- 删除商品按钮 -->
            <el-button
              v-if="canDeleteGoods(row)"
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除商品
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowDown, ArrowUp, Picture, ShoppingCart, Star } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { userInfo } from '../../utils/userStore'

interface GoodsType {
  id: number
  type: number
  type_name: string
  source_url: string
  mall_id: number
  goods_id: string
  goods_name: string
  goods_thumb: string
  goods_pic: string[]
  images: string[]
  goods_video: string
  goods_sku_num: number
  first_sku: string
  first_sku_price: number
  first_sku_currentcy: string
  first_sku_thumb_url: string
  first_sku_thumb_url_h500: string
  formatted_skus: any[]
  goods_property: string
  goods_score: number
  goods_sold_quantity: number
  status: number
  cat_id: number
  cat_name: string
  front_cat_id_2: number
  front_cat_desc: string
  front_cat_2_path_name: string
  platform_relations: any[]
  is_price_modified: boolean
  price_adjustment_info: any
  created_at: string
  updated_at: string
  operator_info: {
    type: string
    name: string
    created_at: string
    updated_at: string
  }
  user_sub_id: number
}

interface Props {
  goodsList: GoodsType[]
  loading: boolean
  pagination: {
    currentPage: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
  }
  sortField: string
  sortOrder: string
  userInfo: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  selectionChange: [selection: GoodsType[]]
  sort: [data: { prop: string; order: string }]
  sizeChange: [size: number]
  currentChange: [page: number]
  imagePreview: [imageList: string[], index: number, goodsName: string]
  skuDetail: [skuList: any[], goodsName: string]
  skuImagePreview: [imageUrl: string]
  copyLink: [goods: GoodsType]
  deleteGoods: [goods: GoodsType]
  priceAdjustment: [goods: GoodsType]
  priceLogs: [goods: GoodsType]
  platformCategoriesDialog: [goods: GoodsType]
  editCategoryRelation: [goods: GoodsType]
}>()

const isMainAccount = computed(() => {
  return !userInfo.isSub
})

const getIndex = (index: number) => {
  return (props.pagination.currentPage - 1) * props.pagination.pageSize + index + 1
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 判断是否可以删除商品
const canDeleteGoods = (goods: GoodsType) => {
  if (isMainAccount.value) {
    // 主账号可以删除所有商品
    return true
  } else {
    // 子账号只能删除自己创建的商品，且必须是今天创建的
    if (goods.user_sub_id !== userInfo.userId) {
      return false
    }

    // 检查商品是否是今天创建的
    const today = new Date().toISOString().split('T')[0] // 格式：YYYY-MM-DD
    const goodsCreateDate = goods.created_at.split(' ')[0] // 提取日期部分

    return goodsCreateDate === today
  }
}

// 事件处理
const handleSelectionChange = (selection: GoodsType[]) => {
  emit('selectionChange', selection)
}

const handleSort = (field: string, order: string) => {
  emit('sort', { prop: field, order })
}

const handleSizeChange = (size: number) => {
  emit('sizeChange', size)
}

const handleCurrentChange = (page: number) => {
  emit('currentChange', page)
}

// 复制商品ID
const handleCopyGoodsId = async (goodsId: number) => {
  try {
    await navigator.clipboard.writeText(goodsId.toString())
    ElMessage.success('商品ID已复制到剪贴板')
  } catch (err) {
    // 降级方案：使用传统方法复制
    const textArea = document.createElement('textarea')
    textArea.value = goodsId.toString()
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('商品ID已复制到剪贴板')
  }
}

const handleCopyLink = (goods: GoodsType) => {
  emit('copyLink', goods)
}

const handleDelete = (goods: GoodsType) => {
  emit('deleteGoods', goods)
}

const handlePriceAdjustment = (goods: GoodsType) => {
  emit('priceAdjustment', goods)
}

const handleEditCategory = (goods: GoodsType) => {
  emit('editCategoryRelation', goods)
}

// 平台相关方法
const hasValidPlatformCategories = (row: any) => {
  return false // 子账号商品列表暂不显示平台分类
}

const getPlatformRelations = (row: any) => {
  return []
}

const getCategoryTypeLabel = (catType: string) => {
  return catType
}

const showPlatformCategoriesDialog = (relation: any) => {
  // 暂不实现
}

const hasN11PlatformRelation = (row: any) => {
  return false // 子账号商品列表暂不显示编辑关联分类
}

// 图片相关方法
const getImageCount = (row: any) => {
  if (row.images && Array.isArray(row.images)) {
    return row.images.length
  }
  if (row.goods_pic && Array.isArray(row.goods_pic)) {
    return row.goods_pic.length
  }
  return 0
}

const getFirstImage = (row: any) => {
  // 优先使用goods_thumb
  if (row.goods_thumb) {
    return row.goods_thumb
  }
  // 其次使用images数组的第一个
  if (row.images && row.images.length > 0) {
    return row.images[0]
  }
  // 最后使用goods_pic数组的第一个
  if (row.goods_pic && Array.isArray(row.goods_pic) && row.goods_pic.length > 0) {
    return row.goods_pic[0]
  }
  return ''
}

const handleImagePreview = (row: any) => {
  let imageList = []
  if (row.images && Array.isArray(row.images) && row.images.length > 0) {
    imageList = row.images
  } else if (row.goods_pic && Array.isArray(row.goods_pic) && row.goods_pic.length > 0) {
    imageList = row.goods_pic
  }

  if (imageList.length > 0) {
    emit('imagePreview', imageList, 0, row.goods_name)
  }
}

const handleSkuImagePreview = (skuItem: any) => {
  emit('skuImagePreview', skuItem.thumb_url)
}

const handleSkuDetail = (row: any) => {
  emit('skuDetail', row.formatted_skus || [], row.goods_name)
}

const handlePriceLogs = (goods: GoodsType) => {
  emit('priceLogs', goods)
}

const formatAdjustmentDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.category-container {
  padding: 8px 0;
}

/* 商品ID样式 */
.goods-id-container {
  margin-bottom: 6px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.goods-id-label {
  font-size: 12px;
  color: #606266;
  margin-right: 4px;
}

.goods-id-value {
  font-size: 12px;
  color: #409eff;
  font-weight: bold;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s;
}

.goods-id-value:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.category-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.category-path {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  word-break: break-all;
  white-space: normal;
}

/* 排序相关样式 */
.header-with-sort {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sort-arrow {
  font-size: 12px;
  color: #c0c4cc;
  cursor: pointer;
  transition: color 0.3s;
  margin-left: 8px;
}

.sort-arrow:hover {
  color: #409eff;
}

.sort-arrow.active {
  color: #409eff;
}

.image-container {
  position: relative;
  display: inline-block;
}

.image-count {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

/* 商品名称相关样式 */
.goods-name-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.goods-name-text {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

/* 更新时间容器样式 */
.update-time-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.operator-info {
  color: #409eff;
  font-weight: 500;
}

.operator-name {
  color: #303133;
  margin-left: 4px;
}

.create-time,
.update-time {
  color: #909399;
}

.goods-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sales-info,
.rating-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  width: fit-content;
}

.sales-info {
  background: #e8f4fd;
  color: #409eff;
}

.rating-info {
  background: #fdf6ec;
  color: #e6a23c;
}

.sales-info .el-icon,
.rating-info .el-icon {
  font-size: 12px;
}

/* 平台关联分类样式 */
.platform-relations {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e4e7ed;
}

.platform-title {
  font-size: 12px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.platform-item {
  position: relative;
}

.platform-badge {
  position: absolute;
  right: 0;
  font-size: 10px;
  font-weight: bold;
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 4px 10px;
  border-radius: 0 4px 0 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 系统分类样式 */
.platform-badge--system_cat {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.platform-item--system_cat {
  border-left: 3px solid #ff6b6b;
  background: linear-gradient(90deg, rgba(255, 107, 107, 0.05) 0%, transparent 100%);
}

/* 用户分类样式 */
.platform-badge--user_cat {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.platform-item--user_cat {
  border-left: 3px solid #4ecdc4;
  background: linear-gradient(90deg, rgba(78, 205, 196, 0.05) 0%, transparent 100%);
}

.category-type-label {
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
  white-space: nowrap;
}

.platform-categories {
  padding-left: 8px;
}

.category-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px 8px;
  margin-bottom: 4px;
  position: relative;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 系统分类项样式 */
.category-item--system_cat {
  background: #fff5f5;
  border: 1px solid #fed7d7;
}

.category-item--system_cat .name-cn {
  color: #c53030;
}

.category-item--system_cat .name-tl {
  color: #e53e3e;
}

.category-item--system_cat .path-cn {
  color: #e53e3e;
}

.category-item--system_cat .path-tl {
  color: #fc8181;
}

/* 用户分类项样式 */
.category-item--user_cat {
  background: #f0fdfa;
  border: 1px solid #a7f3d0;
}

.category-item--user_cat .name-cn {
  color: #065f46;
}

.category-item--user_cat .name-tl {
  color: #047857;
}

.category-item--user_cat .path-cn {
  color: #047857;
}

.category-item--user_cat .path-tl {
  color: #059669;
}

.category-names {
  margin-bottom: 3px;
}

.name-cn {
  font-size: 11px;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 1px;
}

.name-tl {
  font-size: 10px;
  color: #666;
  font-style: italic;
  display: block;
}

.category-paths {
  font-size: 10px;
  line-height: 1.3;
}

.path-cn {
  color: #666;
  display: block;
  margin-bottom: 1px;
}

.path-tl {
  color: #999;
  font-style: italic;
  display: block;
}

.more-categories-btn {
  margin-top: 4px;
  padding: 2px 8px;
  font-size: 11px;
  color: #409eff;
}

/* 系统分类更多按钮样式 */
.more-categories-btn--system_cat {
  color: #e53e3e;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 4px;
}

.more-categories-btn--system_cat:hover {
  color: #c53030;
  background: rgba(255, 107, 107, 0.2);
}

/* 用户分类更多按钮样式 */
.more-categories-btn--user_cat {
  color: #047857;
  background: rgba(78, 205, 196, 0.1);
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 4px;
}

.more-categories-btn--user_cat:hover {
  color: #065f46;
  background: rgba(78, 205, 196, 0.2);
}

/* SKU相关样式 */
.sku-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.sku-item {
  padding: 6px 8px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.sku-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sku-image {
  flex-shrink: 0;
}

.sku-image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  background: #f0f0f0;
  color: #909399;
  font-size: 14px;
  border-radius: 4px;
}

.sku-info {
  flex: 1;
  min-width: 0;
}

.sku-text {
  font-size: 12px;
  color: #333;
  margin-bottom: 2px;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sku-price {
  font-size: 11px;
  color: #e6a23c;
  font-weight: bold;
}

/* SKU操作相关样式 */
.sku-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.adjust-price-btn {
  margin-left: auto;
}

.more-sku-btn {
  margin-top: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

.no-sku {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.price-adjustment-info {
  margin-top: 8px;
  padding: 8px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 3px solid #67c23a;
}

.adjustment-date {
  font-size: 12px;
  color: #67c23a;
  margin-bottom: 4px;
}

.adjustment-logs-btn {
  font-size: 12px;
  padding: 0;
}

/* 排序箭头样式 */
.sort-arrows {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sort-arrow.up {
  margin-bottom: -3px;
}

.sort-arrow.down {
  margin-top: -3px;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
}

.operation-buttons .el-button {
  margin: 0;
}
</style>