import { ElMessage, ElNotification } from 'element-plus';
import { saveUserInfo } from './storage';
import { setLoginExpired } from './loginState';
import { handleUserLogout } from './userStore';

interface ApiResponseData<T = any> {
  status: number;
  code: number;
  msg?: string;
  data: T;
}

/**
 * Type guard to check if data is ApiResponseData<T>
 */
function isApiResponseData<T>(data: any): data is ApiResponseData<T> {
  return typeof data === 'object' && data !== null && 'status' in data && 'code' in data;
}

/**
 * 清除所有登录相关的存储数据
 */
const clearLoginData = async (): Promise<void> => {
  try {
    // 清除用户信息
    await saveUserInfo({
      isLogin: false,
      phone: '',
      expiryDate: '',
      isVip: false,
      token: ''
    });

    console.log('登录数据已清除');
  } catch (error) {
    console.error('清除登录数据失败:', error);
  }
};

/**
 * 发送登录失效消息到所有相关页面
 */
const notifyLoginExpired = (): void => {
  try {
    // 发送消息到background script
    chrome.runtime.sendMessage({
      type: 'LOGIN_EXPIRED',
      timestamp: Date.now()
    }).catch(() => {
      // 忽略发送失败的情况
    });

    console.log('登录失效通知已发送');
  } catch (error) {
    console.error('发送登录失效通知失败:', error);
  }
};

/**
 * 处理 API 响应，检查错误状态并触发通知。
 * @param responseData 接口返回的数据，通常是 response[0].data
 * @param notificationType 通知类型，可选 'message' 或 'notification'，默认为 'notification'
 */
export const handleApiResponse = <T>(responseData: ApiResponseData<T> | string, statusCode: number, notificationType: 'message' | 'notification' = 'notification'): boolean => {
  if (statusCode === 404 || statusCode === 500 || statusCode === 405 || statusCode === 403) {
    if (statusCode === 403) {
      const errorMessage = (typeof responseData === 'string' &&
        responseData.toLowerCase().includes('authentication') &&
        responseData.toLowerCase().includes('failed'))
        ? '请检查appkey是否正确'
        : '权限不足';

      ElNotification.closeAll();
      ElNotification({
        title: '错误提示',
        message: errorMessage,
        type: 'error',
        duration: 3000,
      });
      return false;
    }
    ElNotification.closeAll();
    ElNotification({
      title: '错误提示',
      message: isApiResponseData(responseData) && responseData.status === 404 ? '访问地址错误' : '系统错误，请联系管理员',
      type: 'error',
      duration: 3000,
    });
    return false;
  }
  // 检查是否存在响应数据且 status 不是 200
  console.log('responseData', responseData);
  // 检查登录失效（401状态码）
  if (isApiResponseData(responseData) && responseData.status === 401) {
    console.log('检测到登录失效，状态码401');

    // 使用统一的退出登录处理
    handleUserLogout();

    // 返回 false 表示处理了错误
    return false;
  }

  // 检查VIP权限不足（403状态码且code为1003）
  if (isApiResponseData(responseData) && responseData.status === 403 && responseData.code === 1003) {
    console.log('检测到VIP权限不足，状态码403，错误码1003');

    ElNotification.closeAll();
    ElNotification({
      title: 'VIP权限不足',
      message: responseData.msg || '您不是VIP会员或积分不足，请升级会员后使用',
      type: 'warning',
      duration: 3000,
    });

    // 跳转到卡密激活页面
    const router = (window as any).vueRouter;
    if (router) {
      router.push('/card-usage');
    } else {
      // 如果router不可用，使用location跳转
      window.location.hash = '#/card-usage';
    }

    // 返回 false 表示处理了错误
    return false;
  }

  if (isApiResponseData(responseData) && responseData.code != 1 && responseData.msg) {
    const message = `错误码: ${responseData.status}, 消息: ${responseData.msg}`;
    if (notificationType === 'notification') {
      ElNotification.closeAll();
      ElNotification({
        title: '错误提示',
        message: responseData.msg,
        type: 'error',
        duration: 3000, // 常驻通知
      });
    } else {
      ElMessage({
        message: responseData.msg,
        type: 'error',
      });
    }

    // 返回 false 表示处理了错误
    return false;
  }
  // 返回 true 表示响应正常或未处理错误
  return true;
};

/**
 * 处理 N11 API 响应，检查错误状态并触发通知。
 * @param responseData 接口返回的数据
 * @param notificationType 通知类型，可选 'message' 或 'notification'，默认为 'notification'
 */
export const handleN11ApiResponse = <T>(responseData: ApiResponseData<T> | string, statusCode: number, notificationType: 'message' | 'notification' = 'notification'): boolean => {
  console.log('处理 N11 响应:', responseData, statusCode);

  // 处理500错误
  if(statusCode == 500) {
    // 检查是否是网络错误
    if (typeof responseData === 'object' && responseData.msg &&
        responseData.msg.toLowerCase().includes('network') &&
        responseData.msg.toLowerCase().includes('error')) {
      ElNotification.closeAll();
      ElNotification({
        title: '错误提示',
        message: '当前网络无法访问N11接口,请开启VPN访问',
        type: 'error',
        duration: 3000,
      });
      return false;
    } else {
      // 对于其他500错误，记录日志但不显示通知，让调用方处理
      // 这样可以避免因为响应处理失败而中断任务流程
      console.log('N11 API 500错误，返回false让调用方处理:', responseData);
      return false;
    }
  }

  // 处理其他HTTP错误状态码
  if (statusCode === 404 || statusCode === 405 || statusCode === 403) {
    if (statusCode === 403) {
      const errorMessage = (typeof responseData === 'string' &&
        responseData.toLowerCase().includes('authentication') &&
        responseData.toLowerCase().includes('failed'))
        ? '请检查appkey是否正确'
        : '权限不足';

      ElNotification.closeAll();
      ElNotification({
        title: '错误提示',
        message: errorMessage,
        type: 'error',
        duration: 3000,
      });
      return false;
    }
    ElNotification.closeAll();
    ElNotification({
      title: '错误提示',
      message: isApiResponseData(responseData) && responseData.status === 404 ? '访问地址错误' : '系统错误，请联系管理员',
      type: 'error',
      duration: 3000,
    });
    return false;
  }

  // 对于200状态码，即使有一些异常情况，也返回true让调用方处理
  // 这样可以避免正常的API响应被错误地标记为失败
  if (statusCode === 200) {
    return true;
  }

  return true;
};

export const handleDeepSeekApiResponse = <T>(responseData: ApiResponseData<T> | string, statusCode: number, notificationType: 'message' | 'notification' = 'notification'): boolean => {
  console.log('处理 DeepSeek 响应:', responseData, statusCode);

  // 处理500错误
  if(statusCode == 500) {
    // 检查是否是网络错误
    if (typeof responseData === 'object' && responseData.msg &&
        responseData.msg.toLowerCase().includes('network') &&
        responseData.msg.toLowerCase().includes('error')) {
      ElNotification.closeAll();
      ElNotification({
        title: '错误提示',
        message: '当前网络无法访问deepseek接口',
        type: 'error',
        duration: 3000,
      });
      return false;
    } else {
      // 对于其他500错误，记录日志但不显示通知，让调用方处理
      // 这样可以避免因为响应处理失败而中断任务流程
      console.log('DeepSeek API 500错误，返回false让调用方处理:', responseData);
      return false;
    }
  }

  if(statusCode === 401) {
    const errorMessage = typeof responseData === 'object' && (responseData as any)?.error?.message
      ? (responseData as any).error.message
      : '未知错误';
    ElNotification.closeAll();
    ElNotification({
      title: '错误提示',
      message: 'deepseek接口返回错误信息,' + errorMessage,
      type: 'error',
      duration: 3000,
    });
    return false;
  }

  if (statusCode === 200) {
    return true;
  }

  return true;
};
