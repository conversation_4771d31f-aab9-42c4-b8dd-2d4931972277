<?php

namespace App\Service\User;

use App\Models\User\UserGoodsModel;
use App\Models\User\UserTaskModel;
use App\Models\User\UserTaskDetailModel;
use App\Models\User\UserAccountModel;
use App\Models\User\UserAiKeyModel;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use App\Models\User\UserGoodsCatRelationModel;
use App\Models\System\CatRelationSystemModel;
use App\Service\BaseService;
use App\Utils\Jwt\Jwt;
use App\Exceptions\MyException;
use App\Models\User\UserTaskCurrentcyModel;
use App\Models\User\User as UserModel;
use App\Service\User\UserPointsService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserTaskNewService extends BaseService
{
    protected UserTaskModel $userTaskModel;
    protected UserTaskDetailModel $userTaskDetailModel;
    protected UserAccountModel $userAccountModel;
    protected UserAiKeyModel $userAiKeyModel;
    protected GoodsModel $goodsModel;
    protected GoodsSkuModel $goodsSkuModel;
    protected CatRelationSystemModel $catRelationModel;
    protected UserPointsService $userPointsService;

    public function __construct(
        Jwt $jwtService,
        UserTaskModel $userTaskModel,
        UserTaskDetailModel $userTaskDetailModel,
        UserAccountModel $userAccountModel,
        UserAiKeyModel $userAiKeyModel,
        GoodsModel $goodsModel,
        GoodsSkuModel $goodsSkuModel,
        CatRelationSystemModel $catRelationModel,
        UserPointsService $userPointsService
    ) {
        $this->userTaskModel = $userTaskModel;
        $this->userTaskDetailModel = $userTaskDetailModel;
        $this->userAccountModel = $userAccountModel;
        $this->userAiKeyModel = $userAiKeyModel;
        $this->goodsModel = $goodsModel;
        $this->goodsSkuModel = $goodsSkuModel;
        $this->catRelationModel = $catRelationModel;
        $this->userPointsService = $userPointsService;
        parent::__construct($jwtService);
    }

    /**
     * 生成带前缀的随机字符串
     * 
     * @param string $prefix 字符串前缀
     * @param int $length 随机部分的长度，默认为10
     * @param bool $digits_only 是否仅生成数字，默认为false
     * @return string 前缀+随机字符串
     */
    private function generatePrefixedRandomString(string $prefix = '', int $length = 10, bool $digits_only = false): string
    {
        $randomPart = '';
        
        if ($digits_only) {
            // 仅生成数字，确保不以0开头
            for ($i = 0; $i < $length; $i++) {
                if ($i === 0) {
                    // 第一位从1-9中选择，避免以0开头
                    $characters = '123456789';
                } else {
                    // 其余位从0-9中选择
                    $characters = '0123456789';
                }
                $randomPart .= $characters[rand(0, strlen($characters) - 1)];
            }
        } else {
            // 生成随机字符串，包含字母和数字
            $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            for ($i = 0; $i < $length; $i++) {
                $randomPart .= $characters[rand(0, strlen($characters) - 1)];
            }
        }
        
        return $prefix . $randomPart;
    }

    /**
     * 生成任务详情记录
     */
    public function generateTaskDetails(int $userId, array $params): array
    {
        $taskId = $params['task_id'];
        $offset = $params['offset'] ?? 0;
        $limit = $params['limit'] ?? 100;

        // 验证任务是否存在且属于当前用户
        $task = $this->userTaskModel->where('user_id', $userId)->where('id', $taskId)->first();
        if (!$task) {
            throw new MyException('您没有权限生成该任务详情记录');
        }

        if($task->task_over == 1){
            // 如果任务详情已生成但积分未扣除，需要执行积分扣除
            if($task->points_deduction_status == 0 && $task->sku_count > 0){
                $this->deductPointsForTask($userId, $taskId, $task->sku_count);
            }
            throw new MyException('该任务已完成');
        }

        $store_info = UserAccountModel::where('user_id', $task->user_id)->where('id', $task->user_account_id)->first();
        if(!$store_info){
            throw new MyException('店铺信息不存在');
        }

        // 如果任务详情记录已生成，则不重复生成 这样就避免了再次查询条件 生成的任务数量不准确
        // 因为比如范围是全部采集的商品 第一次生成任务记录后 然后又采集了商品 这样就会导致生成的任务数量不准确
        if($task->goods_statistics_status == 1){
            // 如果任务详情已生成但积分未扣除，需要执行积分扣除
            if($task->points_deduction_status == 0 && $task->sku_count > 0){
                $this->deductPointsForTask($userId, $taskId, $task->sku_count);
            }

            return [
                'has_more' => false,
                'store_brand' => $store_info->brand ?? '',
                'processed_count' => $task->sku_count,
                'total_count' => $task->sku_count,
                'current_offset' => $task->sku_count
            ];
        }

        //实时获取用户积分
        $user_info = UserModel::where('id', $userId)->select('id','points','is_admin')->first();
        if(!$user_info){
            throw new MyException('用户信息不存在');
        }
        $points = $user_info->points;
        if($user_info->is_admin != 1 && $points < $task->sku_count){
            throw new MyException('您的积分不足，需要'.$task->sku_count.'积分，当前积分：'.$points);
        }

        // 获取任务的商品数据
        $goodsQuery = $this->goodsModel
            ->where('user_id', $userId)
            ->where('directory_id', $task->directory_id)
            ->where('status', 1); // 只处理正常状态的商品

        // 如果是指定商品，添加商品ID筛选
        if ($task->is_selected == 1 && $task->selected_ids) {
            $goodsIds = is_array($task->selected_ids) ? $task->selected_ids : json_decode($task->selected_ids, true);
            $goodsQuery->whereIn('id', $goodsIds);
        } else {
            // 按时间范围筛选
            if ($task->day_start && $task->day_end) {
                $goodsQuery->whereDate('created_at', '>=', $task->day_start)
                           ->whereDate('created_at', '<=', $task->day_end);
            }
        }

        // 价格区间筛选：通过关联SKU表进行价格范围过滤（只有大于0的价格才进行筛选）
        if (($task->price_min !== null && $task->price_min > 0) || ($task->price_max !== null && $task->price_max > 0)) {
            $goodsQuery->whereHas('skus', function ($skuQuery) use ($task) {
                if ($task->price_min !== null && $task->price_min > 0) {
                    $skuQuery->where('price', '>=', $task->price_min);
                }
                if ($task->price_max !== null && $task->price_max > 0) {
                    $skuQuery->where('price', '<=', $task->price_max);
                }
            });
        }

        $processedCount = 0;
        $totalCount = $goodsQuery->count();

        // 分页获取商品
        $goods = $goodsQuery->skip($offset)->take($limit)->orderBy('id', $task->sort_order)->get();

        // 初始化批量插入数组
        $batchInsertData = [];



        // 根据account_setting_type决定使用哪个价格配置，并存储到局部变量
        if ($task->account_setting_type == 1) {
            // 使用店铺配置
            $current_price_rate = $store_info->price_rate;
            $current_price_add = $store_info->price_add;
            $current_price_subtract = $store_info->price_subtract;
            $quantity = $store_info->quantity;
            $vat_rate = $store_info->vat_rate;
            $preparing_day = $store_info->preparing_day;

        } else {
            // 使用任务配置
            $current_price_rate = $task->price_rate;
            $current_price_add = $task->price_add;
            $current_price_subtract = $task->price_subtract;
            $quantity = $task->quantity;
            $vat_rate = $task->vat_rate;
            $preparing_day = $task->preparing_day;
        }

        $publish_currency = $task->currentcy;
        if(empty($publish_currency)){
            $publish_currency = 'TL';
        }
        $publish_currency = strtoupper($publish_currency);
        //中间汇率货币
        $exchange_rate_currency = $task->exchange_rate_currency;
        if(empty($exchange_rate_currency)){
            $exchange_rate_currency = 'TL';
        }
        $exchange_rate_currency = strtoupper($exchange_rate_currency);

        // 批量预加载汇率数据
        $exchangeRates = $this->preloadExchangeRates($taskId);

        foreach ($goods as $good) {
            // 获取商品的SKU列表
            $skus = $this->goodsSkuModel->where('user_goods_id', $good->id)->get();
            if ($skus->isEmpty()) {
                //Log::info('商品'.$good->id.'没有SKU');
                continue;
            }
            //同一个商品上传到相同的分类ID
            $categoryId = 0;

            // 优先查询用户自定义的分类关联记录
            $userCatRelation = UserGoodsCatRelationModel::query()
                ->where('user_id', $userId)
                ->where('goods_id', $good->id)
                ->where('third_platform_id', 2)
                ->first();

            if ($userCatRelation && $userCatRelation->third_platform_cat_id > 0) {
                // 如果找到用户自定义分类关联且分类ID大于0，直接使用
                $categoryId = $userCatRelation->third_platform_cat_id;
            } elseif ($good->cat_id > 0) {
                // 如果未找到用户自定义分类或分类ID为0，则使用原有的系统分类逻辑
                $cat_third_ids = CatRelationSystemModel::query()->where('platform_id', 1)
                    ->where('third_platform_id', 2)
                    ->where('cat_platform_id', $good->cat_id)
                    ->value('cat_third_ids');

                if (!empty($cat_third_ids)) {
                    $cat_third_ids = json_decode($cat_third_ids, true);
                    $cat_third_ids = array_values(array_filter(array_unique($cat_third_ids)));
                    if (count($cat_third_ids) > 1) {
                        $categoryId = $cat_third_ids[array_rand($cat_third_ids, 1)];
                    } else {
                        $categoryId = $cat_third_ids[0];
                    }
                    if (!$categoryId) {
                        $categoryId = 0;
                    }
                }
            }
            if ($categoryId <= 0) {
                //Log::info('商品'.$good->id.'的分类'.$good->cat_id.'没有对应的N11上传分类ID');
                continue;
            }
            foreach ($skus as $sku) {
                // 价格区间验证：如果设置了价格区间，检查SKU价格是否在范围内（只有大于0的价格才进行验证）
                if ($task->price_min !== null && $task->price_min > 0 && $sku->price < $task->price_min) {
                    continue; // 跳过价格低于最低价格的SKU
                }
                if ($task->price_max !== null && $task->price_max > 0 && $sku->price > $task->price_max) {
                    continue; // 跳过价格高于最高价格的SKU
                }

                // 生成带前缀的随机ID
                $product_main_id = $this->generatePrefixedRandomString('', 15, true); // 仅生成数字
                $stock_code = $this->generatePrefixedRandomString('sc_', 15, false); // 默认字母数字混合
                //商品原价和货币单位
                $currentcy_from = strtoupper($sku->currentcy);
                $price = $sku->price;
                
                //如果商品原价和货币单位与汇率基准货币相同，则使用商品原价
                if($currentcy_from == $exchange_rate_currency){
                    //Log::info("商品".$good->id."的SKU".$sku->id."的货币单位".$currentcy_from."与汇率基准货币".$exchange_rate_currency."相同，使用商品原价");
                    $price_cal = $price;
                }else{
                    //使用预加载的汇率数据
                    $exchangeRateInfo = $this->getExchangeRateFromCache($exchangeRates, $currentcy_from, $exchange_rate_currency);
                    if(!$exchangeRateInfo){
                        //Log::info("商品".$good->id."的SKU".$sku->id."的货币单位".$currentcy_from."与汇率基准货币".$exchange_rate_currency."不同，但汇率不存在，跳过");
                        continue;
                    }
                    // 根据汇率方向计算价格
                    if($exchangeRateInfo['is_multiply']){
                        $price_cal = $price * $exchangeRateInfo['rate'];
                    }else{
                        $price_cal = $price / $exchangeRateInfo['rate'];
                    }
                    //Log::info("商品".$good->id."的SKU".$sku->id."的货币单位".$currentcy_from."与汇率基准货币".$exchange_rate_currency."不同，汇率：".$exchangeRateInfo['rate']."，使用汇率后的价格".$price_cal);
                }

                // 计算第三方平台价格：原价 * 倍数 + 加值 - 减值
                $price_third_calculated = $this->calculateThirdPartyPrice($price_cal, $current_price_rate, $current_price_add, $current_price_subtract);
                //Log::info("商品".$good->id."的SKU".$sku->id."的货币单位".$currentcy_from.",汇率基准货币".$exchange_rate_currency.",按汇率基准货币调整计算后的价格".$price_third_calculated);
                // 生成随机上浮倍数，确保在后续汇率转换中使用相同的倍数
                $random_markup = $this->generateRandomMarkup();
                $price_list_third_calculated = round($price_third_calculated * $random_markup, 2);
                //如果汇率基准货币与发布货币不同，则使用汇率后的价格
                if($exchange_rate_currency != $publish_currency){
                    //使用预加载的汇率数据
                    $exchangeRateInfo = $this->getExchangeRateFromCache($exchangeRates, $exchange_rate_currency, $publish_currency);
                    if(!$exchangeRateInfo){
                        //Log::info("商品".$good->id."的SKU".$sku->id."的货币单位".$exchange_rate_currency."与发布货币".$publish_currency."不同，但汇率双向查询均不存在，跳过");
                        continue;
                    }

                    // 添加除零检查
                    if($exchangeRateInfo['rate'] <= 0){
                        //Log::error("商品".$good->id."的SKU".$sku->id."汇率数据异常：rate=" . $exchangeRateInfo['rate']);
                        continue;
                    }
                    //Log::info("商品".$good->id."的SKU".$sku->id."的货币单位".$exchange_rate_currency."与发布货币".$publish_currency."不同，原价格".$price_third_calculated.",汇率：".$exchangeRateInfo['rate']);
                    if($exchangeRateInfo['is_multiply']){
                        $price_third_calculated = round($price_third_calculated * $exchangeRateInfo['rate'], 2); 
                        //Log::info("乘以该汇率后的价格".$price_third_calculated);
                    }else{
                        $price_third_calculated = round($price_third_calculated / $exchangeRateInfo['rate'], 2);
                        //Log::info("除以该汇率后的价格".$price_third_calculated);
                    }

                    // 使用相同的随机倍数重新计算展示价格
                    $price_list_third_calculated = round($price_third_calculated * $random_markup, 2);
                }
                
                // 准备插入数据
                $detailData = [
                    'user_id' => $userId,
                    'task_id' => $taskId,
                    'directory_id' => $task->directory_id,
                    'user_account_id' => $task->user_account_id,
                    'user_goods_id' => $good->id,
                    'user_goods_sku_id' => $sku->id,
                    'goods_id' => $sku->goods_id,
                    'goods_name' => $good->goods_name,
                    'goods_name_ai' => '',
                    'is_name_ai' => 0,
                    'goods_property' => $good->goods_property,
                    'goods_property_ai' => '',
                    'is_property_ai' => 0,
                    'thumb_url' => uploadFilePath($sku->thumb_url),
                    'currentcy_goods' => $sku->currentcy,
                    'currentcy' => $publish_currency,
                    'price' => $sku->price,
                    'price_third' => $price_third_calculated,
                    'price_list_third' => $price_list_third_calculated,
                    'quantity' => $quantity,
                    'vat_rate' => $vat_rate,
                    'preparing_day' => $preparing_day,
                    'spec_key_values' => $sku->spec_key_values,
                    'spec_values' => $sku->spec_values,
                    'is_skc_gallery' => $sku->is_skc_gallery,
                    'category_id' => $categoryId,
                    'product_main_id' => $product_main_id,
                    'stock_code' => $stock_code,
                    'status' => 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // 收集数据到批量插入数组
                $batchInsertData[] = $detailData;
            }
        }

        // 分批插入数据
        if (!empty($batchInsertData)) {
            $batchSize = config('jk.batch_insert.task_details_batch_size', 1000);
            $this->batchInsertTaskDetails($batchInsertData, $batchSize);
        }

        // 计算当前已处理的记录数量
        $processedCount = $this->userTaskDetailModel->where('task_id', $taskId)->count();

        // 检查是否还有更多数据
        $hasMore = ($offset + $limit) < $totalCount;
        $currentOffset = $offset + $limit;

        if(!$hasMore){
            // 统计该任务下不同商品的数量
            $goodsCount = $this->userTaskDetailModel
                ->where('task_id', $taskId)
                ->distinct('user_goods_id')
                ->count('user_goods_id');

            // 准备更新数据
            $updateData = [
                'goods_count' => $goodsCount,
                'sku_count' => $processedCount,
                'task_count' => $processedCount,
                'goods_statistics_status' => 1,
                'updated_at' => now()
            ];

            // 如果商品数量和详细记录数量都为0，说明所有记录都是重复的，直接设置为已完成
            if ($goodsCount == 0 && $processedCount == 0) {
                $updateData['goods_ai_name_status'] = 1;
                $updateData['points_deduction_status'] = 1;
                $updateData['task_over'] = 1;
                $updateData['memo'] = '商品任务记录均重复，没有新增任务,直接设置为已完成';
            }

            // 更新任务统计
            $this->userTaskModel->where('id', $taskId)->update($updateData);

            // 如果有实际处理的任务数量且积分未扣除，则扣除积分
            if ($processedCount > 0 && $task->points_deduction_status == 0) {
                $this->deductPointsForTask($userId, $taskId, $processedCount);
            }
        }

        return [
            'has_more' => $hasMore,
            'store_brand' => $store_info->brand ?? '',
            'processed_count' => $processedCount,
            'total_count' => $totalCount,
            'current_offset' => $currentOffset,
        ];
    }

    /**
     * 扣除任务积分的封装方法
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @param int $pointsToDeduct 要扣除的积分数量
     * @throws MyException
     */
    private function deductPointsForTask(int $userId, int $taskId, int $pointsToDeduct): void
    {
        try {
            // 不传递description参数，让UserPointsService自动生成详细描述
            $this->userPointsService->deductPointsForTask($userId, $taskId, $pointsToDeduct);
        } catch (MyException $e) {
            Log::error("任务 {$taskId} 扣除用户 {$userId} 积分失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取下一个待AI改写的任务详情
     */
    public function getNextAiTask(int $userId, int $taskId): array
    {
        // 验证任务是否存在且属于当前用户
        $task = $this->userTaskModel->where('user_id', $userId)->where('id', $taskId)->first();
        if (!$task) {
            throw new MyException('任务不存在或无权限');
        }

        if($task->points_deduction_status == 0){
            //这里再次验证任务是否扣除积分 
            throw new MyException('任务状态异常');
        }

        if($task->task_over == 1){
            throw new MyException('任务已完成');
        }

        if($task->goods_statistics_status == 0){
            throw new MyException('任务详情记录未生成');
        }

        if($task->goods_ai_name_status == 1){
            return ['has_task' => false, 'task_detail' => null];
        }

        // 获取下一个未处理的任务详情
        $taskDetail = $this->userTaskDetailModel
            ->where('user_id', $userId)
            ->where('task_id', $taskId)
            ->where(function($query) {
                $query->where('is_name_ai', 0)
                      ->orWhere('is_property_ai', 0);
            })
            ->orderBy('id', 'asc')
            ->first();

        if (!$taskDetail) {
            return ['has_task' => false, 'task_detail' => null];
        }

        return [
            'has_task' => true,
            'task_detail' => [
                'id' => $taskDetail->id,
                'goods_name' => $taskDetail->goods_name,
                'goods_name_ai' => $taskDetail->goods_name_ai,
                'goods_property' => $taskDetail->goods_property,
                'goods_property_ai' => $taskDetail->goods_property_ai,
                'is_name_ai' => $taskDetail->is_name_ai,
                'is_property_ai' => $taskDetail->is_property_ai,
                'spec_values' => $taskDetail->spec_values,
                'user_account_id' => $taskDetail->user_account_id,
                'thumb_url' => uploadFilePath($taskDetail->thumb_url),
                'price' => $taskDetail->price,
                'currentcy_goods' => $taskDetail->currentcy_goods,//商品原价货币单位
                'price_third' => $taskDetail->price_third,
                'currentcy' => $taskDetail->currentcy //商品发布货币单位
                
            ]
        ];
    }

    /**
     * 更新任务详情AI改写结果
     */
    public function updateAiResult(int $userId, array $params): array
    {
        $taskDetailId = $params['task_detail_id'];
        $goodsNameAi = $params['goods_name_ai'] ?? '';
        $goodsPropertyAi = $params['goods_property_ai'] ?? '';
        if($goodsPropertyAi == 0){
            $goodsPropertyAi = '';
        }

        // 验证任务详情是否存在且属于当前用户
        $taskDetail = $this->userTaskDetailModel
            ->where('user_id', $userId)
            ->where('id', $taskDetailId)
            ->first();

        if (!$taskDetail) {
            throw new MyException('任务详情不存在或无权限');
        }

        if($taskDetail->is_property_ai == 0 && $goodsPropertyAi == ''){
            //如果没有传递 就表示不需要修改商品属性 使用原来的商品属性 这里必须得赋值
            $goodsPropertyAi = $taskDetail->goods_property;
        }

        // 更新数据
        $updateData = [];
        if ($goodsNameAi !== '') {
            $updateData['goods_name_ai'] = $goodsNameAi;
            $updateData['is_name_ai'] = 1;
        }
        if ($goodsPropertyAi !== '') {
            $updateData['goods_property_ai'] = $goodsPropertyAi;
            $updateData['is_property_ai'] = 1;
            //把相同goods_id的商品的is_property_ai设置为1 设置为相同商品属性描述
            $this->userTaskDetailModel
                ->where('user_id', $userId)
                ->where('user_goods_id', $taskDetail->user_goods_id)
                ->update(['goods_property_ai' => $goodsPropertyAi,'is_property_ai' => 1]);
        }

        if (!empty($updateData)) {
            $updateData['updated_at'] = now();
            $this->userTaskDetailModel->where('id', $taskDetailId)->update($updateData);
        }

        return ['updated' => true];
    }

    /**
     * 获取用户随机AI Key
     */
    public function getRandomAiKey(int $userId): array
    {
        $aiKey = $this->userAiKeyModel
            ->where('user_id', $userId)
            ->where('status', 1)
            ->where('ai_type', 1) // DeepSeek
            ->inRandomOrder()
            ->first();

        if (!$aiKey) {
            throw new MyException('未找到可用的AI Key');
        }

        return ['ai_key' => $aiKey->ai_key];
    }

    /**
     * 获取任务AI改写进度
     */
    public function getAiProgress(int $userId, int $taskId): array
    {
        // 验证任务是否存在且属于当前用户
        $task = $this->userTaskModel->where('user_id', $userId)->where('id', $taskId)->first();
        if (!$task) {
            throw new MyException('任务不存在');
        }

        // 统计进度
        $totalCount = $this->userTaskDetailModel
            ->where('user_id', $userId)
            ->where('task_id', $taskId)
            ->count();

        $completedCount = $this->userTaskDetailModel
            ->where('user_id', $userId)
            ->where('task_id', $taskId)
            ->where('is_name_ai', 1)
            ->count();

        $remainingCount = $totalCount - $completedCount;
        $progressPercentage = $totalCount > 0 ? round(($completedCount / $totalCount) * 100, 2) : 0;

        return [
            'total_count' => $totalCount,
            'completed_count' => $completedCount,
            'remaining_count' => $remainingCount,
            'progress_percentage' => $progressPercentage,
        ];
    }

    /**
     * 完成任务AI改写
     */
    public function completeAiTask(int $userId, int $taskId): array
    {
        // 验证任务是否存在且属于当前用户
        $task = $this->userTaskModel->where('user_id', $userId)->where('id', $taskId)->first();
        if (!$task) {
            throw new MyException('任务不存在或无权限');
        }

        // 检查所有任务详情是否都已完成AI改写
        $pendingCount = $this->userTaskDetailModel
            ->where('user_id', $userId)
            ->where('task_id', $taskId)
            ->where('is_name_ai', 0)
            ->count();

        if ($pendingCount > 0) {
            throw new MyException('还有 ' . $pendingCount . ' 个商品未完成AI改写');
        }

        // 更新任务状态
        $this->userTaskModel->where('id', $taskId)->update([
            'goods_ai_name_status' => 1,
            'updated_at' => now(),
        ]);

        return ['completed' => true];
    }

    /**
     * 批量预加载汇率数据
     * @param int $taskId
     * @return array 格式：['from_currency:to_currency' => ['rate' => float, 'is_multiply' => bool]]
     */
    private function preloadExchangeRates(int $taskId): array
    {
        $exchangeRates = [];

        // 查询当前任务的所有汇率数据
        $rates = UserTaskCurrentcyModel::query()
            ->where('task_id', $taskId)
            ->orderBy('id', 'desc')
            ->get(['currentcy_from', 'currentcy_to', 'rate']);

        foreach ($rates as $rate) {
            $key = $rate->currentcy_from . ':' . $rate->currentcy_to;
            // 如果同一个货币对有多个汇率，保留最新的（由于orderBy desc，第一个就是最新的）
            if (!isset($exchangeRates[$key])) {
                $exchangeRates[$key] = [
                    'rate' => $rate->rate,
                    'is_multiply' => true
                ];
            }
        }

        return $exchangeRates;
    }

    /**
     * 从缓存的汇率数据中获取汇率信息
     * @param array $exchangeRates 预加载的汇率数据
     * @param string $fromCurrency 源货币
     * @param string $toCurrency 目标货币
     * @return array|null ['rate' => float, 'is_multiply' => bool] 或 null
     */
    private function getExchangeRateFromCache(array $exchangeRates, string $fromCurrency, string $toCurrency): ?array
    {
        // 先查找正向匹配 (from→to)
        $forwardKey = $fromCurrency . ':' . $toCurrency;
        if (isset($exchangeRates[$forwardKey])) {
            return $exchangeRates[$forwardKey];
        }

        // 查找反向匹配 (to→from)
        $reverseKey = $toCurrency . ':' . $fromCurrency;
        if (isset($exchangeRates[$reverseKey])) {
            return [
                'rate' => $exchangeRates[$reverseKey]['rate'],
                'is_multiply' => false // 反向匹配需要除法操作
            ];
        }

        return null;
    }

    /**
     * 计算第三方平台价格
     * @param float $basePrice 基准价格
     * @param float $priceRate 价格倍数
     * @param float $priceAdd 价格加值
     * @param float $priceSubtract 价格减值
     * @return float
     */
    private function calculateThirdPartyPrice(float $basePrice, float $priceRate, float $priceAdd, float $priceSubtract): float
    {
        return round(($basePrice * $priceRate) + $priceAdd - $priceSubtract, 2);
    }

    /**
     * 生成随机价格上浮倍数
     * @param int $minPercent 最小上浮百分比（默认10）
     * @param int $maxPercent 最大上浮百分比（默认15）
     * @return float
     */
    private function generateRandomMarkup(int $minPercent = 10, int $maxPercent = 15): float
    {
        return 1 + mt_rand($minPercent, $maxPercent) / 100.0;
    }

    /**
     * 分批插入任务详情数据
     * @param array $batchInsertData 要插入的数据数组
     * @param int $batchSize 每批次插入的数量，默认1000条
     * @return int 成功插入的记录数
     * @throws \Exception
     */
    private function batchInsertTaskDetails(array $batchInsertData, int $batchSize = 1000): int
    {
        if (empty($batchInsertData)) {
            return 0;
        }

        $totalCount = count($batchInsertData);
        $chunks = array_chunk($batchInsertData, $batchSize);
        $processedCount = 0;
        $failedBatches = [];

        foreach ($chunks as $index => $chunk) {
            try {
                $this->userTaskDetailModel->insertOrIgnore($chunk);
                $processedCount += count($chunk);
            } catch (\Exception $e) {
                $failedBatches[] = [
                    'batch_index' => $index + 1,
                    'batch_size' => count($chunk),
                    'error' => $e->getMessage()
                ];
                // 如果是占位符过多的错误，尝试更小的批次
                if (strpos($e->getMessage(), 'too many placeholders') !== false) {
                    $smallerBatchSize = max(100, intval($batchSize / 2));
                    $smallerChunks = array_chunk($chunk, $smallerBatchSize);

                    foreach ($smallerChunks as $smallChunk) {
                        try {
                            $this->userTaskDetailModel->insertOrIgnore($smallChunk);
                            $processedCount += count($smallChunk);
                        } catch (\Exception $retryException) {
                            \Log::error("小批次插入仍然失败：" . $retryException->getMessage());
                        }
                    }
                } else {
                }
            }
        }
        return $processedCount;
    }

}