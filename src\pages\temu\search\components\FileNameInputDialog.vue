<template>
  <el-dialog
    :model-value="visible"
    title="设置文件名"
    width="500px"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @update:model-value="handleVisibilityChange"
  >
    <div class="filename-content">
      <el-form :model="form" label-width="80px">
        <el-form-item label="文件名:">
          <el-input
            v-model="form.name"
            placeholder="请输入文件名"
            clearable
            @keyup.enter="handleConfirm"
          >
            <template #suffix>
              <span class="file-extension">.xlsx</span>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="isExporting"
        >
          {{ isExporting ? '正在导出...' : '确认' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue';
import { ElDialog, ElButton, ElInput, ElForm, ElFormItem } from 'element-plus';

interface Props {
  visible: boolean;
  defaultFileName: string;
  isExporting: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', fileName: string): void;
  (e: 'cancel'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const form = reactive({
  name: ''
});

// 监听默认文件名变化
watch(() => props.defaultFileName, (newValue) => {
  form.name = newValue;
}, { immediate: true });

const handleVisibilityChange = (value: boolean) => {
  emit('update:visible', value);
};

const handleConfirm = () => {
  if (!form.name.trim()) {
    return;
  }
  emit('confirm', form.name.trim());
};

const handleCancel = () => {
  emit('cancel');
};
</script>

<style scoped>
.filename-content {
  padding: 16px 0;
}

.file-extension {
  color: #909399;
  font-size: 14px;
  padding-right: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>