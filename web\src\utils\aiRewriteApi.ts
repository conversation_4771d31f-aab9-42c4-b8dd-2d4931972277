import { sendRequestViaBackground } from './api'
import { getApiUrl } from './apiConfig'

// AI改写任务接口
export interface TaskNewGenerateDetailsParams {
  task_id: number
  offset?: number
  limit?: number
}

export interface TaskNewGenerateDetailsResponse {
  has_more: boolean
  processed_count: number
  total_count: number
  current_offset: number
  store_brand: string
}

export interface TaskNewNextAiTaskParams {
  task_id: number
}

export interface TaskDetailForAi {
  id: number
  goods_name: string
  goods_name_ai: string
  goods_property: string
  goods_property_ai: string
  is_name_ai: number
  is_property_ai: number
  spec_values: string
  user_account_id: number
  thumb_url: string
  price: string
  price_third: string
  currentcy: string
  currentcy_goods: string
}

export interface TaskNewNextAiTaskResponse {
  has_task: boolean
  task_detail: TaskDetailForAi | null
}

export interface TaskNewUpdateAiResultParams {
  task_detail_id: number
  goods_name_ai?: string
  goods_property_ai?: string
}

export interface TaskNewAiProgressResponse {
  total_count: number
  completed_count: number
  remaining_count: number
  progress_percentage: number
}

export interface AiKeyResponse {
  ai_key: string
}

// DeepSeek API响应接口
export interface DeepSeekResponse {
  choices: Array<{
    message: {
      content: string
    }
  }>
}

/**
 * 生成任务详情记录
 * @param params 生成参数
 * @returns Promise<TaskNewGenerateDetailsResponse>
 */
export const generateTaskDetails = async (params: TaskNewGenerateDetailsParams): Promise<TaskNewGenerateDetailsResponse> => {
  const url = await getApiUrl('apiTaskNewGenerateDetailsUrl');
  return sendRequestViaBackground({
    funName: 'generateTaskDetails',
    url,
    method: 'post',
    params,
    auth: true
  });
};

/**
 * 获取下一个待AI改写的任务详情
 * @param params 查询参数
 * @returns Promise<TaskNewNextAiTaskResponse>
 */
export const getNextAiTask = async (params: TaskNewNextAiTaskParams): Promise<TaskNewNextAiTaskResponse> => {
  const url = await getApiUrl('apiTaskNewNextAiTaskUrl');
  return sendRequestViaBackground({
    funName: 'getNextAiTask',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 更新任务详情AI改写结果
 * @param params 更新参数
 * @returns Promise<any>
 */
export const updateAiResult = async (params: TaskNewUpdateAiResultParams): Promise<any> => {
  const url = await getApiUrl('apiTaskNewUpdateAiResultUrl');
  return sendRequestViaBackground({
    funName: 'updateAiResult',
    url,
    method: 'post',
    params,
    auth: true
  });
};

/**
 * 获取用户随机AI Key
 * @returns Promise<AiKeyResponse>
 */
export const getRandomAiKey = async (): Promise<AiKeyResponse> => {
  const url = await getApiUrl('apiTaskNewRandomAiKeyUrl');
  return sendRequestViaBackground({
    funName: 'getRandomAiKey',
    url,
    method: 'get',
    auth: true
  });
};

/**
 * 获取任务AI改写进度
 * @param taskId 任务ID
 * @returns Promise<TaskNewAiProgressResponse>
 */
export const getAiProgress = async (taskId: number): Promise<TaskNewAiProgressResponse> => {
  const url = await getApiUrl('apiTaskNewAiProgressUrl');
  return sendRequestViaBackground({
    funName: 'getAiProgress',
    url,
    method: 'get',
    params: { task_id: taskId },
    auth: true
  });
};

/**
 * 完成任务AI改写
 * @param taskId 任务ID
 * @returns Promise<any>
 */
export const completeAiTask = async (taskId: number): Promise<any> => {
  const url = await getApiUrl('apiTaskNewCompleteAiTaskUrl');
  return sendRequestViaBackground({
    funName: 'completeAiTask',
    url,
    method: 'post',
    params: { task_id: taskId },
    auth: true
  });
};

/**
 * 调用DeepSeek API进行AI改写
 * @param prompt 提示词
 * @param aiKey AI Key
 * @returns Promise<string>
 */
export const callDeepSeekApi = async (prompt: string, aiKey: string): Promise<string> => {
  const url = 'https://api.deepseek.com/chat/completions'

  const requestData = {
    model: 'deepseek-chat',
    messages: [
      {
        role: 'user',
        content: prompt
      }
    ],
    max_tokens: 150,
    temperature: 0.7
  }

  const headers = {
    'Authorization': `Bearer ${aiKey}`,
    'Content-Type': 'application/json'
  }

  try {
    const response = await sendRequestViaBackground({
      funName: 'callDeepSeekApi',
      url,
      method: 'post',
      data: requestData,
      headers,
      auth: false,
      encrypto: false,
      timeout: 30000
    })

    console.log('DeepSeek API响应:', response)

    // 优先检查是否是DeepSeek API的错误响应（401等错误状态码）
    if (response && response.error) {
      const errorMessage = response.error.message || '未知错误'
      // 对于认证错误等，直接显示错误信息，不需要额外的包装
      throw new Error(errorMessage)
    }

    // 只有当没有error对象时，才检查成功响应的格式
    if (!response || !response.choices || !Array.isArray(response.choices) || response.choices.length === 0) {
      throw new Error('DeepSeek API响应格式错误：缺少choices字段或choices为空')
    }

    const choice = response.choices[0]
    if (!choice || !choice.message || typeof choice.message.content !== 'string') {
      throw new Error('DeepSeek API响应格式错误：无效的message结构')
    }

    const rewrittenContent = choice.message.content.trim()

    if (!rewrittenContent) {
      throw new Error('DeepSeek API返回空内容')
    }

    // 移除管道符号
    return rewrittenContent.replace(/\|/g, '')

  } catch (error: any) {
    console.error('DeepSeek API调用失败:', error)

    // 如果错误信息不包含"DeepSeek API"前缀，说明是我们直接抛出的错误（如认证错误）
    if (error.message && !error.message.includes('DeepSeek API')) {
      throw error
    }

    // 如果是我们自己抛出的DeepSeek API错误，直接传递
    if (error.message && error.message.includes('DeepSeek API')) {
      throw error
    }

    // 其他错误，包装为DeepSeek API调用错误
    throw new Error(`DeepSeek API调用失败: ${error.message || '网络请求失败'}`)
  }
}

/**
 * 构建标题改写提示词
 * @param skuValues SKU值
 * @param originalGoodsName 原始商品名称
 * @returns string
 */
export const constructTitlePrompt = (skuValues: string, originalGoodsName: string): string => {
  let skuValuesText = ''
  let includeSkuValues = ''

  if (skuValues && skuValues.trim()) {
    skuValuesText = `商品的sku是${skuValues},`
    includeSkuValues = '标题要包含sku值,'
  }

  return `${skuValuesText}商品标题是${originalGoodsName} 重写标题改为土耳其语,${includeSkuValues}包含空格的总长度不能超过85,标题中不能包含符号|,标题要包含商品的主要信息.直接给出改写后的土耳其语言的标题即可,不需要任何额外的其它信息。`
}

/**
 * 构建描述改写提示词
 * @param originalProperty 原始商品描述
 * @returns string
 */
export const constructPropertyPrompt = (originalProperty: string): string => {
  return `给你一段内容${originalProperty},如果该段内容为土耳其语,直接返回数字0,否则翻译成土耳其语,直接给出翻译结果即可`
}

/**
 * 提取SKU值
 * @param specValues 规格值字符串
 * @returns string
 */
export const extractSkuValues = (specValues: string): string => {
  if (!specValues || !specValues.trim()) {
    return ''
  }
  return specValues.trim()
}

/**
 * 为商品名称添加品牌前缀
 * @param goodsName 商品名称
 * @param brand 品牌名称
 * @returns string
 */
export const prependBrand = (goodsName: string, brand: string): string => {
  if (!brand || !brand.trim() || !goodsName || !goodsName.trim()) {
    return goodsName
  }

  // 检查品牌是否已在开头
  if (goodsName.toLowerCase().indexOf(brand.toLowerCase()) === 0) {
    return goodsName
  }

  return `${brand} ${goodsName}`
}
