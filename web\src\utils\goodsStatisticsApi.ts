import { sendRequestViaBackground } from './api'
import { getApiUrl } from './apiConfig'

// 商品统计接口参数
export interface GoodsStatisticsParams {
  directory_id?: number
  time_range?: string
  day_start?: string
  day_end?: string
  goods_ids?: number[]
  publish_currency?: string
  exchange_rate_currency?: string
  price_min?: number
  price_max?: number
}

// 商品统计响应数据
export interface GoodsStatisticsResponse {
  goods_count: number
  sku_count: number
  currency_count: number
  currencies: string[]
  exchange_rates: Record<string, number>
  exchange_rate_currency?: string
  directory_id: number
  time_range: string
  day_start?: string
  day_end?: string
}

/**
 * 获取商品统计信息
 * @param params 统计参数
 * @returns Promise<GoodsStatisticsResponse>
 */
export const getGoodsStatistics = async (params: GoodsStatisticsParams): Promise<GoodsStatisticsResponse> => {
  const url = await getApiUrl('apiGoodsStatisticsUrl')
  return sendRequestViaBackground({
    funName: 'getGoodsStatistics',
    url,
    method: 'get',
    params,
    auth: true
  })
} 