<?php
namespace App\Http\Controllers\Api\V1\Wap\User;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\Controller;
use App\Service\User\SubAccountGoodsService;

class SubAccountGoodsController extends Controller
{
    protected SubAccountGoodsService $subAccountGoodsService;

    public function __construct(SubAccountGoodsService $subAccountGoodsService)
    {
        $this->subAccountGoodsService = $subAccountGoodsService;
        parent::__construct();
    }

    /**
     * 获取子账号商品列表（分页）
     * 主账号查询时查询所有user_id为当前账号id且status=1的商品
     * 子账号查询时查询user_id是主账户ID且user_sub_id是当前账号id且status=1的商品
     * 主账号可以通过sub_account_id参数查询特定子账号的商品
     */
    public function list(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $userPid = $user['pid'];

        $params = $request->only([
            'page', 'pageSize', 'type', 'goods_name', 'goods_id', 'status', 'directory_id',
            'sort_field', 'sort_order', 'time_type', 'start_date', 'end_date', 'sub_account_id',
            // 主账号专属筛选参数
            'price_adjusted', 'cat_adjusted', 'only_sub_account', 'sub_account_name', 'sub_account_phone'
        ]);
        
        $result = $this->subAccountGoodsService->getSubAccountGoodsList($userId, $userPid, $params);

        return $this->apiSuccess($result);
    }

    /**
     * 子账号删除商品
     */
    public function delete(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $userPid = $user['pid'];
        
        $goodsId = (int)$request->input('goods_id');
        $result = $this->subAccountGoodsService->deleteSubAccountGoods($userId, $userPid, $goodsId);

        return $this->apiSuccess($result);
    }
}