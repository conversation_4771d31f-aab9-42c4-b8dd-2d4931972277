import * as utils from '@/utils/index';
import { axios } from '@/api/useAxios';
import { useFetch as useFetchHandler } from '@/api/useFetch';

import { forceRemoveAllCookies } from '@/utils/protocol/common/common';
import config from '@/config';

// 静态导入调试模块
import { debugSetPageSize100, getPageSizeScript } from '@/utils/rpc/debugPageSize';

// 静态导入页面控制器模块
import { getAllRejectedProductsViaRPC } from '@/utils/rpc/pageController';

// 静态导入DOM操作模块
import * as domOperations from '@/utils/rpc/domOperations';

// 静态导入网络监听模块
import * as networkListener from '@/utils/rpc/networkListener';

// 静态导入Temu搜索网络监听模块
import { createTemuSearchNetworkListener } from '@/utils/temu/temuNetworkListener';

// 静态导入N11产品删除模块
import { executeN11ProductDeletion } from '@/utils/n11/n11ProductDeleter';

// 静态导入批量采集模块
import { initMessageManager } from '@/utils/temu/messageManager';

import './clear_cookie';

//调用chrome事件
function chromeEvent(request: { funName: any; pramas: any; }, sender: any, callback: (arg0: any[]) => any) {
  let { funName, pramas } = request;
  let funCode = funName.split('.');
  let chromeFun = chrome;
  const chromeCallback = (...arr: any[]) => callback(arr)
  funCode.forEach((item: string, index: number) => {
    if (typeof chromeFun[item] !== 'undefined')
      if (index + 1 === funCode.length) {
        if (typeof chromeFun[item] === 'function') {
          let callbackindex = pramas.findIndex((item: any) => typeof item === 'object' && 'callback' in item && item['callback'])
          if (callbackindex != -1) pramas[callbackindex] = chromeCallback;
          chromeFun[item](...pramas)
        } else throw new Error('未找到对应的chrome方法')
      } else {
        chromeFun = chromeFun[item]
      }
  })
}

export const removeSpecificRule = async (ruleId: number): Promise<void> => {
  try {
    await chrome.declarativeNetRequest.updateDynamicRules({
      removeRuleIds: [ruleId],
      addRules: []
    });

    console.log(`Rule ${ruleId} removed successfully`);
  } catch (error) {
    console.error(`Failed to remove rule ${ruleId}:`, error);
  }
};

// RPC操作相关函数
/**
 * 执行DOM操作
 */
async function handleRpcDomOperation(request: any, sendResponse: any): Promise<void> {
  try {
    const { operation, tabId, params } = request;

    // 获取当前活动标签页（如果没有指定tabId）
    let targetTabId = tabId;
    if (!targetTabId) {
      const tabs = await new Promise<any[]>((resolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, resolve);
      });

      if (!tabs || tabs.length === 0) {
        sendResponse({ success: false, message: '未找到活动标签页' });
        return;
      }

      targetTabId = tabs[0].id;
    }

    // 使用静态导入的DOM操作模块
    console.log('Background: 使用静态导入的DOM操作模块');

    let result;
    switch (operation) {
      case 'setPageSize100':
        result = await domOperations.setPageSize100(targetTabId);
        break;
      case 'clickNextPage':
        result = await domOperations.clickNextPage(targetTabId);
        break;
      case 'getPageStatus':
        result = await domOperations.getPageStatus(targetTabId);
        break;
      case 'isN11ProductListPage':
        result = await domOperations.isN11ProductListPage(targetTabId);
        break;
      case 'waitForElement':
        result = await domOperations.waitForElement(targetTabId, params);
        break;
      case 'scrollToPosition':
        result = await domOperations.scrollToPosition(targetTabId, params);
        break;
      default:
        result = { success: false, message: `不支持的DOM操作: ${operation}` };
    }

    sendResponse(result);
  } catch (error: any) {
    console.error('RPC DOM操作失败:', error);
    sendResponse({ success: false, message: `DOM操作失败: ${error.message}` });
  }
}

/**
 * 处理新的简化DOM操作（专门用于调试页面大小设置）
 * 使用静态导入的调试模块，避免动态导入问题
 */
async function handleRpcDomOperationNew(request: any, sendResponse: any): Promise<void> {
  try {
    console.log('Background: 开始处理新的DOM操作请求');

    // 获取当前活动标签页
    const tabs = await new Promise<any[]>((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, resolve);
    });

    if (!tabs || tabs.length === 0) {
      sendResponse({ success: false, message: '未找到活动标签页' });
      return;
    }

    const targetTabId = tabs[0].id;
    console.log('Background: 使用标签页ID:', targetTabId);

    // 使用静态导入的调试模块
    try {
      console.log('Background: 使用静态导入的调试模块');
      const result = await debugSetPageSize100(targetTabId);
      console.log('Background: 调试操作完成，结果:', result);
      sendResponse(result);
    } catch (moduleError: any) {
      console.error('Background: 调试模块执行失败:', moduleError);

      // 如果模块方式失败，回退到内联脚本方式
      console.log('Background: 回退到内联脚本方式');
      const results = await chrome.scripting.executeScript({
        target: { tabId: targetTabId },
        func: getPageSizeScript()
      });

      if (results && results[0] && results[0].result) {
        const result = results[0].result;
        console.log('Background: 内联脚本执行结果:', result);
        sendResponse(result);
      } else {
        sendResponse({ success: false, message: '内联脚本执行无返回结果' });
      }
    }

  } catch (error: any) {
    console.error('Background: 新DOM操作失败:', error);
    sendResponse({ success: false, message: `新DOM操作失败: ${error.message}` });
  }
}

// 存储活动的网络监听器
const activeNetworkListeners = new Map<string, any>();

/**
 * 管理网络监听器
 */
async function handleRpcNetworkListen(request: any, sendResponse: any): Promise<void> {
  try {
    const { action, tabId, config } = request;

    // 获取当前活动标签页（如果没有指定tabId）
    let targetTabId = tabId;
    if (!targetTabId) {
      const tabs = await new Promise<any[]>((resolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, resolve);
      });

      if (!tabs || tabs.length === 0) {
        sendResponse({ success: false, message: '未找到活动标签页' });
        return;
      }

      targetTabId = tabs[0].id;
    }

    // 使用静态导入的网络监听模块
    console.log('Background: 使用静态导入的网络监听模块');

    let result;
    switch (action) {
      case 'create':
        const listener = networkListener.createNetworkListener({ tabId: targetTabId, ...config });
        result = await listener.start();
        break;
      default:
        result = { success: false, message: `不支持的网络监听操作: ${action}` };
    }

    sendResponse(result);
  } catch (error: any) {
    console.error('RPC网络监听失败:', error);
    sendResponse({ success: false, message: `网络监听失败: ${error.message}` });
  }
}

/**
 * 管理Temu搜索网络监听器
 */
async function handleTemuSearchNetworkListen(request: any, sendResponse: any): Promise<void> {
  try {
    const { action, tabId, config, listenerId } = request;

    // 获取当前活动标签页（如果没有指定tabId）
    let targetTabId = tabId;
    if (!targetTabId) {
      const tabs = await new Promise<any[]>((resolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, resolve);
      });

      if (!tabs || tabs.length === 0) {
        sendResponse({ success: false, message: '未找到活动标签页' });
        return;
      }

      targetTabId = tabs[0].id;
    }

    console.log('Background: 处理Temu搜索网络监听请求:', action, 'tabId:', targetTabId);

    let result;
    switch (action) {
      case 'start':
        try {
          // 创建Temu搜索网络监听器
          const listener = createTemuSearchNetworkListener({
            tabId: targetTabId,
            timeout: config?.timeout || 30000
          });

          // 启动监听器，设置回调函数
          result = await listener.start((responseData) => {
            console.log('Background: 捕获到Temu搜索响应:', responseData);

            // 向content script发送数据
            chrome.tabs.sendMessage(targetTabId, {
              type: 'temu_search_data',
              listenerId: listenerId,
              data: responseData
            }).catch((error: any) => {
              console.warn('Background: 发送Temu搜索数据失败:', error);
            });
          });

          if (result.success) {
            // 存储监听器以便后续管理
            activeNetworkListeners.set(listenerId || `temu_${targetTabId}`, listener);
            console.log('Background: Temu搜索网络监听器启动成功');
          } else {
            // 如果启动失败，提供更详细的错误信息
            console.error('Background: Temu搜索网络监听器启动失败:', result.message);

            // 特殊处理调试器冲突错误
            if (result.message && result.message.includes('Another debugger is already attached')) {
              result.message = '调试器冲突已自动处理，但仍然失败。请关闭开发者工具后重试，或刷新页面。';
            }
          }
        } catch (error: any) {
          console.error('Background: 启动Temu搜索网络监听器失败:', error);

          // 特殊处理调试器冲突错误
          let errorMessage = error.message || '未知错误';
          if (errorMessage.includes('Another debugger is already attached')) {
            errorMessage = '检测到调试器冲突。请关闭开发者工具后重试，或刷新页面重新开始。';
          }

          result = { success: false, message: `启动失败: ${errorMessage}` };
        }
        break;

      case 'stop':
        try {
          const listenerKey = listenerId || `temu_${targetTabId}`;
          const listener = activeNetworkListeners.get(listenerKey);

          if (listener) {
            result = await listener.stop();
            activeNetworkListeners.delete(listenerKey);
            console.log('Background: Temu搜索网络监听器已停止');
          } else {
            result = { success: true, message: '监听器不存在或已停止' };
          }
        } catch (error: any) {
          console.error('Background: 停止Temu搜索网络监听器失败:', error);
          result = { success: false, message: `停止失败: ${error.message}` };
        }
        break;

      default:
        result = { success: false, message: `不支持的Temu搜索网络监听操作: ${action}` };
    }

    sendResponse(result);
  } catch (error: any) {
    console.error('Background: Temu搜索网络监听失败:', error);
    sendResponse({ success: false, message: `Temu搜索网络监听失败: ${error.message}` });
  }
}


/**
 * 页面控制器操作
 */
async function handleRpcPageControl(request: any, sendResponse: any): Promise<void> {
  try {
    const { action, config, requestId } = request;

    console.log('Background: 开始处理RPC页面控制请求:', action, 'requestId:', requestId);

    // 先检查基础Chrome API可用性
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      sendResponse({ success: false, message: 'Chrome运行时环境不可用' });
      return;
    }

    let result;
    switch (action) {
      case 'getAllRejectedProducts':
        try {
          console.log('Background: 准备导入RPC模块...');

          // 检查基础环境
          if (!chrome || !chrome.tabs || !chrome.scripting) {
            result = { success: false, message: 'Chrome API 环境不完整' };
            break;
          }

          // 检查全局停止标志
          if (typeof globalThis !== 'undefined' && globalThis.shouldStopRPCOperations) {
            console.log('Background: 检测到全局停止标志，终止RPC操作');
            result = { success: false, message: '操作已被取消' };
            break;
          }

          // 创建进度回调函数，向content script发送进度消息
          const progressCallback = (current: number, total: number) => {
            // 在进度回调中也检查停止标志
            if (typeof globalThis !== 'undefined' && globalThis.shouldStopRPCOperations) {
              console.log('Background: 进度回调中检测到停止标志，停止发送进度消息');
              return;
            }

            console.log(`Background: 发送进度更新 ${current}/${total} to requestId: ${requestId}`);

            // 获取当前活动标签页并发送进度消息
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs: any[]) => {
              if (tabs && tabs.length > 0 && tabs[0].id) {
                chrome.tabs.sendMessage(tabs[0].id, {
                  type: 'rpc_progress',
                  requestId: requestId,
                  current: current,
                  total: total
                }).catch((error: any) => {
                  console.warn('Background: 发送进度消息失败:', error);
                });
              }
            });
          };

          // 使用静态导入的页面控制器模块
          console.log('Background: 使用静态导入的PageController模块');
          console.log('Background: 开始执行getAllRejectedProductsViaRPC...');
          result = await getAllRejectedProductsViaRPC(progressCallback);
          console.log('Background: getAllRejectedProductsViaRPC执行完成');
        } catch (executeError: any) {
          console.error('Background: 执行RPC模块失败:', executeError);
          console.error('Background: 错误详情:', executeError.stack);
          result = { success: false, message: `RPC模块错误: ${executeError.message}` };
        }
        break;

      case 'checkN11Page':
        try {
          console.log('Background: 准备检查N11页面...');

          // 直接实现页面检查逻辑，避免导入可能有问题的模块
          if (!chrome || !chrome.tabs || !chrome.scripting) {
            console.warn('Chrome API 环境不完整');
            result = false;
            break;
          }

          // 获取当前活动标签页
          const tabs = await new Promise<any[]>((resolve, reject) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabResult: any[]) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(tabResult);
              }
            });
          });

          if (!tabs || tabs.length === 0) {
            console.warn('未找到活动标签页');
            result = false;
            break;
          }

          const tabId = tabs[0].id;
          const url = tabs[0].url;

          // 首先通过URL进行基础检查
          if (!url || !url.includes('so.n11.com')) {
            console.log('当前页面不是N11网站');
            result = false;
            break;
          }

          // 通过脚本检查页面结构
          try {
            const results = await chrome.scripting.executeScript({
              target: { tabId },
              func: () => {
                try {
                  const url = window.location.href;
                  const hasCorrectUrl = url.includes('so.n11.com') &&
                                       (url.includes('product') || url.includes('urun'));

                  const hasProductList = document.querySelector('.product-list') ||
                                        document.querySelector('[class*="product"]') ||
                                        document.querySelector('.tabManager');

                  return hasCorrectUrl && !!hasProductList;
                } catch (error) {
                  return false;
                }
              }
            });

            result = !!(results && results[0] && results[0].result);
            console.log('Background: N11页面检查完成，结果:', result);
          } catch (scriptError) {
            console.warn('执行页面检查脚本失败:', scriptError);
            // 如果脚本执行失败，回退到URL检查
            result = url.includes('so.n11.com') && (url.includes('product') || url.includes('urun'));
            console.log('Background: 回退URL检查结果:', result);
          }
        } catch (importError: any) {
          console.error('Background: N11页面检查失败:', importError);
          console.error('Background: 错误详情:', importError.stack);
          result = false;
        }
        break;

      case 'getRPCStatus':
        try {
          console.log('Background: 准备获取RPC状态...');

          // 直接实现状态检查逻辑，避免导入可能有问题的模块
          const status = {
            supported: false,
            chromeAPIs: {
              scripting: !!(chrome && chrome.scripting && chrome.scripting.executeScript),
              debugger: !!(chrome && chrome.debugger && chrome.debugger.attach),
              tabs: !!(chrome && chrome.tabs && chrome.tabs.query)
            },
            currentPage: {
              isN11: false,
              url: undefined as string | undefined,
              tabId: undefined as number | undefined
            },
            permissions: {
              debugger: false,
              scripting: false,
              activeTab: false
            }
          };

          // 检查权限
          if (chrome && chrome.permissions) {
            try {
              const permissions = await new Promise<any>((resolve, reject) => {
                chrome.permissions.getAll((permResult: any) => {
                  if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                  } else {
                    resolve(permResult);
                  }
                });
              });

              status.permissions.debugger = permissions.permissions?.includes('debugger') || false;
              status.permissions.scripting = permissions.permissions?.includes('scripting') || false;
              status.permissions.activeTab = permissions.permissions?.includes('activeTab') || false;
            } catch (permError) {
              console.warn('检查权限失败:', permError);
            }
          }

          // 检查当前页面
          if (chrome && chrome.tabs) {
            try {
              const tabs = await new Promise<any[]>((resolve, reject) => {
                chrome.tabs.query({ active: true, currentWindow: true }, (tabResult: any[]) => {
                  if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                  } else {
                    resolve(tabResult);
                  }
                });
              });

              if (tabs && tabs.length > 0) {
                const activeTab = tabs[0];
                status.currentPage.url = activeTab.url;
                status.currentPage.tabId = activeTab.id;

                if (activeTab.url) {
                  status.currentPage.isN11 = activeTab.url.includes('so.n11.com') &&
                                           (activeTab.url.includes('product') || activeTab.url.includes('urun'));
                }
              }
            } catch (tabError) {
              console.warn('获取当前页面信息失败:', tabError);
            }
          }

          // 综合判断是否支持
          const apiSupported = status.chromeAPIs.scripting &&
                              status.chromeAPIs.debugger &&
                              status.chromeAPIs.tabs;

          const permissionsGranted = status.permissions.debugger &&
                                    status.permissions.scripting;

          status.supported = apiSupported && permissionsGranted;

          result = status;
          console.log('Background: RPC状态检查完成，结果:', result);
        } catch (importError: any) {
          console.error('Background: RPC状态检查失败:', importError);
          console.error('Background: 错误详情:', importError.stack);
          result = {
            supported: false,
            message: `状态检查失败: ${importError.message}`,
            error: importError.stack
          };
        }
        break;

      case 'deleteN11Product':
        try {
          console.log('Background: 准备删除N11产品...');

          const { stockCode, config: deleteConfig } = request;

          if (!stockCode) {
            result = { success: false, message: '缺少必要参数: stockCode' };
            break;
          }

          // 检查基础环境
          if (!chrome || !chrome.tabs || !chrome.scripting) {
            result = { success: false, message: 'Chrome API 环境不完整' };
            break;
          }

          // 获取当前活动标签页
          const tabs = await new Promise<any[]>((resolve, reject) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabResult: any[]) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
                return;
              }
              resolve(tabResult);
            });
          });

          if (!tabs || tabs.length === 0) {
            result = { success: false, message: '未找到活动标签页' };
            break;
          }

          const tab = tabs[0];
          const tabId = tab.id;
          const url = tab.url;

          if (!tabId) {
            result = { success: false, message: '无法获取标签页ID' };
            break;
          }

          // 检查是否在N11产品管理页面
          if (!url || !url.includes('so.n11.com/selleroffice/v2/product/products')) {
            result = { success: false, message: '当前页面不是N11产品管理页面' };
            break;
          }

          console.log(`Background: 开始在标签页 ${tabId} 中删除产品 ${stockCode}`);

          // 执行删除操作
          const deleteResults = await chrome.scripting.executeScript({
            target: { tabId },
            func: executeN11ProductDeletion,
            args: [stockCode, deleteConfig || {}]
          });

          if (!deleteResults || deleteResults.length === 0) {
            result = { success: false, message: '删除脚本执行失败，无返回结果' };
            break;
          }

          result = deleteResults[0].result;
          console.log('Background: N11产品删除完成，结果:', result);

        } catch (deleteError: any) {
          console.error('Background: 删除N11产品失败:', deleteError);
          console.error('Background: 错误详情:', deleteError.stack);
          result = {
            success: false,
            message: `删除产品失败: ${deleteError.message}`,
            details: deleteError
          };
        }
        break;

      case 'forceCleanupAll':
        try {
          console.log('Background: 开始强制清理所有RPC资源...');

          // 清理所有网络监听器
          try {
            await networkListener.cleanupAllNetworkListeners();
            console.log('Background: 网络监听器清理完成');
          } catch (cleanupError) {
            console.warn('Background: 清理网络监听器失败:', cleanupError);
          }

          // 清理所有DOM操作相关的debugger连接
          try {
            await domOperations.cleanupAllDebuggers();
            console.log('Background: DOM操作debugger清理完成');
          } catch (cleanupError) {
            console.warn('Background: 清理DOM操作debugger失败:', cleanupError);
          }

          // 设置全局停止标志
          if (typeof globalThis !== 'undefined') {
            globalThis.shouldStopRPCOperations = true;
            console.log('Background: 已设置全局停止标志');

            // 等待一下确保所有正在进行的操作能检测到停止标志
            await new Promise(resolve => setTimeout(resolve, 100));
          }

          result = { success: true, message: '强制清理完成' };
          console.log('Background: 强制清理所有RPC资源完成');

        } catch (cleanupError: any) {
          console.error('Background: 强制清理失败:', cleanupError);
          result = {
            success: false,
            message: `强制清理失败: ${cleanupError.message}`,
            details: cleanupError
          };
        }
        break;

      case 'resetStopFlag':
        try {
          console.log('Background: 开始重置全局停止标志...');

          // 重置全局停止标志
          if (typeof globalThis !== 'undefined') {
            globalThis.shouldStopRPCOperations = false;
            console.log('Background: 已重置全局停止标志为false');
          }

          result = { success: true, message: '全局停止标志已重置' };
          console.log('Background: 全局停止标志重置完成');

        } catch (resetError: any) {
          console.error('Background: 重置停止标志失败:', resetError);
          result = {
            success: false,
            message: `重置停止标志失败: ${resetError.message}`,
            details: resetError
          };
        }
        break;

      default:
        result = { success: false, message: `不支持的页面控制操作: ${action}` };
    }

    console.log('Background: RPC页面控制操作完成，发送响应:', result);
    sendResponse(result);
  } catch (error: any) {
    console.error('Background: RPC页面控制失败:', error);
    console.error('Background: 错误堆栈:', error.stack);
    sendResponse({
      success: false,
      message: `页面控制失败: ${error.message}`,
      error: error.stack
    });
  }
}

chrome.runtime.onInstalled.addListener(async function () {});


let crx_identify = '';
let website_id = '';


chrome.runtime.onMessage.addListener(function (request: any, sender: any, sendResponse: any) {
  let responseInfo:any = null;
  let type = 0;
  let url = '';
  console.log(request);

  // 处理采集完成消息转发
  if (request.type === 'collection_complete') {
    console.log('Background: 收到采集完成消息，准备转发:', request);
    console.log('Background: 消息来源Tab ID:', sender.tab?.id);
    
    // 获取所有标签页并转发消息到搜索页面
    chrome.tabs.query({}, (tabs: any[]) => {
      tabs.forEach((tab) => {
        // 检查是否是Temu搜索页面
        if (tab.url && tab.url.includes('www.temu.com') && 
            (tab.url.includes('/search') || tab.url.includes('search'))) {
          console.log(`Background: 向Temu搜索页面Tab ${tab.id} 转发采集完成消息`);
          chrome.tabs.sendMessage(tab.id, {
            ...request,
            source: 'background-forwarded'
          }).catch((error: any) => {
            console.debug(`Background: 向搜索页面Tab ${tab.id} 转发采集完成消息失败:`, error);
          });
        }
      });
    });
    
    // 同时响应发送方
    sendResponse({ success: true, message: '采集完成消息已转发' });
    return true;
  }

  // 处理进度消息转发
  if (request.type === 'rpc_progress') {
    console.log('Background: 转发进度消息:', request);
    // 获取所有标签页并转发消息
    chrome.tabs.query({}, (tabs: any[]) => {
      tabs.forEach((tab) => {
        if (tab.id && tab.id !== sender.tab?.id) { // 不发送给发送者自己
          chrome.tabs.sendMessage(tab.id, request).catch((error:any) => {
            // 忽略发送失败的错误，因为不是所有标签页都需要接收这个消息
            console.debug(`Background: 向标签页 ${tab.id} 发送进度消息失败:`, error);
          });
        }
      });
    });
    return false; // 不需要响应
  }

  switch (request.funType) {
    case 'chrome': //代理执行chrome方法
      chromeEvent(request, sender, sendResponse)
      break;
    case 'axios': //代理执行chrome方法
      axios(request, sender, sendResponse)
      break;
    case 'fetch':
      useFetchHandler(request, sender, sendResponse);
      break;

    // 新增RPC操作支持
    case 'rpcDomOperation':
      handleRpcDomOperation(request, sendResponse);
      return true; // 异步响应

    case 'rpcDomOperationNew':
      handleRpcDomOperationNew(request, sendResponse);
      return true; // 异步响应

    case 'rpcNetworkListen':
      handleRpcNetworkListen(request, sendResponse);
      return true; // 异步响应

    case 'rpcPageControl':
      handleRpcPageControl(request, sendResponse);
      return true; // 异步响应

    case 'temuSearchNetworkListen':
      handleTemuSearchNetworkListen(request, sendResponse);
      return true; // 异步响应

    case 'getConfig': // 获取配置信息
      const configKey = request.configKey;
      if (configKey && configKey in config) {
        sendResponse({ url: (config as any)[configKey] });
      } else {
        sendResponse({ error: '配置项不存在' });
      }
      break;

    case 'securityVerification': // 处理安全验证通知
      // 创建桌面通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'static/img/icon.png',
        title: '提示',
        message: request.message || '商品不存在或者触发安全验证',
        priority: 2
      }, (notificationId: string) => {
        console.log('安全验证通知已显示，ID:', notificationId);
        // 5秒后自动关闭通知
        setTimeout(() => {
          chrome.notifications.clear(notificationId);
        }, 5000);
        sendResponse({ success: true, notificationId });
      });
      return true; // 表示异步响应

    case 'forceRemoveAllCookies':
      forceRemoveAllCookies()
        .then((result) => {
          console.log('----------------------------------------------清除Cookie成功:');
          sendResponse({ success: true });
        })
        .catch((error) => {
          console.error('-------------------------------------清除Cookie失败:', error);
          sendResponse({ success: false, error });
        });
      return true; // 表示异步响应

    case 'getLoginInfo':
      chrome.storage.sync.get(['crx_identify', 'website_id'], function(data:{crx_identify:string,website_id:string}) {
        crx_identify = data.crx_identify;
        website_id = data.website_id;
        if(crx_identify==undefined || crx_identify==''){
          responseInfo = {
            code: 0,
            page_type:0,
            opt:'login_fail',
            data: {
              crx_identify: '',
              website_id: ''
            }
          };
        }else{
          responseInfo = {
            code: 0,
            page_type:0,
            opt:'login_success_waiting_living_room',
            data: {
              crx_identify: crx_identify,
              website_id: website_id
            }
          };
        }
        sendResponse(responseInfo);
        return true;
      });
      break;

    case 'getCookie':
      chrome.tabs.query({ active: true, lastFocusedWindow: true }, async (tabs:any[]) => {
        let url = tabs[0].url;
        const cookieStr = await utils.getCookies(url);
        console.log('cookieStr==============:', cookieStr);
        responseInfo = {
          code: 0,
          opt:'get_cookie_success',
          data: {
            cookie: cookieStr
          }
        };
        sendResponse(responseInfo);
        return true;
      });
      break;

    // 批量采集相关API
    case 'batchCollection':
      handleBatchCollection(request, sendResponse);
      return true; // 表示异步响应

    case 'createTab':
      handleCreateTab(request, sendResponse);
      return true; // 表示异步响应

    case 'closeTab':
      handleCloseTab(request, sendResponse);
      return true; // 表示异步响应

    case 'waitForTabLoad':
      handleWaitForTabLoad(request, sendResponse);
      return true; // 表示异步响应

    case 'waitForCollectionComplete':
      handleWaitForCollectionComplete(request, sendResponse);
      return true; // 表示异步响应

    case 'testConnection':
      sendResponse({ success: true, message: 'Background script连接正常' });
      return true; // 表示异步响应
  }
  //处理异步响应
  return true
});


chrome.storage.onChanged.addListener((changes:any) => {
  for(let key in changes){
    let val = changes[key];
    if(key=='website_id'){
      console.log('website_id发生改变,当前值为'+val.newValue);
      website_id = val.newValue;
    }
    if(key=='crx_identify'){
      console.log('crx_identify发生改变,当前值为'+val.newValue);
      crx_identify = val.newValue;
    }
  }
});


chrome.storage.sync.get(['crx_identify', 'website_id'], function(data:{crx_identify:string,website_id:string}) {
  crx_identify = data.crx_identify;
  website_id = data.website_id;
});

// 批量采集相关处理函数

/**
 * 处理批量采集请求
 */
async function handleBatchCollection(request: any, sendResponse: any): Promise<void> {
  try {
    const { action, config, onProgress } = request;

    switch (action) {
      case 'start':
        // 初始化消息管理器
        initMessageManager();

        // 这里可以添加批量采集的具体逻辑
        // 由于批量采集逻辑主要在content script中，这里主要负责协调
        sendResponse({ success: true, message: '批量采集已启动' });
        break;

      case 'stop':
        sendResponse({ success: true, message: '批量采集已停止' });
        break;

      default:
        sendResponse({ success: false, message: '未知的批量采集操作' });
    }
  } catch (error) {
    console.error('批量采集处理失败:', error);
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * 处理创建Tab请求
 */
async function handleCreateTab(request: any, sendResponse: any): Promise<void> {
  try {
    const { url } = request;

    if (!url) {
      sendResponse({ success: false, message: 'URL不能为空' });
      return;
    }

    const tab = await chrome.tabs.create({ url });
    sendResponse({
      success: true,
      data: {
        id: tab.id,
        url: tab.url || url,
        status: tab.status || 'loading'
      }
    });
  } catch (error) {
    console.error('创建Tab失败:', error);
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * 处理关闭Tab请求
 */
async function handleCloseTab(request: any, sendResponse: any): Promise<void> {
  try {
    const { tabId } = request;

    if (!tabId) {
      sendResponse({ success: false, message: 'Tab ID不能为空' });
      return;
    }

    await chrome.tabs.remove(tabId);
    sendResponse({ success: true });
  } catch (error) {
    console.error('关闭Tab失败:', error);
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * 处理等待Tab加载请求
 */
async function handleWaitForTabLoad(request: any, sendResponse: any): Promise<void> {
  try {
    const { tabId, timeout = 30000 } = request;

    if (!tabId) {
      sendResponse({ success: false, message: 'Tab ID不能为空' });
      return;
    }

    const startTime = Date.now();
    const checkInterval = 100;

    while (Date.now() - startTime < timeout) {
      try {
        const tab = await chrome.tabs.get(tabId);
        if (tab.status === 'complete') {
          sendResponse({ success: true });
          return;
        }

        // 等待一小段时间再检查
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      } catch (error) {
        // Tab可能不存在了
        throw new Error(`获取Tab信息失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    throw new Error('Tab加载超时');
  } catch (error) {
    console.error('等待Tab加载失败:', error);
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * 处理等待采集完成请求
 */
async function handleWaitForCollectionComplete(request: any, sendResponse: any): Promise<void> {
  try {
    const { goodsId, timeout = 60000 } = request;

    if (!goodsId) {
      sendResponse({ success: false, message: '商品ID不能为空' });
      return;
    }

    console.log(`Background: 开始等待商品 ${goodsId} 采集完成，超时时间 ${timeout}ms`);

    // 创建一个Promise来等待采集完成消息
    const waitForCompletion = new Promise<void>((resolve, reject) => {
      const startTime = Date.now();
      
      // 创建消息监听器
      const messageListener = (message: any, sender: any, sendResponse: any) => {
        // 检查是否是采集完成消息
        if (message.type === 'collection_complete' && message.goodsId === goodsId) {
          console.log(`Background: 收到商品 ${goodsId} 采集完成消息`);
          chrome.runtime.onMessage.removeListener(messageListener);
          resolve();
          return;
        }
        
        // 检查是否是window.postMessage格式的消息
        if (message.source === 'temu-content-script' && 
            message.type === 'collection_complete' && 
            message.goodsId === goodsId) {
          console.log(`Background: 收到商品 ${goodsId} 采集完成消息 (window.postMessage)`);
          chrome.runtime.onMessage.removeListener(messageListener);
          resolve();
          return;
        }
      };

      // 添加消息监听器
      chrome.runtime.onMessage.addListener(messageListener);

      // 设置超时
      const timeoutId = setTimeout(() => {
        console.log(`Background: 等待商品 ${goodsId} 采集完成超时`);
        chrome.runtime.onMessage.removeListener(messageListener);
        reject(new Error(`等待商品 ${goodsId} 采集完成超时`));
      }, timeout);

      // 清理函数
      const cleanup = () => {
        clearTimeout(timeoutId);
        chrome.runtime.onMessage.removeListener(messageListener);
      };

      // 修改resolve和reject以包含清理
      const originalResolve = resolve;
      const originalReject = reject;
      
      resolve = (...args) => {
        cleanup();
        originalResolve(...args);
      };
      
      reject = (...args) => {
        cleanup();
        originalReject(...args);
      };
    });

    // 等待采集完成
    await waitForCompletion;
    console.log(`Background: 商品 ${goodsId} 采集完成确认`);
    sendResponse({ success: true });

  } catch (error) {
    console.error('等待采集完成失败:', error);
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}



