/**
 * 平台相关的公共类型定义
 * 用于统一管理平台关联、第三方平台分类等数据结构
 */

/**
 * 第三方平台分类信息
 */
export interface ThirdPlatformCategory {
  id: number
  name: string
  name_tl: string
  path_name: string
  path_name_tl: string
}

/**
 * 平台关联信息
 */
export interface PlatformRelation {
  id: number
  cat_type: string
  platform_id: number
  third_platform_id: number
  cat_third_ids: Array<number>
  third_platform_categories: Array<ThirdPlatformCategory>
}

/**
 * 平台信息
 */
export interface Platform {
  id: number
  name: string
  code: string
  apiUrl: string
}

/**
 * TEMU分类信息
 */
export interface TemuCategory {
  id: number
  name: string
  path_name: string
  level: number
  is_leaf: boolean
}

/**
 * 扩展的商品接口，包含平台关联信息
 * 这个接口可以被其他组件继承使用
 */
export interface GoodsWithPlatformRelations {
  platform_relations?: Array<PlatformRelation>
}
