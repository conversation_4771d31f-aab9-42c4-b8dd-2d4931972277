<?php

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\UserPointsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserPointsController extends Controller
{
    protected UserPointsService $userPointsService;

    public function __construct(UserPointsService $userPointsService)
    {
        $this->userPointsService = $userPointsService;
        parent::__construct();
    }

    /**
     * 获取用户积分使用记录
     */
    public function getPointsLogs(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];

        $page = (int)$request->get('page', 1);
        $pageSize = (int)$request->get('page_size', 20);

        // 限制每页数量
        $pageSize = min($pageSize, 100);

        // 获取时间筛选参数
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        $result = $this->userPointsService->getUserPointsLogs($userId, $page, $pageSize, $startDate, $endDate);

        return $this->apiSuccess($result, '获取积分使用记录成功');
    }

    /**
     * 检查任务是否已扣除积分
     */
    public function checkTaskPointsDeducted(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $taskId = (int)$request->get('task_id');
        if (!$taskId) {
            return $this->apiError('任务ID不能为空');
        }
        
        $hasDeducted = $this->userPointsService->hasTaskPointsDeducted($userId, $taskId);
        
        return $this->apiSuccess([
            'task_id' => $taskId,
            'has_deducted' => $hasDeducted
        ], '检查成功');
    }
}
