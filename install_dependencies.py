#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
"""

import sys
import subprocess
import os


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 8:
        print("警告: 建议使用Python 3.8或更高版本")
        return False
    
    return True


def install_package(package_name):
    """安装单个包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✓ {package_name} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ {package_name} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"✗ 安装 {package_name} 时出现未知错误: {e}")
        return False


def install_from_requirements():
    """从requirements.txt安装依赖"""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"错误: 找不到 {requirements_file} 文件")
        return False
    
    try:
        print(f"正在从 {requirements_file} 安装依赖...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", requirements_file
        ], capture_output=True, text=True, check=True)
        
        print("✓ 所有依赖安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 安装依赖失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"✗ 安装依赖时出现未知错误: {e}")
        return False


def check_installed_packages():
    """检查已安装的包"""
    required_packages = ["PyQt5", "requests"]
    
    print("\n检查已安装的包:")
    all_installed = True
    
    for package in required_packages:
        try:
            __import__(package.lower().replace("-", "_"))
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
            all_installed = False
    
    return all_installed


def main():
    """主函数"""
    print("跨境蜂助手 - 依赖安装脚本")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        print("建议升级Python版本后再继续")
    
    print("\n开始安装依赖包...")
    
    # 尝试从requirements.txt安装
    if install_from_requirements():
        print("\n验证安装结果:")
        if check_installed_packages():
            print("\n✓ 所有依赖包安装完成！")
            print("现在可以运行应用程序:")
            print("python run_app.py")
        else:
            print("\n✗ 部分依赖包安装失败，请手动安装")
    else:
        print("\n尝试单独安装各个包...")
        
        packages = ["PyQt5==5.15.10", "requests==2.31.0"]
        success_count = 0
        
        for package in packages:
            if install_package(package):
                success_count += 1
        
        if success_count == len(packages):
            print(f"\n✓ 成功安装 {success_count}/{len(packages)} 个包")
        else:
            print(f"\n✗ 只成功安装了 {success_count}/{len(packages)} 个包")


if __name__ == "__main__":
    main()
