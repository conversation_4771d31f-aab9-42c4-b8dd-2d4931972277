<?php

namespace App\Service\User;

use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Models\User\GoodsModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\User\UserGoodsCatRelationModel;

/**
 * 用户商品分类关联服务
 */
class UserGoodsCatRelationService extends BaseService
{
    /**
     * 获取商品的分类关联信息
     * 
     * @param int $userId
     * @param int $goodsId
     * @param int $thirdPlatformId
     * @return array|null
     */
    public function getGoodsCatRelation(int $userId, int $goodsId, int $thirdPlatformId): ?array
    {
        $relation = UserGoodsCatRelationModel::query()
            ->where('user_id', $userId)
            ->where('goods_id', $goodsId)
            ->where('third_platform_id', $thirdPlatformId)
            ->first();
        return $relation ? $relation->toArray() : null;
    }

    /**
     * 保存或更新商品分类关联
     * 
     * @param array $data
     * @return array
     */
    public function saveGoodsCatRelation(array $data): array
    {
        try {
            DB::beginTransaction();

            // 检查是否已存在关联记录
            $existingRelation = UserGoodsCatRelationModel::query()
                ->where('user_id', $data['user_id'])
                ->where('goods_id', $data['goods_id'])
                ->where('third_platform_id', $data['third_platform_id'])
                ->first();

            $now = now();
            
            if ($existingRelation) {
                // 更新现有记录
                $updateData = [
                    'temu_cat_id' => $data['temu_cat_id'],
                    'third_platform_cat_id' => $data['third_platform_cat_id'],
                    'third_platform_cat_name' => $data['third_platform_cat_name'],
                    'third_platform_cat_name_tl' => $data['third_platform_cat_name_tl'] ?? '',
                    'third_platform_cat_path' => $data['third_platform_cat_path'] ?? '',
                    'third_platform_cat_path_tl' => $data['third_platform_cat_path_tl'] ?? '',
                    'status' => 1,
                    'updated_at' => $now
                ];

                UserGoodsCatRelationModel::query()
                    ->where('id', $existingRelation->id)
                    ->update($updateData);

                $relationId = $existingRelation->id;
                $isNew = false;
            } else {
                // 创建新记录
                $insertData = [
                    'user_id' => $data['user_id'],
                    'goods_id' => $data['goods_id'],
                    'temu_cat_id' => $data['temu_cat_id'],
                    'third_platform_id' => $data['third_platform_id'],
                    'third_platform_cat_id' => $data['third_platform_cat_id'],
                    'third_platform_cat_name' => $data['third_platform_cat_name'],
                    'third_platform_cat_name_tl' => $data['third_platform_cat_name_tl'] ?? '',
                    'third_platform_cat_path' => $data['third_platform_cat_path'] ?? '',
                    'third_platform_cat_path_tl' => $data['third_platform_cat_path_tl'] ?? '',
                    'status' => 1,
                    'created_at' => $now,
                    'updated_at' => $now
                ];

                $relationId = UserGoodsCatRelationModel::query()->insertGetId($insertData);
                $isNew = true;
            }

            GoodsModel::query()->where('id', $data['goods_id'])
                        ->update(['is_cat_modified' => 1, 'updated_at' => $now]);

            DB::commit();

            // 返回保存后的记录
            $savedRelation = UserGoodsCatRelationModel::query()->where('id', $relationId)->first();

            return [
                'id' => $relationId,
                'is_new' => $isNew,
                'relation' => $savedRelation->toArray()
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('保存商品分类关联失败');
        }
    }

    /**
     * 删除商品分类关联
     * 
     * @param int $userId
     * @param int $goodsId
     * @param int $thirdPlatformId
     * @return bool
     */
    public function deleteGoodsCatRelation(int $userId, int $goodsId, int $thirdPlatformId): bool
    {
        try {
            $affected = UserGoodsCatRelationModel::query()
                ->where('user_id', $userId)
                ->where('goods_id', $goodsId)
                ->where('third_platform_id', $thirdPlatformId)
                ->delete();

            return $affected > 0;
        } catch (\Exception $e) {
            throw new MyException('删除商品分类关联失败');
        }
    }

    /**
     * 批量获取商品的分类关联信息
     * 
     * @param int $userId
     * @param array $goodsIds
     * @param int $thirdPlatformId
     * @return array
     */
    public function batchGetGoodsCatRelations(int $userId, array $goodsIds, int $thirdPlatformId): array
    {
        try {
            $relations = UserGoodsCatRelationModel::query()
                ->where('user_id', $userId)
                ->whereIn('goods_id', $goodsIds)
                ->where('third_platform_id', $thirdPlatformId)
                ->get()
                ->keyBy('goods_id')
                ->toArray();

            // 转换为数组格式
            $result = [];
            foreach ($relations as $goodsId => $relation) {
                $result[$goodsId] = $relation->toArray();
            }

            return $result;
        } catch (\Exception $e) {
            throw new MyException('批量获取商品分类关联失败');
        }
    }

    /**
     * 根据用户ID和第三方平台ID获取所有关联记录
     * 
     * @param int $userId
     * @param int $thirdPlatformId
     * @return array
     */
    public function getUserCatRelations(int $userId, int $thirdPlatformId): array
    {
        try {
            $relations = UserGoodsCatRelationModel::query()
                ->where('user_id', $userId)
                ->where('third_platform_id', $thirdPlatformId)
                ->orderBy('created_at', 'desc')
                ->get()
                ->toArray();

            return array_map(function($relation) {
                return $relation->toArray();
            }, $relations);
        } catch (\Exception $e) {
            throw new MyException('获取用户分类关联列表失败');
        }
    }
}
