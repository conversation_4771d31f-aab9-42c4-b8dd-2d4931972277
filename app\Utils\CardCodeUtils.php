<?php

namespace App\Utils;

use App\Models\User\CardCodeModel;
use Illuminate\Support\Str;

class CardCodeUtils
{
    /**
     * 生成唯一卡密
     */
    public static function generateUniqueCardCode(string $prefix = 'KJF', int $maxAttempts = 100): string
    {
        if(empty($prefix)){
            $prefix = 'KJF';
        }
        // 验证前缀格式
        if (!self::validatePrefix($prefix)) {
            throw new \InvalidArgumentException('前缀格式不正确，应为1-3个字符，不能以数字0开始');
        }

        for ($i = 0; $i < $maxAttempts; $i++) {
            // 生成随机字符串（包含大小写字母和数字）
            $randomLength = 50 - strlen($prefix) - 1; // 减去前缀和连接符的长度
            if ($randomLength <= 0) {
                throw new \InvalidArgumentException('前缀过长，无法生成有效的卡密');
            }
            
            $randomString = self::generateRandomString($randomLength);
            $cardCode = $prefix . '-' . $randomString;
            $cardCode = strtoupper($cardCode);

            // 检查是否已存在（区分大小写）
            if (!CardCodeModel::byCardCode($cardCode)->exists()) {
                return $cardCode;
            }
        }

        throw new \Exception('生成唯一卡密失败，请重试');
    }

    /**
     * 生成随机字符串
     */
    private static function generateRandomString(int $length): string
    {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $randomString = '';
        
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $randomString;
    }

    /**
     * 验证前缀格式
     */
    public static function validatePrefix(string $prefix): bool
    {
        // 前缀：1-3字符，不能以数字0开始
        return preg_match('/^[a-zA-Z1-9][a-zA-Z0-9]{0,2}$/', $prefix);
    }

    /**
     * 验证卡密格式
     */
    public static function validateCardCode(string $cardCode): bool
    {
        // 卡密格式：前缀-随机字符串，总长度≤50字符
        if (strlen($cardCode) > 50) {
            return false;
        }
        
        return preg_match('/^[a-zA-Z1-9][a-zA-Z0-9]{0,2}-[A-Z0-9]+$/', $cardCode);
    }

    /**
     * 生成批次号
     */
    public static function generateBatchNo(): string
    {
        $timestamp = date('YmdHis');
        $random = strtoupper(Str::random(6));
        return "BATCH_{$timestamp}_{$random}";
    }

    /**
     * 格式化价格
     */
    public static function formatPrice(float $price): string
    {
        return '¥' . number_format($price, 2);
    }

    /**
     * 格式化积分
     */
    public static function formatPoints(int $points): string
    {
        if ($points >= 10000) {
            return number_format($points / 10000, 1) . '万';
        }
        return number_format($points);
    }

    /**
     * 计算使用率
     */
    public static function calculateUsageRate(int $used, int $total): float
    {
        if ($total === 0) {
            return 0.0;
        }
        return round(($used / $total) * 100, 2);
    }

    /**
     * 检查卡密是否过期
     */
    public static function isCardExpired(?string $validUntil): bool
    {
        if (!$validUntil) {
            return false; // 永久有效
        }
        
        return now()->isAfter($validUntil);
    }

    /**
     * 获取卡密剩余有效天数
     */
    public static function getRemainingDays(?string $validUntil): int
    {
        if (!$validUntil) {
            return -1; // -1表示永久有效
        }
        
        $expiry = \Carbon\Carbon::parse($validUntil);
        $now = now();
        
        if ($expiry->isPast()) {
            return 0; // 已过期
        }
        
        return $now->diffInDays($expiry, false);
    }

    /**
     * 获取卡密类型文本
     */
    public static function getCardTypeText(int $cardType): string
    {
        return match($cardType) {
            CardCodeModel::TYPE_VIP_CARD => '有效期卡',
            CardCodeModel::TYPE_POINTS_CARD => '积分卡',
            default => '未知类型'
        };
    }

    /**
     * 获取卡密状态文本
     */
    public static function getCardStatusText(int $status): string
    {
        return match($status) {
            CardCodeModel::STATUS_USED => '已使用',
            CardCodeModel::STATUS_UNUSED => '未使用',
            CardCodeModel::STATUS_DISABLED => '已禁用',
            default => '未知状态'
        };
    }

    /**
     * 获取状态对应的颜色
     */
    public static function getStatusColor(int $status): string
    {
        return match($status) {
            CardCodeModel::STATUS_USED => 'info',
            CardCodeModel::STATUS_UNUSED => 'success',
            CardCodeModel::STATUS_DISABLED => 'danger',
            default => 'info'
        };
    }

    /**
     * 验证卡密创建数据
     */
    public static function validateCardData(array $data): array
    {
        $errors = [];

        // 验证卡密名称
        if (empty($data['card_name']) || strlen($data['card_name']) > 100) {
            $errors[] = '卡密名称不能为空且长度不能超过100字符';
        }

        // 验证卡密类型
        if (!in_array($data['card_type'], [CardCodeModel::TYPE_VIP_CARD, CardCodeModel::TYPE_POINTS_CARD])) {
            $errors[] = '卡密类型无效';
        }

        // 验证价格
        if (!is_numeric($data['price']) || $data['price'] < 0 || $data['price'] > 999999.99) {
            $errors[] = '价格必须为0-999999.99之间的数字';
        }

        // 验证积分
        if (!is_numeric($data['points']) || $data['points'] < 0 || $data['points'] > 999999999) {
            $errors[] = '积分必须为0-999999999之间的整数';
        }

        // 验证VIP相关字段（仅对有效期卡）
        if ($data['card_type'] == CardCodeModel::TYPE_VIP_CARD) {
            if (isset($data['vip_days']) && (!is_numeric($data['vip_days']) || $data['vip_days'] < 0 || $data['vip_days'] > 36500)) {
                $errors[] = 'VIP时长必须为0-36500之间的整数';
            }
            
            if (isset($data['vip_level']) && (!is_numeric($data['vip_level']) || $data['vip_level'] < 1 || $data['vip_level'] > 10)) {
                $errors[] = 'VIP等级必须为1-10之间的整数';
            }
        }

        // 验证前缀
        if (isset($data['prefix']) && !self::validatePrefix($data['prefix'])) {
            $errors[] = '前缀格式不正确，应为1-3个字符，不能以数字0开始';
        }

        // 验证有效期
        if (isset($data['valid_until']) && $data['valid_until']) {
            try {
                $validUntil = \Carbon\Carbon::parse($data['valid_until']);
                if ($validUntil->isPast()) {
                    $errors[] = '有效期不能是过去的时间';
                }
            } catch (\Exception $e) {
                $errors[] = '有效期格式不正确';
            }
        }

        // 验证描述长度
        if (isset($data['description']) && strlen($data['description']) > 500) {
            $errors[] = '描述长度不能超过500字符';
        }

        return $errors;
    }

    /**
     * 验证批量创建数据
     */
    public static function validateBatchCreateData(array $data): array
    {
        $errors = self::validateCardData($data);

        // 验证数量
        if (!isset($data['quantity']) || !is_numeric($data['quantity']) || $data['quantity'] < 1 || $data['quantity'] > 1000) {
            $errors[] = '生成数量必须为1-1000之间的整数';
        }

        return $errors;
    }
}
