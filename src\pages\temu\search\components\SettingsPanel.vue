<template>
  <div class="settings-panel" :class="{ 'collapsed': isCollapsed }" :style="{ opacity: transparency / 100 }">
    <div class="settings-header">
      <div class="plugin-name">{{ pluginName }}</div>
      <h3>设置</h3>
    </div>
    <div class="settings-content">
      <!-- 国家选择区域 -->
      <div v-if="!isCollapsed" class="country-section">
        <label class="country-label">选择国家：</label>
        <div class="country-buttons">
          <button
            @click="selectCountry('turkey')"
            :class="['country-button', { 'country-button-active': selectedCountry === 'turkey' }]"
          >
            土耳其
          </button>
          <button
            @click="selectCountry('canada')"
            :class="['country-button', { 'country-button-active': selectedCountry === 'canada' }]"
          >
            加拿大
          </button>
        </div>
      </div>

      <!-- 筛选条件区域 -->
      <div v-if="!isCollapsed" class="filter-section">
        <!-- 价格筛选 -->
        <div class="filter-item">
          <label class="filter-label">价格：</label>
          <div class="price-inputs">
            <input 
              type="text" 
              placeholder="最小值"
              :value="filterSettings.priceFilter.minPrice || ''"
              @input="handlePriceInput('min', $event)"
              @blur="handlePriceBlur('min', $event)"
              class="price-input"
              :class="{ 'input-error': priceInputError }"
            />
            <span class="separator">-</span>
            <input 
              type="text" 
              placeholder="最大值"
              :value="filterSettings.priceFilter.maxPrice || ''"
              @input="handlePriceInput('max', $event)"
              @blur="handlePriceBlur('max', $event)"
              class="price-input"
              :class="{ 'input-error': priceInputError }"
            />
          </div>
          <div v-if="priceInputError" class="input-error-message">
            {{ priceInputError }}
          </div>
        </div>

        <!-- 销量筛选 -->
        <div class="filter-item">
          <label class="filter-label">销量：</label>
          <div class="sales-inputs">
            <input 
              type="text" 
              placeholder="最小值"
              :value="filterSettings.salesFilter.minSales || ''"
              @input="handleSalesInput('min', $event)"
              @blur="handleSalesBlur('min', $event)"
              class="sales-input"
              :class="{ 'input-error': salesInputError }"
            />
            <span class="separator">-</span>
            <input 
              type="text" 
              placeholder="最大值"
              :value="filterSettings.salesFilter.maxSales || ''"
              @input="handleSalesInput('max', $event)"
              @blur="handleSalesBlur('max', $event)"
              class="sales-input"
              :class="{ 'input-error': salesInputError }"
            />
          </div>
          <div v-if="salesInputError" class="input-error-message">
            {{ salesInputError }}
          </div>
        </div>

        <!-- 本地商品筛选 -->
        <div class="filter-item">
          <label class="filter-label">去除本地商品</label>
          <div class="switch-container">
            <input
              type="checkbox"
              id="remove-local-switch"
              :checked="filterSettings.removeLocalProducts"
              @change="handleRemoveLocalChange"
              class="switch-input"
            />
            <label for="remove-local-switch" class="switch-label">
              <span class="switch-slider"></span>
            </label>
          </div>
        </div>
      </div>

      <!-- 去重按钮 -->
      <div v-if="!isCollapsed" class="deduplication-section">
        <button
          @click="handleDeduplicateEnhanced"
          :disabled="isDeduplicating || hasInputErrors"
          class="deduplication-button"
          :class="{ 'button-disabled': hasInputErrors }"
        >
          <span v-if="isDeduplicating" class="loading-spinner"></span>
          <span v-if="hasInputErrors" class="error-icon">⚠</span>
          {{ getDeduplicationButtonText() }}
        </button>
        <span v-if="deduplicatedCount > 0" class="deduplication-result">
          已去重 {{ deduplicatedCount }} 个商品
        </span>
        <div v-if="hasInputErrors" class="button-error-hint">
          请先修正筛选条件中的错误
        </div>
      </div>

      <!-- 商品多选开关 -->
      <div class="setting-item">
        <label class="setting-label">开启商品多选</label>
        <div class="switch-container">
          <input
            type="checkbox"
            id="multi-select-switch"
            :checked="multiSelectEnabled"
            @change="handleMultiSelectChange"
            class="switch-input"
          />
          <label for="multi-select-switch" class="switch-label">
            <span class="switch-slider"></span>
          </label>
        </div>
      </div>

      <!-- 透明度控制 -->
      <div class="setting-item">
        <label class="setting-label">透明度控制</label>
        <div class="transparency-control">
          <input
            type="range"
            id="transparency-slider"
            min="30"
            max="100"
            :value="transparency"
            @input="handleTransparencyChange"
            class="transparency-slider"
          />
          <span class="transparency-value">{{ transparency }}%</span>
        </div>
      </div>

      <!-- 折叠/展开按钮 -->
      <div class="collapse-section">
        <button
          @click="toggleCollapse"
          class="collapse-button"
          :title="isCollapsed ? '展开面板' : '折叠面板'"
        >
          <span class="collapse-icon">{{ isCollapsed ? '▼' : '▲' }}</span>
          {{ isCollapsed ? '展开面板' : '折叠面板' }}
        </button>
      </div>

      <!-- 导出按钮 -->
      <div v-if="multiSelectEnabled && !isCollapsed" class="export-section">
        <button
          @click="handleExport"
          :disabled="isExporting"
          class="export-button"
        >
          <span v-if="isExporting" class="loading-spinner"></span>
          {{ isExporting ? '正在导出...' : '导出到EXCEL' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import type { ProductFilterSettings } from '@/utils/temu/settingsManager';
import { validatePriceInput, validateSalesInput } from '@/utils/temu/inputValidation';
import { ElMessage } from 'element-plus';

// 国家缓存键
const COUNTRY_STORAGE_KEY = 'temu_search_selected_country';

// 筛选条件缓存键
const FILTER_CACHE_KEYS = {
  TURKEY: 'temu_filter_cache_turkey',
  CANADA: 'temu_filter_cache_canada'
};

// 国家选择的响应式变量
const selectedCountry = ref<'turkey' | 'canada'>('turkey');

// 保存国家到缓存
const saveCountryToCache = async (country: 'turkey' | 'canada') => {
  try {
    if (chrome?.storage?.local) {
      await chrome.storage.local.set({ [COUNTRY_STORAGE_KEY]: country });
    } else {
      // 降级到localStorage
      localStorage.setItem(COUNTRY_STORAGE_KEY, country);
    }
  } catch (error) {
    console.warn('保存国家到缓存失败:', error);
  }
};

// 从缓存加载国家
const loadCountryFromCache = async (): Promise<'turkey' | 'canada'> => {
  try {
    if (chrome?.storage?.local) {
      const result = await chrome.storage.local.get([COUNTRY_STORAGE_KEY]);
      return result[COUNTRY_STORAGE_KEY] || 'turkey';
    } else {
      // 降级到localStorage
      const cached = localStorage.getItem(COUNTRY_STORAGE_KEY);
      return cached === 'canada' ? 'canada' : 'turkey';
    }
  } catch (error) {
    console.warn('从缓存加载国家失败:', error);
    return 'turkey';
  }
};

// 保存筛选条件到缓存
const saveFilterToCache = async (country: 'turkey' | 'canada', filter: ProductFilterSettings) => {
  try {
    const cacheKey = FILTER_CACHE_KEYS[country.toUpperCase() as keyof typeof FILTER_CACHE_KEYS];
    if (chrome?.storage?.local) {
      await chrome.storage.local.set({ [cacheKey]: filter });
    } else {
      // 降级到localStorage
      localStorage.setItem(cacheKey, JSON.stringify(filter));
    }
  } catch (error) {
    console.warn('保存筛选条件失败:', error);
  }
};

// 从缓存加载筛选条件
const loadFilterFromCache = async (country: 'turkey' | 'canada'): Promise<ProductFilterSettings | null> => {
  try {
    const cacheKey = FILTER_CACHE_KEYS[country.toUpperCase() as keyof typeof FILTER_CACHE_KEYS];
    if (chrome?.storage?.local) {
      const result = await chrome.storage.local.get([cacheKey]);
      return result[cacheKey] || null;
    } else {
      // 降级到localStorage
      const localData = localStorage.getItem(cacheKey);
      if (localData) {
        try {
          return JSON.parse(localData);
        } catch (parseError) {
          console.warn('解析localStorage数据失败:', parseError);
        }
      }
      return null;
    }
  } catch (error) {
    console.warn('加载筛选条件失败:', error);
    return null;
  }
};

// 国家选择方法
const selectCountry = async (country: 'turkey' | 'canada') => {
  selectedCountry.value = country;
  
  // 保存到缓存
  await saveCountryToCache(country);
  
  // 加载对应国家的缓存筛选条件
  const cachedFilter = await loadFilterFromCache(country);
  if (cachedFilter) {
    // 使用缓存值覆盖当前筛选设置
    const newFilterSettings: ProductFilterSettings = {
      ...props.filterSettings,
      priceFilter: {
        minPrice: cachedFilter.priceFilter?.minPrice ?? (country === 'turkey' ? 200 : 6.5),
        maxPrice: cachedFilter.priceFilter?.maxPrice ?? (country === 'turkey' ? 1000 : 34)
      },
      salesFilter: {
        minSales: cachedFilter.salesFilter?.minSales ?? 10,
        maxSales: cachedFilter.salesFilter?.maxSales ?? 10000
      },
      removeLocalProducts: cachedFilter.removeLocalProducts ?? false
    };
    emit('filterSettingsChange', newFilterSettings);
  } else {
    // 没有缓存时使用默认值
    const newFilterSettings: ProductFilterSettings = {
      ...props.filterSettings,
      priceFilter: {
        minPrice: country === 'turkey' ? 200 : 6.5,
        maxPrice: country === 'turkey' ? 1000 : 34
      },
      salesFilter: {
        minSales: 10,
        maxSales: 10000
      },
      removeLocalProducts: false
    };
    emit('filterSettingsChange', newFilterSettings);
  }
};

interface Props {
  pluginName: string;
  multiSelectEnabled: boolean;
  isExporting: boolean;
  isDeduplicating: boolean;
  deduplicatedCount: number;
  filterSettings: ProductFilterSettings;
}

interface Emits {
  (e: 'multiSelectChange', value: boolean): void;
  (e: 'export'): void;
  (e: 'deduplicate'): void;
  (e: 'filterSettingsChange', settings: ProductFilterSettings): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 初始化国家选择
const initializeCountrySelection = async () => {
  const cachedCountry = await loadCountryFromCache();
  selectedCountry.value = cachedCountry;
  
  // 加载对应国家的缓存筛选条件
  const cachedFilter = await loadFilterFromCache(cachedCountry);
  
  let newFilterSettings: ProductFilterSettings;
  
  if (cachedFilter) {
    // 使用缓存值优先于默认值
    newFilterSettings = {
      ...props.filterSettings,
      priceFilter: {
        minPrice: cachedFilter.priceFilter?.minPrice ?? (cachedCountry === 'turkey' ? 200 : 6.5),
        maxPrice: cachedFilter.priceFilter?.maxPrice ?? (cachedCountry === 'turkey' ? 1000 : 34)
      },
      salesFilter: {
        minSales: cachedFilter.salesFilter?.minSales ?? 10,
        maxSales: cachedFilter.salesFilter?.maxSales ?? 10000
      },
      removeLocalProducts: cachedFilter.removeLocalProducts ?? false
    };
  } else {
    // 没有缓存时使用默认值
    newFilterSettings = {
      ...props.filterSettings,
      priceFilter: {
        minPrice: cachedCountry === 'turkey' ? 200 : 6.5,
        maxPrice: cachedCountry === 'turkey' ? 1000 : 34
      },
      salesFilter: {
        minSales: 10,
        maxSales: 10000
      },
      removeLocalProducts: false
    };
  }
  
  emit('filterSettingsChange', newFilterSettings);
};

// 组件挂载时初始化
onMounted(() => {
  initializeCountrySelection();
  loadTransparency();
  loadCollapseState();
});

// 透明度相关状态和方法
const transparency = ref(100);
const isCollapsed = ref(false);

// 透明度缓存键
const TRANSPARENCY_CACHE_KEY = 'temu_settings_panel_transparency';
const COLLAPSE_CACHE_KEY = 'temu_settings_panel_collapsed';

// 处理透明度变化
const handleTransparencyChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = parseInt(target.value);
  transparency.value = value;
  saveTransparency(value);
  applyTransparency(value);
};

// 保存透明度到缓存
const saveTransparency = (value: number) => {
  try {
    localStorage.setItem(TRANSPARENCY_CACHE_KEY, value.toString());
  } catch (error) {
    console.warn('保存透明度设置失败:', error);
  }
};

// 从缓存加载透明度
const loadTransparency = () => {
  try {
    const cached = localStorage.getItem(TRANSPARENCY_CACHE_KEY);
    if (cached !== null) {
      const value = parseInt(cached);
      if (!isNaN(value) && value >= 30 && value <= 100) {
        transparency.value = value;
        applyTransparency(value);
      }
    }
  } catch (error) {
    console.warn('加载透明度设置失败:', error);
  }
};

// 应用透明度到面板
const applyTransparency = (value: number) => {
  const opacity = value / 100;
  const panel = document.querySelector('.settings-panel') as HTMLElement;
  if (panel) {
    panel.style.opacity = opacity.toString();
  }
};

// 折叠/展开相关方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  saveCollapseState(isCollapsed.value);
};

// 保存折叠状态到缓存
const saveCollapseState = (collapsed: boolean) => {
  try {
    localStorage.setItem(COLLAPSE_CACHE_KEY, collapsed.toString());
  } catch (error) {
    console.warn('保存折叠状态失败:', error);
  }
};

// 从缓存加载折叠状态
const loadCollapseState = () => {
  try {
    const cached = localStorage.getItem(COLLAPSE_CACHE_KEY);
    if (cached !== null) {
      isCollapsed.value = cached === 'true';
    }
  } catch (error) {
    console.warn('加载折叠状态失败:', error);
  }
};

// 错误状态管理
const priceInputError = ref<string>('');
const salesInputError = ref<string>('');

// 监听筛选设置变化，清除相关错误
watch(() => props.filterSettings, () => {
  // 当设置从外部更新时，清除错误状态
  priceInputError.value = '';
  salesInputError.value = '';
}, { deep: true });

// 计算是否有输入错误
const hasInputErrors = ref(false);

// 监听错误状态变化
watch([priceInputError, salesInputError], ([priceError, salesError]) => {
  hasInputErrors.value = !!(priceError || salesError);
}, { immediate: true });

const handleMultiSelectChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  emit('multiSelectChange', target.checked);
};

const handleExport = () => {
  emit('export');
};

const handleDeduplicate = () => {
  emit('deduplicate');
};

const handlePriceInput = (type: 'min' | 'max', event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;
  
  // 只做基础清理，不验证范围，只在blur时验证
  // 移除非法字符，允许小数点和逗号
  value = value.replace(/[^\d.,]/g, '');
  
  // 处理多个小数点的情况，只保留第一个
  const parts = value.split('.');
  if (parts.length > 2) {
    value = parts[0] + '.' + parts.slice(1).join('');
  }
  
  // 限制小数位数最多2位
  if (parts.length === 2 && parts[1].length > 2) {
    value = parts[0] + '.' + parts[1].substring(0, 2);
  }
  
  // 更新输入框的值，但不立即触发数据更新
  target.value = value;
  
  // 只在输入框失去焦点时更新数据
  // 不在这里触发emit，只在blur时更新
};

const handleSalesInput = (type: 'min' | 'max', event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;
  
  // 只做基础清理，不验证范围，只在blur时验证
  // 只保留数字字符
  value = value.replace(/[^\d]/g, '');
  
  // 更新输入框的值，但不立即触发数据更新
  target.value = value;
  
  // 只在输入框失去焦点时更新数据
  // 不在这里触发emit，只在blur时更新
};

const handleRemoveLocalChange = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const newFilterSettings: ProductFilterSettings = {
    ...props.filterSettings,
    removeLocalProducts: target.checked
  };
  
  emit('filterSettingsChange', newFilterSettings);
  
  // 保存当前筛选条件到缓存
  await saveFilterToCache(selectedCountry.value, newFilterSettings);
};

// 价格范围验证
const validatePriceRange = () => {
  const { minPrice, maxPrice } = props.filterSettings.priceFilter;
  
  if (minPrice !== undefined && maxPrice !== undefined) {
    if (minPrice > maxPrice) {
      priceInputError.value = '最小价格不能大于最大价格';
      ElMessage.warning('价格范围设置有误：最小价格不能大于最大价格');
      return false;
    }
  }
  
  if (minPrice !== undefined && minPrice < 0) {
    priceInputError.value = '价格不能为负数';
    ElMessage.warning('价格不能为负数');
    return false;
  }
  
  if (maxPrice !== undefined && maxPrice < 0) {
    priceInputError.value = '价格不能为负数';
    ElMessage.warning('价格不能为负数');
    return false;
  }
  
  priceInputError.value = '';
  return true;
};

// 价格输入框 blur 事件处理函数
const handlePriceBlur = async (type: 'min' | 'max', event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;
  
  // 清理和格式化输入值
  value = value.replace(/[^\d.,]/g, '');
  const parts = value.split('.');
  if (parts.length > 2) {
    value = parts[0] + '.' + parts.slice(1).join('');
  }
  if (parts.length === 2 && parts[1].length > 2) {
    value = parts[0] + '.' + parts[1].substring(0, 2);
  }
  
  // 更新输入框显示值
  target.value = value;
  
  // 转换为数字
  const numericValue = value && value !== '.' ? parseFloat(value.replace(/,/g, '')) : undefined;
  
  const newFilterSettings: ProductFilterSettings = {
    ...props.filterSettings,
    priceFilter: {
      ...props.filterSettings.priceFilter,
      [type === 'min' ? 'minPrice' : 'maxPrice']: numericValue
    }
  };
  
  emit('filterSettingsChange', newFilterSettings);
  
  // 验证范围
  if (validatePriceRange()) {
    // 保存当前筛选条件到缓存
    await saveFilterToCache(selectedCountry.value, newFilterSettings);
  }
};

// 销量范围验证
const validateSalesRange = () => {
  const { minSales, maxSales } = props.filterSettings.salesFilter;
  
  if (minSales !== undefined && maxSales !== undefined) {
    if (minSales > maxSales) {
      salesInputError.value = '最小销量不能大于最大销量';
      ElMessage.warning('销量范围设置有误：最小销量不能大于最大销量');
      return false;
    }
  }
  
  if (minSales !== undefined && minSales < 0) {
    salesInputError.value = '销量不能为负数';
    ElMessage.warning('销量不能为负数');
    return false;
  }
  
  if (maxSales !== undefined && maxSales < 0) {
    salesInputError.value = '销量不能为负数';
    ElMessage.warning('销量不能为负数');
    return false;
  }
  
  salesInputError.value = '';
  return true;
};

// 销量输入框 blur 事件处理函数
const handleSalesBlur = async (type: 'min' | 'max', event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;
  
  // 清理和格式化输入值
  value = value.replace(/[^\d]/g, '');
  
  // 更新输入框显示值
  target.value = value;
  
  // 转换为数字
  const numericValue = value ? parseInt(value) : undefined;
  
  const newFilterSettings: ProductFilterSettings = {
    ...props.filterSettings,
    salesFilter: {
      ...props.filterSettings.salesFilter,
      [type === 'min' ? 'minSales' : 'maxSales']: numericValue
    }
  };
  
  emit('filterSettingsChange', newFilterSettings);
  
  // 验证范围
  if (validateSalesRange()) {
    // 保存当前筛选条件到缓存
    await saveFilterToCache(selectedCountry.value, newFilterSettings);
  }
};

// 获取去重按钮文本
const getDeduplicationButtonText = (): string => {
  if (hasInputErrors.value) {
    return '请修正筛选条件';
  }
  if (props.isDeduplicating) {
    return '正在处理...';
  }
  return '商品去重';
};

// 增强的去重处理
const handleDeduplicateEnhanced = () => {
  // 在执行去重前进行最终验证
  const priceValid = validatePriceRange();
  const salesValid = validateSalesRange();
  
  if (!priceValid || !salesValid) {
    ElMessage.error('请先修正筛选条件中的错误');
    return;
  }
  
  // 显示开始处理的反馈
  ElMessage.info('开始处理商品筛选和去重...');
  
  // 调用原始的去重处理
  handleDeduplicate();
};
</script>

<style scoped>
.settings-panel {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(24px);
  border-radius: 16px;
  padding: 18px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-width: 240px;
  max-width: 280px;
  position: relative;
  overflow: hidden;
}

.settings-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  pointer-events: none;
  border-radius: 16px;
}

.settings-header {
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.plugin-name {
  font-size: 11px;
  color: #86868b;
  margin-bottom: 6px;
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.settings-header h3 {
  margin: 0;
  font-size: 17px;
  font-weight: 700;
  color: #1d1d1f;
  text-align: center;
  letter-spacing: -0.2px;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 14px;
  position: relative;
  z-index: 1;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  background: rgba(248, 249, 250, 0.6);
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.setting-item:hover {
  background: rgba(248, 249, 250, 0.9);
  border-color: rgba(0, 0, 0, 0.08);
}

.setting-label {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.setting-label::before {
  content: '';
  width: 4px;
  height: 4px;
  background: #007AFF;
  border-radius: 50%;
  flex-shrink: 0;
}

.switch-container {
  position: relative;
  display: flex;
  align-items: center;
}

.switch-input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.switch-label {
  display: block;
  width: 46px;
  height: 28px;
  background: #e5e5e7;
  border-radius: 28px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.switch-label:hover {
  background: #d1d1d6;
}

.switch-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.switch-input:checked + .switch-label {
  background: #34C759;
  box-shadow: inset 0 2px 4px rgba(52, 199, 89, 0.2);
}

.switch-input:checked + .switch-label:hover {
  background: #28A745;
}

.switch-input:checked + .switch-label .switch-slider {
  transform: translateX(18px);
  box-shadow: 0 2px 8px rgba(52, 199, 89, 0.3), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.switch-input:focus + .switch-label {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(52, 199, 89, 0.2);
}

/* 筛选区域内的开关特殊样式 */
.filter-item .switch-container {
  flex-shrink: 0;
}

.filter-item .switch-label {
  width: 42px;
  height: 24px;
}

.filter-item .switch-slider {
  width: 20px;
  height: 20px;
}

.filter-item .switch-input:checked + .switch-label .switch-slider {
  transform: translateX(18px);
}

.deduplication-section {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
}

.deduplication-section::before {
  content: '';
  position: absolute;
  top: -12px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.deduplication-button {
  width: 100%;
  padding: 10px 16px;
  background: linear-gradient(135deg, #34C759, #28A745);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(52, 199, 89, 0.3);
  position: relative;
  overflow: hidden;
}

.deduplication-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.deduplication-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #28A745, #1e7e34);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(52, 199, 89, 0.4);
}

.deduplication-button:hover:not(:disabled)::before {
  left: 100%;
}

.deduplication-button:active:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 199, 89, 0.3);
}

.deduplication-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #9ca3af;
  box-shadow: none;
}

.deduplication-button.button-disabled {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
  opacity: 0.8;
}

.deduplication-button.button-disabled:hover {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
  transform: none !important;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;
}

.error-icon {
  font-size: 14px;
  color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.button-error-hint {
  font-size: 11px;
  color: #ef4444;
  text-align: center;
  padding: 4px 8px;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(239, 68, 68, 0.2);
  margin-top: 4px;
  animation: slideDown 0.2s ease-out;
}

.deduplication-result {
  font-size: 12px;
  color: #34C759;
  font-weight: 600;
  text-align: center;
  padding: 4px 8px;
  background: rgba(52, 199, 89, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(52, 199, 89, 0.2);
}

.export-section {
  margin-top: 8px;
}

.export-button {
  width: 100%;
  padding: 10px 16px;
  background: linear-gradient(135deg, #007AFF, #0056CC);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.export-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.export-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #0056CC, #003d99);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 122, 255, 0.4);
}

.export-button:hover:not(:disabled)::before {
  left: 100%;
}

.export-button:active:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.export-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #9ca3af;
  box-shadow: none;
}

.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 国家选择样式 */
.country-section {
  margin-bottom: 16px;
}

.country-label {
  font-size: 13px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.country-label::before {
  content: '';
  width: 4px;
  height: 4px;
  background: #007AFF;
  border-radius: 50%;
  flex-shrink: 0;
}

.country-buttons {
  display: flex;
  gap: 8px;
}

.country-button {
  flex: 1;
  padding: 8px 12px;
  border: 1.5px solid #e1e5e9;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.country-button:hover {
  border-color: #007AFF;
  color: #007AFF;
  background: rgba(0, 122, 255, 0.05);
}

.country-button-active {
  background: #007AFF;
  color: white;
  border-color: #007AFF;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.country-button-active:hover {
  background: #0056CC;
  border-color: #0056CC;
}

/* 筛选条件样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 14px;
  margin-bottom: 18px;
  padding: 12px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  position: relative;
}

.filter-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #007AFF, #34C759);
  border-radius: 10px 10px 0 0;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.filter-item:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -7px;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(0, 0, 0, 0.05);
}

.filter-label {
  font-size: 13px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-label::before {
  content: '';
  width: 4px;
  height: 4px;
  background: #007AFF;
  border-radius: 50%;
  flex-shrink: 0;
}

.price-inputs,
.sales-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.price-input,
.sales-input {
  flex: 1;
  padding: 8px 10px;
  border: 1.5px solid #e1e5e9;
  border-radius: 8px;
  font-size: 13px;
  background: white;
  transition: all 0.2s ease;
  font-weight: 500;
  min-width: 0;
}

.price-input:hover,
.sales-input:hover {
  border-color: #c7d2fe;
  background: rgba(247, 250, 255, 0.8);
}

.price-input:focus,
.sales-input:focus {
  outline: none;
  border-color: #007AFF;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.15);
  background: white;
  transform: translateY(-1px);
}

.price-input::placeholder,
.sales-input::placeholder {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 400;
}

/* 输入错误状态样式 */
.input-error {
  border-color: #ef4444 !important;
  background: rgba(239, 68, 68, 0.05) !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.input-error:focus {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
}

.input-error-message {
  font-size: 11px;
  color: #ef4444;
  margin-top: 4px;
  padding: 2px 6px;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(239, 68, 68, 0.2);
  display: flex;
  align-items: center;
  gap: 4px;
  animation: slideDown 0.2s ease-out;
}

.input-error-message::before {
  content: '⚠';
  font-size: 10px;
  color: #ef4444;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.separator {
  font-size: 14px;
  color: #6b7280;
  font-weight: 600;
  flex-shrink: 0;
  padding: 0 2px;
}

/* 本地商品筛选特殊样式 */
.filter-item:last-child {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-top: 4px;
}

.filter-item:last-child .filter-label {
  margin-bottom: 0;
}

.filter-item:last-child .filter-label::before {
  background: #34C759;
}

/* 透明度控制样式 */
.transparency-control {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: flex-end;
}

.transparency-slider {
  width: 80px;
  height: 4px;
  background: #e1e5e9;
  border-radius: 4px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
}

.transparency-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #007AFF;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 122, 255, 0.3);
  transition: all 0.2s ease;
}

.transparency-slider::-webkit-slider-thumb:hover {
  background: #0056CC;
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 122, 255, 0.4);
}

.transparency-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #007AFF;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 122, 255, 0.3);
}

.transparency-value {
  font-size: 12px;
  color: #6b7280;
  font-weight: 600;
  min-width: 30px;
  text-align: right;
}

/* 折叠/展开按钮样式 */
.collapse-section {
  margin-top: 8px;
  display: flex;
  justify-content: center;
}

.collapse-button {
  padding: 8px 16px;
  background: rgba(248, 249, 250, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.collapse-button:hover {
  background: rgba(248, 249, 250, 1);
  border-color: rgba(0, 0, 0, 0.12);
  color: #374151;
  transform: translateY(-1px);
}

.collapse-button:active {
  transform: translateY(0);
}

.collapse-icon {
  font-size: 10px;
  transition: transform 0.2s ease;
}

/* 折叠状态样式 */
.settings-panel.collapsed {
  padding: 12px;
  min-height: auto;
}

.settings-panel.collapsed .settings-content > *:not(.collapse-section) {
  display: none;
}

.settings-panel.collapsed .settings-content .collapse-section {
  margin-top: 0;
}

.settings-panel.collapsed .settings-header {
  margin-bottom: 8px;
}

.settings-panel.collapsed .settings-header h3 {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 320px) {
  .settings-panel {
    min-width: 200px;
    max-width: 240px;
    padding: 12px;
  }
  
  .filter-section {
    gap: 12px;
    padding: 10px;
  }
  
  .price-inputs,
  .sales-inputs {
    gap: 6px;
  }
  
  .price-input,
  .sales-input {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .filter-label {
    font-size: 12px;
  }
  
  .separator {
    font-size: 12px;
  }
}

@media (min-width: 321px) and (max-width: 480px) {
  .settings-panel {
    min-width: 220px;
    max-width: 260px;
  }
}

@media (min-width: 481px) {
  .settings-panel {
    min-width: 260px;
    max-width: 300px;
  }
  
  .filter-section {
    padding: 14px;
  }
  
  .price-input,
  .sales-input {
    padding: 9px 12px;
    font-size: 14px;
  }
  
  .filter-label {
    font-size: 14px;
  }
}
</style>