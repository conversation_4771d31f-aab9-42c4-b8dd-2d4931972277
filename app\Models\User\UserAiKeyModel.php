<?php

namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;

class UserAiKeyModel extends BaseModel
{
    protected $table = 'user_ai_key';
    
    protected $fillable = [
        'user_id',
        'ai_key',
        'description',
        'sort_order',
        'status',
        'ai_type'
    ];

    protected $casts = [
        'user_id' => 'integer',
        'sort_order' => 'integer',
        'status' => 'integer',
        'ai_type' => 'integer'
    ];

    /**
     * 按用户ID查询
     */
    public function scopeByUserId(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 按状态查询
     */
    public function scopeByStatus(Builder $query, int $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * 按AI类型查询
     */
    public function scopeByAiType(Builder $query, int $aiType): Builder
    {
        return $query->where('ai_type', $aiType);
    }

    /**
     * 按日期范围查询
     */
    public function scopeByDateRange(Builder $query, ?string $startDate, ?string $endDate): Builder
    {
        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }
        
        return $query;
    }

    /**
     * 排序：按sort_order降序，id降序
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order', 'desc')->orderBy('id', 'desc');
    }

    /**
     * 检查是否属于指定用户
     */
    public function belongsToUser(int $userId): bool
    {
        return $this->user_id === $userId;
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute(): string
    {
        return $this->status === 1 ? '启用' : '禁用';
    }

    /**
     * 获取AI类型名称
     */
    public function getAiTypeNameAttribute(): string
    {
        $types = [
            1 => 'DeepSeek',
        ];
        
        return $types[$this->ai_type] ?? '未知';
    }
} 