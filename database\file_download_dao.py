#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件下载记录数据访问对象
负责file_download_records表的CRUD操作
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from .db_manager import DatabaseManager


class FileDownloadDAO:
    """文件下载记录数据访问对象"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化文件下载记录DAO
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
    
    def insert_file_record(self, goods_id: int, file_type: str, field: str, url: str,
                          model_id: int = None, sku_id: int = None) -> int:
        """
        插入新文件下载记录
        
        Args:
            goods_id: 商品ID
            file_type: 文件类型 (image, video, pdf)
            field: 字段名
            url: 原始URL
            model_id: 模型ID（可选）
            sku_id: SKU ID（可选）
            
        Returns:
            新插入记录的ID
        """
        query = """
            INSERT INTO file_download_records 
            (goods_id, type, field, url, model_id, sku_id, is_downloaded, is_uploaded, 
             download_attempts, upload_attempts, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, 0, 0, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """
        
        return self.db_manager.execute_insert(query, (
            goods_id, file_type, field, url, model_id, sku_id
        ))
    
    def get_files_by_goods_id(self, goods_id: int) -> List[Dict[str, Any]]:
        """
        根据商品ID获取所有文件记录
        
        Args:
            goods_id: 商品ID
            
        Returns:
            文件记录列表
        """
        query = """
            SELECT id, goods_id, type, field, url, url_local, model_id, sku_id,
                   is_downloaded, is_uploaded, download_attempts, upload_attempts,
                   error_message, created_at, updated_at
            FROM file_download_records
            WHERE goods_id = ?
            ORDER BY created_at ASC
        """
        
        rows = self.db_manager.execute_query(query, (goods_id,))
        return [dict(row) for row in rows]
    
    def get_pending_download_files(self, goods_id: int) -> List[Dict[str, Any]]:
        """
        获取待下载的文件记录
        
        Args:
            goods_id: 商品ID
            
        Returns:
            待下载文件记录列表
        """
        query = """
            SELECT id, goods_id, type, field, url, url_local, model_id, sku_id,
                   is_downloaded, is_uploaded, download_attempts, upload_attempts,
                   error_message, created_at, updated_at
            FROM file_download_records
            WHERE goods_id = ? AND is_downloaded = 0
            ORDER BY created_at ASC
        """
        
        rows = self.db_manager.execute_query(query, (goods_id,))
        return [dict(row) for row in rows]
    
    def get_pending_upload_files(self, goods_id: int) -> List[Dict[str, Any]]:
        """
        获取待上传的文件记录（已下载但未上传）

        Args:
            goods_id: 商品ID

        Returns:
            待上传文件记录列表，包含goods_platform_id字段
        """
        query = """
            SELECT fdr.id, fdr.goods_id, fdr.type, fdr.field, fdr.url, fdr.url_local,
                   fdr.model_id, fdr.sku_id, fdr.is_downloaded, fdr.is_uploaded,
                   fdr.download_attempts, fdr.upload_attempts, fdr.error_message,
                   fdr.created_at, fdr.updated_at, g.goods_platform_id
            FROM file_download_records fdr
            JOIN goods g ON fdr.goods_id = g.goods_id
            WHERE fdr.goods_id = ? AND fdr.is_downloaded = 1 AND fdr.is_uploaded = 0
            ORDER BY fdr.created_at ASC
        """

        rows = self.db_manager.execute_query(query, (goods_id,))
        return [dict(row) for row in rows]
    
    def update_download_status(self, record_id: int, is_downloaded: bool, 
                              url_local: str = None, error_message: str = None) -> int:
        """
        更新文件下载状态
        
        Args:
            record_id: 记录ID
            is_downloaded: 是否已下载
            url_local: 本地存储路径
            error_message: 错误消息
            
        Returns:
            受影响的行数
        """
        if url_local:
            query = """
                UPDATE file_download_records 
                SET is_downloaded = ?, url_local = ?, error_message = ?, 
                    download_attempts = download_attempts + 1, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            params = (1 if is_downloaded else 0, url_local, error_message, record_id)
        else:
            query = """
                UPDATE file_download_records 
                SET is_downloaded = ?, error_message = ?, 
                    download_attempts = download_attempts + 1, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            params = (1 if is_downloaded else 0, error_message, record_id)
        
        return self.db_manager.execute_update(query, params)
    
    def update_upload_status(self, record_id: int, is_uploaded: bool, 
                            error_message: str = None) -> int:
        """
        更新文件上传状态
        
        Args:
            record_id: 记录ID
            is_uploaded: 是否已上传
            error_message: 错误消息
            
        Returns:
            受影响的行数
        """
        query = """
            UPDATE file_download_records 
            SET is_uploaded = ?, error_message = ?, 
                upload_attempts = upload_attempts + 1, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """
        
        return self.db_manager.execute_update(query, (
            1 if is_uploaded else 0, error_message, record_id
        ))
    
    def check_all_files_downloaded(self, goods_id: int) -> bool:
        """
        检查商品的所有文件是否都已下载完成
        
        Args:
            goods_id: 商品ID
            
        Returns:
            是否全部下载完成
        """
        query = """
            SELECT COUNT(*) as total_count,
                   SUM(CASE WHEN is_downloaded = 1 THEN 1 ELSE 0 END) as downloaded_count
            FROM file_download_records
            WHERE goods_id = ?
        """
        
        rows = self.db_manager.execute_query(query, (goods_id,))
        if rows:
            row = rows[0]
            total_count = row['total_count']
            downloaded_count = row['downloaded_count']
            return total_count > 0 and total_count == downloaded_count
        return False
    
    def check_all_files_uploaded(self, goods_id: int) -> bool:
        """
        检查商品的所有文件是否都已上传完成
        
        Args:
            goods_id: 商品ID
            
        Returns:
            是否全部上传完成
        """
        query = """
            SELECT COUNT(*) as total_count,
                   SUM(CASE WHEN is_uploaded = 1 THEN 1 ELSE 0 END) as uploaded_count
            FROM file_download_records
            WHERE goods_id = ? AND is_downloaded = 1
        """
        
        rows = self.db_manager.execute_query(query, (goods_id,))
        if rows:
            row = rows[0]
            total_count = row['total_count']
            uploaded_count = row['uploaded_count']
            return total_count > 0 and total_count == uploaded_count
        return False
    
    def delete_files_by_goods_id(self, goods_id: int) -> int:
        """
        删除商品的所有文件记录
        
        Args:
            goods_id: 商品ID
            
        Returns:
            受影响的行数
        """
        query = "DELETE FROM file_download_records WHERE goods_id = ?"
        return self.db_manager.execute_update(query, (goods_id,))
    
    def get_file_statistics(self, goods_id: int) -> Dict[str, int]:
        """
        获取文件统计信息
        
        Args:
            goods_id: 商品ID
            
        Returns:
            统计信息字典
        """
        query = """
            SELECT 
                type,
                COUNT(*) as total_count,
                SUM(CASE WHEN is_downloaded = 1 THEN 1 ELSE 0 END) as downloaded_count,
                SUM(CASE WHEN is_uploaded = 1 THEN 1 ELSE 0 END) as uploaded_count
            FROM file_download_records
            WHERE goods_id = ?
            GROUP BY type
        """
        
        rows = self.db_manager.execute_query(query, (goods_id,))
        statistics = {}
        
        for row in rows:
            file_type = row['type']
            statistics[file_type] = {
                'total': row['total_count'],
                'downloaded': row['downloaded_count'],
                'uploaded': row['uploaded_count'],
                'pending_download': row['total_count'] - row['downloaded_count'],
                'pending_upload': row['downloaded_count'] - row['uploaded_count']
            }
        
        return statistics
