<template>
  <el-dialog
    v-model="visible"
    title="选择商品存储目录"
    width="500px"
    :close-on-click-modal="false"
    :z-index="10002"
    append-to-body
    @open="handleDialogOpen"
  >
    <div class="temp-directory-content">
      <div class="notice">
        <el-icon><InfoFilled /></el-icon>
        <span>请为当前商品选择存储目录</span>
      </div>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="商品存储目录" prop="directory_id">
          <div class="directory-select-wrapper">
            <el-select 
              v-model="form.directory_id" 
              placeholder="请选择存储目录"
              class="directory-select"
              :teleported="false"
              popper-class="select-dropdown-in-dialog"
              :fit-input-width="true"
            >
              <el-option
                v-for="directory in availableDirectories"
                :key="directory.id"
                :label="`${directory.name}${!userInfo.isSub ? ` (${directory.goods_count}个商品)` : ''}`"
                :value="directory.id"
              >
                <div class="directory-option">
                  <span
                    :class="[
                      'directory-name',
                      userInfo.isSub && directory.user_sub_id === 0 ? 'system-directory' : 'user-directory'
                    ]"
                  >
                    {{ directory.name }}
                    <span v-if="userInfo.isSub && directory.user_sub_id === 0" class="system-tag">[系统]</span>
                  </span>
                  <span v-if="!userInfo.isSub" class="goods-count">({{ directory.goods_count }}个商品)</span>
                </div>
              </el-option>
            </el-select>
            <el-button
              v-if="!userInfo.isSub"
              type="success"
              @click="$emit('createDirectory')"
              class="add-directory-btn"
            >
              新增目录
            </el-button>
          </div>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="form.no_remind_24h">24小时内不再提醒设置商品存储目录</el-checkbox>
        </el-form-item>

        <!-- 显示下次提醒时间 -->
        <el-form-item v-if="nextRemindTime" label="下次提醒时间">
          <div class="remind-time-info">
            <span :class="{ 'expired': isRemindTimeExpired }">
              {{ nextRemindTime }}
            </span>
            <span v-if="isRemindTimeExpired" class="expired-tag">【已到期】</span>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch, computed } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import type { Directory } from '@/utils/collectionSettingsApi';
import { userInfo } from '@/utils/userStore';

interface Props {
  modelValue: boolean;
  availableDirectories: Directory[];
  loading?: boolean;
  currentSettings?: {
    no_remind_until?: string | null;
  } | null;
}

interface Emits {
  'update:modelValue': [value: boolean];
  'confirm': [data: { directory_id: number; no_remind_24h: boolean }];
  'createDirectory': [];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const formRef = ref();

const form = reactive({
  directory_id: undefined as number | undefined,
  no_remind_24h: false
});

const formRules = {
  directory_id: [
    { required: true, message: '请选择存储目录', trigger: 'change' }
  ]
};

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
});

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

const handleDialogOpen = async () => {
  // 重置表单
  form.directory_id = undefined;
  form.no_remind_24h = false;
  
  // 调试信息
  console.log('TempDirectoryDialog 打开，可用目录:', props.availableDirectories);
  
  await nextTick();
  
  // 强制设置 Select 下拉选项的 z-index
  setTimeout(() => {
    const selectDropdowns = document.querySelectorAll('.el-select-dropdown, .el-popper');
    selectDropdowns.forEach(dropdown => {
      (dropdown as HTMLElement).style.zIndex = '10004';
    });
  }, 100);
};

const handleConfirm = async () => {
  try {
    await formRef.value.validate();
    
    emit('confirm', {
      directory_id: form.directory_id!,
      no_remind_24h: form.no_remind_24h
    });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const handleCancel = () => {
  visible.value = false;
};

// 计算下次提醒时间
const nextRemindTime = computed(() => {
  return props.currentSettings?.no_remind_until || null;
});

// 检查提醒时间是否已到期
const isRemindTimeExpired = computed(() => {
  if (!nextRemindTime.value) return false;
  const remindTime = new Date(nextRemindTime.value);
  const now = new Date();
  return now >= remindTime;
});

// 不需要格式化，直接显示后端返回的时间字符串
</script>

<style scoped>
.temp-directory-content {
  padding: 10px 0;
}

.notice {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  margin-bottom: 20px;
  color: #1e40af;
  font-size: 14px;
}

.directory-select-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;
}

.directory-select {
  flex: 1;
  min-width: 0;
}

/* 目录选项样式 */
.directory-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.directory-name {
  font-weight: 500;
  flex: 1;
}

.system-directory {
  color: #e6a23c;
}

.user-directory {
  color: #606266;
}

.system-tag {
  color: #e6a23c;
  font-size: 12px;
  font-weight: bold;
  margin-left: 4px;
}

.goods-count {
  color: #909399;
  font-size: 12px;
}

:deep(.directory-select .el-select) {
  width: 100% !important;
}

:deep(.directory-select .el-input) {
  width: 100% !important;
}

:deep(.directory-select .el-input__wrapper) {
  width: 100% !important;
  min-width: 200px !important;
}

.add-directory-btn {
  flex-shrink: 0;
  white-space: nowrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Select 下拉选项样式修复 */
:deep(.el-select-dropdown) {
  z-index: 10004 !important;
  min-width: 300px !important;
}

:deep(.select-dropdown-in-dialog) {
  z-index: 10004 !important;
  min-width: 300px !important;
  width: auto !important;
}

:deep(.select-dropdown-in-dialog .el-select-dropdown__item) {
  background: #ffffff !important;
  color: #606266 !important;
  padding: 0 20px !important;
  height: 34px !important;
  line-height: 34px !important;
  font-size: 14px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  min-width: 280px !important;
}

:deep(.select-dropdown-in-dialog .el-select-dropdown__item:hover) {
  background-color: #f5f7fa !important;
}

:deep(.select-dropdown-in-dialog .el-select-dropdown__item.selected) {
  background-color: #409eff !important;
  color: #ffffff !important;
  font-weight: bold !important;
}

/* 强制修复下拉框宽度 */
:deep(.el-popper) {
  min-width: 300px !important;
}

:deep(.el-select-dropdown__wrap) {
  min-width: 300px !important;
}

:deep(.el-scrollbar__view) {
  min-width: 300px !important;
}

/* 提醒时间显示样式 */
.remind-time-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.remind-time-info .expired {
  color: #f56c6c;
}

.remind-time-info .expired-tag {
  color: #f56c6c;
  font-weight: bold;
  font-size: 12px;
}
</style>