<template>
  <div class="dashboard">
    <h2>控制面板</h2>

    <!-- 第一行：用户信息、TEMU商品采集设置、客服支持（一行三列） -->
    <div class="dashboard-row dashboard-grid-3">
      <!-- 用户信息 -->
      <div class="user-profile-card dashboard-card">
        <div class="card-header">
          <h3>用户信息</h3>
        </div>
        <div class="card-content">
          <div class="info-row">
            <span class="info-label">手机号码:</span>
            <span class="info-value">{{ userInfo.phone || '未设置' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">VIP会员:</span>
            <span class="info-value" :class="{ 'vip-active': userInfo.isVip, 'vip-inactive': !userInfo.isVip }">
              {{ userInfo.isVip ? '已开通' : '未开通' }}
            </span>
          </div>
          <div class="info-row">
            <span class="info-label">当前积分:</span>
            <span class="info-value points-value">{{ userInfo.points || 0 }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">账号类型:</span>
            <span class="info-value" :class="{ 'sub-account-active': userInfo.isSub, 'main-account-active': !userInfo.isSub }">
              {{ userInfo.isSub ? '子账号' : '主账号' }}
            </span>
          </div>
          <div v-if="userInfo.isAdmin" class="info-row">
            <span class="info-label">管理员:</span>
            <span class="info-value" :class="{ 'admin-active': userInfo.isAdmin, 'admin-inactive': !userInfo.isAdmin }">
              {{ userInfo.isAdmin ? '是' : '否' }}
            </span>
          </div>
          <div v-if="userInfo.isVip" class="info-row">
            <span class="info-label">有效期至:</span>
            <span class="info-value expiry-date">{{ userInfo.expiryDate || '未设置' }}</span>
          </div>
        </div>
      </div>

      <!-- TEMU商品采集设置 -->
      <div class="collection-settings-card dashboard-card">
        <div class="card-header">
          <h3>TEMU商品采集设置</h3>
          <el-button
            type="primary"
            size="small"
            @click="openSettingsDialog"
            :loading="settingsLoading"
          >
            设置
          </el-button>
        </div>
        <div class="card-content">
          <div v-if="collectionSettings" class="settings-info">
            <div class="info-row">
              <span class="info-label">采集模式:</span>
              <span class="info-value" :class="getCollectionModeClass()">
                {{ collectionSettings.collection_mode_name || '未设置' }}
              </span>
            </div>
            <div class="info-row">
              <span class="info-label">存储目录:</span>
              <span class="info-value">
                {{ collectionSettings.default_directory_name || '未设置' }}
              </span>
            </div>
            <div v-if="collectionSettings.no_remind_until" class="info-row">
              <span class="info-label">下次提醒:</span>
              <span class="info-value" :class="getRemindTimeClass()">
                {{ formatRemindTime(collectionSettings.no_remind_until) }}
              </span>
            </div>
            <div v-else class="info-row">
              <span class="info-label">提醒状态:</span>
              <span class="info-value remind-always">每次均需手动选择目录</span>
            </div>
          </div>
          <div v-else class="settings-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>正在加载设置信息...</span>
          </div>
        </div>
      </div>

      <!-- 客服支持 -->
      <div class="customer-service-card dashboard-card">
        <div class="card-header">
          <h3>客服支持</h3>
        </div>
        <div class="card-content">
          <div class="qrcode-container">
            <img :src="customerServiceQrUrl" alt="客服二维码" class="qrcode-img" />
            <div class="qrcode-text">咨询请扫码添加客服</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第二行：API凭证管理（单独一行） -->
    <div class="dashboard-row">
      <ApiCredentialsCard
        v-if="canShowApiCredentials"
        :user-info="transformedUserInfo"
        @credentials-updated="handleCredentialsUpdated"
      />
    </div>

    <div class="quick-actions">
      <h3>快捷操作</h3>
      <div class="action-buttons">
        <button class="action-button">
          <span class="action-icon">📦</span>
          <span class="action-text">浏览商品</span>
        </button>
        <button class="action-button">
          <span class="action-icon">🔍</span>
          <span class="action-text">搜索商品</span>
        </button>
        <button class="action-button">
          <span class="action-icon">📊</span>
          <span class="action-text">查看数据</span>
        </button>
        <button class="action-button">
          <span class="action-icon">⚙️</span>
          <span class="action-text">设置</span>
        </button>
      </div>
    </div>

    <!-- 采集设置对话框 -->
    <CollectionSettingsDialog
      v-model="showSettingsDialog"
      :available-directories="availableDirectories"
      :loading="settingsLoading"
      :initial-data="collectionSettings"
      @confirm="handleSettingsConfirm"
      @create-directory="openDirectoryDialog"
    />

    <!-- 目录创建对话框 -->
    <DirectoryCreateDialog
      v-model="showDirectoryDialog"
      :loading="directoryLoading"
      @confirm="handleDirectoryCreate"
    />


  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { userInfo, onUserInfoChange, loadUserInfoFromCache, updateUserInfo } from '../utils/userStore'
import CollectionSettingsDialog from './CollectionSettingsDialog.vue'
import DirectoryCreateDialog from './DirectoryCreateDialog.vue'
import ApiCredentialsCard from './ApiCredentialsCard.vue'
import {
  getUserCollectionSettings,
  saveUserCollectionSettings,
  getUserAvailableDirectories,
  type CollectionSettings,
  type Directory,
  type SaveSettingsData
} from '../utils/collectionSettingsApi'
import { createDirectory } from '../utils/directoryApi'
import { getApiUrl } from '../utils/apiConfig'
import { fetchUserInfo } from '../utils/api'

// 用户信息变化监听器清理函数
let unsubscribe: (() => void) | null = null

// 采集设置相关状态
const collectionSettings = ref<CollectionSettings | null>(null)
const availableDirectories = ref<Directory[]>([])
const settingsLoading = ref(false)
const directoryLoading = ref(false)
const showSettingsDialog = ref(false)
const showDirectoryDialog = ref(false)

// 客服二维码URL
const customerServiceQrUrl = ref('')



// 获取采集模式样式类
const getCollectionModeClass = () => {
  if (!collectionSettings.value) return ''

  switch (collectionSettings.value.collection_mode) {
    case 1:
      return 'mode-auto' // 自动采集 - 绿色
    case 2:
      return 'mode-manual' // 手动采集 - 橙色
    case 3:
      return 'mode-none' // 不采集 - 红色
    default:
      return ''
  }
}

// 获取提醒时间样式类
const getRemindTimeClass = () => {
  if (!collectionSettings.value?.no_remind_until) return ''

  const remindTime = new Date(collectionSettings.value.no_remind_until)
  const now = new Date()

  return now >= remindTime ? 'remind-expired' : 'remind-active'
}

// 权限控制：基于appstatus字段显示或隐藏API凭证功能
const canShowApiCredentials = computed(() => {
  return userInfo.appStatus === 1 && userInfo.isSub === false
})

// 转换用户信息格式以匹配ApiCredentialsCard组件的期望
const transformedUserInfo = computed(() => ({
  id: userInfo.userId || 0,
  phone: userInfo.phone || '',
  is_vip: userInfo.isVip ? 1 : 0,
  vip_end_time: userInfo.expiryDate || '',
  is_admin: userInfo.isAdmin ? 1 : 0,
  is_card_admin: userInfo.isCardAdmin ? 1 : 0,
  is_sub: userInfo.isSub ? 1 : 0,
  appid: userInfo.appId || null,
  appstatus: userInfo.appStatus || 0,
  token: userInfo.token || '',
  points: userInfo.points || 0
}))

// 格式化提醒时间
const formatRemindTime = (timeStr: string) => {
  if (!timeStr) return ''

  const remindTime = new Date(timeStr)
  const now = new Date()

  if (now >= remindTime) {
    return `${timeStr} (已到期)`
  } else {
    return timeStr
  }
}

// 加载采集设置
const loadCollectionSettings = async () => {
  try {
    settingsLoading.value = true
    const settings = await getUserCollectionSettings()
    collectionSettings.value = settings
    console.log('采集设置加载成功:', settings)
  } catch (error) {
    console.error('加载采集设置失败:', error)
    ElMessage.error('加载采集设置失败')
  } finally {
    settingsLoading.value = false
  }
}

// 加载可用目录
const loadAvailableDirectories = async () => {
  try {
    const response = await getUserAvailableDirectories()
    availableDirectories.value = response.list || []
    console.log('可用目录加载成功:', response.list)
  } catch (error) {
    console.error('加载可用目录失败:', error)
    ElMessage.error('加载可用目录失败')
  }
}

// 打开设置对话框
const openSettingsDialog = async () => {
  try {
    settingsLoading.value = true
    await loadAvailableDirectories()
    showSettingsDialog.value = true
  } catch (error) {
    console.error('打开设置对话框失败:', error)
  } finally {
    settingsLoading.value = false
  }
}

// 处理设置确认
const handleSettingsConfirm = async (data: SaveSettingsData) => {
  try {
    settingsLoading.value = true
    const result = await saveUserCollectionSettings(data)

    ElMessage.success('设置保存成功')
    showSettingsDialog.value = false

    // 重新加载设置
    await loadCollectionSettings()

    console.log('设置保存成功:', result)
  } catch (error: any) {
    console.error('保存设置失败:', error)
    ElMessage.error(error.message || '保存设置失败')
  } finally {
    settingsLoading.value = false
  }
}

// 打开目录创建对话框
const openDirectoryDialog = () => {
  showDirectoryDialog.value = true
}

// 处理目录创建
const handleDirectoryCreate = async (data: { name: string; description: string }) => {
  try {
    directoryLoading.value = true

    const result = await createDirectory({
      name: data.name,
      description: data.description,
      status: 1
    })

    ElMessage.success('目录创建成功')
    showDirectoryDialog.value = false

    // 重新加载可用目录
    await loadAvailableDirectories()

    console.log('目录创建成功:', result)
  } catch (error: any) {
    console.error('创建目录失败:', error)
    ElMessage.error(error.message || '创建目录失败')
  } finally {
    directoryLoading.value = false
  }
}

// 加载客服二维码URL
const loadCustomerServiceQrUrl = async () => {
  try {
    const url = await getApiUrl('apiKfUrl')
    customerServiceQrUrl.value = url
    console.log('客服二维码URL加载成功:', url)
  } catch (error) {
    console.error('加载客服二维码URL失败:', error)
  }
}



// 凭证信息更新后的回调处理
const handleCredentialsUpdated = async (updatedUserInfo: any) => {
  try {
    // 更新用户信息状态
    updateUserInfo({
      appId: updatedUserInfo.appid,
      appStatus: updatedUserInfo.appstatus
    })

    console.log('用户凭证信息已更新:', {
      appId: updatedUserInfo.appid,
      appStatus: updatedUserInfo.appstatus
    })
  } catch (error) {
    console.error('更新用户凭证信息失败:', error)
  }
}

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    const updatedUserInfo = await fetchUserInfo()
    if (updatedUserInfo) {
      updateUserInfo({
        appId: updatedUserInfo.appid,
        appStatus: updatedUserInfo.appstatus
      })
    }
  } catch (error) {
    console.error('刷新用户信息失败:', error)
  }
}

onMounted(async () => {
  // 如果用户信息为空，从缓存加载
  if (!userInfo.phone && !userInfo.isLogin) {
    await loadUserInfoFromCache()
  }

  // 监听用户信息变化
  unsubscribe = onUserInfoChange(() => {
    console.log('Dashboard: 用户信息已更新', {
      phone: userInfo.phone,
      isVip: userInfo.isVip,
      expiryDate: userInfo.expiryDate
    })
  })

  // 加载客服二维码URL
  await loadCustomerServiceQrUrl()

  // 如果用户已登录，加载采集设置
  if (userInfo.isLogin) {
    await loadCollectionSettings()
  }
})

onUnmounted(() => {
  // 清理监听器
  if (unsubscribe) {
    unsubscribe()
  }
})
</script>

<style scoped>
.dashboard {
  padding: 10px;
}

h2 {
  margin-top: 0;
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 20px;
}

/* 新的布局样式 */
.dashboard-row {
  margin-bottom: 20px;
}

.dashboard-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  align-items: stretch;
}

.dashboard-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.user-profile-card, .collection-settings-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.customer-service-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  background-color: #eef4ff;
  padding: 15px 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.card-content {
  padding: 20px;
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 100px;
  color: #666;
  text-align: right;
  padding-right: 15px;
}

.info-value {
  flex: 1;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-grid-3 {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .dashboard-grid-3 {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .dashboard {
    padding: 8px;
  }

  .dashboard-row {
    margin-bottom: 15px;
  }

  .card-content {
    padding: 15px;
  }

  .info-label {
    width: 80px;
    font-size: 13px;
  }

  .info-value {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .dashboard {
    padding: 5px;
  }

  .dashboard-grid-3 {
    gap: 10px;
  }

  .card-content {
    padding: 12px;
  }

  .info-label {
    width: 70px;
    font-size: 12px;
  }

  .info-value {
    font-size: 12px;
  }
}

.vip-active, .admin-active {
  color: #52c41a;
  font-weight: bold;
}

.vip-inactive, .admin-inactive {
  color: #ff4d4f;
}

.sub-account-active {
  color: #409eff;
  font-weight: bold;
}

.main-account-active {
  color: #52c41a;
  font-weight: bold;
}

.expiry-date {
  color: #1890ff;
}

.points-value {
  color: #fa8c16;
  font-weight: bold;
}

/* 采集设置相关样式 */
.settings-info {
  min-height: 80px;
}

.settings-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #666;
  min-height: 80px;
}

.mode-auto {
  color: #52c41a;
  font-weight: bold;
}

.mode-manual {
  color: #fa8c16;
  font-weight: bold;
}

.mode-none {
  color: #ff4d4f;
  font-weight: bold;
}

.remind-active {
  color: #52c41a;
}

.remind-expired {
  color: #ff4d4f;
}

.remind-always {
  color: #fa8c16;
}

/* 客服二维码样式 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px 0;
}

.qrcode-img {
  max-width: 128px;
  max-height: 128px;
  width: auto;
  height: auto;
  margin-bottom: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qrcode-text {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.qrcode-url {
  color: #666;
  font-size: 12px;
  word-break: break-all;
  max-width: 300px;
  line-height: 1.4;
}

.quick-actions {
  display:none;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.quick-actions h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.1rem;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border: none;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:hover {
  background-color: #e8f0fe;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 1.5rem;
  margin-bottom: 8px;
}

.action-text {
  color: #555;
  font-size: 0.9rem;
}
</style>
