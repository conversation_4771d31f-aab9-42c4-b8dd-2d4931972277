<?php

namespace App\Models\User;

use App\Models\BaseModel;
use App\Models\TeMu\ProductCategoryTeMuModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserGoodsCatRelationModel extends BaseModel
{
    protected $table = 'user_goods_cat_relation';
    
    protected $fillable = [
        'user_id',
        'goods_id', 
        'temu_cat_id',
        'third_platform_id',
        'third_platform_cat_id',
        'third_platform_cat_name',
        'third_platform_cat_name_tl',
        'third_platform_cat_path',
        'third_platform_cat_path_tl',
        'status'
    ];
    
    protected $casts = [
        'user_id' => 'integer',
        'goods_id' => 'integer',
        'temu_cat_id' => 'integer', 
        'third_platform_id' => 'integer',
        'third_platform_cat_id' => 'integer',
        'third_platform_cat_name' => 'string',
        'third_platform_cat_name_tl' => 'string',
        'third_platform_cat_path' => 'string',
        'third_platform_cat_path_tl' => 'string',
        'status' => 'integer'
    ];
    
    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * 关联商品
     */
    public function goods(): BelongsTo
    {
        return $this->belongsTo(GoodsModel::class, 'goods_id');
    }
    
    /**
     * 关联TEMU分类
     */
    public function temuCategory(): BelongsTo
    {
        return $this->belongsTo(ProductCategoryTeMuModel::class, 'temu_category_id');
    }
}