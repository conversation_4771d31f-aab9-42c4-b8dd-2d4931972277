<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\ApiCredentialService;
use App\Models\User\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ApiCredentialController extends Controller
{
    protected ApiCredentialService $apiCredentialService;

    public function __construct(ApiCredentialService $apiCredentialService)
    {
        $this->apiCredentialService = $apiCredentialService;
        parent::__construct();
    }

    /**
     * 生成 API 凭证
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function generateCredentials(Request $request): JsonResponse
    {

        // 从中间件获取认证用户信息
        $user = $request->attributes->get('user');

        if (!$user) {
            return $this->apiError('用户未登录');
        }
        $user_id = $user['id'] ?? 0;
        $user_id = intval($user_id);
        // 调用服务生成凭证
        $credentials = $this->apiCredentialService->generateCredentials($user_id);
        return $this->apiSuccess($credentials);
    }
}