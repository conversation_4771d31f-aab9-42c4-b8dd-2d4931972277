<?php

namespace App\Service\User;

use App\Models\User\User as UserModel;
use App\Models\User\UserPointsLogModel;
use App\Models\User\UserTaskModel;
use App\Models\User\UserAccountModel;
use App\Service\BaseService;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserPointsService extends BaseService
{
    protected UserModel $userModel;
    protected UserPointsLogModel $userPointsLogModel;
    protected UserTaskModel $userTaskModel;
    protected UserAccountModel $userAccountModel;

    public function __construct(
        UserModel $userModel,
        UserPointsLogModel $userPointsLogModel,
        UserTaskModel $userTaskModel,
        UserAccountModel $userAccountModel
    ) {
        $this->userModel = $userModel;
        $this->userPointsLogModel = $userPointsLogModel;
        $this->userTaskModel = $userTaskModel;
        $this->userAccountModel = $userAccountModel;
    }

    /**
     * 扣除用户积分并记录日志
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @param int $pointsToDeduct 要扣除的积分数量
     * @param string $description 扣除描述（可选，如果为空则自动生成）
     * @return bool
     * @throws MyException
     */
    public function deductPointsForTask(int $userId, int $taskId, int $pointsToDeduct, string $description = ''): bool
    {
        if ($pointsToDeduct <= 0) {
            throw new MyException('扣除积分数量必须大于0');
        }

        $user = $this->userModel->where('id', $userId)->first();
        if (!$user) {
            throw new MyException('用户不存在');
        }
        if($user->is_admin == 1){
            // 管理员不扣除积分 但是要更新任务的积分扣除状态
            $this->userTaskModel->where('id', $taskId)->update([
                'points_deduction_status' => 1,
                'updated_at' => now()
            ]);
            //Log::info("管理员用户 {$userId} 发布{$pointsToDeduct}个任务 不需要扣除积分");
            return true;
        }

        // 检查是否已经有该任务的扣除记录
        $existingLog = $this->userPointsLogModel
            ->where('user_id', $userId)
            ->where('task_id', $taskId)
            ->where('type', UserPointsLogModel::TYPE_TASK_DEDUCTION)
            ->first();

        if ($existingLog) {
            //Log::info("任务ID {$taskId} 的积分已经扣除过，跳过重复扣除");
            return true;
        }

        return DB::transaction(function () use ($userId, $taskId, $pointsToDeduct, $description) {
            // 获取用户当前积分（加锁防止并发问题）
            $user = $this->userModel->where('id', $userId)->lockForUpdate()->first();
            if (!$user) {
                throw new MyException('用户不存在');
            }

            $currentPoints = $user->points;
            if ($currentPoints < $pointsToDeduct) {
                throw new MyException("积分不足，需要{$pointsToDeduct}积分，当前积分：{$currentPoints}");
            }

            // 如果没有提供描述，则自动生成详细描述
            if (empty($description)) {
                $description = $this->generateTaskDescription($taskId, $pointsToDeduct);
            }

            // 计算扣除后的积分
            $pointsAfter = $currentPoints - $pointsToDeduct;

            // 更新用户积分
            $updateResult = $this->userModel->where('id', $userId)->update([
                'points' => $pointsAfter,
                'updated_at' => now()
            ]);

            if (!$updateResult) {
                throw new MyException('更新用户积分失败');
            }

            // 记录积分使用日志
            $logData = [
                'user_id' => $userId,
                'task_id' => $taskId,
                'points_before' => $currentPoints,
                'points_deducted' => $pointsToDeduct,
                'points_after' => $pointsAfter,
                'type' => UserPointsLogModel::TYPE_TASK_DEDUCTION,
                'description' => $description ?: "任务ID {$taskId} 扣除积分",
                'created_at' => now(),
                'updated_at' => now()
            ];

            $logResult = $this->userPointsLogModel->create($logData);
            if (!$logResult) {
                throw new MyException('记录积分使用日志失败');
            }

            // 更新任务的积分扣除状态
            $this->userTaskModel->where('id', $taskId)->update([
                'points_deduction_status' => 1,
                'updated_at' => now()
            ]);

            //Log::info("成功扣除用户 {$userId} 任务 {$taskId} 的积分 {$pointsToDeduct}，扣除前：{$currentPoints}，扣除后：{$pointsAfter}");

            return true;
        });
    }

    /**
     * 检查任务是否已经扣除过积分
     * @param int $userId 用户ID
     * @param int $taskId 任务ID
     * @return bool
     */
    public function hasTaskPointsDeducted(int $userId, int $taskId): bool
    {
        return $this->userPointsLogModel
            ->where('user_id', $userId)
            ->where('task_id', $taskId)
            ->where('type', UserPointsLogModel::TYPE_TASK_DEDUCTION)
            ->exists();
    }

    /**
     * 获取用户积分使用记录
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @param string|null $startDate 开始日期 (Y-m-d格式)
     * @param string|null $endDate 结束日期 (Y-m-d格式)
     * @return array
     */
    public function getUserPointsLogs(int $userId, int $page = 1, int $pageSize = 20, ?string $startDate = null, ?string $endDate = null): array
    {
        $query = $this->userPointsLogModel
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc');

        // 添加时间筛选
        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }
        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        $total = $query->count();
        $logs = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get();

        return [
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
            'data' => $logs->map(function ($log) {
                return [
                    'id' => $log->id,
                    'task_id' => $log->task_id,
                    'points_before' => $log->points_before,
                    'points_deducted' => $log->points_deducted,
                    'points_after' => $log->points_after,
                    'type' => $log->type,
                    'description' => $log->description,
                    'created_at' => $log->created_at
                ];
            })
        ];
    }

    /**
     * 生成任务积分扣除的详细描述
     * @param int $taskId 任务ID
     * @param int $pointsToDeduct 扣除的积分数量
     * @return string
     */
    private function generateTaskDescription(int $taskId, int $pointsToDeduct): string
    {
        try {
            // 获取任务信息
            $task = $this->userTaskModel->where('id', $taskId)->first();
            if (!$task) {
                return "任务ID {$taskId} 发布{$pointsToDeduct}个任务 扣除积分{$pointsToDeduct}";
            }

            // 获取店铺信息
            $store = $this->userAccountModel->where('id', $task->user_account_id)->first();
            $storeName = $store ? $store->account_name : '未知店铺';

            // 生成描述：店铺***发布***个任务 扣除积分****
            return "店铺{$storeName}发布{$pointsToDeduct}个任务 扣除积分{$pointsToDeduct}";

        } catch (\Exception $e) {
            Log::error("生成任务描述失败: " . $e->getMessage());
            return "任务ID {$taskId} 发布{$pointsToDeduct}个任务 扣除积分{$pointsToDeduct}";
        }
    }
}
