/**
 * N11接口测试API
 * 用于测试N11店铺的接口连接
 */
import { sendRequestViaBackground } from './api'

// N11接口测试响应接口
export interface N11TestResponse {
  success: boolean
  message: string
  data?: any
  statusCode?: number
}

/**
 * 测试N11店铺接口连接
 * @param appKey N11店铺的AppKey
 * @returns 测试结果
 */
export const testN11StoreConnection = async (appKey: string): Promise<N11TestResponse> => {
  const catId = 1001397 // 固定的分类ID
  const url = `https://api.n11.com/cdn/category/${catId}/attribute`

  const headers = {
    "Content-Type": "text/json; charset=utf-8",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "appkey": appKey
  }

  try {
    // 使用sendRequestViaBackground发送请求
    const response = await sendRequestViaBackground({
      funName: 'testN11StoreConnection',
      url,
      method: 'get',
      headers,
      auth: false,      // 明确传递false
      encrypto: false,   // 明确传递false，不加密
      timeout:8000
    })

    console.log('N11接口测试响应:', response)

    // 如果能到这里说明请求成功且有数据
    return {
      success: true,
      message: '接口测试成功！N11店铺连接正常',
      data: response,
      statusCode: 200
    }

  } catch (error: any) {
    console.error('N11接口测试失败:', error)

    // 处理各种错误情况
    let errorMessage = '接口测试失败'
    let statusCode = 0

    if (error && typeof error === 'object') {
      // 如果错误对象包含状态码信息
      if (error.status) {
        statusCode = error.status
        switch (error.status) {
          case 401:
            errorMessage = '认证失败，请检查AppKey是否正确'
            break
          case 403:
            errorMessage = '访问被拒绝，请检查AppKey权限'
            break
          case 404:
            errorMessage = '接口地址不存在'
            break
          case 429:
            errorMessage = '请求过于频繁，请稍后再试'
            break
          case 500:
            errorMessage = 'N11服务器内部错误'
            break
          case 502:
          case 503:
          case 504:
            errorMessage = 'N11服务器暂时不可用，请稍后再试'
            break
          default:
            errorMessage = `接口请求失败，状态码: ${error.status}`
        }
      } else if (error.message) {
        errorMessage = error.message
      }
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    return {
      success: false,
      message: errorMessage,
      statusCode
    }
  }
}
