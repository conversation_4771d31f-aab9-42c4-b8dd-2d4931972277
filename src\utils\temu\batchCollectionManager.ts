/**
 * 批量采集管理器 - 核心的批量采集逻辑
 */

// 调试级别枚举
const enum DebugLevel {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  DEBUG = 'DEBUG'
}

// 调试日志函数
function debugLog(level: DebugLevel, message: string): void {
  const timestamp = new Date().toISOString();
  switch(level) {
    case DebugLevel.INFO:
      console.info(`[${timestamp}] [INFO] ${message}`);
      break;
    case DebugLevel.WARNING:
      console.warn(`[${timestamp}] [WARNING] ${message}`);
      break;
    case DebugLevel.ERROR:
      console.error(`[${timestamp}] [ERROR] ${message}`);
      break;
    case DebugLevel.DEBUG:
    default:
      console.debug(`[${timestamp}] [DEBUG] ${message}`);
      break;
  }
}

import { createTab, closeTab, waitForTabLoad, type TabInfo } from './tabManager';
import { messageManager, waitForCollectionComplete, initMessageManager, type CollectionMessage } from './messageManager';
import { getSelectedProductLinks, markProductAsCollected, resetCollectionMarks } from './checkboxManager';
import { extractGoodsIdFromUrl } from './temuSearchUtils';
import { ElMessage, ElNotification } from 'element-plus';

export interface BatchCollectionConfig {
  delayBetweenItems?: number; // 商品间延迟（毫秒）
  timeoutPerItem?: number; // 每个商品超时时间（毫秒）
  maxConcurrentTabs?: number; // 最大并发Tab数
}

export interface BatchCollectionProgress {
  current: number;
  total: number;
  currentUrl: string;
  currentGoodsId: string;
  status: 'running' | 'paused' | 'completed' | 'error';
}

export interface BatchCollectionResult {
  success: boolean;
  totalItems: number;
  completedItems: number;
  failedItems: number;
  errors: string[];
}

class BatchCollectionManager {
  private isRunning = false;
  private currentProgress: BatchCollectionProgress | null = null;
  private progressCallback: ((progress: BatchCollectionProgress) => void) | null = null;
  private activeTabs: Map<string, TabInfo> = new Map();

  /**
   * 检查运行环境是否支持批量采集
   * @returns Promise<{supported: boolean, message: string}>
   */
  private async checkEnvironment(): Promise<{supported: boolean, message: string}> {
    try {
      // 检查Chrome API是否可用
      if (typeof chrome === 'undefined') {
        return {
          supported: false,
          message: 'Chrome API不可用，请确保在Chrome扩展环境中运行'
        };
      }

      if (!chrome.runtime) {
        return {
          supported: false,
          message: 'Chrome runtime API不可用'
        };
      }

      // 检查是否有Chrome tabs API
      const hasTabsAPI = chrome.tabs && typeof chrome.tabs.create === 'function';

      // 检查是否有消息传递能力
      const hasMessageAPI = typeof chrome.runtime.sendMessage === 'function';

      if (!hasTabsAPI && !hasMessageAPI) {
        return {
          supported: false,
          message: 'Chrome tabs API和消息传递API都不可用，请检查扩展权限'
        };
      }

      // 如果有tabs API，检查权限
      if (hasTabsAPI) {
        try {
          await new Promise<void>((resolve, reject) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
              if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
              } else {
                resolve();
              }
            });
          });
        } catch (error) {
          return {
            supported: false,
            message: `Tabs API权限检查失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      }

      // 如果有消息传递API，测试连接
      if (hasMessageAPI) {
        try {
          await new Promise<void>((resolve, reject) => {
            chrome.runtime.sendMessage({
              funType: 'testConnection'
            }, (response: any) => {
              if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
              } else {
                resolve();
              }
            });
          });
        } catch (error) {
          console.warn('消息传递API测试失败，但可能仍可使用:', error);
          // 不阻止继续，因为可能是background script未响应
        }
      }

      return {
        supported: true,
        message: hasTabsAPI ? '环境检查通过（直接API模式）' : '环境检查通过（消息传递模式）'
      };

    } catch (error) {
      return {
        supported: false,
        message: `环境检查异常: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 获取待采集的商品列表
   * @returns Promise<Array<{url: string, goodsId: string}>>
   */
  private async getGoodsList(): Promise<Array<{url: string, goodsId: string}>> {
    debugLog(DebugLevel.INFO, '开始获取商品列表');

    // 初始化消息管理器
    initMessageManager();
    debugLog(DebugLevel.DEBUG, '消息管理器初始化完成');

    // 检查页面上是否有复选框
    const checkboxCount = document.querySelectorAll('.product-checkbox').length;
    debugLog(DebugLevel.DEBUG, `页面上找到 ${checkboxCount} 个复选框`);

    // 检查选中的复选框
    const checkedCheckboxes = document.querySelectorAll('.product-checkbox .checkbox-input:checked');
    debugLog(DebugLevel.DEBUG, `选中的复选框数量: ${checkedCheckboxes.length}`);

    // 检查商品容器
    const productsContainer = document.querySelector('.contentContainer .js-search-goodsList .autoFitList');
    debugLog(DebugLevel.DEBUG, `商品容器存在: ${!!productsContainer}`);
    if (productsContainer) {
      debugLog(DebugLevel.DEBUG, `商品容器中商品数量: ${productsContainer.children.length}`);
    }

    // 获取商品URL列表
    const productUrls = getSelectedProductLinks();
    debugLog(DebugLevel.INFO, `获取到的商品链接数量: ${productUrls.length}`);

    if (productUrls.length === 0) {
      debugLog(DebugLevel.WARNING, '没有获取到任何商品链接，可能的原因：');
      debugLog(DebugLevel.WARNING, '1. 没有选中任何商品');
      debugLog(DebugLevel.WARNING, '2. 复选框未正确添加到页面');
      debugLog(DebugLevel.WARNING, '3. 商品DOM结构发生变化');
      throw new Error('没有找到选中的商品');
    }

    debugLog(DebugLevel.INFO, `准备批量采集，共 ${productUrls.length} 个商品`);
    productUrls.forEach((url, index) => {
      debugLog(DebugLevel.DEBUG, `商品 ${index + 1}: ${url}`);
    });

    // 转换为包含goodsId的对象数组
    const result = productUrls.map((url, index) => ({
      url,
      goodsId: extractGoodsIdFromUrl(url) || `unknown_${index}`
    }));

    debugLog(DebugLevel.INFO, '商品列表转换完成');
    return result;
  }

  /**
   * 开始批量采集
   * @param config 配置选项
   * @param onProgress 进度回调
   * @returns Promise<BatchCollectionResult> 采集结果
   */
  async startBatchCollection(
    config: BatchCollectionConfig = {},
    onProgress?: (progress: BatchCollectionProgress) => void
  ): Promise<BatchCollectionResult> {
    debugLog(DebugLevel.INFO, '开始批量采集');
    debugLog(DebugLevel.DEBUG, `采集配置: ${JSON.stringify(config)}`);

    if (this.isRunning) {
      debugLog(DebugLevel.WARNING, '批量采集已在运行中，无法重复启动');
      throw new Error('批量采集已在运行中');
    }

    this.isRunning = true;
    this.progressCallback = onProgress ?? null;
    debugLog(DebugLevel.INFO, '批量采集状态设置为运行中');

    const result: BatchCollectionResult = {
      success: false,
      totalItems: 0,
      completedItems: 0,
      failedItems: 0,
      errors: []
    };

    try {
      // 获取待采集的商品列表
      debugLog(DebugLevel.INFO, '开始获取待采集的商品列表');
      const goodsList = await this.getGoodsList();
      result.totalItems = goodsList.length;
      debugLog(DebugLevel.INFO, `成功获取 ${goodsList.length} 个待采集商品`);

      if (goodsList.length === 0) {
        result.success = true;
        return result;
      }

      // 检查是否需要初始化复选框
    debugLog(DebugLevel.INFO, '检查是否需要初始化复选框');
    const hasCheckboxes = document.querySelectorAll('.product-checkbox').length > 0;
    debugLog(DebugLevel.DEBUG, `页面已存在复选框: ${hasCheckboxes}`);

    if (!hasCheckboxes) {
      debugLog(DebugLevel.WARNING, '页面上没有复选框，可能需要手动触发添加复选框功能');
      debugLog(DebugLevel.WARNING, '请确保在Temu搜索页面点击了"添加复选框"按钮');
    }

    // 开始采集
    debugLog(DebugLevel.INFO, '开始逐个采集商品');
      for (let i = 0; i < goodsList.length; i++) {
        if (!this.isRunning) {
          break;
        }

        const { url, goodsId } = goodsList[i];

        try {
          debugLog(DebugLevel.INFO, `开始采集第 ${i + 1}/${goodsList.length} 个商品: ${goodsId}`);
          debugLog(DebugLevel.DEBUG, `商品URL: ${url}`);

          this.updateProgress({
            current: i + 1,
            total: goodsList.length,
            currentUrl: url,
            currentGoodsId: goodsId,
            status: 'running'
          });

          await this.collectSingleProduct(url, goodsId, config);
          result.completedItems++;
          debugLog(DebugLevel.INFO, `成功采集第 ${i + 1}/${goodsList.length} 个商品: ${goodsId}`);

          // 商品间延迟
          if (i < goodsList.length - 1 && config.delayBetweenItems) {
            debugLog(DebugLevel.DEBUG, `商品间延迟 ${config.delayBetweenItems}ms`);
            await this.delay(config.delayBetweenItems);
          }

        } catch (error) {
          result.failedItems++;
          const errorMessage = `商品 ${goodsId} 采集失败: ${error instanceof Error ? error.message : String(error)}`;
          result.errors.push(errorMessage);
          debugLog(DebugLevel.ERROR, errorMessage);
          debugLog(DebugLevel.DEBUG, `错误详情: ${error instanceof Error ? error.stack : String(error)}`);
          
          // 确保单个商品失败不会影响批量采集状态
          debugLog(DebugLevel.INFO, `商品 ${goodsId} 采集失败，但继续处理下一个商品`);
        }
      }

      result.success = result.failedItems === 0;
      debugLog(DebugLevel.INFO, `批量采集完成: 成功 ${result.completedItems} 个, 失败 ${result.failedItems} 个`);

    } catch (error) {
      const errorMessage = `批量采集过程中发生错误: ${error instanceof Error ? error.message : String(error)}`;
      result.errors.push(errorMessage);
      debugLog(DebugLevel.ERROR, errorMessage);
      debugLog(DebugLevel.DEBUG, `错误详情: ${error instanceof Error ? error.stack : String(error)}`);
    } finally {
      // 只有在真正完成或被手动停止时才重置状态
      if (this.isRunning) {
        debugLog(DebugLevel.INFO, '批量采集正常完成，重置运行状态');
      } else {
        debugLog(DebugLevel.INFO, '批量采集已被停止，清理资源');
      }
      this.isRunning = false;
      this.progressCallback = null;
      this.cleanup();
      debugLog(DebugLevel.INFO, '批量采集结束，资源已清理');
    }

    return result;
  }

  /**
   * 采集单个商品
   * @param url 商品URL
   * @param goodsId 商品ID
   * @param config 配置选项
   */
  private async collectSingleProduct(
    url: string,
    goodsId: string,
    config: BatchCollectionConfig
  ): Promise<void> {
    debugLog(DebugLevel.DEBUG, `开始采集单个商品: ${goodsId}, URL: ${url}`);
    const timeout = config.timeoutPerItem || 60000;
    const maxRetries = 3;
    let retryCount = 0;
    debugLog(DebugLevel.DEBUG, `采集配置: 超时 ${timeout}ms, 最大重试 ${maxRetries} 次`);

    while (retryCount < maxRetries) {
      try {
        debugLog(DebugLevel.DEBUG, `采集尝试 ${retryCount + 1}/${maxRetries}`);
        debugLog(DebugLevel.INFO, '通过消息传递调用background script');
        await this.collectSingleProductViaMessage(url, goodsId, config);
        
        // 如果成功，直接返回，不再重试
        debugLog(DebugLevel.INFO, `商品 ${goodsId} 采集成功`);
        return;
        
      } catch (error) {
        retryCount++;
        debugLog(DebugLevel.ERROR, `采集商品 ${goodsId} 失败 (尝试 ${retryCount}/${maxRetries}): ${error instanceof Error ? error.message : String(error)}`);
        debugLog(DebugLevel.DEBUG, `错误详情: ${error instanceof Error ? error.stack : String(error)}`);

        // 清理可能残留的活动Tab记录
        if (this.activeTabs.has(goodsId)) {
          debugLog(DebugLevel.DEBUG, `清理商品 ${goodsId} 的活动Tab记录`);
          this.activeTabs.delete(goodsId);
        }

        // 如果是最后一次重试，抛出错误
        if (retryCount >= maxRetries) {
          debugLog(DebugLevel.ERROR, `采集商品 ${goodsId} 失败，已达到最大重试次数 ${maxRetries}`);
          debugLog(DebugLevel.INFO, `商品 ${goodsId} 最终失败，但批量采集将继续处理下一个商品`);
          throw new Error(`采集商品 ${goodsId} 失败，已重试 ${maxRetries} 次: ${error instanceof Error ? error.message : String(error)}`);
        }

        // 等待一段时间后重试
        const delayTime = 2000 * retryCount;
        debugLog(DebugLevel.DEBUG, `等待 ${delayTime}ms 后重试采集商品 ${goodsId}`);
        await this.delay(delayTime);
      }
    }
  }

  /**
   * 通过消息传递采集单个商品
   * @param url 商品URL
   * @param goodsId 商品ID
   * @param config 配置选项
   */
  private async collectSingleProductViaMessage(
    url: string,
    goodsId: string,
    config: BatchCollectionConfig
  ): Promise<void> {
    debugLog(DebugLevel.INFO, `通过消息传递采集商品: ${goodsId}, URL: ${url}`);
    const timeout = config.timeoutPerItem || 60000;
    debugLog(DebugLevel.DEBUG, `采集配置: 超时 ${timeout}ms`);

    // 检查是否已经在处理这个商品
    if (this.activeTabs.has(goodsId)) {
      debugLog(DebugLevel.WARNING, `商品 ${goodsId} 已在处理中，跳过重复处理`);
      throw new Error(`商品 ${goodsId} 已在处理中`);
    }

    let tabId: number | null = null;
    let isCompleted = false;

    try {
      // 通过消息传递调用background script的API
      if (typeof chrome === 'undefined' || !chrome.runtime) {
        debugLog(DebugLevel.ERROR, 'Chrome runtime API不可用');
        throw new Error('Chrome runtime API不可用');
      }

      // 创建Tab
      debugLog(DebugLevel.DEBUG, `发送消息创建Tab: ${url}`);
      const createResponse = await new Promise<any>((resolve, reject) => {
        chrome.runtime.sendMessage({
          funType: 'createTab',
          url
        }, (response: any) => {
          if (chrome.runtime.lastError) {
            debugLog(DebugLevel.ERROR, `创建Tab失败: ${chrome.runtime.lastError.message}`);
            reject(new Error(`创建Tab失败: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve(response);
        });
      });

      debugLog(DebugLevel.DEBUG, `收到创建Tab响应: ${JSON.stringify(createResponse)}`);

      if (!createResponse.success) {
        debugLog(DebugLevel.ERROR, `创建Tab失败: ${createResponse.error || createResponse.message}`);
        throw new Error(`创建Tab失败: ${createResponse.error || createResponse.message}`);
      }

      const tab = createResponse.data;
      tabId = tab.id;
      debugLog(DebugLevel.INFO, `成功创建Tab: ID=${tab.id}, URL=${tab.url}`);

      // 将Tab添加到活动标签列表
      this.activeTabs.set(goodsId, tab);
      debugLog(DebugLevel.DEBUG, `已将Tab ${tab.id} 添加到活动标签列表`);

      // 同时等待Tab加载和采集完成，哪个先完成就用哪个
      debugLog(DebugLevel.DEBUG, `同时等待Tab ${tab.id} 加载和商品 ${goodsId} 采集完成`);
      
      const tabLoadPromise = new Promise<any>((resolve, reject) => {
        chrome.runtime.sendMessage({
          funType: 'waitForTabLoad',
          tabId: tab.id,
          timeout: 30000
        }, (response: any) => {
          if (chrome.runtime.lastError) {
            debugLog(DebugLevel.ERROR, `等待Tab加载失败: ${chrome.runtime.lastError.message}`);
            reject(new Error(`等待Tab加载失败: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve({ type: 'tabLoad', data: response });
        });
      });

      const collectionCompletePromise = new Promise<any>((resolve, reject) => {
        chrome.runtime.sendMessage({
          funType: 'waitForCollectionComplete',
          goodsId,
          timeout
        }, (response: any) => {
          if (chrome.runtime.lastError) {
            debugLog(DebugLevel.ERROR, `等待采集完成失败: ${chrome.runtime.lastError.message}`);
            reject(new Error(`等待采集完成失败: ${chrome.runtime.lastError.message}`));
            return;
          }
          resolve({ type: 'collectionComplete', data: response });
        });
      });

      // 优先等待采集完成消息，因为这是最终目标
      // 如果采集很快完成，就不需要等待Tab加载
      try {
        debugLog(DebugLevel.DEBUG, `优先等待商品 ${goodsId} 采集完成消息`);
        
        // 使用较短的超时时间先尝试等待采集完成
        const quickCollectionPromise = new Promise<any>((resolve, reject) => {
          const quickTimeout = setTimeout(() => {
            reject(new Error('快速采集检查超时'));
          }, 5000); // 5秒快速检查
          
          chrome.runtime.sendMessage({
            funType: 'waitForCollectionComplete',
            goodsId,
            timeout: 5000
          }, (response: any) => {
            clearTimeout(quickTimeout);
            if (chrome.runtime.lastError) {
              reject(new Error(`快速采集检查失败: ${chrome.runtime.lastError.message}`));
              return;
            }
            resolve(response);
          });
        });
        
        const quickResult = await quickCollectionPromise;
        
        if (quickResult.success) {
          debugLog(DebugLevel.INFO, `商品 ${goodsId} 快速采集完成，无需等待Tab加载`);
        } else {
          throw new Error('快速采集检查未成功');
        }
        
      } catch (quickError) {
        debugLog(DebugLevel.DEBUG, `快速采集检查失败，回退到完整流程: ${quickError instanceof Error ? quickError.message : String(quickError)}`);
        
        // 回退到原来的逻辑：同时等待Tab加载和采集完成
        const raceResult = await Promise.race([tabLoadPromise, collectionCompletePromise]);
        
        debugLog(DebugLevel.DEBUG, `首先完成的操作: ${raceResult.type}`);
        
        if (raceResult.type === 'collectionComplete') {
          // 如果采集先完成，检查响应
          debugLog(DebugLevel.DEBUG, `收到等待采集完成响应: ${JSON.stringify(raceResult.data)}`);
          
          if (!raceResult.data.success) {
            debugLog(DebugLevel.ERROR, `等待采集完成失败: ${raceResult.data.error || raceResult.data.message}`);
            throw new Error(`等待采集完成失败: ${raceResult.data.error || raceResult.data.message}`);
          }
          
          debugLog(DebugLevel.INFO, `商品 ${goodsId} 采集完成（采集先于Tab加载完成）`);
        } else {
          // 如果Tab加载先完成，检查响应并继续等待采集完成
          debugLog(DebugLevel.DEBUG, `收到等待Tab加载响应: ${JSON.stringify(raceResult.data)}`);
          
          if (!raceResult.data.success) {
            debugLog(DebugLevel.ERROR, `等待Tab加载失败: ${raceResult.data.error || raceResult.data.message}`);
            throw new Error(`等待Tab加载失败: ${raceResult.data.error || raceResult.data.message}`);
          }
          
          debugLog(DebugLevel.INFO, `Tab ${tab.id} 加载完成，继续等待采集完成`);
          
          // 等待采集完成
          const completeResponse = await collectionCompletePromise;
          debugLog(DebugLevel.DEBUG, `收到等待采集完成响应: ${JSON.stringify(completeResponse.data)}`);
          
          if (!completeResponse.data.success) {
            debugLog(DebugLevel.ERROR, `等待采集完成失败: ${completeResponse.data.error || completeResponse.data.message}`);
            throw new Error(`等待采集完成失败: ${completeResponse.data.error || completeResponse.data.message}`);
          }
          
          debugLog(DebugLevel.INFO, `商品 ${goodsId} 采集完成（Tab加载先完成）`);
        }
      }

      debugLog(DebugLevel.INFO, `商品 ${goodsId} 采集完成`);
      isCompleted = true;

      // 标记商品为已采集
      try {
        const productElement = this.findProductElementByGoodsId(goodsId);
        if (productElement) {
          markProductAsCollected(productElement);
          debugLog(DebugLevel.DEBUG, `已标记商品 ${goodsId} 为已采集`);
        }
      } catch (error) {
        debugLog(DebugLevel.WARNING, `标记商品 ${goodsId} 为已采集时失败: ${error}`);
      }

      debugLog(DebugLevel.INFO, `商品 ${goodsId} 通过消息传递采集成功`);

    } catch (error) {
      debugLog(DebugLevel.ERROR, `商品 ${goodsId} 采集过程中发生错误: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    } finally {
      // 清理资源
      try {
        // 关闭Tab
        if (tabId) {
          debugLog(DebugLevel.DEBUG, `关闭Tab ${tabId}`);
          const closeResponse = await new Promise<any>((resolve, reject) => {
            chrome.runtime.sendMessage({
              funType: 'closeTab',
              tabId: tabId
            }, (response: any) => {
              if (chrome.runtime.lastError) {
                debugLog(DebugLevel.WARNING, `关闭Tab ${tabId} 失败: ${chrome.runtime.lastError.message}`);
              } else if (!response.success) {
                debugLog(DebugLevel.WARNING, `关闭Tab ${tabId} 失败: ${response.error || response.message}`);
              } else {
                debugLog(DebugLevel.INFO, `成功关闭Tab ${tabId}`);
              }
              resolve(response);
            });
          });
        }
      } catch (error) {
        debugLog(DebugLevel.WARNING, `关闭Tab ${tabId} 失败: ${error instanceof Error ? error.message : String(error)}`);
      }

      // 从活动标签列表中移除
      this.activeTabs.delete(goodsId);
      debugLog(DebugLevel.DEBUG, `已从活动标签列表中移除商品 ${goodsId}`);
    }
  }

  /**
   * 更新进度
   * @param progress 进度信息
   */
  private updateProgress(progress: BatchCollectionProgress): void {
    this.currentProgress = progress;
    if (this.progressCallback) {
      this.progressCallback(progress);
    }
  }

  /**
   * 获取当前进度
   * @returns BatchCollectionProgress | null
   */
  getCurrentProgress(): BatchCollectionProgress | null {
    return this.currentProgress;
  }

  /**
   * 停止批量采集
   */
  stopBatchCollection(): void {
    if (!this.isRunning) {
      debugLog(DebugLevel.WARNING, '批量采集未运行，无需停止');
      return;
    }

    debugLog(DebugLevel.INFO, '停止批量采集');
    this.isRunning = false;

    // 关闭所有活动的Tab
    this.activeTabs.forEach(async (tab) => {
      try {
        await closeTab(tab.id);
      } catch (error) {
        console.warn(`关闭Tab ${tab.id} 失败:`, error);
      }
    });

    this.activeTabs.clear();

    // 更新进度状态
    if (this.currentProgress) {
      this.updateProgress({
        ...this.currentProgress,
        status: 'paused'
      });
    }
  }

  /**
   * 检查是否正在运行
   * @returns boolean
   */
  isCollectionRunning(): boolean {
    return this.isRunning;
  }

  /**
   * 根据商品ID查找对应的DOM元素
   * @param goodsId 商品ID
   * @returns 商品DOM元素或null
   */
  private findProductElementByGoodsId(goodsId: string): Element | null {
    const productsContainer = document.querySelector('.contentContainer .js-search-goodsList .autoFitList');
    if (!productsContainer) {
      return null;
    }

    const productElements = productsContainer.children;
    for (let i = 0; i < productElements.length; i++) {
      const productElement = productElements[i];
      const linkElement = productElement.querySelector('a[href]') as HTMLAnchorElement;
      if (linkElement && linkElement.href) {
        const extractedGoodsId = extractGoodsIdFromUrl(linkElement.href);
        if (extractedGoodsId === goodsId) {
          return productElement;
        }
      }
    }

    return null;
  }

  /**
   * 延迟函数
   * @param ms 延迟时间（毫秒）
   * @returns Promise<void>
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 重置采集状态
   */
  resetCollectionState(): void {
    debugLog(DebugLevel.INFO, '重置采集状态');
    resetCollectionMarks();
    debugLog(DebugLevel.INFO, '已重置所有商品的采集标记');
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    this.activeTabs.clear();
    this.currentProgress = null;
    this.progressCallback = null;
  }
}

// 创建单例实例
export const batchCollectionManager = new BatchCollectionManager();

/**
 * 开始批量采集的便捷函数
 * @param config 配置选项
 * @param onProgress 进度回调
 * @returns Promise<BatchCollectionResult>
 */
export const startBatchCollection = async (
  config: BatchCollectionConfig = {},
  onProgress?: (progress: BatchCollectionProgress) => void
): Promise<BatchCollectionResult> => {
  return batchCollectionManager.startBatchCollection(config, onProgress);
};

/**
 * 停止批量采集的便捷函数
 */
export const stopBatchCollection = (): void => {
  batchCollectionManager.stopBatchCollection();
};

/**
 * 检查批量采集是否正在运行
 * @returns boolean
 */
export const isBatchCollectionRunning = (): boolean => {
  return batchCollectionManager.isCollectionRunning();
};

/**
 * 获取当前批量采集进度
 * @returns BatchCollectionProgress | null
 */
export const getBatchCollectionProgress = (): BatchCollectionProgress | null => {
  return batchCollectionManager.getCurrentProgress();
};
