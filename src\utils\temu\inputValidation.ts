/**
 * 输入验证工具函数
 * 用于商品筛选功能的输入验证和格式化
 * 包含错误处理和用户反馈功能
 */

import { ElMessage } from 'element-plus';

/**
 * 输入验证错误类型
 */
export enum InputValidationError {
  INVALID_FORMAT = 'INVALID_FORMAT',
  OUT_OF_RANGE = 'OUT_OF_RANGE',
  NEGATIVE_VALUE = 'NEGATIVE_VALUE',
  RANGE_CONFLICT = 'RANGE_CONFLICT',
  TOO_LONG = 'TOO_LONG',
  INVALID_DECIMAL = 'INVALID_DECIMAL'
}

/**
 * 输入验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  value: string;
  error?: InputValidationError;
  message?: string;
  suggestion?: string;
}

/**
 * 验证价格输入（增强版）
 * 只允许数字、小数点和逗号，最多保留2位小数
 * 
 * @param value 用户输入的价格字符串
 * @param showFeedback 是否显示用户反馈消息
 * @returns 验证结果
 */
export const validatePriceInputEnhanced = (value: string, showFeedback: boolean = false): ValidationResult => {
  if (!value) {
    return { isValid: true, value: '' };
  }
  
  // 检查长度限制
  if (value.length > INPUT_VALIDATION_CONFIG.PRICE_MAX_LENGTH) {
    const message = `价格输入过长，最多允许${INPUT_VALIDATION_CONFIG.PRICE_MAX_LENGTH}个字符`;
    if (showFeedback) {
      ElMessage.warning(message);
    }
    return {
      isValid: false,
      value: value.substring(0, INPUT_VALIDATION_CONFIG.PRICE_MAX_LENGTH),
      error: InputValidationError.TOO_LONG,
      message,
      suggestion: '请输入更简洁的价格值'
    };
  }
  
  // 移除非法字符，只保留数字、小数点和逗号
  let cleaned = value.replace(INPUT_VALIDATION_CONFIG.PRICE_ALLOWED_CHARS, '');
  
  // 检查是否有非法字符被移除
  if (cleaned !== value) {
    const message = '价格只能包含数字、小数点和逗号';
    if (showFeedback) {
      ElMessage.warning(message);
    }
  }
  
  // 处理多个小数点的情况
  const parts = cleaned.split('.');
  if (parts.length > 2) {
    // 如果有多个小数点，只保留第一个，其余的合并到小数部分
    cleaned = parts[0] + '.' + parts.slice(1).join('');
    const message = '价格只能包含一个小数点';
    if (showFeedback) {
      ElMessage.warning(message);
    }
  }
  
  // 限制小数位数最多2位
  if (parts.length === 2 && parts[1].length > INPUT_VALIDATION_CONFIG.PRICE_DECIMAL_PLACES) {
    cleaned = parts[0] + '.' + parts[1].substring(0, INPUT_VALIDATION_CONFIG.PRICE_DECIMAL_PLACES);
    const message = `价格小数位数最多${INPUT_VALIDATION_CONFIG.PRICE_DECIMAL_PLACES}位`;
    if (showFeedback) {
      ElMessage.warning(message);
    }
  }
  
  // 验证数值范围
  const numericValue = parseFloat(cleaned.replace(/,/g, ''));
  if (!isNaN(numericValue) && numericValue < 0) {
    const message = '价格不能为负数';
    if (showFeedback) {
      ElMessage.error(message);
    }
    return {
      isValid: false,
      value: cleaned,
      error: InputValidationError.NEGATIVE_VALUE,
      message,
      suggestion: '请输入正数价格'
    };
  }
  
  return { isValid: true, value: cleaned };
};

/**
 * 验证价格输入（保持向后兼容）
 * 只允许数字、小数点和逗号，最多保留2位小数
 * 
 * @param value 用户输入的价格字符串
 * @returns 清理和格式化后的价格字符串
 */
export const validatePriceInput = (value: string): string => {
  const result = validatePriceInputEnhanced(value, false);
  return result.value;
};

/**
 * 验证销量输入（增强版）
 * 只允许输入整数
 * 
 * @param value 用户输入的销量字符串
 * @param showFeedback 是否显示用户反馈消息
 * @returns 验证结果
 */
export const validateSalesInputEnhanced = (value: string, showFeedback: boolean = false): ValidationResult => {
  if (!value) {
    return { isValid: true, value: '' };
  }
  
  // 检查长度限制
  if (value.length > INPUT_VALIDATION_CONFIG.SALES_MAX_LENGTH) {
    const message = `销量输入过长，最多允许${INPUT_VALIDATION_CONFIG.SALES_MAX_LENGTH}个字符`;
    if (showFeedback) {
      ElMessage.warning(message);
    }
    return {
      isValid: false,
      value: value.substring(0, INPUT_VALIDATION_CONFIG.SALES_MAX_LENGTH),
      error: InputValidationError.TOO_LONG,
      message,
      suggestion: '请输入更合理的销量值'
    };
  }
  
  // 只保留数字字符
  const cleaned = value.replace(INPUT_VALIDATION_CONFIG.SALES_ALLOWED_CHARS, '');
  
  // 检查是否有非法字符被移除
  if (cleaned !== value) {
    const message = '销量只能包含数字';
    if (showFeedback) {
      ElMessage.warning(message);
    }
  }
  
  // 验证数值范围
  const numericValue = parseInt(cleaned);
  if (!isNaN(numericValue) && numericValue < 0) {
    const message = '销量不能为负数';
    if (showFeedback) {
      ElMessage.error(message);
    }
    return {
      isValid: false,
      value: cleaned,
      error: InputValidationError.NEGATIVE_VALUE,
      message,
      suggestion: '请输入正整数销量'
    };
  }
  
  return { isValid: true, value: cleaned };
};

/**
 * 验证销量输入（保持向后兼容）
 * 只允许输入整数
 * 
 * @param value 用户输入的销量字符串
 * @returns 清理后的销量字符串（只包含数字）
 */
export const validateSalesInput = (value: string): string => {
  const result = validateSalesInputEnhanced(value, false);
  return result.value;
};

/**
 * 格式化价格输入
 * 移除前导零，确保格式正确
 * 
 * @param value 价格字符串
 * @returns 格式化后的价格字符串
 */
export const formatPriceInput = (value: string): string => {
  if (!value) return '';
  
  // 先进行基本验证
  const validated = validatePriceInput(value);
  if (!validated) return '';
  
  // 移除前导零，但保留小数点前的单个0
  let formatted = validated.replace(/^0+(?=\d)/, '');
  
  // 如果字符串以小数点开头，添加前导0
  if (formatted.startsWith('.')) {
    formatted = '0' + formatted;
  }
  
  // 如果字符串为空或只有小数点，返回空字符串
  if (formatted === '' || formatted === '.') {
    return '';
  }
  
  return formatted;
};

/**
 * 格式化销量输入
 * 移除前导零
 * 
 * @param value 销量字符串
 * @returns 格式化后的销量字符串
 */
export const formatSalesInput = (value: string): string => {
  if (!value) return '';
  
  // 先进行基本验证
  const validated = validateSalesInput(value);
  if (!validated) return '';
  
  // 移除前导零
  const formatted = validated.replace(/^0+/, '');
  
  // 如果全部都是0，返回单个0
  return formatted || '0';
};

/**
 * 清理价格输入
 * 移除所有非数字、非小数点、非逗号的字符
 * 
 * @param value 原始输入字符串
 * @returns 清理后的字符串
 */
export const cleanPriceInput = (value: string): string => {
  if (!value) return '';
  return value.replace(/[^\d.,]/g, '');
};

/**
 * 清理销量输入
 * 移除所有非数字字符
 * 
 * @param value 原始输入字符串
 * @returns 清理后的字符串
 */
export const cleanSalesInput = (value: string): string => {
  if (!value) return '';
  return value.replace(/[^\d]/g, '');
};

/**
 * 验证价格范围
 * 检查最小值是否小于等于最大值
 * 
 * @param minPrice 最小价格
 * @param maxPrice 最大价格
 * @returns 验证结果对象
 */
export const validatePriceRange = (minPrice?: string, maxPrice?: string): {
  isValid: boolean;
  error?: string;
} => {
  if (!minPrice && !maxPrice) {
    return { isValid: true };
  }
  
  const min = minPrice ? parseFloat(minPrice.replace(/,/g, '')) : undefined;
  const max = maxPrice ? parseFloat(maxPrice.replace(/,/g, '')) : undefined;
  
  if (min !== undefined && isNaN(min)) {
    return { isValid: false, error: '最小价格格式不正确' };
  }
  
  if (max !== undefined && isNaN(max)) {
    return { isValid: false, error: '最大价格格式不正确' };
  }
  
  if (min !== undefined && max !== undefined && min > max) {
    return { isValid: false, error: '最小价格不能大于最大价格' };
  }
  
  if (min !== undefined && min < 0) {
    return { isValid: false, error: '价格不能为负数' };
  }
  
  if (max !== undefined && max < 0) {
    return { isValid: false, error: '价格不能为负数' };
  }
  
  return { isValid: true };
};

/**
 * 验证销量范围
 * 检查最小值是否小于等于最大值
 * 
 * @param minSales 最小销量
 * @param maxSales 最大销量
 * @returns 验证结果对象
 */
export const validateSalesRange = (minSales?: string, maxSales?: string): {
  isValid: boolean;
  error?: string;
} => {
  if (!minSales && !maxSales) {
    return { isValid: true };
  }
  
  const min = minSales ? parseInt(minSales) : undefined;
  const max = maxSales ? parseInt(maxSales) : undefined;
  
  if (min !== undefined && isNaN(min)) {
    return { isValid: false, error: '最小销量格式不正确' };
  }
  
  if (max !== undefined && isNaN(max)) {
    return { isValid: false, error: '最大销量格式不正确' };
  }
  
  if (min !== undefined && max !== undefined && min > max) {
    return { isValid: false, error: '最小销量不能大于最大销量' };
  }
  
  if (min !== undefined && min < 0) {
    return { isValid: false, error: '销量不能为负数' };
  }
  
  if (max !== undefined && max < 0) {
    return { isValid: false, error: '销量不能为负数' };
  }
  
  return { isValid: true };
};
/*
*
 * 处理价格输入事件
 * 结合验证和格式化功能，用于输入框的实时处理
 * 
 * @param event 输入事件
 * @returns 处理后的值
 */
export const handlePriceInputEvent = (event: Event): string => {
  const target = event.target as HTMLInputElement;
  if (!target) return '';
  
  const value = target.value;
  const validated = validatePriceInput(value);
  const formatted = formatPriceInput(validated);
  
  // 更新输入框的值
  target.value = formatted;
  
  return formatted;
};

/**
 * 处理销量输入事件
 * 结合验证和格式化功能，用于输入框的实时处理
 * 
 * @param event 输入事件
 * @returns 处理后的值
 */
export const handleSalesInputEvent = (event: Event): string => {
  const target = event.target as HTMLInputElement;
  if (!target) return '';
  
  const value = target.value;
  const validated = validateSalesInput(value);
  const formatted = formatSalesInput(validated);
  
  // 更新输入框的值
  target.value = formatted;
  
  return formatted;
};

/**
 * 转换价格字符串为数字
 * 处理逗号分隔符
 * 
 * @param priceStr 价格字符串
 * @returns 价格数字，无法解析时返回null
 */
export const parsePriceString = (priceStr: string): number | null => {
  if (!priceStr) return null;
  
  // 移除逗号分隔符
  const cleaned = priceStr.replace(/,/g, '');
  const parsed = parseFloat(cleaned);
  
  return isNaN(parsed) ? null : parsed;
};

/**
 * 转换销量字符串为数字
 * 
 * @param salesStr 销量字符串
 * @returns 销量数字，无法解析时返回null
 */
export const parseSalesString = (salesStr: string): number | null => {
  if (!salesStr) return null;
  
  const parsed = parseInt(salesStr);
  return isNaN(parsed) ? null : parsed;
};

/**
 * 检查输入值是否为空或无效
 * 
 * @param value 输入值
 * @returns 是否为空或无效
 */
export const isEmptyOrInvalid = (value?: string): boolean => {
  return !value || value.trim() === '' || value === '0';
};

/**
 * 输入验证配置
 */
export const INPUT_VALIDATION_CONFIG = {
  // 价格输入的最大长度
  PRICE_MAX_LENGTH: 10,
  // 销量输入的最大长度
  SALES_MAX_LENGTH: 8,
  // 价格的最大小数位数
  PRICE_DECIMAL_PLACES: 2,
  // 允许的价格字符正则
  PRICE_ALLOWED_CHARS: /[^\d.,]/g,
  // 允许的销量字符正则
  SALES_ALLOWED_CHARS: /[^\d]/g,
  // 价格的最大值
  PRICE_MAX_VALUE: 999999.99,
  // 销量的最大值
  SALES_MAX_VALUE: 99999999,
} as const;

/**
 * 实时输入验证处理器
 * 用于输入框的实时验证和反馈
 */
export class InputValidationHandler {
  private static instance: InputValidationHandler;
  private validationErrors: Map<string, string> = new Map();
  
  static getInstance(): InputValidationHandler {
    if (!InputValidationHandler.instance) {
      InputValidationHandler.instance = new InputValidationHandler();
    }
    return InputValidationHandler.instance;
  }
  
  /**
   * 处理价格输入验证
   */
  handlePriceInput(inputId: string, value: string, showFeedback: boolean = true): ValidationResult {
    const result = validatePriceInputEnhanced(value, showFeedback);
    
    if (!result.isValid && result.message) {
      this.validationErrors.set(inputId, result.message);
    } else {
      this.validationErrors.delete(inputId);
    }
    
    return result;
  }
  
  /**
   * 处理销量输入验证
   */
  handleSalesInput(inputId: string, value: string, showFeedback: boolean = true): ValidationResult {
    const result = validateSalesInputEnhanced(value, showFeedback);
    
    if (!result.isValid && result.message) {
      this.validationErrors.set(inputId, result.message);
    } else {
      this.validationErrors.delete(inputId);
    }
    
    return result;
  }
  
  /**
   * 获取指定输入的错误信息
   */
  getError(inputId: string): string | undefined {
    return this.validationErrors.get(inputId);
  }
  
  /**
   * 清除指定输入的错误信息
   */
  clearError(inputId: string): void {
    this.validationErrors.delete(inputId);
  }
  
  /**
   * 清除所有错误信息
   */
  clearAllErrors(): void {
    this.validationErrors.clear();
  }
  
  /**
   * 检查是否有任何验证错误
   */
  hasErrors(): boolean {
    return this.validationErrors.size > 0;
  }
  
  /**
   * 获取所有错误信息
   */
  getAllErrors(): string[] {
    return Array.from(this.validationErrors.values());
  }
}

/**
 * 全局输入验证处理器实例
 */
export const globalInputValidator = InputValidationHandler.getInstance();

/**
 * 创建输入错误消息
 */
export const createInputErrorMessage = (error: InputValidationError, context: string): string => {
  const errorMessages = {
    [InputValidationError.INVALID_FORMAT]: `${context}格式不正确`,
    [InputValidationError.OUT_OF_RANGE]: `${context}超出允许范围`,
    [InputValidationError.NEGATIVE_VALUE]: `${context}不能为负数`,
    [InputValidationError.RANGE_CONFLICT]: `${context}范围设置冲突`,
    [InputValidationError.TOO_LONG]: `${context}输入过长`,
    [InputValidationError.INVALID_DECIMAL]: `${context}小数位数不正确`
  };
  
  return errorMessages[error] || `${context}输入错误`;
};

/**
 * 显示输入验证错误
 */
export const showInputValidationError = (error: InputValidationError, context: string, suggestion?: string): void => {
  const message = createInputErrorMessage(error, context);
  
  ElMessage({
    message: suggestion ? `${message}：${suggestion}` : message,
    type: 'warning',
    duration: 3000,
    showClose: true
  });
};

/**
 * 批量验证筛选设置
 */
export const validateFilterSettings = (settings: {
  priceMin?: string;
  priceMax?: string;
  salesMin?: string;
  salesMax?: string;
}): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // 验证价格设置
  if (settings.priceMin || settings.priceMax) {
    const priceValidation = validatePriceRange(settings.priceMin, settings.priceMax);
    if (!priceValidation.isValid && priceValidation.error) {
      errors.push(priceValidation.error);
    }
  }
  
  // 验证销量设置
  if (settings.salesMin || settings.salesMax) {
    const salesValidation = validateSalesRange(settings.salesMin, settings.salesMax);
    if (!salesValidation.isValid && salesValidation.error) {
      errors.push(salesValidation.error);
    }
  }
  
  // 检查是否有任何筛选条件
  const hasAnyFilter = settings.priceMin || settings.priceMax || settings.salesMin || settings.salesMax;
  if (!hasAnyFilter) {
    warnings.push('未设置任何筛选条件，将跳过筛选步骤');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 安全的数值转换
 */
export const safeParseNumber = (value: string, type: 'price' | 'sales'): number | null => {
  try {
    if (!value || value.trim() === '') return null;
    
    if (type === 'price') {
      const cleaned = value.replace(/,/g, '');
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? null : parsed;
    } else {
      const parsed = parseInt(value);
      return isNaN(parsed) ? null : parsed;
    }
  } catch (error) {
    console.error(`数值转换失败 (${type}):`, error);
    return null;
  }
};

/**
 * 输入防抖处理
 */
export const createInputDebouncer = (callback: Function, delay: number = 300) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => callback.apply(null, args), delay);
  };
};