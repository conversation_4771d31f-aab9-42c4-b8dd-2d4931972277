#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨境蜂助手 - 主程序入口
负责应用程序的启动和初始化
"""

import sys
from utils.app_initializer import initialize_app


def main():
    """主函数"""
    try:
        # 初始化应用程序
        app, success = initialize_app(sys.argv)

        if not success or app is None:
            print("应用程序初始化失败")
            sys.exit(1)

        # 导入主窗口（在初始化完成后导入，确保路径已设置）
        from ui.main_window import MainWindow

        # 创建并显示主窗口
        window = MainWindow()
        window.show()

        print("应用程序启动成功")

        # 运行应用程序
        sys.exit(app.exec_())

    except Exception as e:
        print(f"启动应用程序时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
