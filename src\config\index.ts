import { ta } from "element-plus/es/locale";

const apiUrls = {
  production: {
    n11DebugMode: false,
    loginUrl    : 'https://trade.dailyhotnews.com.cn/api/login/loginSms',
    registerUrl : 'https://trade.dailyhotnews.com.cn/api/user/register',
    captcha     : 'https://trade.dailyhotnews.com.cn/api/captcha',
    temuGoodsAddUrl : 'https://trade.dailyhotnews.com.cn/api/temu/goodsAdd',
    temuCheckGoodsExistsUrl : 'https://trade.dailyhotnews.com.cn/api/temu/checkGoodsExists',
    temuBatchCheckGoodsExistsUrl : 'https://trade.dailyhotnews.com.cn/api/temu/batchCheckGoodsExists',
    userInfoUrl : 'https://trade.dailyhotnews.com.cn/api/user/info',
    userLogoutUrl : 'https://trade.dailyhotnews.com.cn/api/user/logout',
    storeListUrl : 'https://trade.dailyhotnews.com.cn/api/store/list',
    storeCreateUrl : 'https://trade.dailyhotnews.com.cn/api/store/create',
    storeUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/store/update',
    storeDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/store/delete',
    storeDetailUrl : 'https://trade.dailyhotnews.com.cn/api/store/detail',
    storeBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/store/batch-update',
    goodsListUrl : 'https://trade.dailyhotnews.com.cn/api/goods/list',
    goodsAddUrl : 'https://trade.dailyhotnews.com.cn/api/goods/add',
    goodsUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods/update',
    goodsDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/goods/delete',
    goodsDetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods/detail',
    goodsBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods/batch-update',
    goodsAdjustSkuPriceUrl : 'https://trade.dailyhotnews.com.cn/api/goods/adjust-sku-price',
    goodsPriceAdjustmentLogsUrl : 'https://trade.dailyhotnews.com.cn/api/goods/price-adjustment-logs',
    catTemuWebpageMainUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_webpage_main',
    catTemuWebpageListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_webpage_list',
    catTemuMainUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_main',
    catTemuListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list',
    catTemuListLazyUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list_lazy',
    catTemuListflatUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list_flat',
    catTemuChildrenCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_children_count',
    catTemuChildrenCountsUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_children_counts',
    catN11ListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_list',
    catN11DetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_detail',
    catN11UpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_update',
    catRelationSetUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_relation_set',
    catRelationListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_relation_list',
    userStoreInfoUrl : 'https://trade.dailyhotnews.com.cn/api/user/store_info',
    taskAddUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/add',
    taskListUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/list',
    taskStartUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/start',
    taskUpdateUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/update',
    taskDetailUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail',
    taskDetailListUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail-list',
    taskQueryResultsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/query-results',
    taskPendingQueryUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/pending-query',
    taskBatchUpdateUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/batch-update',
    taskSaveUploadParamsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/save-upload-params',
    taskRetryUploadParamsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/retry-upload-params',
    taskBatchRetryUploadUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/batch-retry-upload',
    taskBatchRetryUploadByStatusUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/batch-retry-upload-by-status',
    catTemuDetailUrl: 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_detail',
    goodsDirectoryListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/list',
    goodsDirectoryCreateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/create',
    goodsDirectoryUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update',
    goodsDirectoryDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/delete',
    goodsDirectoryDetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/detail',
    goodsDirectoryBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/batch-update',
    goodsDirectoryUpdateGoodsCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update-goods-count',
    goodsDirectoryUpdateAllGoodsCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update-all-goods-count',
    userCollectionSettingsUrl : 'https://trade.dailyhotnews.com.cn/api/user/collection-settings',
    userAvailableDirectoriesUrl : 'https://trade.dailyhotnews.com.cn/api/user/available-directories',
    userCheckNeedSetupUrl : 'https://trade.dailyhotnews.com.cn/api/user/check-need-setup',
    userProcessGoodsImagesUrl : 'https://trade.dailyhotnews.com.cn/api/user/process-goods-images',
    goodsNeedImageProcessUrl : 'https://trade.dailyhotnews.com.cn/api/goods/need-image-process',
    goodsStatisticsUrl : 'https://trade.dailyhotnews.com.cn/api/goods/statistics',
    // 商品分类关联相关API
    goodsCatRelationGetUrl : 'https://trade.dailyhotnews.com.cn/api/goods_cat_relation/get',
    goodsCatRelationSaveUrl : 'https://trade.dailyhotnews.com.cn/api/goods_cat_relation/save',
    goodsCatRelationDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/goods_cat_relation/delete',
    goodsCatRelationBatchGetUrl : 'https://trade.dailyhotnews.com.cn/api/goods_cat_relation/batch-get',
    kfUrl : 'https://trade.dailyhotnews.com.cn/qrcode/kf.png',
    //卡密管理相关API
    cardCodeListUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/list',
    cardCodeCreateUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/create',
    cardCodeBatchCreateUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/batch-create',
    cardCodeUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/update',
    cardCodeDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/delete',
    cardCodeBatchDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/batch-delete',
    cardCodeDetailUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/detail',
    cardCodeStatisticsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/statistics',
    cardCodeUseCardUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/use-card',
    cardCodeUserUsageRecordsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/user-usage-records',
    cardCodeUsageRecordsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/usage-records',
    cardCodeCardTypeOptionsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/card-type-options',
    cardCodeStatusOptionsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/status-options',
    cardCodeCopyUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/copy',
    cardCodeVipDaysUnitOptionsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/vip-days-unit-options',
    cardCodeCopyStatusOptionsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/copy-status-options',
    cardCodeMemberOptionsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/member-options',
    // AI Key管理相关API
    aiKeyListUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/list',
    aiKeyCreateUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/create',
    aiKeyUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/update',
    aiKeyDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/delete',
    aiKeyDetailUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/detail',
    aiKeyBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/batch-update',
    n11RejectedProductBatchSaveUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/batch-save',
    n11RejectedProductPendingCountUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/pending-count',
    n11RejectedProductNextPendingUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/next-pending',
    n11RejectedProductUpdateStatusUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/update-status',
    n11RejectedProductBatchUpdateStatusUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/batch-update-status',
    n11RejectedProductStatisticsUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/statistics',
    // AI改写任务相关API
    taskNewGenerateDetailsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/generate-details',
    taskNewNextAiTaskUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/next-ai-task',
    taskNewUpdateAiResultUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/update-ai-result',
    taskNewRandomAiKeyUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/random-ai-key',
    taskNewAiProgressUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/ai-progress',
    taskNewCompleteAiTaskUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/complete-ai-task',
    // 积分相关API
    pointsLogsUrl: 'https://trade.dailyhotnews.com.cn/api/user/points/logs',
    pointsCheckTaskDeductedUrl: 'https://trade.dailyhotnews.com.cn/api/user/points/check-task-deducted',
    // API凭证管理相关API
    generateCredentialsUrl: 'https://trade.dailyhotnews.com.cn/api/user/generate-api-credentials',
    // 子账号管理相关API
    subAccountListUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/list',
    subAccountCreateUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/create',
    subAccountUpdateUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/update',
    subAccountDeleteUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/delete',
    subAccountToggleStatusUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/toggle-status',
    subAccountDetailUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/detail',
    // 子账号商品管理相关API
    subAccountGoodsListUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account-goods/list',
    subAccountGoodsDeleteUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account-goods/delete',
  },
  development_1: {
    n11DebugMode: false,
    loginUrl    : 'https://trade.dailyhotnews.com.cn/api/login/loginSms',
    registerUrl : 'https://trade.dailyhotnews.com.cn/api/user/register',
    captcha     : 'https://trade.dailyhotnews.com.cn/api/captcha',
    temuGoodsAddUrl : 'https://trade.dailyhotnews.com.cn/api/temu/goodsAdd',
    temuCheckGoodsExistsUrl : 'https://trade.dailyhotnews.com.cn/api/temu/checkGoodsExists',
    temuBatchCheckGoodsExistsUrl : 'https://trade.dailyhotnews.com.cn/api/temu/batchCheckGoodsExists',
    userInfoUrl : 'https://trade.dailyhotnews.com.cn/api/user/info',
    userLogoutUrl : 'https://trade.dailyhotnews.com.cn/api/user/logout',
    storeListUrl : 'https://trade.dailyhotnews.com.cn/api/store/list',
    storeCreateUrl : 'https://trade.dailyhotnews.com.cn/api/store/create',
    storeUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/store/update',
    storeDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/store/delete',
    storeDetailUrl : 'https://trade.dailyhotnews.com.cn/api/store/detail',
    storeBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/store/batch-update',
    goodsListUrl : 'https://trade.dailyhotnews.com.cn/api/goods/list',
    goodsAddUrl : 'https://trade.dailyhotnews.com.cn/api/goods/add',
    goodsUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods/update',
    goodsDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/goods/delete',
    goodsDetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods/detail',
    goodsBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods/batch-update',
    goodsAdjustSkuPriceUrl : 'https://trade.dailyhotnews.com.cn/api/goods/adjust-sku-price',
    goodsPriceAdjustmentLogsUrl : 'https://trade.dailyhotnews.com.cn/api/goods/price-adjustment-logs',
    catTemuWebpageMainUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_webpage_main',
    catTemuWebpageListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_webpage_list',
    catTemuMainUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_main',
    catTemuListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list',
    catTemuListLazyUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list_lazy',
    catTemuListflatUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list_flat',
    catTemuChildrenCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_children_count',
    catTemuChildrenCountsUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_children_counts',
    catN11ListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_list',
    catN11DetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_detail',
    catN11UpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_update',
    catRelationSetUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_relation_set',
    catRelationListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_relation_list',
    userStoreInfoUrl : 'https://trade.dailyhotnews.com.cn/api/user/store_info',
    taskAddUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/add',
    taskListUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/list',
    taskStartUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/start',
    taskUpdateUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/update',
    taskDetailUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail',
    taskDetailListUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail-list',
    taskQueryResultsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/query-results',
    taskPendingQueryUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/pending-query',
    taskBatchUpdateUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/batch-update',
    taskSaveUploadParamsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/save-upload-params',
    taskRetryUploadParamsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/retry-upload-params',
    taskBatchRetryUploadUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/batch-retry-upload',
    taskBatchRetryUploadByStatusUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/batch-retry-upload-by-status',
    catTemuDetailUrl: 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_detail',
    goodsDirectoryListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/list',
    goodsDirectoryCreateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/create',
    goodsDirectoryUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update',
    goodsDirectoryDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/delete',
    goodsDirectoryDetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/detail',
    goodsDirectoryBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/batch-update',
    goodsDirectoryUpdateGoodsCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update-goods-count',
    goodsDirectoryUpdateAllGoodsCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update-all-goods-count',
    userCollectionSettingsUrl : 'https://trade.dailyhotnews.com.cn/api/user/collection-settings',
    userAvailableDirectoriesUrl : 'https://trade.dailyhotnews.com.cn/api/user/available-directories',
    userCheckNeedSetupUrl : 'https://trade.dailyhotnews.com.cn/api/user/check-need-setup',
    userProcessGoodsImagesUrl : 'https://trade.dailyhotnews.com.cn/api/user/process-goods-images',
    goodsNeedImageProcessUrl : 'https://trade.dailyhotnews.com.cn/api/goods/need-image-process',
    goodsStatisticsUrl : 'https://trade.dailyhotnews.com.cn/api/goods/statistics',
    // 商品分类关联相关API
    goodsCatRelationGetUrl : 'https://trade.dailyhotnews.com.cn/api/goods_cat_relation/get',
    goodsCatRelationSaveUrl : 'https://trade.dailyhotnews.com.cn/api/goods_cat_relation/save',
    goodsCatRelationDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/goods_cat_relation/delete',
    goodsCatRelationBatchGetUrl : 'https://trade.dailyhotnews.com.cn/api/goods_cat_relation/batch-get',
    kfUrl : 'https://trade.dailyhotnews.com.cn/qrcode/kf.png',

    //卡密管理相关API
    cardCodeListUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/list',
    cardCodeCreateUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/create',
    cardCodeBatchCreateUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/batch-create',
    cardCodeUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/update',
    cardCodeDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/delete',
    cardCodeBatchDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/batch-delete',
    cardCodeDetailUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/detail',
    cardCodeStatisticsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/statistics',
    cardCodeUseCardUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/use-card',
    cardCodeUserUsageRecordsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/user-usage-records',
    cardCodeUsageRecordsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/usage-records',
    cardCodeCardTypeOptionsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/card-type-options',
    cardCodeStatusOptionsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/status-options',
    cardCodeCopyUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/copy',
    cardCodeVipDaysUnitOptionsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/vip-days-unit-options',
    cardCodeCopyStatusOptionsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/copy-status-options',
    cardCodeMemberOptionsUrl : 'https://trade.dailyhotnews.com.cn/api/card_code/member-options',
    // AI Key管理相关API
    aiKeyListUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/list',
    aiKeyCreateUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/create',
    aiKeyUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/update',
    aiKeyDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/delete',
    aiKeyDetailUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/detail',
    aiKeyBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/ai_key/batch-update',
    n11RejectedProductBatchSaveUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/batch-save',
    n11RejectedProductPendingCountUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/pending-count',
    n11RejectedProductNextPendingUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/next-pending',
    n11RejectedProductUpdateStatusUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/update-status',
    n11RejectedProductBatchUpdateStatusUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/batch-update-status',
    n11RejectedProductStatisticsUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/statistics',
    // AI改写任务相关API
    taskNewGenerateDetailsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/generate-details',
    taskNewNextAiTaskUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/next-ai-task',
    taskNewUpdateAiResultUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/update-ai-result',
    taskNewRandomAiKeyUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/random-ai-key',
    taskNewAiProgressUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/ai-progress',
    taskNewCompleteAiTaskUrl: 'https://trade.dailyhotnews.com.cn/api/user/task-new/complete-ai-task',
    // 积分相关API
    pointsLogsUrl: 'https://trade.dailyhotnews.com.cn/api/user/points/logs',
    pointsCheckTaskDeductedUrl: 'https://trade.dailyhotnews.com.cn/api/user/points/check-task-deducted',
    // API凭证管理相关API
    generateCredentialsUrl: 'https://trade.dailyhotnews.com.cn/api/user/generate-api-credentials',
    // 子账号管理相关API
    subAccountListUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/list',
    subAccountCreateUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/create',
    subAccountUpdateUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/update',
    subAccountDeleteUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/delete',
    subAccountToggleStatusUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/toggle-status',
    subAccountDetailUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account/detail',
    // 子账号商品管理相关API
    subAccountGoodsListUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account-goods/list',
    subAccountGoodsDeleteUrl: 'https://trade.dailyhotnews.com.cn/api/sub-account-goods/delete',
  },
  development: {
    n11DebugMode: true,
    loginUrl    : 'http://tsa.test.com/api/login/loginSms',
    registerUrl : 'http://tsa.test.com/api/user/register',
    captcha     : 'http://tsa.test.com/api/captcha',
    temuGoodsAddUrl : 'http://tsa.test.com/api/temu/goodsAdd',
    temuCheckGoodsExistsUrl : 'http://tsa.test.com/api/temu/checkGoodsExists',
    temuBatchCheckGoodsExistsUrl : 'http://tsa.test.com/api/temu/batchCheckGoodsExists',
    userInfoUrl : 'http://tsa.test.com/api/user/info',
    userLogoutUrl : 'http://tsa.test.com/api/user/logout',
    storeListUrl : 'http://tsa.test.com/api/store/list',
    storeCreateUrl : 'http://tsa.test.com/api/store/create',
    storeUpdateUrl : 'http://tsa.test.com/api/store/update',
    storeDeleteUrl : 'http://tsa.test.com/api/store/delete',
    storeDetailUrl : 'http://tsa.test.com/api/store/detail',
    storeBatchUpdateUrl : 'http://tsa.test.com/api/store/batch-update',
    goodsListUrl : 'http://tsa.test.com/api/goods/list',
    goodsAddUrl : 'http://tsa.test.com/api/goods/add',
    goodsUpdateUrl : 'http://tsa.test.com/api/goods/update',
    goodsDeleteUrl : 'http://tsa.test.com/api/goods/delete',
    goodsDetailUrl : 'http://tsa.test.com/api/goods/detail',
    goodsBatchUpdateUrl : 'http://tsa.test.com/api/goods/batch-update',
    goodsAdjustSkuPriceUrl : 'http://tsa.test.com/api/goods/adjust-sku-price',
    goodsPriceAdjustmentLogsUrl : 'http://tsa.test.com/api/goods/price-adjustment-logs',
    catTemuWebpageMainUrl : 'http://tsa.test.com/api/goods_category/cat_temu_webpage_main',
    catTemuWebpageListUrl : 'http://tsa.test.com/api/goods_category/cat_temu_webpage_list',
    catTemuMainUrl : 'http://tsa.test.com/api/goods_category/cat_temu_main',
    catTemuListUrl : 'http://tsa.test.com/api/goods_category/cat_temu_list',
    catTemuListLazyUrl : 'http://tsa.test.com/api/goods_category/cat_temu_list_lazy',
    catTemuListflatUrl : 'http://tsa.test.com/api/goods_category/cat_temu_list_flat',
    catTemuChildrenCountUrl : 'http://tsa.test.com/api/goods_category/cat_temu_children_count',
    catTemuChildrenCountsUrl : 'http://tsa.test.com/api/goods_category/cat_temu_children_counts',
    catN11ListUrl : 'http://tsa.test.com/api/goods_category/cat_n11_list',
    catN11DetailUrl : 'http://tsa.test.com/api/goods_category/cat_n11_detail',
    catN11UpdateUrl : 'http://tsa.test.com/api/goods_category/cat_n11_update',
    catRelationSetUrl : 'http://tsa.test.com/api/goods_category/cat_relation_set',
    catRelationListUrl : 'http://tsa.test.com/api/goods_category/cat_relation_list',
    userStoreInfoUrl : 'http://tsa.test.com/api/user/store_info',
    taskAddUrl: 'http://tsa.test.com/api/user/task/add',
    taskListUrl: 'http://tsa.test.com/api/user/task/list',
    taskStartUrl: 'http://tsa.test.com/api/user/task/start',
    taskUpdateUrl: 'http://tsa.test.com/api/user/task/update',
    taskDetailUrl: 'http://tsa.test.com/api/user/task/detail',
    taskDetailListUrl: 'http://tsa.test.com/api/user/task/detail-list',
    taskQueryResultsUrl: 'http://tsa.test.com/api/user/task/detail/query-results',
    taskPendingQueryUrl: 'http://tsa.test.com/api/user/task/detail/pending-query',
    taskBatchUpdateUrl: 'http://tsa.test.com/api/user/task/detail/batch-update',
    taskSaveUploadParamsUrl: 'http://tsa.test.com/api/user/task/detail/save-upload-params',
    taskRetryUploadParamsUrl: 'http://tsa.test.com/api/user/task/detail/retry-upload-params',
    taskBatchRetryUploadUrl: 'http://tsa.test.com/api/user/task/detail/batch-retry-upload',
    taskBatchRetryUploadByStatusUrl: 'http://tsa.test.com/api/user/task/detail/batch-retry-upload-by-status',
    catTemuDetailUrl: 'http://tsa.test.com/api/goods_category/cat_temu_detail',
    goodsDirectoryListUrl : 'http://tsa.test.com/api/goods_directory/list',
    goodsDirectoryCreateUrl : 'http://tsa.test.com/api/goods_directory/create',
    goodsDirectoryUpdateUrl : 'http://tsa.test.com/api/goods_directory/update',
    goodsDirectoryDeleteUrl : 'http://tsa.test.com/api/goods_directory/delete',
    goodsDirectoryDetailUrl : 'http://tsa.test.com/api/goods_directory/detail',
    goodsDirectoryBatchUpdateUrl : 'http://tsa.test.com/api/goods_directory/batch-update',
    goodsDirectoryUpdateGoodsCountUrl : 'http://tsa.test.com/api/goods_directory/update-goods-count',
    goodsDirectoryUpdateAllGoodsCountUrl : 'http://tsa.test.com/api/goods_directory/update-all-goods-count',
    userCollectionSettingsUrl : 'http://tsa.test.com/api/user/collection-settings',
    userAvailableDirectoriesUrl : 'http://tsa.test.com/api/user/available-directories',
    userCheckNeedSetupUrl : 'http://tsa.test.com/api/user/check-need-setup',
    userProcessGoodsImagesUrl : 'http://tsa.test.com/api/user/process-goods-images',
    goodsNeedImageProcessUrl : 'http://tsa.test.com/api/goods/need-image-process',
    goodsStatisticsUrl : 'http://tsa.test.com/api/goods/statistics',
    // 商品分类关联相关API
    goodsCatRelationGetUrl : 'http://tsa.test.com/api/goods_cat_relation/get',
    goodsCatRelationSaveUrl : 'http://tsa.test.com/api/goods_cat_relation/save',
    goodsCatRelationDeleteUrl : 'http://tsa.test.com/api/goods_cat_relation/delete',
    goodsCatRelationBatchGetUrl : 'http://tsa.test.com/api/goods_cat_relation/batch-get',
    kfUrl : 'http://tsa.test.com/qrcode/kf.png',

    //卡密管理相关API
    cardCodeListUrl : 'http://tsa.test.com/api/card_code/list',
    cardCodeCreateUrl : 'http://tsa.test.com/api/card_code/create',
    cardCodeBatchCreateUrl : 'http://tsa.test.com/api/card_code/batch-create',
    cardCodeUpdateUrl : 'http://tsa.test.com/api/card_code/update',
    cardCodeDeleteUrl : 'http://tsa.test.com/api/card_code/delete',
    cardCodeBatchDeleteUrl : 'http://tsa.test.com/api/card_code/batch-delete',
    cardCodeDetailUrl : 'http://tsa.test.com/api/card_code/detail',
    cardCodeStatisticsUrl : 'http://tsa.test.com/api/card_code/statistics',
    cardCodeUseCardUrl : 'http://tsa.test.com/api/card_code/use-card',
    cardCodeUserUsageRecordsUrl : 'http://tsa.test.com/api/card_code/user-usage-records',
    cardCodeUsageRecordsUrl : 'http://tsa.test.com/api/card_code/usage-records',
    cardCodeCardTypeOptionsUrl : 'http://tsa.test.com/api/card_code/card-type-options',
    cardCodeStatusOptionsUrl : 'http://tsa.test.com/api/card_code/status-options',
    cardCodeCopyUrl : 'http://tsa.test.com/api/card_code/copy',
    cardCodeVipDaysUnitOptionsUrl : 'http://tsa.test.com/api/card_code/vip-days-unit-options',
    cardCodeCopyStatusOptionsUrl : 'http://tsa.test.com/api/card_code/copy-status-options',
    cardCodeMemberOptionsUrl : 'http://tsa.test.com/api/card_code/member-options',

    //AI Key管理相关API
    aiKeyListUrl : 'http://tsa.test.com/api/ai_key/list',
    aiKeyCreateUrl : 'http://tsa.test.com/api/ai_key/create',
    aiKeyUpdateUrl : 'http://tsa.test.com/api/ai_key/update',
    aiKeyDeleteUrl : 'http://tsa.test.com/api/ai_key/delete',
    aiKeyDetailUrl : 'http://tsa.test.com/api/ai_key/detail',
    aiKeyBatchUpdateUrl : 'http://tsa.test.com/api/ai_key/batch-update',

    n11RejectedProductBatchSaveUrl: 'http://tsa.test.com/api/n11/rejected-products/batch-save',
    n11RejectedProductPendingCountUrl: 'http://tsa.test.com/api/n11/rejected-products/pending-count',
    n11RejectedProductNextPendingUrl: 'http://tsa.test.com/api/n11/rejected-products/next-pending',
    n11RejectedProductUpdateStatusUrl: 'http://tsa.test.com/api/n11/rejected-products/update-status',
    n11RejectedProductBatchUpdateStatusUrl: 'http://tsa.test.com/api/n11/rejected-products/batch-update-status',
    n11RejectedProductStatisticsUrl: 'http://tsa.test.com/api/n11/rejected-products/statistics',
    // AI改写任务相关API
    taskNewGenerateDetailsUrl: 'http://tsa.test.com/api/user/task-new/generate-details',
    taskNewNextAiTaskUrl: 'http://tsa.test.com/api/user/task-new/next-ai-task',
    taskNewUpdateAiResultUrl: 'http://tsa.test.com/api/user/task-new/update-ai-result',
    taskNewRandomAiKeyUrl: 'http://tsa.test.com/api/user/task-new/random-ai-key',
    taskNewAiProgressUrl: 'http://tsa.test.com/api/user/task-new/ai-progress',
    taskNewCompleteAiTaskUrl: 'http://tsa.test.com/api/user/task-new/complete-ai-task',
    // 积分相关API
    pointsLogsUrl: 'http://tsa.test.com/api/user/points/logs',
    pointsCheckTaskDeductedUrl: 'http://tsa.test.com/api/user/points/check-task-deducted',
    // API凭证管理相关API
    generateCredentialsUrl: 'http://tsa.test.com/api/user/generate-api-credentials',
    // 子账号管理相关API
    subAccountListUrl: 'http://tsa.test.com/api/sub-account/list',
    subAccountCreateUrl: 'http://tsa.test.com/api/sub-account/create',
    subAccountUpdateUrl: 'http://tsa.test.com/api/sub-account/update',
    subAccountDeleteUrl: 'http://tsa.test.com/api/sub-account/delete',
    subAccountToggleStatusUrl: 'http://tsa.test.com/api/sub-account/toggle-status',
    subAccountDetailUrl: 'http://tsa.test.com/api/sub-account/detail',
    // 子账号商品管理相关API
    subAccountGoodsListUrl: 'http://tsa.test.com/api/sub-account-goods/list',
    subAccountGoodsDeleteUrl: 'http://tsa.test.com/api/sub-account-goods/delete',
  }
};

export default {
  ay :'hU5IfoZGq43TPkWr',
  api: '*',
  // N11调试模式开关 - 前端配置，true为调试模式（不真正调用N11 API），false为正常模式
  n11DebugMode: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11DebugMode,
  apiLoginUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.loginUrl,
  apiRegisterUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.registerUrl,
  apiCaptchaUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.captcha,
  apiTemuGoodsAddUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.temuGoodsAddUrl,
  apiTemuCheckGoodsExistsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.temuCheckGoodsExistsUrl,
  apiTemuBatchCheckGoodsExistsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.temuBatchCheckGoodsExistsUrl,
  apiUserInfoUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userInfoUrl,
  apiUserLogoutUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userLogoutUrl,
  apiStoreListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeListUrl,
  apiStoreCreateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeCreateUrl,
  apiStoreUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeUpdateUrl,
  apiStoreDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeDeleteUrl,
  apiStoreDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeDetailUrl,
  apiStoreBatchUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeBatchUpdateUrl,
  apiGoodsListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsListUrl,
  apiGoodsAddUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsAddUrl,
  apiGoodsUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsUpdateUrl,
  apiGoodsDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDeleteUrl,
  apiGoodsDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDetailUrl,
  apiGoodsBatchUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsBatchUpdateUrl,
  apiGoodsAdjustSkuPriceUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsAdjustSkuPriceUrl,
  apiGoodsPriceAdjustmentLogsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsPriceAdjustmentLogsUrl,
  apiCatTemuWebpageMainUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuWebpageMainUrl,
  apiCatTemuWebpageListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuWebpageListUrl,
  apiCatTemuMainUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuMainUrl,
  apiCatTemuListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuListUrl,
  apiCatTemuListLazyUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuListLazyUrl,
  apiCatTemuListflatUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuListflatUrl,
  apiCatTemuChildrenCountUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuChildrenCountUrl,
  apiCatTemuChildrenCountsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuChildrenCountsUrl,
  apiCatN11ListUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catN11ListUrl,
  apiCatN11DetailUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catN11DetailUrl,
  apiCatN11UpdateUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catN11UpdateUrl,
  apiCatRelationSetUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catRelationSetUrl,
  apiCatRelationListUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catRelationListUrl,
  apiUserStoreInfoUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userStoreInfoUrl,
  apiTaskAddUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskAddUrl,
  apiTaskListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskListUrl,
  apiTaskStartUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskStartUrl,
  apiTaskUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskUpdateUrl,
  apiTaskDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskDetailUrl,
  apiTaskDetailListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskDetailListUrl,
  apiTaskQueryResultsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskQueryResultsUrl,
  apiTaskPendingQueryUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskPendingQueryUrl,
  apiTaskBatchUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskBatchUpdateUrl,
  apiTaskSaveUploadParamsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskSaveUploadParamsUrl,
  apiTaskRetryUploadParamsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskRetryUploadParamsUrl,
  apiTaskBatchRetryUploadUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskBatchRetryUploadUrl,
  apiTaskBatchRetryUploadByStatusUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskBatchRetryUploadByStatusUrl,
  apiCatTemuDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuDetailUrl,
  apiGoodsDirectoryListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryListUrl,
  apiGoodsDirectoryCreateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryCreateUrl,
  apiGoodsDirectoryUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryUpdateUrl,
  apiGoodsDirectoryDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryDeleteUrl,
  apiGoodsDirectoryDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryDetailUrl,
  apiGoodsDirectoryBatchUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryBatchUpdateUrl,
  apiGoodsDirectoryUpdateGoodsCountUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryUpdateGoodsCountUrl,
  apiGoodsDirectoryUpdateAllGoodsCountUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryUpdateAllGoodsCountUrl,
  apiUserCollectionSettingsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userCollectionSettingsUrl,
  apiUserAvailableDirectoriesUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userAvailableDirectoriesUrl,
  apiUserCheckNeedSetupUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userCheckNeedSetupUrl,
  apiUserProcessGoodsImagesUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userProcessGoodsImagesUrl,
  apiGoodsNeedImageProcessUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsNeedImageProcessUrl,
  apiGoodsStatisticsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsStatisticsUrl,
  apiGoodsCatRelationGetUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsCatRelationGetUrl,
  apiGoodsCatRelationSaveUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsCatRelationSaveUrl,
  apiGoodsCatRelationDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsCatRelationDeleteUrl,
  apiGoodsCatRelationBatchGetUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsCatRelationBatchGetUrl,
  apiKfUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.kfUrl,
  // 卡密管理相关API
  apiCardCodeListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeListUrl,
  apiCardCodeCreateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeCreateUrl,
  apiCardCodeBatchCreateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeBatchCreateUrl,
  apiCardCodeUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeUpdateUrl,
  apiCardCodeDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeDeleteUrl,
  apiCardCodeBatchDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeBatchDeleteUrl,
  apiCardCodeDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeDetailUrl,
  apiCardCodeStatisticsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeStatisticsUrl,
  apiCardCodeUseCardUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeUseCardUrl,
  apiCardCodeUserUsageRecordsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeUserUsageRecordsUrl,
  apiCardCodeUsageRecordsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeUsageRecordsUrl,
  apiCardCodeCardTypeOptionsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeCardTypeOptionsUrl,
  apiCardCodeStatusOptionsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeStatusOptionsUrl,
  apiCardCodeCopyUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeCopyUrl,
  apiCardCodeVipDaysUnitOptionsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeVipDaysUnitOptionsUrl,
  apiCardCodeCopyStatusOptionsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeCopyStatusOptionsUrl,
  apiCardCodeMemberOptionsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.cardCodeMemberOptionsUrl,
  // AI Key管理相关API
  apiAiKeyListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.aiKeyListUrl,
  apiAiKeyCreateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.aiKeyCreateUrl,
  apiAiKeyUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.aiKeyUpdateUrl,
  apiAiKeyDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.aiKeyDeleteUrl,
  apiAiKeyDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.aiKeyDetailUrl,
  apiAiKeyBatchUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.aiKeyBatchUpdateUrl,
  // N11重新上传商品相关API
  apiN11RejectedProductBatchSaveUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductBatchSaveUrl,
  apiN11RejectedProductPendingCountUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductPendingCountUrl,
  apiN11RejectedProductNextPendingUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductNextPendingUrl,
  apiN11RejectedProductUpdateStatusUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductUpdateStatusUrl,
  apiN11RejectedProductBatchUpdateStatusUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductBatchUpdateStatusUrl,
  apiN11RejectedProductStatisticsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductStatisticsUrl,
  // AI改写任务相关API
  apiTaskNewGenerateDetailsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskNewGenerateDetailsUrl,
  apiTaskNewNextAiTaskUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskNewNextAiTaskUrl,
  apiTaskNewUpdateAiResultUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskNewUpdateAiResultUrl,
  apiTaskNewRandomAiKeyUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskNewRandomAiKeyUrl,
  apiTaskNewAiProgressUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskNewAiProgressUrl,
  apiTaskNewCompleteAiTaskUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskNewCompleteAiTaskUrl,
  // 积分相关API
  apiPointsLogsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.pointsLogsUrl,
  apiPointsCheckTaskDeductedUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.pointsCheckTaskDeductedUrl,
  // API凭证管理相关API
  apiGenerateCredentialsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.generateCredentialsUrl,
  // 子账号管理相关API
  apiSubAccountListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.subAccountListUrl,
  apiSubAccountCreateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.subAccountCreateUrl,
  apiSubAccountUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.subAccountUpdateUrl,
  apiSubAccountDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.subAccountDeleteUrl,
  apiSubAccountToggleStatusUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.subAccountToggleStatusUrl,
  apiSubAccountDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.subAccountDetailUrl,
  // 子账号商品管理相关API
  apiSubAccountGoodsListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.subAccountGoodsListUrl,
  apiSubAccountGoodsDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.subAccountGoodsDeleteUrl,
  urlPage:{
    'living_room_index' : 'https://leads.cluerich.com/pc/analysis/live-screen?fullscreen=0',
  },
};
