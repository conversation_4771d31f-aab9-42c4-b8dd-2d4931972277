<?php

declare(strict_types=1);

namespace App\Exceptions;

/**
 * 文件处理异常
 * 用于EXE API接口的文件处理失败场景
 */
class FileProcessException extends \Exception
{
    protected $code = 422;
    protected $message = '文件处理失败';

    public function __construct(?string $message = null, ?int $code = null, ?\Throwable $previous = null)
    {
        parent::__construct(
            $message ?? $this->message,
            $code ?? $this->code,
            $previous
        );
    }

    /**
     * 获取异常的响应数据
     *
     * @return array
     */
    public function getResponseData(): array
    {
        return [
            'code' => $this->getCode(),
            'message' => $this->getMessage(),
            'data' => null,
            'errors' => [
                'type' => 'file_process_error',
                'detail' => '文件处理过程中发生错误'
            ]
        ];
    }
}