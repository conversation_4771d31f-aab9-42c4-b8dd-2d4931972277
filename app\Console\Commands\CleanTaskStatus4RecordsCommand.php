<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class CleanTaskStatus4RecordsCommand extends Command
{
    /**
     * 命令名称和参数
     *
     * @var string
     */
    protected $signature = 'task:clean-status4-records';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '清理状态为4的任务详情记录并重新计算任务统计';

    /**
     * 统计信息
     */
    private $stats = [
        'status4_records' => 0,
        'affected_tasks' => 0,
        'deleted_detail_records' => 0,
        'deleted_goods_name_records' => 0,
        'deleted_up_params_records' => 0,
        'updated_tasks' => 0,
        'failed_tasks' => [],
        'task_ids' => []
    ];

    /**
     * 执行命令
     */
    public function handle()
    {
        try {
            $this->info('开始清理状态为4的任务详情记录...');
            
            // 1. 查询并统计状态为4的记录
            if (!$this->analyzeStatus4Records()) {
                return Command::FAILURE;
            }

            // 2. 显示统计信息并确认
            if (!$this->confirmExecution()) {
                $this->info('操作已取消');
                return Command::SUCCESS;
            }

            // 3. 执行清理操作
            $this->executeCleanup();

            // 4. 重新计算任务统计
            $this->recalculateTaskStats();

            // 5. 显示结果统计
            $this->showResults();

            return Command::SUCCESS;
            
        } catch (Exception $e) {
            $this->error("执行过程中发生错误: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 分析状态为4的记录
     */
    private function analyzeStatus4Records(): bool
    {
        $this->info('正在分析状态为4的记录...');
        
        try {
            // 查询所有状态为4的记录
            $status4Records = DB::table('user_task_detail')
                ->select('id', 'task_id')
                ->where('status', 4)
                ->get();

            $this->stats['status4_records'] = $status4Records->count();
            
            if ($this->stats['status4_records'] == 0) {
                $this->info('没有找到状态为4的记录，无需处理');
                return false;
            }

            // 统计涉及的任务ID（去重）
            $this->stats['task_ids'] = $status4Records->pluck('task_id')->unique()->values()->toArray();
            $this->stats['affected_tasks'] = count($this->stats['task_ids']);

            $this->info("找到 {$this->stats['status4_records']} 条状态为4的记录");
            $this->info("涉及 {$this->stats['affected_tasks']} 个不同的任务ID");
            
            return true;
            
        } catch (Exception $e) {
            $this->error("分析记录时发生错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 确认执行
     */
    private function confirmExecution(): bool
    {
        $this->newLine();
        $this->info('=== 清理统计 ===');
        $this->info("待删除的详情记录数: {$this->stats['status4_records']}");
        $this->info("涉及的任务数量: {$this->stats['affected_tasks']}");
        $this->info("涉及的任务ID: " . implode(', ', $this->stats['task_ids']));
        $this->newLine();
        
        $this->warn('此操作将会：');
        $this->warn('1. 删除所有状态为4的任务详情记录');
        $this->warn('2. 删除相关的商品名称记录');
        $this->warn('3. 删除相关的上传参数记录');
        $this->warn('4. 重新计算任务统计数据');
        $this->newLine();
        
        return $this->confirm('确认要执行清理操作吗？此操作不可逆！');
    }

    /**
     * 执行清理操作
     */
    private function executeCleanup(): void
    {
        $this->info('开始执行清理操作...');
        
        DB::beginTransaction();
        
        try {
            // 获取所有状态为4的记录ID，用于删除关联记录
            $status4DetailIds = DB::table('user_task_detail')
                ->where('status', 4)
                ->pluck('id')
                ->toArray();

            $this->info('正在删除关联记录...');
            
            // 删除 user_task_detail_goods_name 中的关联记录
            $deletedGoodsName = DB::table('user_task_detail_goods_name')
                ->whereIn('task_detail_id', $status4DetailIds)
                ->delete();
            $this->stats['deleted_goods_name_records'] = $deletedGoodsName;
            $this->info("删除商品名称记录: {$deletedGoodsName} 条");

            // 删除 user_task_detail_up_params 中的关联记录
            $deletedUpParams = DB::table('user_task_detail_up_params')
                ->whereIn('task_detail_id', $status4DetailIds)
                ->delete();
            $this->stats['deleted_up_params_records'] = $deletedUpParams;
            $this->info("删除上传参数记录: {$deletedUpParams} 条");

            // 删除状态为4的主记录
            $deletedDetailRecords = DB::table('user_task_detail')
                ->where('status', 4)
                ->delete();
            $this->stats['deleted_detail_records'] = $deletedDetailRecords;
            $this->info("删除任务详情记录: {$deletedDetailRecords} 条");

            DB::commit();
            $this->info('清理操作完成');
            
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception("清理操作失败: " . $e->getMessage());
        }
    }

    /**
     * 重新计算任务统计
     */
    private function recalculateTaskStats(): void
    {
        $this->info('开始重新计算任务统计...');
        
        $progressBar = $this->output->createProgressBar(count($this->stats['task_ids']));
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');
        $progressBar->setMessage('准备开始计算...');
        $progressBar->start();

        foreach ($this->stats['task_ids'] as $taskId) {
            try {
                $progressBar->setMessage("正在处理任务ID: {$taskId}");
                
                // 计算该任务下所有status > 0的记录数量（已完成数量）
                $completedCount = DB::table('user_task_detail')
                    ->where('task_id', $taskId)
                    ->where('status', '>', 0)
                    ->count();

                // 计算该任务下的总SKU数量
                $totalSkuCount = DB::table('user_task_detail')
                    ->where('task_id', $taskId)
                    ->count();

                // 更新任务统计
                $updated = DB::table('user_task')
                    ->where('id', $taskId)
                    ->update([
                        'task_num' => $completedCount,
                        'task_count' => $totalSkuCount,
                        'updated_at' => now()
                    ]);

                if ($updated) {
                    $this->stats['updated_tasks']++;
                } else {
                    $this->stats['failed_tasks'][] = $taskId;
                }
                
            } catch (Exception $e) {
                $this->stats['failed_tasks'][] = $taskId;
                $progressBar->setMessage("任务ID {$taskId} 处理失败: " . $e->getMessage());
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);
        $this->info('任务统计计算完成');
    }

    /**
     * 显示结果统计
     */
    private function showResults(): void
    {
        $this->info('=== 清理结果统计 ===');
        $this->info("删除的任务详情记录: {$this->stats['deleted_detail_records']}");
        $this->info("删除的商品名称记录: {$this->stats['deleted_goods_name_records']}");
        $this->info("删除的上传参数记录: {$this->stats['deleted_up_params_records']}");
        $this->info("更新成功的任务: {$this->stats['updated_tasks']}");
        
        if (!empty($this->stats['failed_tasks'])) {
            $this->warn("更新失败的任务ID: " . implode(', ', $this->stats['failed_tasks']));
            $this->warn("失败任务数量: " . count($this->stats['failed_tasks']));
        }
        
        // 计算成功率
        $totalTasks = count($this->stats['task_ids']);
        if ($totalTasks > 0) {
            $successRate = round($this->stats['updated_tasks'] / $totalTasks * 100, 2);
            $this->info("任务更新成功率: {$successRate}%");
        }
        
        $this->newLine();
        $this->info('清理操作已完成！');
    }
} 