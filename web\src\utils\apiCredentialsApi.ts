/**
 * API凭证管理相关API接口
 */
import { sendRequestViaBackground } from './api'
import { getApiUrl } from './apiConfig'

// API凭证数据类型定义
export interface ApiCredentials {
  appid: string | null
  appsecret?: string // 仅在首次获取时存在
  hasCredentials: boolean
}

// 生成API凭证响应类型定义
export interface GenerateCredentialsResponse {
  code: number
  message: string
  data: {
    appid: string
    appsecret: string
  }
}

// API凭证数据模型（完整数据）
export interface ApiCredentialsData {
  appid: string
  appsecret: string // 仅在生成时返回
  created_at: string
  updated_at: string
}

// API错误类型定义
export interface ApiError {
  code: number
  message: string
  data?: any
}

/**
 * 生成API凭证
 * 调用Laravel后端接口生成新的API凭证
 * @returns Promise<ApiCredentialsData> 生成的凭证信息
 */
export const generateApiCredentials = async (): Promise<ApiCredentialsData> => {
  try {
    const url = await getApiUrl('apiGenerateCredentialsUrl');
    console.log('生成API凭证URL:', url);
    
    const response = await sendRequestViaBackground({
      funName: 'generateApiCredentials',
      url,
      method: 'post',
      auth: true,
      encrypto: true
    });

    // 响应数据解析和验证
    if (!response) {
      throw new Error('API响应为空');
    }

    // 检查响应格式
    if (typeof response !== 'object') {
      throw new Error('API响应格式错误');
    }

    // 验证必要字段
    if (!response.appid || !response.appsecret) {
      throw new Error('API响应缺少必要的凭证信息');
    }

    // 返回格式化的凭证数据
    return {
      appid: response.appid,
      appsecret: response.appsecret,
      created_at: response.created_at || new Date().toISOString(),
      updated_at: response.updated_at || new Date().toISOString()
    };
  } catch (error) {
    console.error('生成API凭证失败:', error);
    
    // 错误处理和重新抛出
    if (error instanceof Error) {
      throw error;
    }
    
    // 处理API错误响应
    if (typeof error === 'object' && error !== null) {
      const apiError = error as ApiError;
      if (apiError.message) {
        throw new Error(apiError.message);
      }
    }
    
    throw new Error('生成API凭证时发生未知错误');
  }
};