#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作线程类
用于执行耗时操作，避免阻塞UI
"""

import json
import time
from PyQt5.QtCore import QThread, pyqtSignal
from typing import Dict, Any, List
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.goods_service import GoodsService
from database.db_manager import DatabaseManager
from database.goods_dao import GoodsDAO
from database.file_download_dao import FileDownloadDAO
from utils.file_manager import FileManager
from utils.upload_manager import UploadManager
from utils.helpers import cleanup_system


class ConfigValidationWorker(QThread):
    """配置验证工作线程"""

    # 信号定义
    validation_completed = pyqtSignal(bool, str, dict)  # (是否成功, 消息, 用户信息)
    error_occurred = pyqtSignal(str)  # 错误信息（专门用于状态栏错误显示）

    def __init__(self, appid: str, appsecret: str):
        super().__init__()
        self.appid = appid
        self.appsecret = appsecret
        self.goods_service = GoodsService()

    def run(self):
        """执行验证"""
        try:
            is_valid, error_msg, user_data = self.goods_service.validate_config(self.appid, self.appsecret)
            if not is_valid:
                # 发送错误信号到状态栏
                self.error_occurred.emit(f"配置验证失败: {error_msg}")
            self.validation_completed.emit(is_valid, error_msg, user_data)
        except Exception as e:
            validation_error_message = f"验证过程出错: {str(e)}"
            self.error_occurred.emit(validation_error_message)  # 发送错误信号到状态栏
            self.validation_completed.emit(False, validation_error_message, {})


class GoodsDataWorker(QThread):
    """商品数据处理工作线程 - 完整的商品数据处理流程"""

    # 信号定义
    data_received = pyqtSignal(bool, dict, str)  # (是否成功, 数据, 消息)
    progress_updated = pyqtSignal(str)  # 进度消息
    error_occurred = pyqtSignal(str)  # 错误信息（专门用于状态栏错误显示）
    info_message_occurred = pyqtSignal(str)  # 信息提示（专门用于状态栏友好提示显示）
    goods_progress_updated = pyqtSignal(dict)  # 商品级别进度 (商品信息)
    file_progress_updated = pyqtSignal(dict)  # 文件级别进度 (文件进度信息)
    processing_completed = pyqtSignal(bool, str)  # 处理完成 (是否成功, 消息)
    progress_reset_requested = pyqtSignal()  # 请求重置进度显示
    progress_activate_requested = pyqtSignal()  # 请求激活进度显示

    def __init__(self, appid: str, appsecret: str):
        super().__init__()
        self.appid = appid
        self.appsecret = appsecret
        self.goods_service = GoodsService()
        self.db_manager = DatabaseManager()
        self.goods_dao = GoodsDAO(self.db_manager)
        self.file_dao = FileDownloadDAO(self.db_manager)
        self.file_manager = FileManager()
        self.upload_manager = UploadManager(self.goods_service, self.file_dao)
        self.cleanup_performed = False  # 标记是否已执行过清理
        self.is_running = True

    def stop(self):
        """停止处理"""
        self.is_running = False

    def run(self):
        """执行完整的商品数据处理流程"""
        try:
            self.progress_updated.emit("开始处理商品数据...")
            # 添加标志来跟踪是否应该显示成功完成消息
            should_show_completion_message = False

            while self.is_running:
                # 获取待处理商品
                success, goods_data, error_msg = self._get_pending_goods()

                if not success:
                    error_message = f"获取商品失败: {error_msg}"
                    self.progress_updated.emit(error_message)
                    self.error_occurred.emit(error_message)  # 发送错误信号到状态栏
                    self._wait_and_retry(10)
                    continue

                # 检查接口响应是否有效
                if not goods_data:
                    self.progress_updated.emit("暂无待处理商品，10秒后重试...")

                    # 在没有待处理商品时执行清理操作（只执行一次）
                    if not self.cleanup_performed:
                        self._perform_cleanup_no_pending_goods()

                    self._wait_and_retry(10)
                    continue

                # 检查特定的"暂无待处理商品"响应（code=200且message="暂无待处理商品"）
                if (goods_data.get('code') == 200 and
                    goods_data.get('message') == "暂无待处理商品"):
                    self.progress_updated.emit("暂无待处理商品，10秒后重试...")
                    # 发送友好的信息提示到状态栏
                    self.info_message_occurred.emit("暂无待处理商品")

                    # 在没有待处理商品时执行清理操作和重置进度显示（只执行一次）
                    if not self.cleanup_performed:
                        self._perform_cleanup_no_pending_goods()
                        # 发送重置进度显示的信号
                        self.progress_reset_requested.emit()
                        # 设置标志，表示可以显示完成消息（因为确实没有更多商品要处理）
                        should_show_completion_message = True

                    self._wait_and_retry(10)
                    continue

                # 检查其他非200状态码的情况
                if goods_data.get('code') != 200:
                    self.progress_updated.emit("暂无待处理商品，10秒后重试...")

                    # 在没有待处理商品时执行清理操作和重置进度显示（只执行一次）
                    if not self.cleanup_performed:
                        self._perform_cleanup_no_pending_goods()
                        # 发送重置进度显示的信号
                        self.progress_reset_requested.emit()
                        # 设置标志，表示可以显示完成消息（因为确实没有更多商品要处理）
                        should_show_completion_message = True

                    self._wait_and_retry(10)
                    continue

                data = goods_data.get('data')
                if not data or data.get('goods_id') is None:
                    self.progress_updated.emit("暂无待处理商品，10秒后重试...")
                    # 发送友好的信息提示到状态栏
                    self.info_message_occurred.emit("暂无待处理商品")

                    # 在没有待处理商品时执行清理操作和重置进度显示（只执行一次）
                    if not self.cleanup_performed:
                        self._perform_cleanup_no_pending_goods()
                        # 发送重置进度显示的信号
                        self.progress_reset_requested.emit()
                        # 设置标志，表示可以显示完成消息（因为确实没有更多商品要处理）
                        should_show_completion_message = True

                    self._wait_and_retry(10)
                    continue

                # 处理单个商品
                # 重置清理标志，允许下次遇到"暂无待处理商品"时再次执行清理
                self.cleanup_performed = False
                # 重置完成消息标志，因为我们找到了要处理的商品
                should_show_completion_message = False

                # 确保进度面板处于活动状态（防止从等待状态恢复时出现显示问题）
                self.progress_activate_requested.emit()

                success = self._process_single_goods(data)

                if success:
                    self.progress_updated.emit("商品处理完成，1秒后处理下一个商品...")
                    time.sleep(1)
                else:
                    # 单个商品处理失败时，不中断整个流程，继续处理下一个商品
                    self.progress_updated.emit("商品处理失败，1秒后继续处理下一个商品...")
                    time.sleep(1)
                    # 不执行 break，让循环继续

            # 只有在正常运行状态下且应该显示完成消息时才发出处理完成信号
            if self.is_running and should_show_completion_message:
                self.processing_completed.emit(True, "商品数据处理完成")

        except Exception as e:
            process_error_message = f"处理过程出错: {str(e)}"
            self.progress_updated.emit(process_error_message)
            self.error_occurred.emit(process_error_message)  # 发送错误信号到状态栏
            self.processing_completed.emit(False, process_error_message)

    def _get_pending_goods(self):
        """获取待处理商品"""
        return self.goods_service.get_pending_goods(self.appid, self.appsecret)

    def _wait_and_retry(self, seconds: int):
        """等待指定秒数，显示倒计时"""
        for i in range(seconds, 0, -1):
            if not self.is_running:
                break
            self.progress_updated.emit(f"{i}秒后再次获取")
            time.sleep(1)

    def _process_single_goods(self, goods_data: dict) -> bool:
        """处理单个商品"""
        try:
            goods_id = goods_data.get('goods_id')
            goods_name = goods_data.get('goods_name', '')

            # 发送商品级别进度更新
            self.goods_progress_updated.emit({
                'goods_id': goods_id,
                'goods_platform_id': goods_data.get('goods_platform_id'),
                'goods_name': goods_name,
                'thumbnail': self._get_thumbnail_url(goods_data),
                'pending_count': goods_data.get('pending_total_count', 0)
            })

            self.progress_updated.emit(f"开始处理商品: {goods_name} (ID: {goods_id})")

            # 保存商品信息到数据库
            self._save_goods_to_database(goods_data)

            # 解析并保存文件记录
            file_records = self._parse_and_save_file_records(goods_data)

            if not file_records:
                self.progress_updated.emit("该商品没有需要处理的文件")
                return self._update_goods_status(goods_data)

            # 下载文件
            download_success = self._download_files(goods_id, file_records)
            if not download_success:
                return False

            # 本地化文件
            upload_success = self._upload_files(goods_id)
            if not upload_success:
                return False

            # 更新商品状态
            return self._update_goods_status(goods_data)

        except Exception as e:
            goods_error_message = f"处理商品时出错: {str(e)}"
            self.progress_updated.emit(goods_error_message)
            self.error_occurred.emit(goods_error_message)  # 发送错误信号到状态栏
            return False

    def _get_thumbnail_url(self, goods_data: dict) -> str:
        """获取商品缩略图URL"""
        try:
            media_files = goods_data.get('media_files', {})
            images = media_files.get('images', [])

            # 优先选择goods_pic类型的图片
            for img in images:
                if img.get('type') == 'goods_pic':
                    return img.get('url', '')

            # 如果没有goods_pic，选择第一张图片
            if images:
                return images[0].get('url', '')

            return ''
        except Exception:
            return ''

    def _save_goods_to_database(self, goods_data: dict):
        """保存商品信息到数据库"""
        try:
            self.goods_dao.insert_goods(
                user_id=goods_data.get('user_id'),
                phone=goods_data.get('phone', ''),
                goods_id=goods_data.get('goods_id'),
                goods_name=goods_data.get('goods_name', ''),
                goods_platform_id=goods_data.get('goods_platform_id'),
                directory_name=goods_data.get('directory_name', ''),
                media_files=goods_data.get('media_files', {}),
                pending_total_count=goods_data.get('pending_total_count', 0)
            )
        except Exception as e:
            save_goods_error_message = f"保存商品信息失败: {str(e)}"
            self.progress_updated.emit(save_goods_error_message)
            self.error_occurred.emit(save_goods_error_message)  # 发送错误信号到状态栏

    def _parse_and_save_file_records(self, goods_data: dict) -> List[dict]:
        """解析并保存文件记录"""
        file_records = []
        goods_id = goods_data.get('goods_id')

        try:
            # 清除该商品的旧文件记录
            self.file_dao.delete_files_by_goods_id(goods_id)

            media_files = goods_data.get('media_files', {})

            # 处理图片文件
            for img in media_files.get('images', []):
                url = img.get('url', '')
                if self.file_manager.is_remote_url(url):
                    record_id = self.file_dao.insert_file_record(
                        goods_id=goods_id,
                        file_type=img.get('type', 'image'),
                        field=img.get('field', ''),
                        url=url,
                        model_id=img.get('model_id'),
                        sku_id=img.get('sku_id')
                    )
                    file_records.append({
                        'id': record_id,
                        'goods_id': goods_id,
                        'type': img.get('type', 'image'),
                        'field': img.get('field', ''),
                        'url': url,
                        'model_id': img.get('model_id'),
                        'sku_id': img.get('sku_id')
                    })

            # 处理视频文件
            for video in media_files.get('videos', []):
                url = video.get('url', '')
                if self.file_manager.is_remote_url(url):
                    record_id = self.file_dao.insert_file_record(
                        goods_id=goods_id,
                        file_type=video.get('type', 'goods_video'),
                        field=video.get('field', ''),
                        url=url
                    )
                    file_records.append({
                        'id': record_id,
                        'goods_id': goods_id,
                        'type': video.get('type', 'goods_video'),
                        'field': video.get('field', ''),
                        'url': url
                    })

            # 处理PDF文件
            for pdf in media_files.get('pdfs', []):
                url = pdf.get('url', '')
                if self.file_manager.is_remote_url(url):
                    record_id = self.file_dao.insert_file_record(
                        goods_id=goods_id,
                        file_type=pdf.get('type', 'goods_pdf'),
                        field=pdf.get('field', ''),
                        url=url
                    )
                    file_records.append({
                        'id': record_id,
                        'goods_id': goods_id,
                        'type': pdf.get('type', 'goods_pdf'),
                        'field': pdf.get('field', ''),
                        'url': url
                    })

            return file_records

        except Exception as e:
            parse_files_error_message = f"解析文件记录失败: {str(e)}"
            self.progress_updated.emit(parse_files_error_message)
            self.error_occurred.emit(parse_files_error_message)  # 发送错误信号到状态栏
            return []

    def _download_files(self, goods_id: int, file_records: List[dict]) -> bool:
        """下载文件（支持并发下载）"""
        try:
            total_files = len(file_records)
            if total_files == 0:
                return True

            # 获取并发下载数量配置
            concurrent_count = self._get_concurrent_download_count()
            self.progress_updated.emit(f"开始下载文件，共{total_files}个（并发数：{concurrent_count}）")

            # 获取商品信息用于生成路径
            goods_info = self.goods_dao.get_goods_by_id(goods_id)
            if not goods_info:
                goods_info_error_message = "获取商品信息失败"
                self.progress_updated.emit(goods_info_error_message)
                self.error_occurred.emit(goods_info_error_message)  # 发送错误信号到状态栏
                return False

            user_id = goods_info['user_id']
            goods_platform_id = goods_info['goods_platform_id']

            # 准备下载任务
            download_tasks = []
            record_mapping = {}  # 用于映射任务到记录

            for record in file_records:
                url = record['url']
                file_type = record['type']

                # 生成本地存储路径，使用goods_platform_id
                relative_path = self.file_manager.generate_file_path(
                    user_id, goods_platform_id, file_type, url
                )
                local_path = self.file_manager.get_full_path(relative_path)

                task = {
                    'url': url,
                    'local_path': local_path,
                    'record_id': record['id'],
                    'relative_path': relative_path
                }
                download_tasks.append(task)
                record_mapping[record['id']] = record

            # 执行并发下载
            downloaded_count = 0

            def progress_callback(completed, total, current_file, success):
                """下载进度回调"""
                nonlocal downloaded_count
                if success:
                    downloaded_count += 1

                # 发送文件级别进度更新
                self.file_progress_updated.emit({
                    'stage': 'download',
                    'current': completed,
                    'total': total,
                    'file_type': 'file',
                    'progress_percent': int(completed / total * 100)
                })

                status = "成功" if success else "失败"
                self.progress_updated.emit(f"正在下载第{completed}个文件: {current_file} ({status})")

            def stop_check():
                """检查是否需要停止"""
                return not self.is_running

            # 使用并发下载
            success_count, total_count = self.file_manager.download_files_concurrent(
                download_tasks,
                max_workers=concurrent_count,
                progress_callback=progress_callback,
                stop_flag=stop_check
            )

            # 更新数据库状态
            self._update_download_status_batch(download_tasks, record_mapping)

            # 更新商品的下载完成状态
            all_downloaded = self.file_dao.check_all_files_downloaded(goods_id)
            self.goods_dao.update_files_downloaded_status(goods_id, all_downloaded)

            download_result_message = f"文件下载完成，成功{success_count}/{total_files}个"
            self.progress_updated.emit(download_result_message)

            # 如果下载失败率超过50%，发送错误信号
            if total_files > 0 and success_count / total_files < 0.5:
                error_message = f"下载失败率过高: 成功{success_count}/{total_files}个"
                self.error_occurred.emit(error_message)

            return success_count > 0

        except Exception as e:
            download_exception_message = f"下载文件时出错: {str(e)}"
            self.progress_updated.emit(download_exception_message)
            self.error_occurred.emit(download_exception_message)  # 发送错误信号到状态栏
            return False

    def _get_concurrent_download_count(self) -> int:
        """获取当前配置的并发下载数量"""
        try:
            return self.goods_dao.db_manager.execute_query(
                "SELECT concurrent_download_count FROM config WHERE appid = ?",
                (self.appid,)
            )[0]['concurrent_download_count'] or 5
        except:
            return 5  # 默认值

    def _get_concurrent_upload_count(self) -> int:
        """获取当前配置的并发本地化数量"""
        try:
            return self.goods_dao.db_manager.execute_query(
                "SELECT concurrent_upload_count FROM config WHERE appid = ?",
                (self.appid,)
            )[0]['concurrent_upload_count'] or 2
        except:
            return 2  # 默认值

    def _update_download_status_batch(self, download_tasks: List[dict], record_mapping: dict):
        """批量更新下载状态"""
        try:
            for task in download_tasks:
                record_id = task['record_id']
                local_path = task['local_path']
                relative_path = task['relative_path']

                # 检查文件是否下载成功
                if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
                    # 下载成功
                    self.file_dao.update_download_status(
                        record_id, True, relative_path, None
                    )
                else:
                    # 下载失败
                    self.file_dao.update_download_status(
                        record_id, False, None, "文件下载失败或文件为空"
                    )
        except Exception as e:
            self.progress_updated.emit(f"更新下载状态时出错: {str(e)}")

    def _upload_files(self, goods_id: int) -> bool:
        """本地化文件（支持并发处理）"""
        try:
            # 获取待上传的文件
            upload_files = self.file_dao.get_pending_upload_files(goods_id)
            total_files = len(upload_files)

            if total_files == 0:
                self.progress_updated.emit("没有需要本地化的文件")
                return True

            # 获取并发本地化数量配置
            concurrent_count = self._get_concurrent_upload_count()
            self.progress_updated.emit(f"开始本地化文件，共{total_files}个（并发数：{concurrent_count}）")

            # 准备本地化任务
            upload_tasks = []
            record_mapping = {}  # 用于映射任务到数据库记录

            for file_record in upload_files:
                local_path = self.file_manager.get_full_path(file_record['url_local'])

                task = {
                    'appid': self.appid,
                    'appsecret': self.appsecret,
                    'goods_id': file_record['goods_id'],
                    'goods_platform_id': file_record['goods_platform_id'],
                    'file_type': file_record['type'],
                    'field_name': file_record['field'],
                    'local_path': local_path,
                    'original_url': file_record['url'],
                    'record_id': file_record['id']
                }
                upload_tasks.append(task)
                record_mapping[file_record['id']] = file_record

            # 执行并发本地化
            uploaded_count = 0

            def progress_callback(completed, total, current_task, success):
                """本地化进度回调"""
                nonlocal uploaded_count
                if success:
                    uploaded_count += 1

                # 发送文件级别进度更新
                self.file_progress_updated.emit({
                    'stage': 'upload',
                    'current': completed,
                    'total': total,
                    'file_type': current_task.get('file_type', ''),
                    'progress_percent': int(completed / total * 100)
                })

                status = "成功" if success else "失败"
                file_name = current_task.get('local_path', '').split('/')[-1] if current_task.get('local_path') else ''
                self.progress_updated.emit(f"正在本地化第{completed}个文件: {file_name} ({status})")

            def stop_check():
                """检查是否需要停止"""
                return not self.is_running

            # 使用并发本地化
            success_count, total_count = self.upload_manager.upload_files_concurrent(
                upload_tasks,
                max_workers=concurrent_count,
                progress_callback=progress_callback,
                stop_flag=stop_check
            )

            # 更新数据库状态
            self._update_upload_status_batch(upload_tasks, record_mapping)

            upload_result_message = f"文件本地化完成，成功{success_count}/{total_files}个"
            self.progress_updated.emit(upload_result_message)

            # 如果本地化失败率超过50%，发送错误信号
            if total_files > 0 and success_count / total_files < 0.5:
                error_message = f"本地化失败率过高: 成功{success_count}/{total_files}个"
                self.error_occurred.emit(error_message)

            return success_count > 0

        except Exception as e:
            upload_exception_message = f"文件本地化时出错: {str(e)}"
            self.progress_updated.emit(upload_exception_message)
            self.error_occurred.emit(upload_exception_message)  # 发送错误信号到状态栏
            return False

    def _update_upload_status_batch(self, upload_tasks: List[dict], record_mapping: dict):
        """批量更新本地化状态（备用方法，主要状态更新已在并发本地化中完成）"""
        try:
            # 验证所有文件的本地化状态是否正确更新
            for task in upload_tasks:
                record_id = task.get('record_id')
                if record_id and record_id in record_mapping:
                    # 由于并发本地化中已经处理了成功/失败的逻辑，这里主要用于验证
                    # 可以在这里添加额外的状态检查逻辑
                    pass
        except Exception as e:
            self.progress_updated.emit(f"验证本地化状态时出错: {str(e)}")

    def _update_goods_status(self, goods_data: dict) -> bool:
        """更新商品状态"""
        try:
            goods_id = goods_data.get('goods_id')

            # 获取所有文件记录，构建本地化后的商品数据
            file_records = self.file_dao.get_files_by_goods_id(goods_id)

            # 更新商品数据中的文件URL为本地路径
            updated_goods_data = self._build_updated_goods_data(goods_data, file_records)

            self.progress_updated.emit("正在更新商品状态...")

            # 调用状态更新接口
            success, _, error_msg = self.goods_service.update_goods_status(
                self.appid, self.appsecret, updated_goods_data
            )

            if success:
                self.progress_updated.emit("商品状态更新成功")

                # 商品状态更新成功后执行清理操作
                self._perform_cleanup_after_success()

                # 注意：不在这里重置进度显示，因为可能还有下一个商品要处理
                # 只有在真正没有待处理商品时才重置进度显示

                return True
            else:
                error_message = f"商品状态更新失败: {error_msg}"
                self.progress_updated.emit(error_message)
                self.error_occurred.emit(error_message)  # 发送错误信号到状态栏
                return False

        except Exception as e:
            update_exception_message = f"更新商品状态时出错: {str(e)}"
            self.progress_updated.emit(update_exception_message)
            self.error_occurred.emit(update_exception_message)  # 发送错误信号到状态栏
            return False

    def _build_updated_goods_data(self, original_data: dict, file_records: List[dict]) -> dict:
        """构建更新后的商品数据，将远程URL替换为本地路径"""
        try:
            # 创建URL映射表
            url_mapping = {}
            uploaded_count = 0
            total_count = len(file_records)

            for record in file_records:
                if record['is_uploaded'] and record['url_local']:
                    url_mapping[record['url']] = record['url_local']
                    uploaded_count += 1

            # 调试信息
            # self.progress_updated.emit(f"URL映射表构建完成: {uploaded_count}/{total_count} 个文件已成功本地化并有本地路径")

            if not url_mapping:
                self.progress_updated.emit("警告: 没有找到已本地化的文件，将使用原始URL")
                return original_data

            # 深拷贝原始数据
            updated_data = json.loads(json.dumps(original_data))

            # 更新媒体文件中的URL
            media_files = updated_data.get('media_files', {})
            replaced_count = 0

            # 更新图片URL
            for img in media_files.get('images', []):
                original_url = img.get('url')
                if original_url in url_mapping:
                    img['url'] = url_mapping[original_url]
                    replaced_count += 1

            # 更新视频URL
            for video in media_files.get('videos', []):
                original_url = video.get('url')
                if original_url in url_mapping:
                    video['url'] = url_mapping[original_url]
                    replaced_count += 1

            # 更新PDF URL
            for pdf in media_files.get('pdfs', []):
                original_url = pdf.get('url')
                if original_url in url_mapping:
                    pdf['url'] = url_mapping[original_url]
                    replaced_count += 1

            self.progress_updated.emit(f"URL替换完成: {replaced_count} 个URL已替换为本地路径")
            return updated_data

        except Exception as e:
            build_data_error_message = f"构建更新数据时出错: {str(e)}"
            self.progress_updated.emit(build_data_error_message)
            self.error_occurred.emit(build_data_error_message)  # 发送错误信号到状态栏
            return original_data

    def _perform_cleanup_after_success(self):
        """商品状态更新成功后执行清理操作"""
        try:
            self.progress_updated.emit("清理缓存数据...")

            success, message = cleanup_system()

            if success:
                self.progress_updated.emit(f"系统清理完成: {message}")
            else:
                error_message = f"系统清理失败: {message}"
                self.progress_updated.emit(error_message)
                self.error_occurred.emit(error_message)

        except Exception as e:
            error_message = f"执行清理操作时出错: {str(e)}"
            self.progress_updated.emit(error_message)
            self.error_occurred.emit(error_message)

    def _perform_cleanup_no_pending_goods(self):
        """没有待处理商品时执行清理操作"""
        try:
            self.progress_updated.emit("清理缓存数据...")

            success, message = cleanup_system()

            if success:
                self.progress_updated.emit(f"缓存数据清理完成: {message}")
                self.cleanup_performed = True  # 标记已执行清理
            else:
                error_message = f"缓存数据清理失败: {message}"
                self.progress_updated.emit(error_message)
                self.error_occurred.emit(error_message)

        except Exception as e:
            error_message = f"执行缓存清理操作时出错: {str(e)}"
            self.progress_updated.emit(error_message)
            self.error_occurred.emit(error_message)
