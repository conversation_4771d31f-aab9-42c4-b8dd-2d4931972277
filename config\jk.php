<?php
/*
 * 系统相关配置
 * */
return [
    'app_url' => env('APP_URL',''),
    'platform'=>[
        [
            'name'   => 'APP',
            'values' => 1,
        ],
        [
            'name'   => '微信小程序',
            'values' => 2,
        ],
        [
            'name'   => '抖音小程序',
            'values' => 3,
        ],
    ],
    //文件上传
    'allowExtension' => [
        'image' => ['jpg','png','jpeg','gif','bmp'],
        'video' => ['mp4','avi','flv','mpeg','mov'],
        'audio' => ['mp3','wav','aac','wma','amr'],
        'file'  => [],
    ],
    //列表每页显示条数
    'page' => [
        'admin_per_page' => (int)env('ADMIN_PER_PAGE',15),//后台列表每页条数
    ],

    //批量插入配置
    'batch_insert' => [
        'task_details_batch_size' => (int)env('TASK_DETAILS_BATCH_SIZE', 1000), //任务详情批量插入每批次数量
    ],

    'system_config_cat'=>[
        1 => '站点配置'
    ],
    'system_config'=>[
        [
            'label' => '站点名称',
            'name'  => 'SiteName',
            'type'  => 'text',
            'value' => '',
            'sort'  => 1,
            'cat'   => 1
        ],
        [
            'label' => '占地面积(亩)',
            'name'  => 'SiteArea',
            'type'  => 'text',
            'value' => '',
            'sort'  => 5,
            'cat'   => 1
        ],
        [
            'label' => '品种数量(种)',
            'name'  => 'SiteVariety',
            'type'  => 'text',
            'value' => '',
            'sort'  => 3,
            'cat'   => 1
        ],
        [
            'label' => '引进数量(种)',
            'name'  => 'SiteIntroduction',
            'type'  => 'text',
            'value' => '',
            'sort'  => 4,
            'cat'   => 1
        ],
        [
            'label' => '土地轮播间隔(秒)',
            'name'  => 'SiteLandInterval',
            'type'  => 'text',
            'value' => '',
            'sort'  => 2,
            'cat'   => 1
        ],
        [
            'label' => '查看二维码验证',
            'name'  => 'SiteQrcodeCheck',
            'type'  => 'radio',
            'options' => [
                '0' => '关闭',
                '1' => '开启'
            ],
            'value' => 1,
            'sort'  => 6,
            'cat'   => 1
        ],
        [
            'label' => '大屏数据查看密码',
            'name'  => 'SiteScreenPassword',
            'type'  => 'text',
            'value' => '',
            'sort'  => 7,
            'cat'   => 1
        ],
        [
            'label' => '飞书通知地址',
            'name'  => 'feishu_notify_url',
            'type'  => 'textarea',
            'value' => '',
            'sort'  => 8,
            'cat'   => 1
        ],
        [
            'label' => '飞书通知状态',
            'name'  => 'feishu_notify_status',
            'type'  => 'radio',
            'options' => [
                '0' => '关闭',
                '1' => '开启'
            ],
            'value' => 1,
            'sort'  => 9,
            'cat'   => 1
        ]
    ]
];
