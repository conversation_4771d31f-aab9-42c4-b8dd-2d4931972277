/**
 * 商品去重工具
 * 负责检测和删除重复的商品
 */

import { extractGoodsIdFromUrl, batchValidateGoodsBeforeCollection } from '@/utils/goodsValidationApi';

/**
 * 商品去重结果
 */
export interface DeduplicationResult {
  /** 删除的商品数量 */
  deletedCount: number;
  /** 是否成功 */
  success: boolean;
  /** 错误信息（如果有） */
  error?: string;
}

/**
 * 执行商品去重操作
 * @returns 去重结果
 */
export const deduplicateProducts = async (): Promise<DeduplicationResult> => {
  try {
    // 获取所有商品容器（使用S.html中的确切选择器）
    const productsContainer = document.querySelector('.contentContainer .js-search-goodsList .autoFitList');

    if (!productsContainer) {
      console.error('所有商品容器选择器都未找到');
      return {
        deletedCount: 0,
        success: false,
        error: '未找到商品容器'
      };
    }

    // 获取商品元素（使用S.html中的确切选择器）
    const productElements = productsContainer.querySelectorAll('.EKDT7a3v');

    if (productElements.length === 0) {
      console.error('所有商品元素选择器都未找到');
      return {
        deletedCount: 0,
        success: false,
        error: '未找到商品'
      };
    }

    console.log(`找到 ${productElements.length} 个商品`);

    // 收集所有商品ID和对应的DOM元素
    const goodsMap = new Map<string, Element>();
    let foundLinks = 0;
    let extractedIds = 0;

    productElements.forEach((productElement, index) => {
      // 查找商品链接
      const linkElement = productElement.querySelector('a[href]') as HTMLAnchorElement;
      if (linkElement && linkElement.href) {
        foundLinks++;
        // 提取商品ID
        const goodsId = extractGoodsIdFromUrl(linkElement.href);
        if (goodsId) {
          extractedIds++;
          // 添加数据属性到商品容器
          productElement.setAttribute('data-goods-id', goodsId);
          goodsMap.set(goodsId, productElement);
          console.log(`商品 ${index}: ID=${goodsId}, URL=${linkElement.href}`);
        } else {
          console.warn(`商品 ${index}: 无法提取商品ID, URL=${linkElement.href}`);
        }
      } else {
        console.warn(`商品 ${index}: 未找到商品链接`);
      }
    });

    console.log(`找到 ${foundLinks} 个商品链接，提取到 ${extractedIds} 个商品ID`);

    if (goodsMap.size === 0) {
      return {
        deletedCount: 0,
        success: false,
        error: '未找到有效的商品ID'
      };
    }

    console.log(`收集到 ${goodsMap.size} 个商品ID`);

    // 将商品ID分批次（每批80个）
    const allGoodsIds = Array.from(goodsMap.keys());
    const batchSize = 80;
    const batches = [];

    for (let i = 0; i < allGoodsIds.length; i += batchSize) {
      batches.push(allGoodsIds.slice(i, i + batchSize));
    }

    console.log(`分 ${batches.length} 批次处理`);

    // 调用批量验证API
    const allExistingGoods = new Set<string>();

    // 分批处理
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`处理第 ${i + 1}/${batches.length} 批次，包含 ${batch.length} 个商品`);

      try {
        const existingGoods = await batchValidateGoodsBeforeCollection(batch);
        console.log(`批次 ${i} API返回数据:`, existingGoods);
        existingGoods.forEach(id => {
          console.log(`商品ID ${id}: 存在=true`);
          allExistingGoods.add(id);
        });
      } catch (error) {
        console.error(`第 ${i + 1} 批次验证失败:`, error);
        // 继续处理下一批次
      }
    }

    let deletedCount = 0;
    console.log(`准备删除 ${allExistingGoods.size} 个重复商品`, Array.from(allExistingGoods));
    console.log(`DOM映射中的商品ID列表:`, Array.from(goodsMap.keys()));

    if (allExistingGoods.size > 0) {
      // 删除对应的商品DOM元素
      allExistingGoods.forEach(goodsId => {
        const productElement = goodsMap.get(goodsId);
        if (productElement) {
          console.log(`找到商品ID ${goodsId} 的DOM元素，准备删除...`, productElement);
          productElement.remove();
          deletedCount++;
          console.log(`已删除商品ID ${goodsId} 的DOM元素`);
        } else {
          console.warn(`商品ID ${goodsId} 存在于API返回但找不到对应的DOM元素`);
        }
      });
    }

    console.log(`实际删除 ${deletedCount} 个商品`);

    return {
      deletedCount,
      success: true
    };

  } catch (error) {
    console.error('商品去重失败:', error);
    return {
      deletedCount: 0,
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
};
