/**
 * 设置管理工具
 * 负责保存和加载用户设置
 */

// Chrome API 类型声明 - 扩展现有的chrome命名空间
declare global {
  interface Window {
    chrome: {
      storage: {
        local: {
          get(keys?: string | string[] | { [key: string]: any } | null): Promise<{ [key: string]: any }>;
          set(items: { [key: string]: any }): Promise<void>;
        };
      };
    };
  }
}

// 使用全局chrome对象
const chrome = (globalThis as any).chrome || (window as any).chrome;

/**
 * 设置验证错误类型
 */
export enum SettingsValidationError {
  INVALID_PRICE_RANGE = 'INVALID_PRICE_RANGE',
  INVALID_SALES_RANGE = 'INVALID_SALES_RANGE',
  INVALID_PRICE_VALUE = 'INVALID_PRICE_VALUE',
  INVALID_SALES_VALUE = 'INVALID_SALES_VALUE',
  STORAGE_ERROR = 'STORAGE_ERROR'
}

/**
 * 设置验证异常
 */
export class SettingsError extends Error {
  constructor(
    public type: SettingsValidationError,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'SettingsError';
  }
}

/**
 * 商品筛选设置
 */
export interface ProductFilterSettings {
  /** 价格筛选 */
  priceFilter: {
    minPrice?: number;
    maxPrice?: number;
  };
  /** 销量筛选 */
  salesFilter: {
    minSales?: number;
    maxSales?: number;
  };
  /** 是否去除本地商品 */
  removeLocalProducts: boolean;
}

/**
 * 设置数据结构
 */
export interface TemuSearchSettings {
  /** 是否开启多选功能 */
  multiSelectEnabled: boolean;
  /** 筛选设置 */
  filterSettings: ProductFilterSettings;
}

/**
 * 存储键
 */
const STORAGE_KEY = 'temu_search_settings';

/**
 * 默认筛选设置
 */
const DEFAULT_FILTER_SETTINGS: ProductFilterSettings = {
  priceFilter: {
    minPrice: undefined,
    maxPrice: undefined
  },
  salesFilter: {
    minSales: undefined,
    maxSales: undefined
  },
  removeLocalProducts: true
};

/**
 * 默认设置
 */
const DEFAULT_SETTINGS: TemuSearchSettings = {
  multiSelectEnabled: false,
  filterSettings: DEFAULT_FILTER_SETTINGS
};

/**
 * 验证筛选设置
 * @param filterSettings 筛选设置
 * @throws {SettingsError} 验证失败时抛出异常
 */
const validateFilterSettings = (filterSettings: ProductFilterSettings): void => {
  const { priceFilter, salesFilter } = filterSettings;
  
  // 验证价格范围
  if (priceFilter.minPrice !== undefined && priceFilter.maxPrice !== undefined) {
    if (priceFilter.minPrice < 0 || priceFilter.maxPrice < 0) {
      throw new SettingsError(
        SettingsValidationError.INVALID_PRICE_VALUE,
        '价格值不能为负数',
        { minPrice: priceFilter.minPrice, maxPrice: priceFilter.maxPrice }
      );
    }
    if (priceFilter.minPrice > priceFilter.maxPrice) {
      throw new SettingsError(
        SettingsValidationError.INVALID_PRICE_RANGE,
        '最小价格不能大于最大价格',
        { minPrice: priceFilter.minPrice, maxPrice: priceFilter.maxPrice }
      );
    }
  }
  
  // 验证单个价格值
  if (priceFilter.minPrice !== undefined && priceFilter.minPrice < 0) {
    throw new SettingsError(
      SettingsValidationError.INVALID_PRICE_VALUE,
      '最小价格不能为负数',
      { minPrice: priceFilter.minPrice }
    );
  }
  
  if (priceFilter.maxPrice !== undefined && priceFilter.maxPrice < 0) {
    throw new SettingsError(
      SettingsValidationError.INVALID_PRICE_VALUE,
      '最大价格不能为负数',
      { maxPrice: priceFilter.maxPrice }
    );
  }
  
  // 验证销量范围
  if (salesFilter.minSales !== undefined && salesFilter.maxSales !== undefined) {
    if (salesFilter.minSales < 0 || salesFilter.maxSales < 0) {
      throw new SettingsError(
        SettingsValidationError.INVALID_SALES_VALUE,
        '销量值不能为负数',
        { minSales: salesFilter.minSales, maxSales: salesFilter.maxSales }
      );
    }
    if (salesFilter.minSales > salesFilter.maxSales) {
      throw new SettingsError(
        SettingsValidationError.INVALID_SALES_RANGE,
        '最小销量不能大于最大销量',
        { minSales: salesFilter.minSales, maxSales: salesFilter.maxSales }
      );
    }
  }
  
  // 验证单个销量值
  if (salesFilter.minSales !== undefined && salesFilter.minSales < 0) {
    throw new SettingsError(
      SettingsValidationError.INVALID_SALES_VALUE,
      '最小销量不能为负数',
      { minSales: salesFilter.minSales }
    );
  }
  
  if (salesFilter.maxSales !== undefined && salesFilter.maxSales < 0) {
    throw new SettingsError(
      SettingsValidationError.INVALID_SALES_VALUE,
      '最大销量不能为负数',
      { maxSales: salesFilter.maxSales }
    );
  }
  
  // 验证销量值为整数
  if (salesFilter.minSales !== undefined && !Number.isInteger(salesFilter.minSales)) {
    throw new SettingsError(
      SettingsValidationError.INVALID_SALES_VALUE,
      '销量值必须为整数',
      { minSales: salesFilter.minSales }
    );
  }
  
  if (salesFilter.maxSales !== undefined && !Number.isInteger(salesFilter.maxSales)) {
    throw new SettingsError(
      SettingsValidationError.INVALID_SALES_VALUE,
      '销量值必须为整数',
      { maxSales: salesFilter.maxSales }
    );
  }
};

/**
 * 验证完整设置
 * @param settings 设置对象
 * @throws {SettingsError} 验证失败时抛出异常
 */
const validateSettings = (settings: TemuSearchSettings): void => {
  if (!settings || typeof settings !== 'object') {
    throw new SettingsError(
      SettingsValidationError.STORAGE_ERROR,
      '设置对象格式无效'
    );
  }
  
  if (typeof settings.multiSelectEnabled !== 'boolean') {
    throw new SettingsError(
      SettingsValidationError.STORAGE_ERROR,
      'multiSelectEnabled必须为布尔值'
    );
  }
  
  if (!settings.filterSettings || typeof settings.filterSettings !== 'object') {
    throw new SettingsError(
      SettingsValidationError.STORAGE_ERROR,
      'filterSettings对象格式无效'
    );
  }
  
  validateFilterSettings(settings.filterSettings);
};

/**
 * 清理和标准化设置数据
 * @param settings 原始设置数据
 * @returns 清理后的设置数据
 */
const sanitizeSettings = (settings: any): TemuSearchSettings => {
  const sanitized: TemuSearchSettings = {
    multiSelectEnabled: Boolean(settings.multiSelectEnabled),
    filterSettings: {
      priceFilter: {
        minPrice: settings.filterSettings?.priceFilter?.minPrice !== undefined 
          ? Number(settings.filterSettings.priceFilter.minPrice) || undefined
          : undefined,
        maxPrice: settings.filterSettings?.priceFilter?.maxPrice !== undefined 
          ? Number(settings.filterSettings.priceFilter.maxPrice) || undefined
          : undefined
      },
      salesFilter: {
        minSales: settings.filterSettings?.salesFilter?.minSales !== undefined 
          ? Math.floor(Number(settings.filterSettings.salesFilter.minSales)) || undefined
          : undefined,
        maxSales: settings.filterSettings?.salesFilter?.maxSales !== undefined 
          ? Math.floor(Number(settings.filterSettings.salesFilter.maxSales)) || undefined
          : undefined
      },
      removeLocalProducts: Boolean(settings.filterSettings?.removeLocalProducts ?? true)
    }
  };
  
  return sanitized;
};

/**
 * 从本地存储加载设置
 * @returns 设置对象
 */
export const loadSettings = async (): Promise<TemuSearchSettings> => {
  try {
    const result = await chrome.storage.local.get([STORAGE_KEY]);
    
    if (result[STORAGE_KEY]) {
      const rawSettings = result[STORAGE_KEY];
      console.log('加载原始设置:', rawSettings);
      
      try {
        // 清理和标准化数据
        const sanitizedSettings = sanitizeSettings(rawSettings);
        
        // 确保向后兼容性，为现有用户提供默认筛选设置
        const mergedSettings: TemuSearchSettings = {
          ...DEFAULT_SETTINGS,
          ...sanitizedSettings,
          filterSettings: {
            ...DEFAULT_FILTER_SETTINGS,
            ...sanitizedSettings.filterSettings
          }
        };
        
        // 验证合并后的设置
        validateSettings(mergedSettings);
        
        console.log('加载设置成功:', mergedSettings);
        return mergedSettings;
      } catch (validationError) {
        console.warn('设置验证失败，使用默认设置:', validationError);
        // 验证失败时返回默认设置，但不抛出异常
        return DEFAULT_SETTINGS;
      }
    }
    
    console.log('未找到保存的设置，使用默认设置');
    return DEFAULT_SETTINGS;
  } catch (error) {
    console.error('加载设置失败:', error);
    // 存储访问失败时也返回默认设置
    return DEFAULT_SETTINGS;
  }
};

/**
 * 保存设置到本地存储
 * @param settings 要保存的设置
 * @throws {SettingsError} 验证失败或存储失败时抛出异常
 */
export const saveSettings = async (settings: TemuSearchSettings): Promise<void> => {
  try {
    // 验证设置
    validateSettings(settings);
    
    // 清理和标准化设置数据
    const sanitizedSettings = sanitizeSettings(settings);
    
    // 再次验证清理后的数据
    validateSettings(sanitizedSettings);
    
    // 保存到存储
    await chrome.storage.local.set({ [STORAGE_KEY]: sanitizedSettings });
    console.log('设置已保存:', sanitizedSettings);
  } catch (error) {
    if (error instanceof SettingsError) {
      console.error('设置验证失败:', error.message, error.details);
      throw error;
    } else {
      console.error('保存设置失败:', error);
      throw new SettingsError(
        SettingsValidationError.STORAGE_ERROR,
        '保存设置到存储失败',
        error
      );
    }
  }
};

/**
 * 更新单个设置项
 * @param key 设置键
 * @param value 设置值
 * @throws {SettingsError} 验证失败或保存失败时抛出异常
 */
export const updateSetting = async <K extends keyof TemuSearchSettings>(
  key: K,
  value: TemuSearchSettings[K]
): Promise<void> => {
  try {
    const currentSettings = await loadSettings();
    const newSettings = { ...currentSettings, [key]: value };
    await saveSettings(newSettings);
  } catch (error) {
    if (error instanceof SettingsError) {
      throw error;
    } else {
      throw new SettingsError(
        SettingsValidationError.STORAGE_ERROR,
        `更新设置项 ${String(key)} 失败`,
        error
      );
    }
  }
};

/**
 * 更新筛选设置
 * @param filterSettings 筛选设置
 * @throws {SettingsError} 验证失败或保存失败时抛出异常
 */
export const updateFilterSettings = async (filterSettings: ProductFilterSettings): Promise<void> => {
  try {
    // 先验证筛选设置
    validateFilterSettings(filterSettings);
    
    // 更新设置
    await updateSetting('filterSettings', filterSettings);
  } catch (error) {
    if (error instanceof SettingsError) {
      throw error;
    } else {
      throw new SettingsError(
        SettingsValidationError.STORAGE_ERROR,
        '更新筛选设置失败',
        error
      );
    }
  }
};

/**
 * 重置设置为默认值
 * @throws {SettingsError} 保存失败时抛出异常
 */
export const resetSettings = async (): Promise<void> => {
  try {
    await saveSettings(DEFAULT_SETTINGS);
    console.log('设置已重置为默认值');
  } catch (error) {
    throw new SettingsError(
      SettingsValidationError.STORAGE_ERROR,
      '重置设置失败',
      error
    );
  }
};

/**
 * 获取默认设置
 * @returns 默认设置对象的副本
 */
export const getDefaultSettings = (): TemuSearchSettings => {
  return JSON.parse(JSON.stringify(DEFAULT_SETTINGS));
};