<template>
  <div class="sub-account-goods-list">
    <!-- 页面头部组件 -->
    <SubAccountPageHeader
      :current-directory-name="currentDirectoryName"
      :current-sub-account-name="currentSubAccountName"
      :selected-goods="selectedGoods"
      @go-back="goBackToDirectoryList"
    />

    <!-- 采集设置卡片 - 仅子账号显示 -->
    <div v-if="userInfo.isSub" class="collection-settings-section">
      <CollectionSettingsCard />
    </div>

    <!-- 搜索表单组件 -->
    <SubAccountSearchForm
      v-model="searchForm"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 商品表格组件 -->
    <SubAccountGoodsTable
      :goods-list="goodsList"
      :loading="loading"
      :pagination="pagination"
      :sort-field="sortField"
      :sort-order="sortOrder"
      :user-info="userInfo"
      @selection-change="handleSelectionChange"
      @sort="handleSort"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @image-preview="handleImagePreview"
      @sku-detail="showSkuDialog"
      @sku-image-preview="handleSkuImagePreview"
      @copy-link="handleCopyGoodsLink"
      @delete-goods="handleDeleteSingleGoods"
      @price-adjustment="handlePriceAdjustment"
      @price-logs="handlePriceLogs"
    />

    <!-- SKU相关组件 -->
    <SkuComponents
      v-model:sku-visible="skuDialogVisible"
      v-model:sku-image-preview-visible="skuImagePreviewVisible"
      v-model:price-adjustment-visible="priceAdjustmentVisible"
      v-model:price-logs-visible="priceLogsVisible"
      :current-sku-list="currentSkuList"
      :current-sku-image-url="currentSkuImageUrl"
      :current-sku-name="currentSkuName"
      :current-goods-for-price-adjustment="currentGoodsForPriceAdjustment"
      :current-goods-for-price-logs="currentGoodsForPriceLogs"
      :price-adjustment-form="priceAdjustmentForm"
      :price-adjustment-submitting="priceAdjustmentSubmitting"
      :price-adjustment-logs="priceAdjustmentLogs"
      :price-logs-loading="priceLogsLoading"
      :price-logs-pagination="priceLogsPagination"
      @batch-price-adjustment="handleBatchPriceAdjustment"
      @price-adjustment-submit="handlePriceAdjustmentSubmit"
      @price-logs-page-size-change="handlePriceLogsPageSizeChange"
      @price-logs-current-change="handlePriceLogsCurrentChange"
    />

    <!-- 图片预览组件 -->
    <ImagePreview
      v-model:visible="imagePreviewVisible"
      v-model:current-index="currentImageIndex"
      :image-list="currentImageList"
      :goods-name="currentGoodsName"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { userInfo } from '../utils/userStore'
import CollectionSettingsCard from './CollectionSettingsCard.vue'

// 导入子账号专用组件
import SubAccountPageHeader from './subAccountGoodsList/SubAccountPageHeader.vue'
import SubAccountSearchForm from './subAccountGoodsList/SubAccountSearchForm.vue'
import SubAccountGoodsTable from './subAccountGoodsList/SubAccountGoodsTable.vue'
import SkuComponents from './goodsList/SkuComponents.vue'
import ImagePreview from './goodsList/ImagePreview.vue'

// 导入API
import {
  getSubAccountGoodsList,
  deleteSubAccountGoods,
  type SubAccountGoods as GoodsType,
  type SubAccountGoodsListParams
} from '../utils/subAccountGoodsApi'

// 导入价格调整相关API
import {
  adjustSkuPrice,
  getPriceAdjustmentLogs,
  type AdjustSkuPriceParams,
  type PriceAdjustmentLogsParams,
  type PriceAdjustmentLog
} from '../utils/goodsApi'

// 接口定义
interface SearchForm {
  goods_name: string
  goods_id: number | null
  status: number | null
  price_adjusted: string | null
  cat_adjusted: string | null
  time_type: string
  date_range: string[] | null
  sub_account_name: string
  sub_account_phone: string
  only_sub_account: boolean
  sub_account_id: number | null
}

// 响应式数据
const loading = ref(false)
const goodsList = ref<GoodsType[]>([])
const selectedGoods = ref<GoodsType[]>([])

const router = useRouter()
const route = useRoute()

// 当前目录信息
const currentDirectoryId = ref<string>('')
const currentDirectoryName = ref<string>('')

// 当前子账号信息
const currentSubAccountId = ref<string>('')
const currentSubAccountName = ref<string>('')

// 搜索表单
const searchForm = ref<SearchForm>({
  goods_name: '',
  goods_id: null,
  status: null,
  price_adjusted: null,
  cat_adjusted: null,
  time_type: 'created_at',
  date_range: null,
  sub_account_name: '',
  sub_account_phone: '',
  only_sub_account: false,
  sub_account_id: null // 主账号查询特定子账号时使用
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 排序数据
const sortField = ref<string>('')
const sortOrder = ref<string>('')

// 对话框状态
const skuDialogVisible = ref(false)
const skuImagePreviewVisible = ref(false)
const imagePreviewVisible = ref(false)
const currentImageIndex = ref(0)
const currentImageList = ref<string[]>([])
const currentGoodsName = ref('')

// SKU相关数据
const currentSkuList = ref<any[]>([])
const currentSkuImageUrl = ref('')
const currentSkuName = ref('')

// 价格调整相关数据
const priceAdjustmentVisible = ref(false)
const priceLogsVisible = ref(false)
const currentGoodsForPriceAdjustment = ref<any>(null)
const currentGoodsForPriceLogs = ref<any>(null)
const priceAdjustmentForm = ref({
  skus: [],
  batch_price: null
})
const priceAdjustmentSubmitting = ref(false)
const priceAdjustmentLogs = ref<any[]>([])
const priceLogsLoading = ref(false)
const priceLogsPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 移除了 collectionSettingsCardRef，因为不再需要手动控制采集设置组件

// 获取商品列表
const loadGoodsList = async () => {
  loading.value = true
  try {
    const params: SubAccountGoodsListParams = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      directory_id: currentDirectoryId.value && currentDirectoryId.value !== '0' ? parseInt(currentDirectoryId.value) : undefined,
      goods_name: searchForm.value.goods_name || undefined,
      goods_id: searchForm.value.goods_id || undefined,
      status: searchForm.value.status,
      time_type: searchForm.value.time_type || undefined,
      start_date: searchForm.value.date_range?.[0] || undefined,
      end_date: searchForm.value.date_range?.[1] || undefined,
      sort_field: sortField.value || undefined,
      sort_order: sortOrder.value || undefined,
      sub_account_id: currentSubAccountId.value && currentSubAccountId.value !== '0' ? parseInt(currentSubAccountId.value) : (searchForm.value.sub_account_id || undefined),
      // 主账号专属筛选参数
      price_adjusted: searchForm.value.price_adjusted || undefined,
      cat_adjusted: searchForm.value.cat_adjusted || undefined,
      only_sub_account: searchForm.value.only_sub_account ? true : undefined,
      // 子账号查询参数
      sub_account_name: searchForm.value.sub_account_name || undefined,
      sub_account_phone: searchForm.value.sub_account_phone || undefined
    }



    const response = await getSubAccountGoodsList(params)
    goodsList.value = response.list
    const responsePagination = response.pagination
    pagination.total = responsePagination.total
    pagination.totalPages = responsePagination.totalPages
    pagination.hasNext = responsePagination.hasNext
    pagination.hasPrevious = responsePagination.hasPrevious
  } catch (error) {
    console.error('获取商品列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadGoodsList()
}

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    goods_name: '',
    goods_id: null,
    status: null,
    price_adjusted: null,
    cat_adjusted: null,
    time_type: 'created_at',
    date_range: null,
    sub_account_name: '',
    sub_account_phone: '',
    only_sub_account: false,
    sub_account_id: null
  }
  sortField.value = ''
  sortOrder.value = ''
  pagination.currentPage = 1
  loadGoodsList()
}

// 返回目录列表
const goBackToDirectoryList = () => {
  router.push('/products')
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadGoodsList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadGoodsList()
}

// 排序处理
const handleSort = ({ prop, order }: { prop: string; order: string }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  loadGoodsList()
}

// 选择变化
const handleSelectionChange = (selection: GoodsType[]) => {
  selectedGoods.value = selection
}

// 图片预览
const handleImagePreview = (imageList: string[], index: number, goodsName: string) => {
  currentImageList.value = imageList
  currentImageIndex.value = index
  currentGoodsName.value = goodsName
  imagePreviewVisible.value = true
}

// SKU详情
const showSkuDialog = (skuList: any[], goodsName: string) => {
  currentSkuList.value = skuList
  currentSkuName.value = goodsName
  skuDialogVisible.value = true
}

// SKU图片预览
const handleSkuImagePreview = (imageUrl: string) => {
  currentSkuImageUrl.value = imageUrl
  skuImagePreviewVisible.value = true
}

// 复制商品链接
const handleCopyGoodsLink = (goods: GoodsType) => {
  const url = `https://www.temu.com/goods.html?goods_id=${goods.goods_id}`
  navigator.clipboard.writeText(url).then(() => {
    ElMessage.success('商品链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}

// 删除单个商品
const handleDeleteSingleGoods = async (goods: GoodsType) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品"${goods.goods_name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteSubAccountGoods(goods.id)
    ElMessage.success('商品删除成功')
    loadGoodsList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除商品失败:', error)
      ElMessage.error('删除商品失败')
    }
  }
}

// 价格调整相关函数
const handlePriceAdjustment = (goods: GoodsType) => {
  if (!goods.formatted_skus || goods.formatted_skus.length === 0) {
    ElMessage.warning('该商品没有SKU信息，无法调整价格')
    return
  }

  // 设置当前商品
  currentGoodsForPriceAdjustment.value = goods

  // 构建价格调整表单数据
  priceAdjustmentForm.value.skus = goods.formatted_skus.map((sku: any) => ({
    id: sku.id,
    spec_values: sku.sku,
    original_price: parseFloat(sku.price) || 0,
    new_price: parseFloat(sku.price) || 0,
    currency: sku.currentcy || 'USD',
    thumb_url: sku.thumb_url || ''
  }))
  priceAdjustmentForm.value.batch_price = null

  // 显示价格调整对话框
  priceAdjustmentVisible.value = true
}

const handlePriceLogs = (goods: GoodsType) => {
  // 设置当前商品
  currentGoodsForPriceLogs.value = goods

  // 重置分页
  priceLogsPagination.value.currentPage = 1

  // 显示价格日志对话框
  priceLogsVisible.value = true

  // 加载价格调整日志
  loadPriceAdjustmentLogs()
}

// 加载价格调整日志
const loadPriceAdjustmentLogs = async () => {
  if (!currentGoodsForPriceLogs.value) return

  priceLogsLoading.value = true
  try {
    const params: PriceAdjustmentLogsParams = {
      goods_id: currentGoodsForPriceLogs.value.id!,
      page: priceLogsPagination.value.currentPage,
      pageSize: priceLogsPagination.value.pageSize
    }

    const response = await getPriceAdjustmentLogs(params)
    priceAdjustmentLogs.value = response.list
    priceLogsPagination.value.total = response.pagination.total
  } catch (error) {
    console.error('获取价格调整日志失败:', error)
    ElMessage.error('获取价格调整日志失败')
  } finally {
    priceLogsLoading.value = false
  }
}

// 批量调整价格
const handleBatchPriceAdjustment = () => {
  if (!priceAdjustmentForm.value.batch_price || priceAdjustmentForm.value.batch_price <= 0) {
    ElMessage.warning('请输入批量调整价格')
    return
  }

  if (priceAdjustmentForm.value.batch_price < 0.01) {
    ElMessage.error('价格不能小于0.01')
    return
  }

  // 将批量价格应用到所有SKU
  priceAdjustmentForm.value.skus.forEach(sku => {
    sku.new_price = priceAdjustmentForm.value.batch_price!
  })

  ElMessage.success('SKU价格已批量填入，请点击确定调整按钮')
}

// 提交价格调整
const handlePriceAdjustmentSubmit = async () => {
  if (!currentGoodsForPriceAdjustment.value) return

  // 验证所有价格不能小于0.01
  const invalidPrices = priceAdjustmentForm.value.skus.filter(sku => sku.new_price < 0.01)
  if (invalidPrices.length > 0) {
    ElMessage.error('所有商品价格不能小于0.01')
    return
  }

  try {
    priceAdjustmentSubmitting.value = true

    // 构建调整参数
    const adjustments = priceAdjustmentForm.value.skus
      .filter(sku => Math.abs(sku.new_price - sku.original_price) >= 0.01)
      .map(sku => ({
        sku_id: sku.id,
        new_price: sku.new_price
      }))

    if (adjustments.length === 0) {
      ElMessage.warning('您没有调整商品SKU价格')
      return
    }

    const params: AdjustSkuPriceParams = {
      goods_id: currentGoodsForPriceAdjustment.value.id!,
      sku_adjustments: adjustments
    }

    const result = await adjustSkuPrice(params)
    ElMessage.success(result.message)

    // 关闭对话框并重新加载商品列表
    priceAdjustmentVisible.value = false
    loadGoodsList()

  } catch (error) {
    console.error('价格调整失败:', error)
    ElMessage.error('价格调整失败')
  } finally {
    priceAdjustmentSubmitting.value = false
  }
}

// 价格调整日志分页处理
const handlePriceLogsPageSizeChange = (pageSize: number) => {
  priceLogsPagination.value.pageSize = pageSize
  priceLogsPagination.value.currentPage = 1
  loadPriceAdjustmentLogs()
}

const handlePriceLogsCurrentChange = (page: number) => {
  priceLogsPagination.value.currentPage = page
  loadPriceAdjustmentLogs()
}

// 移除了 handleSettingsUpdated 函数，因为 CollectionSettingsCard 现在是独立组件

// 组件挂载时加载数据
onMounted(() => {
  // 检查路由名称来确定是按目录查看还是按子账号查看
  if (route.name === 'SubAccountGoodsListByUser') {
    // 按子账号查看模式
    currentSubAccountId.value = route.params.subAccountId as string || '0'
    currentSubAccountName.value = route.query.subAccountName as string || ''
    currentDirectoryId.value = '0' // 不限制目录
    currentDirectoryName.value = ''
  } else {
    // 按目录查看模式（原有逻辑）
    currentDirectoryId.value = route.params.directoryId as string || '0'
    currentDirectoryName.value = route.query.directoryName as string || ''
    currentSubAccountId.value = '0' // 不限制子账号
    currentSubAccountName.value = ''
  }

  // 只加载商品列表，CollectionSettingsCard 会自动加载自己的数据
  loadGoodsList()
})
</script>

<style scoped>
.sub-account-goods-list {
  padding: 20px;
}

.collection-settings-section {
  margin-bottom: 20px;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}
</style>