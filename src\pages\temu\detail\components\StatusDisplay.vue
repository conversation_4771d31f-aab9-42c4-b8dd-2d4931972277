<template>
  <div class="status-display" :class="{ 'collapsed': isCollapsed }">
    <!-- 折叠状态 - 显示圆圈和折叠按钮 -->
    <div v-if="isCollapsed" class="collapsed-content">
      <div 
        class="status-circle" 
        :class="getStatusCircleClass()"
        :title="getStatusTooltip()"
        @click="toggleCollapse"
      ></div>
      <el-button 
        text 
        size="small" 
        @click="toggleCollapse"
        class="toggle-btn collapsed-toggle"
        :title="'展开状态栏'"
      >
        》
      </el-button>
    </div>
    
    <!-- 展开状态 - 显示完整信息 -->
    <div v-else class="expanded-content">
      <div class="content-wrapper">
        <!-- 第一行：基本信息 -->
        <div class="basic-info-row">
          <span class="plugin-name">[{{ pluginName }}]</span>
          <span class="user-info">用户：{{ userInfo?.name || '未知' }}</span>
        </div>
        <!-- 第二行：目录和模式信息 -->
        <div class="status-info-row">
          <span class="directory-info">目录：{{ currentSettings?.default_directory_name || '未设置' }}</span>
          <span class="mode-info">模式：{{ currentSettings?.collection_mode_name || '未设置' }}</span>
        </div>
        <!-- 第三行：提醒信息 -->
        <div class="remind-info" :class="getRemindInfoClass()">
          <template v-if="currentSettings?.no_remind_until && currentSettings?.is_in_no_remind_period">
            <span class="remind-label">下次选择目录</span>
            <span class="remind-time">{{ currentSettings.no_remind_until }}</span>
          </template>
          <template v-else>
            每次均需手动选择存储目录
          </template>
        </div>
        <!-- 第四行：商品ID重复检查开关 -->
        <div class="duplicate-check-row">
          <div class="switch-container">
            <span class="switch-label">启用商品ID重复检查</span>
            <el-switch
              v-model="duplicateCheckEnabled"
              @change="onDuplicateCheckChange"
              size="small"
              :active-color="'#67c23a'"
              :inactive-color="'#dcdfe6'"
            />
          </div>
        </div>
        <!-- 第五行：设置按钮 -->
        <div class="settings-row">
          <el-button type="primary" size="small" @click="$emit('openSettings')">设置</el-button>
        </div>
      </div>
      <el-button 
        text 
        size="small" 
        @click="toggleCollapse"
        class="toggle-btn expanded-toggle"
        :title="'折叠状态栏'"
      >
        《
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElSwitch } from 'element-plus';
// 不再需要图标导入，使用文本符号
import type { CollectionSettings } from '@/utils/collectionSettingsApi';

interface Props {
  userInfo: any;
  currentSettings: CollectionSettings | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  openSettings: [];
  duplicateCheckToggle: [enabled: boolean];
}>();

const pluginName = ref('跨境蜂');
const isCollapsed = ref(false);
const duplicateCheckEnabled = ref(false);

// 缓存键名
const COLLAPSE_CACHE_KEY = 'temu_status_display_collapsed';
const DUPLICATE_CHECK_KEY = 'temu_duplicate_check_enabled';

// 获取状态圆圈的样式类
const getStatusCircleClass = () => {
  const mode = props.currentSettings?.collection_mode;
  
  switch (mode) {
    case 1:
      return 'auto-collect'; // 自动采集 - 绿色
    case 2:
      return 'manual-collect'; // 手动采集 - 橙色
    case 3:
      return 'no-collect'; // 不采集 - 红色
    default:
      return 'unknown'; // 未知状态 - 灰色
  }
};

// 获取状态圆圈的工具提示
const getStatusTooltip = () => {
  const modeName = props.currentSettings?.collection_mode_name || '未设置';
  const directoryName = props.currentSettings?.default_directory_name || '未设置';
  const remindInfo = getRemindInfoText();
  
  return `采集模式：${modeName}\n存储目录：${directoryName}\n${remindInfo}\n点击展开查看详情`;
};

// 获取提醒信息文本（用于工具提示）
const getRemindInfoText = () => {
  if (!props.currentSettings) {
    return '';
  }

  const settings = props.currentSettings;
  
  // 如果有no_remind_until设置且还在免提醒期内
  if (settings.no_remind_until && settings.is_in_no_remind_period) {
    return `下次选择目录：${settings.no_remind_until}`;
  }
  
  // 如果没有设置免提醒或者已经到期，表示每次都需要手动选择
  return '每次均需手动选择存储目录';
};

// 获取提醒信息的样式类
const getRemindInfoClass = () => {
  if (!props.currentSettings) {
    return 'remind-default';
  }

  const settings = props.currentSettings;
  
  // 如果有no_remind_until设置且还在免提醒期内
  if (settings.no_remind_until && settings.is_in_no_remind_period) {
    return 'remind-scheduled'; // 已设置免提醒时间
  }
  
  // 每次都需要手动选择
  return 'remind-manual';
};

// 切换展开/折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  // 缓存状态到localStorage
  localStorage.setItem(COLLAPSE_CACHE_KEY, isCollapsed.value.toString());
};

// 从缓存加载展开/折叠状态
const loadCollapseState = () => {
  try {
    const cached = localStorage.getItem(COLLAPSE_CACHE_KEY);
    if (cached !== null) {
      isCollapsed.value = cached === 'true';
    }
  } catch (error) {
    console.warn('加载状态栏折叠状态失败:', error);
  }
};

// 从本地存储加载重复检查开关状态
const loadDuplicateCheckState = () => {
  try {
    const cached = localStorage.getItem(DUPLICATE_CHECK_KEY);
    if (cached !== null) {
      duplicateCheckEnabled.value = cached === 'true';
    } else {
      // 默认值为 false
      duplicateCheckEnabled.value = false;
    }
  } catch (error) {
    console.warn('加载商品ID重复检查状态失败:', error);
    duplicateCheckEnabled.value = false;
  }
};

// 保存重复检查开关状态到本地存储
const saveDuplicateCheckState = (enabled: boolean) => {
  try {
    localStorage.setItem(DUPLICATE_CHECK_KEY, enabled.toString());
  } catch (error) {
    console.warn('保存商品ID重复检查状态失败:', error);
  }
};

// 重复检查开关变化处理
const onDuplicateCheckChange = (val: string | number | boolean) => {
  const enabled = Boolean(val);
  console.log('商品ID重复检查开关状态变化:', enabled);
  
  // 保存到本地存储
  saveDuplicateCheckState(enabled);
  
  // 通知父组件
  emit('duplicateCheckToggle', enabled);
};

// 从manifest.json获取插件名称
const getPluginName = async (): Promise<string> => {
  try {
    if (chrome.runtime?.getManifest) {
      const manifest = chrome.runtime.getManifest();
      return manifest.name || '跨境蜂';
    }
    return '跨境蜂';
  } catch (error) {
    console.warn('获取插件名称失败:', error);
    return '跨境蜂';
  }
};

onMounted(async () => {
  pluginName.value = await getPluginName();
  loadCollapseState();
  loadDuplicateCheckState();
});
</script>

<style scoped>
.status-display {
  position: fixed;
  top: 0px;
  left: 2px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  z-index: 10001;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

/* 展开状态样式 */
.expanded-content {
  padding: 10px 12px 10px 12px;
  display: flex;
  align-items: flex-start;
  max-width: 220px; 
  position: relative;
  min-height: auto;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  margin-right: 30px; /* 为右侧折叠按钮预留空间 */
}

.basic-info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.status-info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.duplicate-check-row {
  display: flex;
  justify-content: flex-start;
  margin-top: 4px;
}

.duplicate-check-row .switch-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}

.duplicate-check-row .switch-label {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  flex: 1;
}

.settings-row {
  display: flex;
  justify-content: flex-start;
  margin-top: 2px;
}

/* 折叠状态样式 */
.collapsed {
  padding: 8px;
  max-width: 60px;
}

.collapsed-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 状态圆圈样式 */
.status-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
  position: relative;
  cursor: help;
}

/* 不同采集模式的圆圈颜色 */
.status-circle.auto-collect {
  background-color: #67c23a; /* 绿色 - 自动采集 */
  box-shadow: 0 0 4px rgba(103, 194, 58, 0.5);
}

.status-circle.manual-collect {
  background-color: #e6a23c; /* 橙色 - 手动采集 */
  box-shadow: 0 0 4px rgba(230, 162, 60, 0.5);
}

.status-circle.no-collect {
  background-color: #f56c6c; /* 红色 - 不采集 */
  box-shadow: 0 0 4px rgba(245, 108, 108, 0.5);
}

.status-circle.unknown {
  background-color: #909399; /* 灰色 - 未知状态 */
  box-shadow: 0 0 4px rgba(144, 147, 153, 0.5);
}

/* 圆圈闪烁动画（仅自动采集模式） */
.status-circle.auto-collect::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  border: 2px solid rgba(103, 194, 58, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 切换按钮样式 */
.toggle-btn {
  padding: 4px !important;
  min-width: auto !important;
  height: 24px !important;
  color: #606266 !important;
  transition: all 0.2s ease;
}

.toggle-btn:hover {
  color: #409eff !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
}

.collapsed-toggle {
  font-size: 16px;
}

.expanded-toggle {
  font-size: 16px;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  flex-shrink: 0;
}

/* 插件名称样式 */
.plugin-name {
  color: #409eff;
  font-weight: bold;
  font-size: 13px;
}

/* 信息文本样式 */
.user-info, .directory-info, .mode-info {
  color: #606266;
  white-space: nowrap;
  font-size: 12px;
}

/* 提醒信息样式 */
.remind-info {
  font-size: 10px;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  padding: 4px 6px;
  border-radius: 4px;
  font-weight: 500;
  line-height: 1.4;
  width: 100%;
  margin-top: 2px;
  box-sizing: border-box;
  max-width: 100%;
}

.remind-label {
  display: block;
  font-size: 9px;
  color: inherit;
  margin-bottom: 1px;
  opacity: 0.8;
}

.remind-time {
  display: block;
  font-size: 11px;
  font-weight: bold;
  color: #303133;
  margin-top: 1px;
}

.remind-info.remind-manual {
  color: #e6a23c;
  background-color: rgba(230, 162, 60, 0.1);
  border: 1px solid rgba(230, 162, 60, 0.3);
}

.remind-info.remind-scheduled {
  color: #67c23a;
  background-color: rgba(103, 194, 58, 0.1);
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.remind-info.remind-default {
  color: #909399;
  background-color: rgba(144, 147, 153, 0.1);
  border: 1px solid rgba(144, 147, 153, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-display {
    top: 10px;
    left: 10px;
  }
  
  .expanded-content {
    max-width: 200px;
    padding: 8px 10px;
  }
  
  .collapsed {
    padding: 6px;
    max-width: 50px;
  }
  
  .status-circle {
    width: 14px;
    height: 14px;
  }
  
  .plugin-name {
    font-size: 12px;
  }
  
  .user-info, .directory-info, .mode-info {
    font-size: 11px;
  }
  
  .remind-info {
    font-size: 9px;
  }
  
  .basic-info-row, .status-info-row {
    gap: 6px;
  }
  
  .remind-label {
    font-size: 8px;
  }
  
  .remind-time {
    font-size: 10px;
  }
}

/* 工具提示样式 */
.status-circle[title] {
  cursor: help;
}
</style> 